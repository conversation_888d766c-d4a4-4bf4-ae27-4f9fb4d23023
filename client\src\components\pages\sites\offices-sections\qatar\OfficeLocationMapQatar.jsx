import OfficeLocationMap from "@/components/ui/OfficeLocationMap";

function OfficeLocationMapQatar({ t }) {
  return (
    <OfficeLocationMap
      title={t("qatar:officeLocation:label")}
      subtitle={t("qatar:officeLocation:title")}
      address={t("qatar:officeLocation:address")}
      tel={t("qatar:officeLocation:tel1")}
      email={t("qatar:officeLocation:mail")}
      linkText={t("qatar:officeLocation:talk")}
      mapSrc="https://www.google.com/maps/embed?pb=!1m14!1m12!1m3!1d2550.338911969447!2d51.516638227215374!3d25.313479565965885!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!5e0!3m2!1sfr!2stn!4v1732012378259!5m2!1sfr!2stn"
    
    />
  );
}
export default OfficeLocationMapQatar;
