"use client";
import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { toast } from "react-toastify";

import { axiosGetJson } from "@/config/axios";
import { API_URLS } from "@/utils/urls";
import Loading from "@/components/loading/Loading";

const page = () => {
  const router = useRouter();

  useEffect(() => {
    const handleLogout = async () => {
      try {
        await axiosGetJson.post(API_URLS.logout);
        window.location.href = "/";
      } catch (error) {
        toast.error("Logout failed", error);
      }
    };

    handleLogout();
  }, [router]);

  return <Loading logout />;
};

export default page;
