"use client";
import LaborLaws from "../LaborLaws";
import { createList } from '@/utils/functions';
export const LABOR_KEYS = {
    workingHours: {
        title: "france:franceLabor:workingHours:title",
        subtitle1: "france:franceLabor:workingHours:subTitle1",
        subtitle2: "france:franceLabor:workingHours:subTitle2",
        description1: "france:franceLabor:workingHours:description",
        data1: "france:franceLabor:workingHours:data1",
        data2: "france:franceLabor:workingHours:data2",
        data3: "france:franceLabor:workingHours:data3",
    },
    employmentContracts: {
        title: "france:franceLabor:employmentContracts:title",
        subtitle1: "france:franceLabor:employmentContracts:title1",
        data1: "france:franceLabor:employmentContracts:data1",
        data2: "france:franceLabor:employmentContracts:data2",
        data3: "france:franceLabor:employmentContracts:data3",
    },

};
export const franceLaborData = {
    title: "france:franceLabor:title",
    sections: [
        {
            id: 1,
            title: LABOR_KEYS.workingHours.title,
            subsections: [
                {
                    title: LABOR_KEYS.workingHours.subtitle1,
                    description: LABOR_KEYS.workingHours.description1,
                },
                {
                    title: LABOR_KEYS.workingHours.subtitle2,
                    description: "france:franceLabor:workingHours:description2",
                },
                {
                    list: createList([LABOR_KEYS.workingHours.data1, LABOR_KEYS.workingHours.data2, LABOR_KEYS.workingHours.data3]),
                },
                {
                    description: "france:franceLabor:workingHours:description3",
                    list: createList(["france:franceLabor:workingHours:data4", "france:franceLabor:workingHours:data5"]),
                },
            ],
        },
        {
            id: 2,
            title: LABOR_KEYS.employmentContracts.title,
            subsections: [
                {
                    title: LABOR_KEYS.employmentContracts.subtitle1,
                    list: createList([LABOR_KEYS.employmentContracts.data1, LABOR_KEYS.employmentContracts.data2, LABOR_KEYS.employmentContracts.data3]),
                },
            ],
        },
        {
            id: 3,
            title: "france:franceLabor:payroll:title",
            type: "payroll",
            titleKey: "france:franceLabor:payroll:title1",
            items: [
                {
                    titleKey: "france:franceLabor:payroll:fiscalYear:title",
                    dateKeys: ["france:franceLabor:payroll:fiscalYear:date1", "france:franceLabor:payroll:fiscalYear:date2"],
                    descriptionKey: "france:franceLabor:payroll:fiscalYear:description",
                },
                {
                    titleKey: "france:franceLabor:payroll:payrollCycle:title",
                    dateKeys: ["france:franceLabor:payroll:payrollCycle:date"],
                    descriptionKey: "france:franceLabor:payroll:payrollCycle:description",
                },
                {
                    titleKey: "france:franceLabor:payroll:minimumWage:title",
                    dateKeys: ["france:franceLabor:payroll:minimumWage:wage", "france:franceLabor:payroll:minimumWage:date"],
                    descriptionKey: "france:franceLabor:payroll:minimumWage:description",
                },
                {
                    titleKey: "france:franceLabor:payroll:payrollManagement:title",
                    dateKeys: ["france:franceLabor:payroll:payrollManagement:date1"],
                    descriptionKey: "france:franceLabor:payroll:payrollManagement:description",
                },
            ],
        },
        {
            id: 4,
            title: "france:franceLabor:termination:title",
            subsections: [
                {
                    title: "france:franceLabor:termination:title1",
                    description: "france:franceLabor:termination:description1",
                },
                {
                    title: "france:franceLabor:termination:title2",
                    description: "france:franceLabor:termination:description2",
                },
                {
                    list: createList(["france:franceLabor:termination:data1", "france:franceLabor:termination:data2", "france:franceLabor:termination:data3"]),
                },
                {
                    title: "france:franceLabor:termination:title3",
                    list: createList([
                        "france:franceLabor:termination:dataNoticePeriod1",
                        "france:franceLabor:termination:dataNoticePeriod2",
                        "france:franceLabor:termination:dataNoticePeriod3"
                    ]),
                },
                {
                    title: "france:franceLabor:termination:title4",
                    description: "france:franceLabor:termination:description4",
                },
            ],
        },
        {
            id: 5,
            title: "france:franceLabor:leaveEntitlements:title",
            type: "leaveEntitlements",
            data: {
                description: "france:franceLabor:leaveEntitlements:description",
                leaves: [
                    { date: "france:franceLabor:leaveEntitlements:leaves:dataS1:date", title: "france:franceLabor:leaveEntitlements:leaves:dataS1:title" },
                    { date: "france:franceLabor:leaveEntitlements:leaves:dataS2:date", title: "france:franceLabor:leaveEntitlements:leaves:dataS2:title" },
                    { date: "france:franceLabor:leaveEntitlements:leaves:dataS3:date", title: "france:franceLabor:leaveEntitlements:leaves:dataS3:title" },
                    { date: "france:franceLabor:leaveEntitlements:leaves:dataS4:date", title: "france:franceLabor:leaveEntitlements:leaves:dataS4:title" },
                    { date: "france:franceLabor:leaveEntitlements:leaves:dataS5:date", title: "france:franceLabor:leaveEntitlements:leaves:dataS5:title" },
                    { date: "france:franceLabor:leaveEntitlements:leaves:dataS6:date", title: "france:franceLabor:leaveEntitlements:leaves:dataS6:title" },
                    { date: "france:franceLabor:leaveEntitlements:leaves:dataS7:date", title: "france:franceLabor:leaveEntitlements:leaves:dataS7:title" },

                ],
                maternityLeave: {
                    title: "france:franceLabor:leaveEntitlements:leaves:maternityLeave:title",
                    description: ["france:franceLabor:leaveEntitlements:leaves:maternityLeave:description1"],
                },
                sickLeave: {
                    title: "france:franceLabor:leaveEntitlements:leaves:sickLeave:title",
                    description: ["france:franceLabor:leaveEntitlements:leaves:sickLeave:description"],
                },
            },
        },
        {
            id: 6,
            title: "france:franceLabor:tax:title",
            subsections: [
                { description: "france:franceLabor:tax:description" },
                {
                    title: "france:franceLabor:tax:title1",
                    description: "france:franceLabor:tax:description1",
                },
                {
                    list: createList(["france:franceLabor:tax:data1", "france:franceLabor:tax:data2", "france:franceLabor:tax:data3", "france:franceLabor:tax:data4"]),
                },
                {
                    title: "france:franceLabor:tax:title2",
                    description: "france:franceLabor:tax:description2",
                    list: createList(["france:franceLabor:tax:dataS1", "france:franceLabor:tax:dataS2", "france:franceLabor:tax:dataS3"]),
                },
            ],
        },
        {
            id: 7,
            title: "france:franceLabor:visa:title",
            subsections: [
                { description: "france:franceLabor:visa:description1" },
                {
                    list: createList(["france:franceLabor:visa:ul1", "france:franceLabor:visa:ul1"]),
                },
                { description: "france:franceLabor:visa:description2" },
                { description: "france:franceLabor:visa:description22" },
                { title: "france:franceLabor:visa:title1" },
                {
                    list: createList(["france:franceLabor:visa:dataVisa1", "france:franceLabor:visa:dataVisa2", "france:franceLabor:visa:dataVisa3", "france:franceLabor:visa:dataVisa4", "france:franceLabor:visa:dataVisa5", "france:franceLabor:visa:dataVisa6"]),
                },
                { title: "france:franceLabor:visa:title2" },
                {
                    list: createList(["france:franceLabor:visa:data1", "france:franceLabor:visa:data2", "france:franceLabor:visa:data3", "france:franceLabor:visa:data4"]),
                },
            ],
        },
    ],
};


export default function FranceLaborData() {
    return <LaborLaws data={franceLaborData} />;
}
