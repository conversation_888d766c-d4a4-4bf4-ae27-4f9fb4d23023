"use client";
import { Container } from "@mui/material";
import { useTranslation } from "react-i18next";

function GloablBenefits() {
  const { t } = useTranslation();
  return (
    <Container id="approach-payroll-section" className="custom-max-width">
      <h2 className="heading-h1 text-center">
        {" "}
        {t("joinUs:gloablBenefits:title")}
      </h2>
      <div className="locations">
        <div className="location-item four-items">
          <p className="label">{t("joinUs:gloablBenefits:diversity:title")}</p>
          <p className="value paragraph">
            {t("joinUs:gloablBenefits:diversity:description")}
          </p>
        </div>
        <div className="location-item four-items">
          <p className="label">{t("joinUs:gloablBenefits:health:title")}</p>
          <p className="value paragraph">
            {t("joinUs:gloablBenefits:health:description")}
          </p>
        </div>
        <div className="location-item four-items">
          <p className="label">{t("joinUs:gloablBenefits:engaging:title")}</p>
          <p className="value paragraph">
            {t("joinUs:gloablBenefits:engaging:description")}
          </p>
        </div>
        <div className="location-item four-items">
          <p className="label"> {t("joinUs:gloablBenefits:career:title")}</p>
          <p className="value paragraph">
            {t("joinUs:gloablBenefits:career:description")}{" "}
          </p>
        </div>
      </div>
    </Container>
  );
}

export default GloablBenefits;
