"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.fileSchema = void 0;
const joi_1 = __importDefault(require("joi"));
exports.fileSchema = joi_1.default.object({
    resource: joi_1.default.string().required(),
    ipSender: joi_1.default.string().required(),
    uuid: joi_1.default.string().required(),
    fileName: joi_1.default.string().required(),
    fileType: joi_1.default.string().required(),
    fileSize: joi_1.default.string().required(),
    checksum: joi_1.default.string(),
});
//# sourceMappingURL=files.validations.js.map