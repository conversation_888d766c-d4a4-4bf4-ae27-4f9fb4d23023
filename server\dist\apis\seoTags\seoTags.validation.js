"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createSeoTagsSchema = void 0;
const Joi = __importStar(require("joi"));
const constants_1 = require("@/utils/helpers/constants");
const seoVersionSchema = Joi.object({
    language: Joi.string()
        .valid(...Object.values(constants_1.Language))
        .optional(),
    metaTitle: Joi.string().min(3).required(),
    metaDescription: Joi.string().min(3).required(),
    canonical: Joi.string().optional(),
});
exports.createSeoTagsSchema = Joi.object({
    versions: Joi.array().items(seoVersionSchema).min(1).required(),
    slug: Joi.string().min(3).required(),
    robotMeta: Joi.string().min(3).required(),
});
//# sourceMappingURL=seoTags.validation.js.map