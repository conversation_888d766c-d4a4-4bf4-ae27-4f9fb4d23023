"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.auth = void 0;
const bcrypt_1 = __importDefault(require("bcrypt"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
exports.auth = {
    randomPassword: async () => {
        const alphanumeric = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        const specialCharacters = '!@*-_=+?.%^';
        const allCharacters = alphanumeric + specialCharacters;
        let password = '';
        for (let i = 0; i < 12; i++) {
            const randomIndex = Math.floor(Math.random() * allCharacters.length);
            password += allCharacters.charAt(randomIndex);
        }
        return password;
    },
    hashPassword: async (password) => {
        try {
            const salt = await bcrypt_1.default.genSalt(10);
            const hashedPassword = await bcrypt_1.default.hash(password, salt);
            return hashedPassword;
        }
        catch (error) {
            throw new Error('Error hashing password: ' + error.message);
        }
    },
    comparePassword: async (password, hashedPassword) => {
        const isMatch = await bcrypt_1.default.compare(password, hashedPassword);
        return isMatch;
    },
    generateToken: async (payload, tokenKey, expiresIn) => {
        const token = jsonwebtoken_1.default.sign(payload, tokenKey, { expiresIn: expiresIn });
        return token;
    },
    verifyToken: async (token, key) => {
        try {
            const decoded = jsonwebtoken_1.default.verify(token, key);
            return { payload: decoded, expired: false };
        }
        catch (error) {
            return { payload: null, expired: true };
        }
    },
};
//# sourceMappingURL=authenticated.service.js.map