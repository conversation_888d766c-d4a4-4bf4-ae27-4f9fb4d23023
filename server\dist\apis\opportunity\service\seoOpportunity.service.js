"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const seoOpportunity_model_1 = __importDefault(require("../model/seoOpportunity.model"));
const messages_1 = require("@/utils/helpers/messages");
class SeoOpportunityService {
    constructor() {
        this.seoOpportunity = seoOpportunity_model_1.default;
    }
    async create(seoData) {
        const oldSeo = await this.seoOpportunity.findOne({
            metaTitle: seoData.metaTitle,
            metaDescription: seoData.metaDescription,
        });
        if (oldSeo) {
            throw new http_exception_1.default(409, messages_1.MESSAGES.SEOTAGS.META_TITLE_EXISTS);
        }
        return await this.seoOpportunity.create(seoData);
    }
    async update(id, seoData) {
        return await this.seoOpportunity.findByIdAndUpdate(id, seoData, { new: true });
    }
    async get() {
        return await this.seoOpportunity.findOne().exec();
    }
}
exports.default = SeoOpportunityService;
//# sourceMappingURL=seoOpportunity.service.js.map