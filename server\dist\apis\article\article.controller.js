"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const article_service_1 = __importDefault(require("./article.service"));
const authentication_middleware_1 = __importDefault(require("@/middlewares/authentication.middleware"));
const authorization_middleware_1 = require("@/middlewares/authorization.middleware");
const article_interface_1 = require("./article.interface");
const constants_1 = require("@/utils/helpers/constants");
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const cache_middleware_1 = require("@/middlewares/cache.middleware");
const multer_1 = __importDefault(require("multer"));
const validateApi<PERSON>ey_middleware_1 = __importDefault(require("@/middlewares/validateApiKey.middleware"));
const validation_middleware_1 = require("@/middlewares/validation.middleware");
const article_validations_1 = require("./article.validations");
class ArticleController {
    constructor() {
        this.path = '/articles';
        this.articleService = new article_service_1.default();
        this.router = (0, express_1.Router)();
        this.upload = (0, multer_1.default)();
        this.updateMainFields = async (req, res, next) => {
            try {
                const { articleId } = req.params;
                const updateData = req.body;
                res.send(await this.articleService.updateMainfieldsArticle(articleId, updateData));
            }
            catch (error) {
                next(error);
            }
        };
        this.createArticle = async (request, response, next) => {
            try {
                const articleData = request.body;
                const newArticle = await this.articleService.createArticle(articleData);
                response.status(201).send({ article: newArticle });
            }
            catch (error) {
                next(error);
            }
        };
        this.createArticleAutoSave = async (request, response, next) => {
            try {
                const articleData = request.body;
                const newArticle = await this.articleService.createArticleautoSave(articleData);
                response.status(201).send(newArticle);
            }
            catch (error) {
                next(error);
            }
        };
        this.fixSlug = async (request, response, next) => {
            try {
                response.status(201).send(await this.articleService.importArticlesContentImages());
            }
            catch (error) {
                next(error);
            }
        };
        this.getAllarchivedArticles = async (req, res, next) => {
            try {
                const queries = req.query;
                const language = req.query.language || article_interface_1.Language.ENGLISH;
                const articles = await this.articleService.getArchivedArticles(queries, language);
                res.send(articles);
            }
            catch (error) {
                next(error);
            }
        };
        this.getAllArticles = async (req, res, next) => {
            try {
                const queries = req.query;
                const language = req.query.language || article_interface_1.Language.ENGLISH;
                const articles = await this.articleService.getArticles(queries, language);
                res.send(articles);
            }
            catch (error) {
                next(error);
            }
        };
        this.getAllArticlesDashboard = async (req, res, next) => {
            try {
                const queries = req.query;
                const language = req.query.language || article_interface_1.Language.ENGLISH;
                const articles = await this.articleService.getArticlesDashboard(queries, language);
                res.send(articles);
            }
            catch (error) {
                next(error);
            }
        };
        this.getArticleByUrl = async (req, res, next) => {
            try {
                const { url, language } = req.params;
                const currentUser = req.user;
                const articles = await this.articleService.getArticleByUrl(language, url, currentUser);
                res.send(articles);
            }
            catch (error) {
                next(error);
            }
        };
        this.updateArticleVersion = async (req, res, next) => {
            try {
                const { articleId, language } = req.params;
                const versionData = req.body;
                if (!articleId || !language) {
                    res.status(400).send('Missing article ID or language.');
                }
                const languageEnum = language;
                const updatedArticle = await this.articleService.updateArticleVersion(articleId, languageEnum, versionData);
                res.send(updatedArticle);
            }
            catch (error) {
                next(error);
            }
        };
        this.updatearticleAuto = async (req, res, next) => {
            try {
                const { articleId } = req.params;
                const articleData = req.body;
                if (!articleId) {
                    res.status(400).send('Missing article ID or language.');
                }
                const updatedArticle = await this.articleService.updateArticleAuto(articleId, articleData);
                res.send(updatedArticle);
            }
            catch (error) {
                next(error);
            }
        };
        this.getArticlesByCategory = async (req, res, next) => {
            try {
                const { language, urlCategory } = req.params;
                const queries = req.query;
                const articles = await this.articleService.getArticlesByCategory(language, urlCategory, queries);
                res.send(articles);
            }
            catch (error) {
                next(error);
            }
        };
        this.getArticleWithPrevAndNext = async (req, res, next) => {
            const { url, language } = req.params;
            try {
                const articles = await this.articleService.getArticleWithPrevAndNext(language, url);
                if (!articles) {
                    throw new http_exception_1.default(404, 'Article not found');
                }
                res.send(articles);
            }
            catch (error) {
                next(error);
            }
        };
        this.archivedallArticle = async (request, response, next) => {
            try {
                const articleId = request.params.id;
                const deletedArticle = await this.articleService.archivedallArticle(articleId);
                if (!deletedArticle) {
                    throw new http_exception_1.default(404, `No article found with ID ${articleId}.`);
                }
                response.status(204).send();
            }
            catch (error) {
                next(error);
            }
        };
        this.getSlugBySlug = async (req, res, next) => {
            try {
                const { url, language } = req.params;
                const articles = await this.articleService.getSlugBySlug(language, url);
                res.json(articles);
            }
            catch (error) {
                next(error);
            }
        };
        this.archiverArticleByLanguageAndId = async (req, res, next) => {
            try {
                const { language, articleId } = req.params;
                if (!language || !articleId) {
                    throw new http_exception_1.default(400, 'Missing language or articleId parameter.');
                }
                const deletedArticle = await this.articleService.archiverArticleByLanguageAndId(language, articleId);
                if (!deletedArticle) {
                    throw new http_exception_1.default(404, `No article or version found for language ${language} with articleId ${articleId}.`);
                }
                res.send({
                    message: `Version with language ${language} deleted successfully from article with ID ${articleId}.`,
                    deletedArticle,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.toggleArchiveStatusByLanguageAndId = async (req, res) => {
            try {
                const { language, articleId } = req.params;
                const { archive } = req.body;
                if (!language || !articleId) {
                    res.status(400).json({ message: 'Missing language or articleId parameter.' });
                    return;
                }
                if (archive === undefined) {
                    res.status(400).json({ message: 'Missing archive flag (true or false) in the request body.' });
                    return;
                }
                const updatedArticle = await this.articleService.toggleArchiveStatusByLanguageAndId(language, articleId, archive);
                if (!updatedArticle) {
                    res.status(404).json({ message: `No article or version found for language ${language} with articleId ${articleId}.` });
                    return;
                }
                res.status(200).json({
                    message: `Version with language ${language} has been ${archive ? 'archived' : 'unarchived'} successfully from article with ID ${articleId}.`,
                    updatedArticle,
                });
            }
            catch (error) {
                console.error(`Error in toggleArchiveStatusByLanguageAndId controller:`, error);
                res.status(500).json({ message: 'Internal server error.' });
            }
        };
        this.deletearticle = async (req, res, next) => {
            try {
                const { language, articleId } = req.params;
                if (!language || !articleId) {
                    throw new http_exception_1.default(404, 'Missing language or articleId parameter.');
                }
                const deletedArticle = await this.articleService.deleteArticle(language, articleId);
                res.send({
                    message: `Version with language ${language} deleted successfully from article with ID ${articleId}.`,
                    deletedArticle,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.deleteCategoryVersionController = async (req, res, next) => {
            const { categoryVersionId, newCategoryVersionId } = req.params;
            try {
                await this.articleService.deleteCategoryVersionAndHandleArticles(categoryVersionId, newCategoryVersionId);
                res.send({ message: 'Category version and associated articles handled successfully' });
            }
            catch (error) {
                next(error);
            }
        };
        this.getArticleByIdAndLang = async (req, res, next) => {
            try {
                const { articleId, language } = req.params;
                const article = await this.articleService.getArticleByIdAndLang(language, articleId);
                if (!article) {
                    if (language === 'fr') {
                        res.status(204).send();
                        return;
                    }
                    else {
                        res.status(404).json({ message: 'Article not found' });
                        return;
                    }
                }
                res.status(200).json(article);
            }
            catch (error) {
                next(error);
            }
        };
        this.getArticleById = async (req, res, next) => {
            try {
                const { articleId } = req.params;
                const article = await this.articleService.getArticleById(articleId);
                res.status(200).json(article);
            }
            catch (error) {
                next(error);
            }
        };
        this.importArticlesFromPentabell = async (request, response, next) => {
            try {
                await this.articleService.importTranslatedArticlesFromPentabell(request.file);
                response.send({ message: 'Imported' });
            }
            catch (error) {
                next(error);
            }
        };
        this.importCategories = async (request, response, next) => {
            try {
                await this.articleService.importCategories(request.file);
                response.send({ message: 'Imported' });
            }
            catch (error) {
                next(error);
            }
        };
        this.editArticleLinks = async (request, response, next) => {
            try {
                const { oldLink, newLink } = request.body;
                const articles = await this.articleService.editArticleLinks(oldLink, newLink);
                response.send(articles);
            }
            catch (error) {
                next(error);
            }
        };
        this.getArticlesTitles = async (req, res, next) => {
            try {
                const language = req.query.language || article_interface_1.Language.ENGLISH;
                const articles = await this.articleService.getarticlesTitles(language);
                res.send(articles);
            }
            catch (error) {
                next(error);
            }
        };
        this.getOppositeLanguageVersions = async (req, res, next) => {
            try {
                const { language, versionIds } = req.params;
                if (!language || !versionIds)
                    return next(new http_exception_1.default(400, 'Missing language or versionIds parameter.'));
                const versionIdsArray = versionIds.split(',');
                const result = await this.articleService.getOppositeLanguageVersionsArticle(language, versionIdsArray);
                res.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.updateBlogSitemap = async (request, response, next) => {
            try {
                await this.articleService.updateBlogSitemap();
                response.send({ message: 'Article sitemap file created' });
            }
            catch (error) {
                next(error);
            }
        };
        this.initialiseRoutes();
    }
    initialiseRoutes() {
        this.router.get(`${this.path}`, validateApiKey_middleware_1.default, this.getAllArticles);
        this.router.get(`${this.path}/dashboard`, validateApiKey_middleware_1.default, cache_middleware_1.validateCache, this.getAllArticlesDashboard);
        this.router.get(`${this.path}/archived`, validateApiKey_middleware_1.default, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), cache_middleware_1.validateCache, this.getAllarchivedArticles);
        this.router.get(`${this.path}/:language/listarticle`, validateApiKey_middleware_1.default, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), cache_middleware_1.validateCache, this.getArticlesTitles);
        this.router.get(`${this.path}/:language/:versionIds/translation`, validateApiKey_middleware_1.default, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), cache_middleware_1.validateCache, this.getOppositeLanguageVersions);
        this.router.get(`${this.path}/:language/:articleId`, validateApiKey_middleware_1.default, cache_middleware_1.validateCache, this.getArticleByIdAndLang);
        this.router.get(`${this.path}/opposite/:language/:url`, validateApiKey_middleware_1.default, cache_middleware_1.validateCache, this.getSlugBySlug);
        this.router.get(`${this.path}/:language/blog/:url`, validateApiKey_middleware_1.default, this.getArticleByUrl);
        this.router.get(`${this.path}/:language/article/:url`, validateApiKey_middleware_1.default, cache_middleware_1.validateCache, this.getArticleWithPrevAndNext);
        this.router.get(`${this.path}/:language/blog/category/:urlCategory`, validateApiKey_middleware_1.default, cache_middleware_1.validateCache, this.getArticlesByCategory);
        this.router.get(`${this.path}/:articleId`, this.getArticleById);
        this.router.put(`${this.path}/:articleId`, this.updateMainFields);
        this.router.post(`${this.path}`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), cache_middleware_1.invalidateCache, (0, validation_middleware_1.validationMiddleware)(article_validations_1.articleSchema), this.createArticle);
        this.router.post(`${this.path}/import`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN]), this.upload.single('file'), cache_middleware_1.invalidateCache, this.importArticlesFromPentabell);
        this.router.post(`${this.path}/category`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN]), this.upload.single('file'), cache_middleware_1.invalidateCache, this.importCategories);
        this.router.post(`${this.path}/fix`, cache_middleware_1.invalidateCache, this.fixSlug);
        this.router.post(`${this.path}/:language/:articleId`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), cache_middleware_1.invalidateCache, this.updateArticleVersion);
        this.router.post(`${this.path}/auto`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), cache_middleware_1.invalidateCache, this.createArticleAutoSave);
        this.router.put(`${this.path}/updatelinks`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN]), cache_middleware_1.invalidateCache, this.editArticleLinks);
        this.router.put(`${this.path}/:language/:articleId/desarchiver`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), cache_middleware_1.invalidateCache, this.toggleArchiveStatusByLanguageAndId);
        this.router.put(`${this.path}/:articleId/auto`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), cache_middleware_1.invalidateCache, this.updatearticleAuto);
        this.router.put(`${this.path}/`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), cache_middleware_1.invalidateCache, this.updateBlogSitemap);
        this.router.delete(`${this.path}/archiver/:id`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), cache_middleware_1.invalidateCache, this.archivedallArticle);
        this.router.delete(`${this.path}/:language/:articleId`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), cache_middleware_1.invalidateCache, this.archiverArticleByLanguageAndId);
        this.router.delete(`{this.path}/categories/:categoryVersionId/:newCategoryVersionId`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), cache_middleware_1.invalidateCache, this.deleteCategoryVersionController);
        this.router.delete(`${this.path}/:language/:articleId/article`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), cache_middleware_1.invalidateCache, this.deletearticle);
    }
}
exports.default = ArticleController;
//# sourceMappingURL=article.controller.js.map