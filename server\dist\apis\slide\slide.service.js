"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const slide_model_1 = __importDefault(require("./slide.model"));
const mongoose_1 = __importStar(require("mongoose"));
const messages_1 = require("@/utils/helpers/messages");
const constants_1 = require("@/utils/helpers/constants");
class SlideService {
    async addSlide(slideData) {
        const defaultLanguage = constants_1.Language.ENGLISH;
        const maxOrderSlide = await slide_model_1.default.findOne().sort({ order: -1 }).exec();
        const nextOrder = maxOrderSlide && maxOrderSlide.order ? maxOrderSlide.order + 1 : 1;
        for (const version of slideData.versionslide) {
            if (!version.language) {
                version.language = defaultLanguage;
            }
        }
        const slideVersions = slideData.versionslide.map((versionData) => {
            return {
                ...versionData,
            };
        });
        const newSlide = new slide_model_1.default({
            ...slideData,
            versionslide: slideVersions,
            order: nextOrder,
        });
        const savedSlide = await newSlide.save();
        return savedSlide;
    }
    async get(sliderId) {
        const slider = await slide_model_1.default.findById(sliderId).lean();
        if (!slider)
            throw new http_exception_1.default(404, messages_1.MESSAGES.OPPORTUNITY.NOT_FOUND);
        return slider;
    }
    async deleteSlide(sliderId) {
        try {
            const deletedSlide = await slide_model_1.default.findByIdAndDelete(sliderId).exec();
            if (!deletedSlide) {
                throw new http_exception_1.default(404, messages_1.MESSAGES.SLIDER.NOT_FOUND);
            }
            return {
                message: messages_1.MESSAGES.SLIDER.NOT_FOUND,
                deletedSlide,
            };
        }
        catch (error) {
            throw new http_exception_1.default(500, messages_1.MESSAGES.GENERAL.SERVER_ERROR);
        }
    }
    async updateSlideByLanguageandId(language, slideId, updateData) {
        const slideToUpdate = await slide_model_1.default.findById(slideId);
        if (!slideToUpdate)
            throw new http_exception_1.default(404, messages_1.MESSAGES.SLIDER.NOT_FOUND);
        let updatedVersion = null;
        for (const version of slideToUpdate.versionslide) {
            if (version.language === language) {
                const titleChanged = updateData.title && updateData.title !== version.title;
                Object.assign(version, updateData);
                updatedVersion = version;
                break;
            }
        }
        if (!updatedVersion)
            throw new http_exception_1.default(404, messages_1.MESSAGES.SLIDER.MISSING_LANGUAGE_OR_ID);
        await slideToUpdate.save();
        return slideToUpdate;
    }
    async addversionSlide(slideId, newVersion) {
        const existingSlide = await slide_model_1.default.findById(slideId);
        if (!existingSlide)
            throw new http_exception_1.default(404, messages_1.MESSAGES.SLIDER.NOT_FOUND);
        if (!Array.isArray(existingSlide.versionslide)) {
            existingSlide.versionslide = [];
        }
        existingSlide.versionslide.push(newVersion);
        const updatedSlide = await existingSlide.save();
        return updatedSlide;
    }
    async updateSlide(slideId, language, versionData) {
        const existingSlide = await slide_model_1.default.findById(slideId);
        if (!existingSlide)
            throw new http_exception_1.default(404, messages_1.MESSAGES.SLIDER.NOT_FOUND);
        let isVersionExisting = false;
        for (const version of existingSlide.versionslide) {
            if (version.language === language) {
                isVersionExisting = true;
                break;
            }
        }
        if (isVersionExisting) {
            return await this.updateSlideByLanguageandId(language, slideId, versionData);
        }
        else {
            const newVersion = {
                language,
                title: versionData.title,
                link: versionData.link || versionData.title.toLowerCase().replace(/\s+/g, '-'),
                linkBtn: versionData.linkBtn,
                img: versionData.img,
                imgMobile: versionData.imgMobile,
                _id: new mongoose_1.Types.ObjectId(),
                createdAt: new Date(),
                updatedAt: new Date(),
                visibility: versionData.visibility,
                isArchived: versionData.isArchived,
            };
            return await this.addversionSlide(slideId, newVersion);
        }
    }
    async toglleArchivedslide(language, slideId, archive) {
        const slideToModify = await slide_model_1.default.findById(slideId);
        if (!slideToModify)
            throw new http_exception_1.default(404, messages_1.MESSAGES.SLIDER.NOT_FOUND);
        const versionIndex = slideToModify.versionslide.findIndex(version => version.language === language);
        if (versionIndex === -1)
            throw new http_exception_1.default(404, messages_1.MESSAGES.SLIDER.MISSING_LANGUAGE_OR_ID);
        const versionToModify = slideToModify.versionslide[versionIndex];
        versionToModify.isArchived = archive;
        await slideToModify.save();
        return slideToModify;
    }
    async archiverversionslide(language, slideId, isArchived) {
        const slideToUpdate = await slide_model_1.default.findById(slideId);
        if (!slideToUpdate)
            throw new http_exception_1.default(404, messages_1.MESSAGES.SLIDER.NOT_FOUND);
        const versionIndex = slideToUpdate.versionslide.findIndex(version => version.language === language);
        if (versionIndex === -1)
            throw new http_exception_1.default(404, messages_1.MESSAGES.SLIDER.MISSING_LANGUAGE_OR_ID);
        const versionToUpdate = slideToUpdate.versionslide[versionIndex];
        versionToUpdate.isArchived = isArchived;
        await slideToUpdate.save();
        return slideToUpdate;
    }
    async getSliders(queries, language) {
        const { paginated = 'true', searchQuery, sortOrder = 'desc', isArchived, visibility, createdAt } = queries;
        const pageNumber = Number(queries.pageNumber) || 1;
        const pageSize = Number(queries.pageSize) || 10;
        const queryConditions = {};
        if (createdAt) {
            const date = new Date(createdAt);
            queryConditions['createdAt'] = {
                $gte: new Date(date.getFullYear(), date.getMonth(), date.getDate()),
                $lte: new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59, 999),
            };
        }
        if (searchQuery) {
            queryConditions['$or'] = [{ 'versionslide.title': new RegExp(`.*${searchQuery}.*`, 'i') }];
        }
        if (language) {
            queryConditions['versionslide.language'] = language;
        }
        if (visibility) {
            queryConditions['versionslide.visibility'] = visibility;
        }
        if (isArchived) {
            queryConditions['versionslide.isArchived'] = isArchived;
        }
        const sortCriteria = {};
        if (sortOrder) {
            sortCriteria['order'] = sortOrder === 'asc' ? 1 : -1;
        }
        const skip = (pageNumber - 1) * pageSize;
        const slideQuery = slide_model_1.default.find(queryConditions).sort(sortCriteria);
        const sliders = await slideQuery.lean();
        const filtredSliders = await Promise.all(sliders.map(async (slide) => {
            const filteredVersions = slide.versionslide.filter((version) => version.language === language &&
                (!visibility || version.visibility === visibility) &&
                (!searchQuery || new RegExp(`.*${searchQuery}.*`, 'i').test(version.title)));
            const existingLanguages = slide.versionslide.map((version) => version.language);
            return {
                ...slide,
                versionslide: filteredVersions,
                existingLanguages: Array.from(new Set(existingLanguages)),
            };
        }));
        const nonEmptySliders = filtredSliders.filter((slide) => slide.versionslide.length > 0);
        if (nonEmptySliders.length === 0) {
            return {
                pageNumber: Number(pageNumber),
                pageSize: Number(pageSize),
                totalPages: 0,
                totalSliders: 0,
                sliders: [],
            };
        }
        if (paginated === 'false') {
            return nonEmptySliders.sort((a, b) => b.order - a.order);
        }
        const totalSliders = await slide_model_1.default.countDocuments(queryConditions);
        const totalPages = Math.ceil(totalSliders / pageSize);
        return {
            pageNumber: Number(pageNumber),
            pageSize: Number(pageSize),
            totalPages,
            totalSliders,
            sliders: nonEmptySliders
                .sort((a, b) => a.order - b.order)
                .slice(skip, skip + Number(pageSize)),
        };
    }
    async updateSlidersOrder(slidersOrder) {
        try {
            const failedUpdates = [];
            for (const slider of slidersOrder) {
                const { id, order } = slider;
                if (!id || !order) {
                    failedUpdates.push({ id, error: 'Invalid ID or order.' });
                    continue;
                }
                const result = await slide_model_1.default.updateOne({ _id: id }, { $set: { order: order } });
                if (result.modifiedCount === 0) {
                    failedUpdates.push({ id, error: 'No modifications made (order already the same or invalid).' });
                }
                else {
                    console.log(`Successfully updated slider with ID: ${id}.`);
                }
            }
            if (failedUpdates.length > 0) {
                return {
                    success: false,
                    message: 'Some sliders were not updated.',
                    failedUpdates: failedUpdates
                };
            }
            return { success: true, message: 'Order updated successfully for all sliders.' };
        }
        catch (error) {
            console.error('Error updating orders: ', error);
            return { success: false, message: 'Error updating orders', };
        }
    }
    async getSlideBylanguageandId(language, id) {
        const sliders = await slide_model_1.default
            .aggregate([
            { $match: { _id: mongoose_1.default.Types.ObjectId.createFromHexString(id) } },
            { $unwind: '$versionslide' },
            { $match: { 'versionslide.language': language } },
            {
                $group: {
                    _id: {
                        id: '$_id',
                        language: '$versionslide.language',
                        link: '$versionslide.link',
                        title: '$versionslide.title',
                        imgMobile: '$versionslide.imgMobile',
                        isArchived: '$versionslide.isArchived',
                        img: '$versionslide.img',
                        linkBtn: '$versionslide.linkBtn',
                        altImg: '$versionslide.altImg',
                        visibility: '$versionslide.visibility',
                        createdAt: '$versionslide.createdAt',
                        updatedAt: '$versionslide.updatedAt',
                    }
                },
            },
            {
                $project: {
                    _id: '$_id.id',
                    versionslide: {
                        language: '$_id.language',
                        title: '$_id.title',
                        linkBtn: '$_id.linkBtn',
                        imgMobile: '$_id.imgMobile',
                        isArchived: '$_id.isArchived',
                        link: '$_id.link',
                        img: '$_id.img',
                        altImg: '$_id.altImg',
                        visibility: '$_id.visibility',
                    }
                },
            },
        ])
            .exec();
        if (sliders.length === 0)
            throw new http_exception_1.default(404, messages_1.MESSAGES.SLIDER.NOT_FOUND);
        return sliders[0];
    }
    async getSlideById(id) {
        const slide = await slide_model_1.default.findById(id);
        if (!slide)
            throw new http_exception_1.default(404, messages_1.MESSAGES.SLIDER.NOT_FOUND);
        return slide;
    }
    async disarchiveslide(language, slideId) {
        const slidedesarchiverForm = await slide_model_1.default.findById(slideId);
        if (!slidedesarchiverForm)
            throw new http_exception_1.default(404, messages_1.MESSAGES.SLIDER.NOT_FOUND);
        const versionIndex = slidedesarchiverForm.versionslide.findIndex(version => version.language === language);
        if (versionIndex === -1)
            throw new http_exception_1.default(404, messages_1.MESSAGES.SLIDER.MISSING_LANGUAGE_OR_ID);
        const versionToDelete = slidedesarchiverForm.versionslide[versionIndex];
        versionToDelete.isArchived = false;
        await slidedesarchiverForm.save();
        return slidedesarchiverForm;
    }
    async archiveddisarchivedSlider(sliderId, isArchived) {
        const slider = await slide_model_1.default.findById(sliderId);
        if (!slider) {
            throw new http_exception_1.default(404, messages_1.MESSAGES.SLIDER.NOT_FOUND);
        }
        await slide_model_1.default.findByIdAndUpdate(sliderId, { $set: { 'versionslide.$[].isArchived': isArchived } });
        return isArchived
            ? messages_1.MESSAGES.SLIDER.ARCHIVED
            : messages_1.MESSAGES.SLIDER.DISARCHIVED;
    }
}
exports.default = SlideService;
//# sourceMappingURL=slide.service.js.map