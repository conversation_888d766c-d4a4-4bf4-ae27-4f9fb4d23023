"use client"
import React from "react";
import CustomButton from "@/components/ui/CustomButton";
import { Container } from "@mui/material";

function CTAPayroll({ title, description, btnLink, btnText, img, imgAlt, light }) {
  return (
    <div id="cta-payroll" className={light == true ? 'light' : null}>
      <Container className="custom-max-width">
        <div className="div">
          <div
            id="last-blog"
            style={{
              backgroundImage: `linear-gradient(to left, rgba(35, 71, 145, 0), rgba(35, 71, 145, 0.6)), url(${img})`,
              backgroundSize: "cover",
              backgroundPosition: "center",
              backgroundRepeat: "no-repeat",
              padding: "20px",

            }}
          >
            {imgAlt && (
              <img
                width={0}
                height={0}
                alt={imgAlt}
                src=""
                style={{ display: "none" }}
                loading="lazy"
              />
            )}
            <p className="title">{title}</p>
            <p className="description">
              {
                description && description
              }

            </p>
            <CustomButton
              text={btnText}
              className={"btn btn-outlined white"}
              link={btnLink}
            />
          </div>

        </div>

      </Container>
    </div>
  );
}

export default CTAPayroll;
