import { useEffect, useState } from "react";
import { <PERSON>, CardContent, TextField, Grid } from "@mui/material";

import CustomButton from "@/components/ui/CustomButton";
import SvgRefreshIcon from "@/assets/images/icons/refreshIcon.svg";
import CustomMultiBarChart from "@/components/ui/charts/CustomMultiBarchart";
import { useGetPlatformStat } from "../stats.hooks";

export default function PlateformActivities({ chartSettings, t }) {
  const [dateFromPlatform, setDateFromPlatform] = useState(() => {
    const fourMonthsBefore = new Date();
    fourMonthsBefore.setMonth(fourMonthsBefore.getMonth() - 4);
    fourMonthsBefore.setDate(1);
    return fourMonthsBefore.toISOString().split("T")[0];
  });
  const [dateToPlatform, setDateToPlatform] = useState(() => {
    const today = new Date();
    return today.toISOString().split("T")[0];
  });
  const [searchPlatform, setSearchPlatform] = useState(false);

  const resetSearchPlatform = () => {
    setDateToPlatform(() => {
      const today = new Date();
      return today.toISOString().split("T")[0];
    });
    setDateFromPlatform("2024-09-01");
    setSearchPlatform(!searchPlatform);
  };

  const getDataPlatforActivity = useGetPlatformStat({
    dateFrom: dateFromPlatform,
    dateTo: dateToPlatform,
  });

  const platformAactivity = {
    title: t("statsDash:platformActivity"),
    dataKey: ["opportunities", "articles", "newsletters", "contacts"],
    dataset: getDataPlatforActivity?.data,
    color: ["#FFCC00", "#FFA135", "#FFD985", "#FF7700"],
  };

  useEffect(() => {
    getDataPlatforActivity.refetch();
  }, [searchPlatform]);

  return (
    <Card className="card">
      <CardContent>
        <div className="barchartfilter-wrapper">
          <Grid container className="chart-grid" spacing={1}>
            <Grid item xs={12} sm={12} md={3}>
              <p className="heading-h3" gutterBottom>
                {platformAactivity.title}
              </p>
            </Grid>

            <Grid item xs={12} sm={6} md={2.5} xl={3}>
              <TextField
                label={t("statsDash:fromDate")}
                type="date"
                value={dateFromPlatform}
                onChange={(e) => setDateFromPlatform(e.target.value)}
                fullWidth
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={2.5} xl={3}>
              <TextField
                label={t("statsDash:toDate")}
                type="date"
                value={dateToPlatform}
                onChange={(e) => setDateToPlatform(e.target.value)}
                fullWidth
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid
              item
              xs={2}
              sm={1}
              md={1.5}
              xl={1}
              className="btns-filter dashboard"
            >
              <CustomButton
                icon={<SvgRefreshIcon />}
                className={"btn btn-outlined btn-refresh full-width"}
                onClick={resetSearchPlatform}
              />
            </Grid>
            <Grid item xs={10} sm={11} md={2.5} xl={2}>
              <CustomButton
                text={t("statsDash:filter")}
                onClick={() => {
                  setSearchPlatform(!searchPlatform);
                }}
                className={"btn btn-outlined btn-filter-stat full-width"}
              />
            </Grid>
          </Grid>
        </div>
        <div className="chart-wrapper">
          {platformAactivity.dataset?.length > 0 && (
            <div className="labelstats-wrapper">
              <div className="label-wrapper">
                <span className="newopportunities-dot" />
                <span className="label-chart">
                  {t("statsDash:newOpportunities")}
                </span>
              </div>
              <div className="label-wrapper">
                <span className="neswarticles-dot" />
                <span className="label-chart">
                  {t("statsDash:newArticles")}
                </span>
              </div>
              <div className="label-wrapper">
                <span className="newsletters-dot" />
                <span className="label-chart">
                  {t("statsDash:newslettersSubscriptions")}
                </span>
              </div>
              <div className="label-wrapper">
                <span className="newcontact-dot" />
                <span className="label-chart">
                  {t("statsDash:newContacts")}
                </span>
              </div>
            </div>
          )}

          <CustomMultiBarChart
            chart={platformAactivity}
            chartSettings={chartSettings}
          />
        </div>
      </CardContent>
    </Card>
  );
}
