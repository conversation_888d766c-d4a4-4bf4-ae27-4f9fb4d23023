import { Container } from "@mui/material";


import SvglocationPin from "@/assets/images/icons/locationPin.svg";
import SvgArrow from "@/assets/images/icons/arrow.svg";
import SvgcallUs from "@/assets/images/icons/callUs.svg";
import Svgemail from "@/assets/images/icons/email.svg";

import { findCountryFlag, findCountryLabel } from "@/utils/functions";
import CustomButton from "./CustomButton";

function LocationsList({
  t,
  contacts,
  sectionSubtitleKey,
  sectionTitleKey,
  exploreMoreTextKey = "exploreMore",
  viewOnMapTextKey = "viewOnMap",
}) {
  return (
    <div id="africa-locations">
      <Container className="custom-max-width">
        {sectionSubtitleKey && (
          <p className="sub-heading text-center text-blue">
            {t(sectionSubtitleKey)}
          </p>
        )}
        {sectionTitleKey && (
          <h2 className="heading-h1 text-center">
            {t(sectionTitleKey)}
          </h2>
        )}
      </Container>
      <Container className="contact-items-section custom-max-width">
        {contacts.map((contact, index) => (
          <div className="contact-item" key={index}>
            <div>
              <div className="country-img">
                <p className="country-label">
                  <img
                    width={22}
                    height={14}
                    src={findCountryFlag(contact.country)}
                    alt={findCountryLabel(contact.country)}
                    loading="lazy"
                  />
                  {findCountryLabel(contact.country)}
                </p>
                <img
                  width={388}
                  height={253}
                  src={contact.img.src}
                  alt={contact.alt}
                  loading="lazy"
                />
              </div>
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "flex-end",
                }}
              >
                <h3 className="sub-heading text-blue">{contact.title}</h3>
                {contact.iconNew && <span>{contact.iconNew}</span>}
              </div>
              <div>
                {contact.locations.map((location, locIndex) => (
                  <p className="row-item" key={locIndex}>
                    <span>
                      <SvglocationPin />
                    </span>
                    {location}
                  </p>
                ))}

                <p className="row-item">
                  <span>
                    <SvgcallUs />
                  </span>
                  {contact.phones.map((phone, phoneIndex) => (
                    <span key={phoneIndex}>
                      {phone}
                      {contact.phones.length > 1 && phoneIndex !== contact.phones.length - 1 ? (
                        <>
                          <br />
                        </>
                      ) : null}
                    </span>
                  ))}
                </p>

                <p className="row-item">
                  <span>
                    <Svgemail />
                  </span>
                  {contact.email}
                </p>
              </div>
            </div>
            <div className="btns">
              <CustomButton
                text={t(sectionSubtitleKey ? `${sectionSubtitleKey.split(':')[0]}:locations:${exploreMoreTextKey}` : exploreMoreTextKey)}
                className={"btn btn-outlined "}
                link={contact.link}
              />
              <CustomButton
                text={t(sectionSubtitleKey ? `${sectionSubtitleKey.split(':')[0]}:locations:${viewOnMapTextKey}` : viewOnMapTextKey)}
                className={"btn btn-ghost"}
                link={contact.link}
                icon={<SvgArrow />}
              />
            </div>
          </div>
        ))}
      </Container>
    </div>
  );
}

export default LocationsList;
