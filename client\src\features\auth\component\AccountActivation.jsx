"use client";
import { useEffect, useState, useRef } from "react";
import { axiosGetJson } from "../../../config/axios";
import { jwtDecode } from "jwt-decode";
import { baseURL } from "../../../utils/urls";
import { useRouter } from "next/navigation";
import { authRoutes } from "@/helpers/routesList";
import Loading from "@/components/loading/Loading";

const AccountActivation = ({ token, t }) => {
  const [loading, setLoading] = useState(false);
  const [activationStatus, setActivationStatus] = useState("pending");
  const activationStatusRef = useRef(activationStatus); 
  let router = useRouter();

  useEffect(() => {
    const activateAccount = async (token) => {
      setLoading(true);
      if (!token) {
        return;
      }

      try {
        let decodedToken = jwtDecode(token);
        let currentDate = new Date();
        if (decodedToken.exp * 1000 < currentDate.getTime()) {
          router.push(`/${authRoutes.resend.route}`);
          return;
        }

        const response = await axiosGetJson.get(
          `${baseURL}/activation/${token}`
        );
        if (response.data.alreadyActivated) {
          setActivationStatus("alreadyActivated");
        } else {
          setActivationStatus("success");
        }
      } catch (error) {
        setActivationStatus("failed");
      } finally {
        setLoading(false);
      }
    };

  
    if (activationStatusRef.current === "pending") {
      activateAccount(token);
      activationStatusRef.current = "processing"; 
    }
  }, [token, router, t, loading]); 


  useEffect(() => {
    if (activationStatus === "pending") {
      window.location.href = `/${authRoutes.login.route}/?error=${t("activation:messagepending")}`;
    } else if (activationStatus === "success") {
      window.location.href = `/activation-account`;
    } else if (activationStatus === "alreadyActivated") {
      window.location.href = `/${authRoutes.login.route}/?error=${t("activation:messagepending")}`;
    }
  }, [activationStatus, t]);

  return (
    <div>
   <Loading />
    </div>
  );
};

export default AccountActivation;
