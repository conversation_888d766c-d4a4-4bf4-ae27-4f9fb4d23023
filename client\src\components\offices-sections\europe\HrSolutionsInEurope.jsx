import HrSolutionsSection from "@/components/ui/HrSolutionsSection";

function HrSolutionsInEurope({ t }) {
  const sections = [];
  let i = 1;
  while (t(`europe:whyPentabell:s${i}:title`, { defaultValue: "" })) {
    sections.push({
      title: t(`europe:whyPentabell:s${i}:title`),
      description: t(`europe:whyPentabell:s${i}:description`)
    });
    i++;
  }

  return (
    <HrSolutionsSection
      title={t("europe:whyPentabell:title")}
      items={sections}
    />
  );
}

export default HrSolutionsInEurope;