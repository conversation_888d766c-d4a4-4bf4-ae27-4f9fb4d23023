import SvgUsers from "@/assets/images/icons/yellow/Users.svg";
import Svglanguage from "@/assets/images/icons/yellow/language.svg";
import Svggdp from "@/assets/images/icons/yellow/gdp.svg";
import Svgcurrency from "@/assets/images/icons/yellow/currency.svg";
import SvgcapitalCity from "@/assets/images/icons/yellow/capitalCity.svg";
import Svggross from "@/assets/images/icons/yellow/gross.svg";
import OfficeInfo from "@/components/ui/OfficeInfo"; 

function OfficeInfoTN({ t }) {
  const data = [
    {
      icon: <SvgUsers />,
      titleKey: "Tunisia:officeInfoTN:title1",
      descriptionKey: "Tunisia:officeInfoTN:description1"
    },
    {
      icon: <Svglanguage />,
      titleKey: "Tunisia:officeInfoTN:title2",
      descriptionKey: "Tunisia:officeInfoTN:description2"
    },
    {
      icon: <Svggdp />,
      titleKey: "Tunisia:officeInfoTN:title3",
      descriptionKey: "Tunisia:officeInfoTN:description3"
    },
    {
      icon: <Svgcurrency />,
      titleKey: "Tunisia:officeInfoTN:title4",
      descriptionKey: "Tunisia:officeInfoTN:description4"
    },
    {
      icon: <SvgcapitalCity />,
      titleKey: "Tunisia:officeInfoTN:title5",
      descriptionKey: "Tunisia:officeInfoTN:description5"
    },
    {
      icon: <Svggross />,
      titleKey: "Tunisia:officeInfoTN:title6",
      descriptionKey: "Tunisia:officeInfoTN:description6"
    }
  ];

  return <OfficeInfo data={data} t={t} />;
}

export default OfficeInfoTN;
