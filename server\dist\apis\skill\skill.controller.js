"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const multer_1 = __importDefault(require("multer"));
const authentication_middleware_1 = __importDefault(require("@/middlewares/authentication.middleware"));
const skill_service_1 = __importDefault(require("./skill.service"));
const validation_middleware_1 = require("@/middlewares/validation.middleware");
const skill_validations_1 = require("./skill.validations");
const mongoId_validation_middleware_1 = __importDefault(require("@/middlewares/mongoId-validation.middleware"));
const queries_validation_middleware_1 = __importDefault(require("@/middlewares/queries-validation.middleware"));
const authorization_middleware_1 = require("@/middlewares/authorization.middleware");
const validateApiKey_middleware_1 = __importDefault(require("@/middlewares/validateApiKey.middleware"));
const constants_1 = require("@/utils/helpers/constants");
const cache_middleware_1 = require("@/middlewares/cache.middleware");
const messages_1 = require("@/utils/helpers/messages");
class SkillController {
    constructor() {
        this.path = '/skills';
        this.router = (0, express_1.Router)();
        this.skillService = new skill_service_1.default();
        this.upload = (0, multer_1.default)();
        this.getSkills = async (request, response, next) => {
            try {
                const query = request.query;
                const result = await this.skillService.getAll(query);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.create = async (request, response, next) => {
            try {
                const query = request.body;
                const result = await this.skillService.create(query);
                response.status(201).send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.createMany = async (request, response, next) => {
            try {
                const { file } = request;
                if (!file) {
                    return response.status(400).send({
                        message: messages_1.MESSAGES.FILE.NO_FILE,
                    });
                }
                const result = await this.skillService.createMany(file);
                response.status(201).send({
                    message: messages_1.MESSAGES.SKILLS.IMPORTED,
                    skills: result,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.delete = async (request, response, next) => {
            try {
                const id = request.params.id;
                const deletedSkill = await this.skillService.delete(id);
                response.send({
                    message: messages_1.MESSAGES.SKILLS.DELETED,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.update = async (request, response, next) => {
            try {
                const id = request.params.id;
                const skill = request.body;
                const result = await this.skillService.update(id, skill);
                response.status(200).send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.initialiseRoutes();
    }
    initialiseRoutes() {
        this.router.get(`${this.path}`, validateApiKey_middleware_1.default, authentication_middleware_1.default, queries_validation_middleware_1.default, cache_middleware_1.validateCache, this.getSkills);
        this.router.post(`${this.path}`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.CANDIDATE]), (0, validation_middleware_1.validationMiddleware)(skill_validations_1.createSkillSchema), cache_middleware_1.invalidateCache, this.create);
        this.router.post(`${this.path}/import`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN]), this.upload.single('file'), cache_middleware_1.invalidateCache, this.createMany);
        this.router.delete(`${this.path}/:id`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN]), mongoId_validation_middleware_1.default, cache_middleware_1.invalidateCache, this.delete);
        this.router.put(`${this.path}/:id`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN]), mongoId_validation_middleware_1.default, (0, validation_middleware_1.validationMiddleware)(skill_validations_1.createSkillSchema), cache_middleware_1.invalidateCache, this.update);
    }
}
exports.default = SkillController;
//# sourceMappingURL=skill.controller.js.map