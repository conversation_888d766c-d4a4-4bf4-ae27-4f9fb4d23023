import { useState, useEffect } from "react";
import Pagination from "@mui/material/Pagination";
import PropTypes from "prop-types";
import { PaginationItem, Select } from "@mui/material";
import ArrowForwardIcon from "@/assets/images/icons/ArrowForwardIcon.svg";
import ArrowBackIcon from "@/assets/images/icons/ArrowBackIcon.svg";

const CustomPagination = ({
  totalPages,
  currentPage,
  onPageChange,
  onPageSizeChange,
  showSelectPageSize,
  type,
  searchQueryParams,
}) => {
  const [selectedPageSize, setSelectedPageSize] = useState(5); 

  const handlePageChange = (event, page) => {
    if (type === "ssr") {
      const searchParams = new URLSearchParams(searchQueryParams);
      searchParams.set("pageNumber", page);
      window.location.href = `?${searchParams.toString()}`;
    } else {
      onPageChange(parseInt(page));
    }
  };

  const handlePageSizeChange = (event) => {
    const newSize = Number(event.target.value);
    setSelectedPageSize(newSize);
    onPageSizeChange(newSize);
  };

  useEffect(() => {
    setSelectedPageSize(selectedPageSize);
  }, [selectedPageSize]);

  return (
    <center className="pagination">
      {showSelectPageSize && (
        <div className="page-size-container">
          <Select
            value={selectedPageSize}
            onChange={handlePageSizeChange}
            className="page-size-select"
          >
            <option value="3">3</option>
            <option value="5">5</option>
            <option value="8">8</option>
          </Select>
        </div>
      )}

      <div className="pagination-wrapper">
        <Pagination
          count={totalPages}
          page={currentPage}
          onChange={handlePageChange}
          siblingCount={0} // Show 1 page before and after the current page
          boundaryCount={1} // Show the first and last page
          renderItem={(item) => (
            <PaginationItem
              slots={{ previous: ArrowBackIcon, next: ArrowForwardIcon }}
              {...item}
            />
          )}
          // shape="rounded"
          sx={{
            "& .MuiPaginationItem-root": {
              // borderRadius: "20%",
              // border: "1px solid #ccc",
              fontWeight: "400",
              color: "#798BA3",
            },
            "& .Mui-selected": {
              backgroundColor: "transparent !important",
              color: "#0B3051",
              fontWeight: "700",
            },
          }}
        />
      </div>
    </center>
  );
};

CustomPagination.propTypes = {
  totalPages: PropTypes.number.isRequired,
  currentPage: PropTypes.number.isRequired,
  onPageChange: PropTypes.func.isRequired,
  onPageSizeChange: PropTypes.func.isRequired,
};

export default CustomPagination;
