"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/layout",{

/***/ "(app-pages-browser)/./src/components/ui/DropdownMenu.jsx":
/*!********************************************!*\
  !*** ./src/components/ui/DropdownMenu.jsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Menu/Menu.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Fade/Fade.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _assets_images_icons_arrowDown_svg__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../assets/images/icons/arrowDown.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowDown.svg\");\n/* harmony import */ var _assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../assets/images/icons/arrowUp.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowUp.svg\");\n/* harmony import */ var _utils_functions__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/functions */ \"(app-pages-browser)/./src/utils/functions.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst DropdownMenu = (param)=>{\n    let { buttonLabel, buttonHref, menuItems, subMenu, locale, withFlag, selectedFlag } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const open = Boolean(anchorEl);\n    const isDetailBlogPath = ()=>pathname.includes(\"/blog/\") && !pathname.includes(\"/blog/category/\") && pathname !== \"/blog/\" && pathname !== \"/fr/blog/\";\n    const handleClick = (event)=>{\n        setAnchorEl(event.currentTarget);\n    };\n    const handleClose = ()=>{\n        setAnchorEl(null);\n    };\n    const handleClick2 = (event, item)=>{\n        event.stopPropagation();\n        if (item.onClick) {\n            item.onClick();\n        } else if (item.subItems == undefined) handleClose();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: subMenu ? `sub-dropdown-menu` : `dropdown-menu`,\n        onMouseEnter: (e)=>setAnchorEl(e.currentTarget),\n        onMouseLeave: handleClose,\n        style: {\n            display: \"inline-block\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: pathname.includes(buttonHref) && buttonHref !== \"\" ? \"navbar-link dropdown-toggle active\" : isDetailBlogPath() ? \"navbar-link dropdown-toggle whiteBg\" : \"navbar-link dropdown-toggle\",\n                id: \"fade-button\",\n                \"aria-controls\": open ? \"fade-menu\" : undefined,\n                \"aria-haspopup\": \"true\",\n                \"aria-expanded\": open ? \"true\" : undefined,\n                onClick: handleClick,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: pathname.includes(buttonHref) && buttonHref !== \"\" ? \"dropdown-toggle-link active\" : isDetailBlogPath() ? \"dropdown-toggle-link whiteBg\" : \"dropdown-toggle-link\",\n                        href: buttonLabel?.toLowerCase() === \"resources\" || buttonLabel?.toLowerCase() === \"ressources\" ? \"#\" : buttonHref ? (0,_utils_functions__WEBPACK_IMPORTED_MODULE_6__.generateLocalizedSlug)(locale, buttonHref).replace(\"/en/\", \"/\") : pathname.replace(\"/en/\", \"/\"),\n                        locale: locale === \"en\" ? \"en\" : \"fr\",\n                        onClick: (e)=>{\n                            if (buttonLabel?.toLowerCase() === \"resources\" || buttonLabel?.toLowerCase() === \"ressources\") {\n                                e.preventDefault();\n                                e.stopPropagation();\n                            }\n                        },\n                        sx: {\n                            ...buttonLabel?.toLowerCase() === \"resources\" || buttonLabel?.toLowerCase() === \"ressources\" ? {\n                                cursor: \"default\",\n                                textDecoration: \"none\",\n                                \"&:hover\": {\n                                    textDecoration: \"none\"\n                                }\n                            } : {}\n                        },\n                        children: withFlag ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            className: \"flag-lang\",\n                            src: selectedFlag.src,\n                            width: 26,\n                            height: 22,\n                            disabled: true,\n                            loading: \"lazy\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 108,\n                            columnNumber: 13\n                        }, undefined) : buttonLabel\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, undefined),\n                    open ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowDown_svg__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 120,\n                        columnNumber: 17\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 120,\n                        columnNumber: 36\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                id: \"fade-menu\",\n                MenuListProps: {\n                    \"aria-labelledby\": \"fade-button\"\n                },\n                anchorEl: anchorEl,\n                open: open,\n                onClose: handleClose,\n                TransitionComponent: _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                disableScrollLock: true,\n                anchorOrigin: {\n                    vertical: \"bottom\",\n                    horizontal: subMenu ? \"right\" : \"left\"\n                },\n                transformOrigin: {\n                    vertical: \"top\",\n                    horizontal: subMenu ? \"left\" : \"left\"\n                },\n                children: menuItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: item.subItems ? `sub-menu dropdown-item` : `dropdown-item`,\n                        onClick: (e)=>handleClick2(e, item),\n                        sx: {\n                            // Remove default hover background for items with submenus\n                            ...item.subItems && {\n                                \"&:hover\": {\n                                    backgroundColor: \"transparent !important\"\n                                },\n                                \"&.Mui-focusVisible\": {\n                                    backgroundColor: \"transparent !important\"\n                                },\n                                \"&.Mui-selected\": {\n                                    backgroundColor: \"transparent !important\"\n                                }\n                            }\n                        },\n                        children: item.subItems ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DropdownMenu, {\n                            buttonLabel: item?.i18nName ? t(item?.i18nName) : item.name,\n                            buttonHref: item.route,\n                            menuItems: item.subItems,\n                            subMenu: true,\n                            locale: locale\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 165,\n                            columnNumber: 15\n                        }, undefined) : item.route ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            href: (0,_utils_functions__WEBPACK_IMPORTED_MODULE_6__.generateLocalizedSlug)(locale, item.route),\n                            locale: locale === \"en\" ? \"en\" : \"fr\",\n                            className: pathname.includes(item.route) ? \"dropdown-item-link active\" : \"dropdown-item-link\",\n                            children: [\n                                item.icon ?? item.icon,\n                                \" \",\n                                item?.i18nName ? t(item?.i18nName) : item.name\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 173,\n                            columnNumber: 15\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"dropdown-item-link\",\n                            href: \"#\",\n                            children: withFlag ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                className: \"flag-lang\",\n                                src: item.flag.src,\n                                width: 26,\n                                height: 22,\n                                loading: \"lazy\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                                lineNumber: 188,\n                                columnNumber: 19\n                            }, undefined) : item?.i18nName ? t(item?.i18nName) : item.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 186,\n                            columnNumber: 15\n                        }, undefined)\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 143,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DropdownMenu, \"kmMXD6S/WNK2NQEz2iCMSS4bykM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation\n    ];\n});\n_c = DropdownMenu;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DropdownMenu);\nvar _c;\n$RefreshReg$(_c, \"DropdownMenu\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/DropdownMenu.jsx\n"));

/***/ })

});