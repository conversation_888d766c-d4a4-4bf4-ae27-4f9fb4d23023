import { useTranslation } from "react-i18next";
import StatusCard from "@/components/ui/charts/StatusCard";
import job from "@/assets/images/applicationsdashboard.png";
const AdminDashboardStats = ({
  applications,
  articles,
  comments,
  contacts,
  opportunities,
  candidates,
  cvUploaded,
  logins,
  activeOpportunities,
}) => {
  const { t } = useTranslation();

  const handleRedirect = (path) => {
    window.location.href = path;
  };

  const statsList = [
    {
      title: t("statsTotalNumbers:totalCandidates"),
      value: candidates,
      iconAlt: "candidates",
      className: "stats-job",
    },
    {
      title: t("statsTotalNumbers:totalCvUploaded"),
      value: cvUploaded,
      iconAlt: "cv-uploaded",
      className: "stats-application",
    },
    {
      title: t("statsTotalNumbers:logins"),
      value: logins,
      iconAlt: "logins",
      className: "stats-job",
    },
    {
      title: t("statsTotalNumbers:totalApplications"),
      value: applications,
      iconAlt: "applications",
      className: "stats-contact cursor",
      onClick: () => handleRedirect("/backoffice/applications"),
    },
    {
      title: t("statsTotalNumbers:totalArticles"),
      value: articles,
      iconAlt: "articles",
      className: "stats-contact cursor",
      onClick: () => handleRedirect("/backoffice/blogs"),
    },
    {
      title: t("statsTotalNumbers:totalOpportunities"),
      value: (
        <>
          {opportunities}{" "}
          <span className="active">ACTIVE : {activeOpportunities}</span>
        </>
      ),
      iconAlt: "opportunities",
      className: "stats-job cursor",
      onClick: () => handleRedirect("/backoffice/opportunities"),
    },
    {
      title: t("statsTotalNumbers:totalComments"),
      value: comments,
      iconAlt: "comments",
      className: "stats-application cursor",
      onClick: () => handleRedirect("/backoffice/comments"),
    },
    {
      title: t("statsTotalNumbers:totalContacts"),
      value: contacts || 0,
      iconAlt: "contacts",
      className: "stats-job cursor",
      onClick: () => handleRedirect("/backoffice/contacts"),
    },
  ];

  return (
    <>
      <div className="stats-container">
        {statsList.slice(0, 4).map((item, index) => (
          <StatusCard
            key={index}
            iconSrc={job.src}
            iconAlt={item.iconAlt}
            title={item.title}
            value={item.value}
            cardClassName={item.className}
            iconClassName="stats-icon"
            onClick={item.onClick}
          />
        ))}
      </div>
      <div className="stats-container">
        {statsList.slice(4).map((item, index) => (
          <StatusCard
            key={index + 4}
            iconSrc={job.src}
            iconAlt={item.iconAlt}
            title={item.title}
            value={item.value}
            cardClassName={item.className}
            iconClassName="stats-icon"
            onClick={item.onClick}
          />
        ))}
      </div>
    </>
  );
};

export default AdminDashboardStats;
