"use client";
import { Container, Grid } from "@mui/material";
import Image from "next/image";
import purposeImg from "../../assets/images/website/purposeImg.jpg";
import SvgVisionIcon from "../../assets/images/icons/visionIcon.svg";
import SvgTargetIcon from "../../assets/images/icons/targetIcon.svg";
import { useTranslation } from "react-i18next";

function WhatWeBeleiveSection({ locale }) {
  const { t } = useTranslation();
  return (
    <Container className="purpose-section custom-max-width" id="what-we-beleive-section">
      <h2 className="heading-h1 text-center">{t("aboutUs:whatwebelieve")}</h2>
      <Grid className="container" container columnSpacing={3} rowSpacing={2}>
        <Grid item xs={12} sm={6}>
          {locale === "en" && (
            <h3 className="heading-h2">{t("aboutUs:purpose:title")}</h3>
          )}
          <p className="sub-heading">{t("aboutUs:purpose:description")}</p>
        </Grid>
        <Grid item xs={12} sm={6}>
          <img alt={t("aboutUs:purpose:altImg")} src={purposeImg.src} loading="lazy"/>
        </Grid>

        <Grid item xs={12} sm={6}>
          <div className="vision-mission-section">
            <SvgVisionIcon />
            <h3 className="heading-h2 text-white">
              {t("aboutUs:vision:title")}
            </h3>
            <p className="sub-heading text-white">
              {t("aboutUs:vision:description")}
            </p>
          </div>
        </Grid>
        <Grid item xs={12} sm={6}>
          <div className="vision-mission-section">
            <SvgTargetIcon />

            <h3 className="heading-h2 text-white">
              {t("aboutUs:mission:title")}
            </h3>
            <p className="sub-heading text-white">
              {t("aboutUs:mission:description")}
            </p>
          </div>
        </Grid>
      </Grid>
    </Container>
  );
}

export default WhatWeBeleiveSection;
