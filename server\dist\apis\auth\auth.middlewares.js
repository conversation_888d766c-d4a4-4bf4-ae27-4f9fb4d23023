"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.confirmAccountMiddleware = exports.resetPasswordMiddleware = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const auth_validations_1 = require("./auth.validations");
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const resetPasswordMiddleware = async (request, response, next) => {
    const token = request.params.token;
    const secretKey = process.env.RESET_PASSWORD_TOKEN_PRIVATE_KEY;
    jsonwebtoken_1.default.verify(token, secretKey, async (err, decoded) => {
        if (err) {
            if (err.name === 'TokenExpiredError') {
                next(new http_exception_1.default(400, 'Token has expired'));
            }
            else {
                next(new http_exception_1.default(400, `Error decoding Token: ${err.message}`));
            }
        }
        else {
            const expirationTime = new Date(decoded.exp * 1000);
            if (expirationTime < new Date()) {
                next(new http_exception_1.default(400, 'Token has expired'));
            }
            next();
        }
    });
};
exports.resetPasswordMiddleware = resetPasswordMiddleware;
const confirmAccountMiddleware = async (request, response, next) => {
    const token = request.params.token;
    const { error } = auth_validations_1.confirmAccountSchema.validate({ token });
    if (error)
        response.status(406).json({ error: error.details[0].message });
    next();
};
exports.confirmAccountMiddleware = confirmAccountMiddleware;
//# sourceMappingURL=auth.middlewares.js.map