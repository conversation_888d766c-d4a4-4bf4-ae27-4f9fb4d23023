import OurPartners from "@/components/pages/sites/sections/OurPartners";
import banner from "@/assets/images/website/banner/europe.webp";
import GlobalHRServicesSection from "@/components/pages/sites/sections/GlobalHRServicesSection";
import r1 from "@/assets/images/services/recrutement1.png";
import r2 from "@/assets/images/services/recrutement2.png";
import r3 from "@/assets/images/services/recrutement3.png";
import r4 from "@/assets/images/services/recrutement4.png";
import serviceImg from "@/assets/images/test/service.png";
import CTAPayroll from "@/components/pages/sites/services/CTAPayroll";
import AfricaForm from "@/features/forms/components/AfricaForm";

import ctaAfricaBg from "@/assets/images/services/ctaAfricaPage.png";
import CTAContact from "@/components/pages/sites/offices-sections/CTAContact";
import initTranslations from "@/app/i18n";
import EuropeBanner from "@/components/pages/sites/offices-sections/europe/EuropeBanner";
import NavigateEuropeanMarket from "@/components/pages/sites/offices-sections/europe/NavigateEuropeanMarket";
import HrSolutionsInEurope from "@/components/pages/sites/offices-sections/europe/HrSolutionsInEurope";
import LocationsInEurope from "@/components/pages/sites/offices-sections/europe/LocationsInEurope";
import { websiteRoutesList } from "@/helpers/routesList";
import { axiosGetJsonSSR } from "@/config/axios";
import serviceimgS1 from "@/assets/images/services/service1.png";

export async function generateMetadata({ params: { locale } }) {
  const canonicalUrl = `https://www.pentabell.com/${
    locale !== "en" ? `${locale}/` : ""
  }/international-recruitment-staffing-company-in-europe/`;

  const languages = {
    fr: `https://www.pentabell.com/fr/international-recruitment-staffing-company-in-europe/`,
    en: `https://www.pentabell.com/international-recruitment-staffing-company-in-europe/`,
    "x-default": `https://www.pentabell.com/international-recruitment-staffing-company-in-europe/`,
  };
  const { t } = await initTranslations(locale, ["servicesByCountry"]);
  try {
    const res = await axiosGetJsonSSR.get(
      `${process.env.NEXT_PUBLIC_BASE_API_URL_SSR}/seoTags/${locale}/international-recruitment-staffing-company-in-europe`
    );
    const seoTags = res?.data?.status === 200;
    if (seoTags) {
      return {
        title: res?.data?.data?.versions[0]?.metaTitle,
        description: res?.data?.data?.versions[0]?.metaDescription,
        robots: res?.data?.data?.robotMeta,
        alternates: {
          canonical: canonicalUrl,
          languages,
        },
      };
    }
  } catch (error) {
    console.error("Error fetching SEO tags:", error);
  }
  return {
    title: t("servicesByCountry:europe:metaTitle"),
    description: t("servicesByCountry:europe:metaDescription"),
    alternates: {
      canonical: canonicalUrl,
      languages,
    },
    robots: "follow, index, max-snippet:-1, max-image-preview:large",
  };
}

async function companyInEurope({ params: { locale } }) {
  const { t } = await initTranslations(locale, ["Tunisia", "europe"]);

  const SERVICES = [
    {
      id: "s1",
      title: t("Tunisia:services:dataS1:title"),
      description: t("Tunisia:services:dataS1:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.payrollServices.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: serviceImg,
      altImg: t("Tunisia:services:dataS1:altImg"),
    },
    {
      id: "s2",
      title: t("Tunisia:services:dataS2:title"),
      description: t("Tunisia:services:dataS2:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.consultingServices.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r4,
      altImg: t("Tunisia:services:dataS2:altImg"),
    },
    {
      id: "s3",
      title: t("Tunisia:services:dataS3:title"),
      description: t("Tunisia:services:dataS3:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.technicalAssistance.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r3,
      altImg: t("Tunisia:services:dataS3:altImg"),
    },
    {
      id: "s4",
      title: t("Tunisia:services:dataS4:title"),
      description: t("Tunisia:services:dataS4:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.aiSourcing.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r2,
      altImg: t("Tunisia:services:dataS4:altImg"),
    },
    {
      id: "s5",
      title: t("Tunisia:services:dataS5:title"),
      description: t("Tunisia:services:dataS5:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.directHiring.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r1,
      altImg: t("Tunisia:services:dataS5:altImg"),
    },
  ];
  return (
    <div>
      <EuropeBanner
        altImg={t("europe:banner:altImg")}
        bannerImg={banner}
        height={"100vh"}
        t={t}
      />
      <OurPartners disableTxt={true} />
      <NavigateEuropeanMarket t={t} />
      <GlobalHRServicesSection
        title={t("europe:services:title")}
        subTitle={t("europe:services:subTitle")}
        SERVICES={SERVICES}
        defaultImage={serviceimgS1}
      />

      <CTAPayroll
        light={true}
        title={t("europe:hire:title")}
        description={t("europe:hire:description")}
        btnLink={`/${websiteRoutesList.contact.route}`}
        btnText={t("europe:hire:getStarted")}
        img={ctaAfricaBg.src}
        imgAlt={t("europe:contact:altImage")}
      />

      <HrSolutionsInEurope t={t} />
      <LocationsInEurope t={t} />
      <CTAContact country={"europe"} t={t} />
      <AfricaForm country={"Europe"} />
    </div>
  );
}

export default companyInEurope;
