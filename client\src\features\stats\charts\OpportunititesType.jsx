import { useEffect, useState } from "react";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Card,
  CardContent,
  Grid,
  TextField,
  Select,
  MenuItem,
} from "@mui/material";

import CustomButton from "@/components/ui/CustomButton";
import SvgexpandIcon from "@/assets/images/icons/arrowUp.svg";
import SvgRefreshIcon from "@/assets/images/icons/refreshIcon.svg";
import CustomPieChart from "@/components/ui/charts/CustomPieChart";
import { useGetOpportunitiesStat } from "../stats.hooks";
import { OpportunityType } from "@/utils/constants";

export default function OpportunititesType({ Industry, t, language }) {
  const [dateFromOpportunity, setDateFromOpportunity] = useState(() => {
    const fourMonthsBefore = new Date();
    fourMonthsBefore.setMonth(fourMonthsBefore.getMonth() - 4);
    fourMonthsBefore.setDate(1);
    return fourMonthsBefore.toISOString().split("T")[0];
  });
  const [dateToOpportunity, setDateToOpportunity] = useState(() => {
    const today = new Date();
    return today.toISOString().split("T")[0];
  });
  const [opportunityType, setOpportunityType] = useState("");

  const [industry, setIndustry] = useState("");
  const [searchOpportunity, setSearchOpportunity] = useState(false);

  const resetSearchOpportunity = () => {
    setDateToOpportunity(() => {
      const today = new Date();
      return today.toISOString().split("T")[0];
    });
    setDateFromOpportunity("2024-09-01");
    setOpportunityType("");
    setIndustry("");
    setSearchOpportunity(!searchOpportunity);
  };

  const getDataPieOpportunities = useGetOpportunitiesStat({
    dateFrom: dateFromOpportunity,
    dateTo: dateToOpportunity,
    opportunityType,
    industry,
    barChart: null,
    language
  });

  const pieChart = {
    title: t("statsDash:opportunities"),
    dataset: getDataPieOpportunities?.data?.map((opp) => ({
      label: opp.type,
      value: opp.totalOpportunities,
    })),
    colors: ["#234791", "#D5E5FF", "#227B94"],
  };

  useEffect(() => {
    getDataPieOpportunities.refetch();
  }, [searchOpportunity]);

  return (
    <Card className="card">
      <CardContent>
        <p className="heading-h3" gutterBottom>
          {pieChart.title}
        </p>
        <Accordion elevation={0} disableGutters={true}>
          <AccordionSummary
            aria-controls="panel1bh-content"
            id="panel1bh-header"
            className="svg-accordion"
            expandIcon={<SvgexpandIcon />}
          >
            <h3 className="label-pentabell">{t("statsDash:filters")}</h3>
          </AccordionSummary>
          <AccordionDetails elevation={0}>
            <Grid container className="chart-grid" spacing={1}>
              {" "}
              <Grid item xs={12} sm={6}>
                <span className="blue-text">{t("statsDash:type")} :</span>
                <Select
                  className="select-pentabell blue-text"
                  value={opportunityType || ""}
                  defaultValue={""}
                  onChange={(event) => setOpportunityType(event.target.value)}
                >
                  <MenuItem className="blue-text" value="" selected disabled>
                    <em>{t("statsDash:opportunityType")}</em>
                  </MenuItem>
                  <MenuItem className="blue-text" value="">
                    {t("statsDash:all")}
                  </MenuItem>
                  {OpportunityType.map((item, index) => (
                    <MenuItem className="blue-text" key={index} value={item}>
                      {item}
                    </MenuItem>
                  ))}
                </Select>
              </Grid>
              <Grid item xs={12} sm={6} md={6}>
                <span className="blue-text">{t("statsDash:industry")} :</span>
                <Select
                  className="select-pentabell blue-text"
                  value={industry || ""}
                  onChange={(event) => setIndustry(event.target.value)}
                >
                  <MenuItem className="blue-text" value={""} selected disabled>
                    <em>{t("statsDash:industry")}</em>
                  </MenuItem>
                  <MenuItem className="blue-text" value={""}>
                    {t("statsDash:all")}
                  </MenuItem>
                  {Industry.map((item, index) => (
                    <MenuItem className="blue-text" key={index} value={item}>
                      {item}
                    </MenuItem>
                  ))}
                </Select>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label={t("statsDash:fromDate")}
                  type="date"
                  value={dateFromOpportunity}
                  onChange={(e) => setDateFromOpportunity(e.target.value)}
                  fullWidth
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label={t("statsDash:toDate")}
                  type="date"
                  value={dateToOpportunity}
                  onChange={(e) => setDateToOpportunity(e.target.value)}
                  fullWidth
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={3} sm={1} md={4} className="btns-filter dashboard">
                <CustomButton
                  icon={<SvgRefreshIcon />}
                  className={"btn btn-outlined btn-refresh full-width"}
                  onClick={resetSearchOpportunity}
                />
              </Grid>
              <Grid item xs={11} sm={11} md={8}>
                <CustomButton
                  text={t("statsDash:filter")}
                  onClick={() => {
                    setSearchOpportunity(!searchOpportunity);
                  }}
                  className={"btn btn-outlined btn-filter-stat full-width"}
                />
              </Grid>{" "}
            </Grid>
          </AccordionDetails>
        </Accordion>
        <div className="chart-wrapper">
          {" "}
          <CustomPieChart donuts={false} chart={pieChart} />{" "}
          {pieChart.dataset?.some((item) => item["value"] > 0) && (
            <div className="labelstats-wrapper">
              <div className="label-wrapper">
                <span className="public-dot" />
                <span className="label-chart">{t("statsDash:public")}</span>
              </div>
              <div className="label-wrapper">
                <span className="privateopportunity-dot" />
                <span className="label-chart">{t("statsDash:private")}</span>
              </div>
              <div className="label-wrapper">
                <span className="draft-dot" />
                <span className="label-chart">{t("statsDash:draft")}</span>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
