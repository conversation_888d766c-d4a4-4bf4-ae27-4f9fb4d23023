"use client";
import LaborLaws from "../LaborLaws";
import { createList } from '../../../utils/functions';
export const LABOR_KEYS = {
    workingHours: {
        title: "Tunisia:tunisiaLabor:workingHours:title",
    },
    employmentContracts: {
        title: "Tunisia:tunisiaLabor:employmentContracts:title",
    },

};
export const tunisiaLaborData = {
    title: "Tunisia:tunisiaLabor:title",
    sections: [
        {
            id: 1,
            title: LABOR_KEYS.workingHours.title,
            subsections: [
                {
                    title: "Tunisia:tunisiaLabor:workingHours:title1",
                    description: "Tunisia:tunisiaLabor:workingHours:description1",
                },
                {
                    title: "Tunisia:tunisiaLabor:workingHours:title2",
                    description: "Tunisia:tunisiaLabor:workingHours:description2"
                },

            ],
        },
        {
            id: 2,
            title: LABOR_KEYS.employmentContracts.title,
            subsections: [

                {
                    description: "Tunisia:tunisiaLabor:employmentContracts:title1",
                    list: createList(["Tunisia:tunisiaLabor:employmentContracts:data1", "Tunisia:tunisiaLabor:employmentContracts:data2", "Tunisia:tunisiaLabor:employmentContracts:data3", "Tunisia:tunisiaLabor:employmentContracts:data4", "Tunisia:tunisiaLabor:employmentContracts:data5", "Tunisia:tunisiaLabor:employmentContracts:data6", "Tunisia:tunisiaLabor:employmentContracts:data7"]),
                },
                {
                    title: "Tunisia:tunisiaLabor:employmentContracts:title2",
                    description: "Tunisia:tunisiaLabor:employmentContracts:description",
                    list: createList(["Tunisia:tunisiaLabor:employmentContracts:dataS1", "Tunisia:tunisiaLabor:employmentContracts:dataS2"]),

                },
            ],
        },
        {
            id: 3,
            title: "Tunisia:tunisiaLabor:payroll:title",
            type: "payroll",
            titleKey: "Tunisia:tunisiaLabor:payroll:title1",
            descriptionKey: "Tunisia:tunisiaLabor:payroll:description",
            items: [
                {
                    titleKey: "Tunisia:tunisiaLabor:payroll:fiscalYear:title",
                    dateKeys: ["Tunisia:tunisiaLabor:payroll:fiscalYear:date1", "Tunisia:tunisiaLabor:payroll:fiscalYear:date2"],
                    descriptionKey: "Tunisia:tunisiaLabor:payroll:fiscalYear:description",
                },
                {
                    titleKey: "Tunisia:tunisiaLabor:payroll:payrollCycle:title",
                    dateKeys: ["Tunisia:tunisiaLabor:payroll:payrollCycle:date"],
                    descriptionKey: "Tunisia:tunisiaLabor:payroll:payrollCycle:description",
                },
                {
                    titleKey: "Tunisia:tunisiaLabor:payroll:minimumWage:title",
                    dateKeys: ["Tunisia:tunisiaLabor:payroll:minimumWage:wage", "Tunisia:tunisiaLabor:payroll:minimumWage:date"],
                    descriptionKey: "Tunisia:tunisiaLabor:payroll:minimumWage:description",
                },
                {
                    titleKey: "Tunisia:tunisiaLabor:payroll:payrollManagement:title",
                    dateKeys: ["Tunisia:tunisiaLabor:payroll:payrollManagement:date1", "Tunisia:tunisiaLabor:payroll:payrollManagement:date2"],
                    descriptionKey: "Tunisia:tunisiaLabor:payroll:payrollManagement:description",
                },


            ],
        },
        {
            id: 4,
            title: "Tunisia:tunisiaLabor:termination:title",
            subsections: [
                {
                    title: "Tunisia:tunisiaLabor:termination:title1",
                    description: "Tunisia:tunisiaLabor:termination:description1",
                },

                {
                    list: createList(["Tunisia:tunisiaLabor:termination:data1", "Tunisia:tunisiaLabor:termination:data2", "Tunisia:tunisiaLabor:termination:data3", "Tunisia:tunisiaLabor:termination:data4"]),
                },
                {
                    title: "Tunisia:tunisiaLabor:termination:title2",
                    description: "Tunisia:tunisiaLabor:termination:description2",
                },
                {
                    title: "Tunisia:tunisiaLabor:termination:title3",
                    description: "Tunisia:tunisiaLabor:termination:description3"
                },
            ],
        },
        {
            id: 5,
            title: "Tunisia:tunisiaLabor:leaveEntitlements:title",
            type: "leaveEntitlements",
            data: {
                subTitle: "Tunisia:tunisiaLabor:leaveEntitlements:description",


                leaves: [
                    { date: "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS1:date", title: "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS1:title" },
                    { date: "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS2:date", title: "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS2:title" },
                    { date: "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS3:date", title: "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS3:title" },
                    { date: "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS4:date", title: "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS4:title" },
                    { date: "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS5:date", title: "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS5:title" },
                    { date: "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS6:date", title: "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS6:title" },
                    { date: "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS7:date", title: "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS7:title" },
                    { date: "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS8:date", title: "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS8:title" },
                    { date: "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS9:date", title: "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS9:title" },
                    { date: "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS10:date", title: "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS10:title" },
                    { date: "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS11:date", title: "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS11:title" },
                    { date: "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS12:date", title: "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS12:title" },
                    { date: "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS13:date", title: "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS13:title" },
                    { date: "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS14:date", title: "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS14:title" },
                    { date: "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS15:date", title: "Tunisia:tunisiaLabor:leaveEntitlements:leaves:dataS15:title" },
                ],
                annualLeave: {
                    title: "Tunisia:tunisiaLabor:leaveEntitlements:leaves:annualLeave:title",
                    description: [
                        "Tunisia:tunisiaLabor:leaveEntitlements:leaves:annualLeave:description1", "Tunisia:tunisiaLabor:leaveEntitlements:leaves:annualLeave:description2"
                    ],
                },
                maternityLeave: {
                    title: "Tunisia:tunisiaLabor:leaveEntitlements:leaves:maternityLeave:title",
                    description: ["Tunisia:tunisiaLabor:leaveEntitlements:leaves:maternityLeave:description1", "Tunisia:tunisiaLabor:leaveEntitlements:leaves:maternityLeave:description2"],
                },
                paternityLeave: {
                    title: "Tunisia:tunisiaLabor:leaveEntitlements:leaves:paternityLeave:title",
                    description: "Tunisia:tunisiaLabor:leaveEntitlements:leaves:paternityLeave:description",
                },
                sickLeave: {
                    title: "Tunisia:tunisiaLabor:leaveEntitlements:leaves:sickLeave:title",
                    description: "Tunisia:tunisiaLabor:leaveEntitlements:leaves:sickLeave:description",
                },

            },
        },
        {
            id: 6,
            title: "Tunisia:tunisiaLabor:tax:title",
            subsections: [
                { description: "Tunisia:tunisiaLabor:tax:description" },
                {
                    title: "Tunisia:tunisiaLabor:tax:title1",
                    description: "Tunisia:tunisiaLabor:tax:description1"

                },
                {
                    list: createList(["Tunisia:tunisiaLabor:tax:data1", "Tunisia:tunisiaLabor:tax:data2", "Tunisia:tunisiaLabor:tax:data3"]),
                },
                {
                    title: "Tunisia:tunisiaLabor:tax:title2",
                    description: "Tunisia:tunisiaLabor:tax:description2",
                    list: createList(["Tunisia:tunisiaLabor:tax:dataS1", "Tunisia:tunisiaLabor:tax:dataS2", "Tunisia:tunisiaLabor:tax:dataS3"]),
                },
            ],
        },
        {
            id: 7,
            title: "Tunisia:tunisiaLabor:visa:title",
            subsections: [
                { description: "Tunisia:tunisiaLabor:visa:description1" },
                { description: "Tunisia:tunisiaLabor:visa:description2" },
                {
                    title: "Tunisia:tunisiaLabor:visa:title1",

                },
                { title: "Tunisia:tunisiaLabor:visa:title2" },
                {

                    description: "Tunisia:tunisiaLabor:visa:description3"
                },
                {
                    title: "Tunisia:tunisiaLabor:visa:title3",
                    list: createList(["Tunisia:tunisiaLabor:visa:data1", "Tunisia:tunisiaLabor:visa:data2", "Tunisia:tunisiaLabor:visa:data3", "Tunisia:tunisiaLabor:visa:data4", "Tunisia:tunisiaLabor:visa:data5", "Tunisia:tunisiaLabor:visa:data6", "Tunisia:tunisiaLabor:visa:data7", "Tunisia:tunisiaLabor:visa:data8"]),

                },
            ],
        },
    ],
};


export default function TunisiaLaborData() {
    return <LaborLaws data={tunisiaLaborData} />;
}
