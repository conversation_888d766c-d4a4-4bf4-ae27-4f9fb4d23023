"use client ";
import { DataGrid } from "@mui/x-data-grid";
import { useTranslation } from "react-i18next";
import activeicon from "@/assets/images/active.png";
import DialogModal from "@/features/user/component/updateProfile/experience/DialogModal";
import SvgBookmark from "@/assets/images/icons/bookmark.svg";
import { useState } from "react";
import { findIndustryColoredIcon } from "@/utils/functions";
import { useMediaQuery, useTheme } from "@mui/material";
import { axiosGetJson } from "@/config/axios";
import CustomButton from "@/components/ui/CustomButton";
import { websiteRoutesList } from "@/helpers/routesList";

function JobFavourites({
  jobFavourites,
  typeOfFavourite,
  setShortLists,
  getFavourites,
  totalOpportunities,
  paginationModel,
  setPaginationModel,
}) {
  const { t, i18n } = useTranslation();
  const [jobsTodelete, setJobsTodelete] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const truncateTitlefavorite = (title) => {
    const words = title?.split(" ");
    if (words?.length >= 3) {
      return words.slice(0, 2).join(" ") + "...";
    } else {
      return title;
    }
  };
  const isStatusActive = (dateOfExpiration) => {
    const expirationDate = new Date(dateOfExpiration);
    const currentDate = new Date();

    return expirationDate > currentDate;
  };

  const handeleDeleteconfirmation = (opportunityId) => {
    setJobsTodelete(opportunityId);
    setOpenDialog(true);
  };
  const handlecanceldelete = () => {
    setOpenDialog(false);
  };
  const deleteOpportunityFromShortlist = async (opportunityId) => {
    try {
      await axiosGetJson.delete(`/favourite/${opportunityId}`, {
        data: { type: typeOfFavourite },
      });
      setShortLists(
        jobFavourites.filter((opportunity) => opportunity._id !== opportunityId)
      );
      getFavourites.refetch();
    } catch (error) {}
  };
  const handleToggleOpportunity = async () => {
    try {
      await deleteOpportunityFromShortlist(jobsTodelete);
    } catch (error) {}
    setOpenDialog(false);
  };

  const rowsJobs =
    jobFavourites
      ?.map((item) => {
        // Get the version for the current language (fr or en)
        // versions is now an object with language keys
        const version = item?.versions?.[i18n.language];

        if (!version) {
          console.warn(
            `No version found for language ${i18n.language} in opportunity ${item?._id}`
          );
          return null; // Skip this item if no version is found
        }

        return {
          id: item._id,
          title: truncateTitlefavorite(version.title),
          dateCreation: item?.createdAt,
          industry: item?.industry,
          status: item?.dateOfExpiration,
          url: version.url,
          language: i18n.language, // Use the current language
          actions: item?._id,
        };
      })
      .filter(Boolean) || [];

  const theme = useTheme();

  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const columnsJobs = [
    {
      field: "title",
      headerName: t("favourite:job"),
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell  hide-on-mobile",
      renderCell: (params) => {
        const item = params.row.industry;
        return (
          <div className="job-title-cell">
            {findIndustryColoredIcon(item)}
            <CustomButton
              text={truncateTitlefavorite(params.value)}
              leftIcon={true}
              externalLink
              link={
                params.row.language === "en"
                  ? `/${websiteRoutesList.opportunities.route}/${params.row.url}`
                  : `/fr/${websiteRoutesList.opportunities.route}/${params.row.url}`
              }
              className="btn btn-ghost edit-blog"
            />
          </div>
        );
      },
      flex: 1,
    },
    {
      field: "dateCreation",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell  hide-on-mobile",

      headerName: t("favourite:dateCreation"),
      flex: 1,
    },
    {
      field: "status",
      headerClassName: "datagrid-header",

      headerName: t("favourite:status"),
      renderCell: (params) => (
        <>
          {" "}
          {isStatusActive(params.value) ? (
            <>
              {" "}
              <img
                src={activeicon.src}
                alt="active-icon"
                style={{ marginRight: "10px" }}
                loading="lazy"
              />
              <span className="status">{t("application:active")}</span>
            </>
          ) : (
            <>
              <img
                src={activeicon.src}
                alt="expired-icon"
                style={{ marginRight: "10px" }}
                loading="lazy"
              />
              <span className="status">{t("application:expired")}</span>
            </>
          )}
        </>
      ),
      flex: 1,
    },
    {
      field: "actions",
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell  hide-on-mobile",
      headerName: t("favourite:actions"),
      renderCell: (params) => {
        return (
          <div className="action-buttons">
            {/* <Button
              onClick={() => {
                handeleDeleteconfirmation(params.id);
              }}
              className={"btn btn-ghost"}
              icon={""}
            >
              <img src={saveIcon.src} alt="detail-icon" loading="lazy" />{" "}
            </Button> */}
            <CustomButton
              icon={<SvgBookmark className="btn-filled-yellow" />}
              onClick={() => handeleDeleteconfirmation(params.id)}
              className={"btn btn-ghost bookmark"}
            />
          </div>
        );
      },
      flex: 1,
    },
  ];
  return (
    <>
      <div style={{ height: "100%", width: "100%", padding: "20px 0px" }}>
        <DataGrid
          rows={rowsJobs}
          columns={columnsJobs}
          localeText={{ noRowsLabel: "No result found." }}
          pagination
          paginationMode="server"
          pageSizeOptions={[5, 10, 25]}
          paginationModel={paginationModel}
          onPaginationModelChange={setPaginationModel}
          rowCount={totalOpportunities || 0}
          className="pentabell-table"
          columnVisibilityModel={{
            dateCreation: !isMobile,
            status: !isMobile,
          }}
        />
      </div>

      <DialogModal
        open={openDialog}
        message={t("messages:supprimerapplicationfavoris")}
        onClose={handlecanceldelete}
        onConfirm={handleToggleOpportunity}
      />
    </>
  );
}
export default JobFavourites;
