"use client";
import { Container } from "@mui/material";
import { useTranslation } from "react-i18next";

function ISOSection() {
  const { t } = useTranslation();

  return (
    <div id="new-year-countdown">
      <Container className="new-year-container">
        <div className="new-year-content">
          <div className="element" />
          <div className="text-section">
            <p className="sm-title">{t("global:isoSection:smYellowTitle")}</p>
            <h2 className="bg-title">{t("global:isoSection:bgWhiteTitle")}</h2>
            <p className="content">{t("global:isoSection:content")}</p>
          </div>
        </div>
      </Container>
    </div>
  );
}

export default ISOSection;
