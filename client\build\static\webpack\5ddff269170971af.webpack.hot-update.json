{"c": ["app/[locale]/(website)/layout", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/@mui/icons-material/esm/Logout.js", "(app-pages-browser)/./node_modules/@mui/icons-material/esm/Settings.js", "(app-pages-browser)/./node_modules/@mui/material/AppBar/AppBar.js", "(app-pages-browser)/./node_modules/@mui/material/AppBar/appBarClasses.js", "(app-pages-browser)/./node_modules/@mui/material/Collapse/Collapse.js", "(app-pages-browser)/./node_modules/@mui/material/Collapse/collapseClasses.js", "(app-pages-browser)/./node_modules/@mui/material/Divider/Divider.js", "(app-pages-browser)/./node_modules/@mui/material/Divider/dividerClasses.js", "(app-pages-browser)/./node_modules/@mui/material/Drawer/Drawer.js", "(app-pages-browser)/./node_modules/@mui/material/Drawer/drawerClasses.js", "(app-pages-browser)/./node_modules/@mui/material/ListItem/ListItem.js", "(app-pages-browser)/./node_modules/@mui/material/ListItem/listItemClasses.js", "(app-pages-browser)/./node_modules/@mui/material/ListItemButton/ListItemButton.js", "(app-pages-browser)/./node_modules/@mui/material/ListItemButton/listItemButtonClasses.js", "(app-pages-browser)/./node_modules/@mui/material/ListItemIcon/ListItemIcon.js", "(app-pages-browser)/./node_modules/@mui/material/ListItemIcon/listItemIconClasses.js", "(app-pages-browser)/./node_modules/@mui/material/ListItemSecondaryAction/ListItemSecondaryAction.js", "(app-pages-browser)/./node_modules/@mui/material/ListItemSecondaryAction/listItemSecondaryActionClasses.js", "(app-pages-browser)/./node_modules/@mui/material/ListItemText/ListItemText.js", "(app-pages-browser)/./node_modules/@mui/material/ListItemText/listItemTextClasses.js", "(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js", "(app-pages-browser)/./node_modules/@mui/material/MenuItem/menuItemClasses.js", "(app-pages-browser)/./node_modules/@mui/material/Slide/Slide.js", "(app-pages-browser)/./node_modules/@mui/material/Toolbar/Toolbar.js", "(app-pages-browser)/./node_modules/@mui/material/Toolbar/toolbarClasses.js", "(app-pages-browser)/./node_modules/@socket.io/component-emitter/lib/esm/index.js", "(app-pages-browser)/./node_modules/engine.io-client/build/esm/contrib/has-cors.js", "(app-pages-browser)/./node_modules/engine.io-client/build/esm/contrib/parseqs.js", "(app-pages-browser)/./node_modules/engine.io-client/build/esm/contrib/parseuri.js", "(app-pages-browser)/./node_modules/engine.io-client/build/esm/globals.js", "(app-pages-browser)/./node_modules/engine.io-client/build/esm/index.js", "(app-pages-browser)/./node_modules/engine.io-client/build/esm/socket.js", "(app-pages-browser)/./node_modules/engine.io-client/build/esm/transport.js", "(app-pages-browser)/./node_modules/engine.io-client/build/esm/transports/index.js", "(app-pages-browser)/./node_modules/engine.io-client/build/esm/transports/polling-fetch.js", "(app-pages-browser)/./node_modules/engine.io-client/build/esm/transports/polling-xhr.js", "(app-pages-browser)/./node_modules/engine.io-client/build/esm/transports/polling.js", "(app-pages-browser)/./node_modules/engine.io-client/build/esm/transports/websocket.js", "(app-pages-browser)/./node_modules/engine.io-client/build/esm/transports/webtransport.js", "(app-pages-browser)/./node_modules/engine.io-client/build/esm/util.js", "(app-pages-browser)/./node_modules/engine.io-parser/build/esm/commons.js", "(app-pages-browser)/./node_modules/engine.io-parser/build/esm/contrib/base64-arraybuffer.js", "(app-pages-browser)/./node_modules/engine.io-parser/build/esm/decodePacket.browser.js", "(app-pages-browser)/./node_modules/engine.io-parser/build/esm/encodePacket.browser.js", "(app-pages-browser)/./node_modules/engine.io-parser/build/esm/index.js", "(app-pages-browser)/./node_modules/next/dist/api/image.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cshared%5C%5Clib%5C%5Clazy-dynamic%5C%5Cdynamic-bailout-to-csr.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cshared%5C%5Clib%5C%5Clazy-dynamic%5C%5Cpreload-css.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Cnode_modules%5C%5Creact-toastify%5C%5Cdist%5C%5Creact-toastify.esm.mjs%22%2C%22ids%22%3A%5B%22ToastContainer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Cnode_modules%5C%5Creact-toastify%5C%5Cdist%5C%5CReactToastify.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Clayouts%5C%5CFooter.jsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Clayouts%5C%5CHeader.jsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CTranslationProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Csrc%5C%5Clib%5C%5Creact-query-client.js%22%2C%22ids%22%3A%5B%22ReactQueryProvider%22%5D%7D&server=false!", "(app-pages-browser)/./node_modules/next/dist/client/image-component.js", "(app-pages-browser)/./node_modules/next/dist/compiled/picomatch/index.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/amp-context.shared-runtime.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/amp-mode.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/get-img-props.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/head.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/image-blur-svg.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/image-config-context.shared-runtime.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/image-config.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/image-external.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/image-loader.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/match-local-pattern.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/match-remote-pattern.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/side-effect.js", "(app-pages-browser)/./node_modules/socket.io-client/build/esm/contrib/backo2.js", "(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js", "(app-pages-browser)/./node_modules/socket.io-client/build/esm/manager.js", "(app-pages-browser)/./node_modules/socket.io-client/build/esm/on.js", "(app-pages-browser)/./node_modules/socket.io-client/build/esm/socket.js", "(app-pages-browser)/./node_modules/socket.io-client/build/esm/url.js", "(app-pages-browser)/./node_modules/socket.io-parser/build/esm/binary.js", "(app-pages-browser)/./node_modules/socket.io-parser/build/esm/index.js", "(app-pages-browser)/./node_modules/socket.io-parser/build/esm/is-binary.js", "(app-pages-browser)/./src/assets/images/charte/logo-picto-dark.webp", "(app-pages-browser)/./src/assets/images/charte/logo-picto-light.webp", "(app-pages-browser)/./src/assets/images/charte/txt_ar.webp", "(app-pages-browser)/./src/assets/images/charte/txt_ar_dark.webp", "(app-pages-browser)/./src/assets/images/charte/txt_en.webp", "(app-pages-browser)/./src/assets/images/charte/txt_en_dark.webp", "(app-pages-browser)/./src/assets/images/charte/txt_hi.webp", "(app-pages-browser)/./src/assets/images/charte/txt_hi_dark.webp", "(app-pages-browser)/./src/assets/images/charte/txt_zh.webp", "(app-pages-browser)/./src/assets/images/charte/txt_zh_dark.webp", "(app-pages-browser)/./src/assets/images/flag/en.png", "(app-pages-browser)/./src/assets/images/flag/fr.png", "(app-pages-browser)/./src/assets/images/icons/Line.svg", "(app-pages-browser)/./src/assets/images/icons/arrowDown.svg", "(app-pages-browser)/./src/assets/images/icons/arrowUp.svg", "(app-pages-browser)/./src/assets/images/icons/closeMenu.svg", "(app-pages-browser)/./src/assets/images/icons/notifications.svg", "(app-pages-browser)/./src/assets/images/icons/openMenu.svg", "(app-pages-browser)/./src/components/languageChanger.js", "(app-pages-browser)/./src/components/layouts/Header.jsx", "(app-pages-browser)/./src/components/layouts/MobileMenu.jsx", "(app-pages-browser)/./src/components/ui/DropdownMenu.jsx", "(app-pages-browser)/./src/components/ui/DropdownMenuMobile.jsx", "(app-pages-browser)/./src/components/ui/MyAccountDropdown.jsx", "(app-pages-browser)/./src/components/ui/NotificationComponent.jsx", "(app-pages-browser)/./src/features/user/hooks/updateProfile.hooks.js", "(app-pages-browser)/./src/features/user/services/updateProfile.js", "(app-pages-browser)/./src/utils/messages.js"]}