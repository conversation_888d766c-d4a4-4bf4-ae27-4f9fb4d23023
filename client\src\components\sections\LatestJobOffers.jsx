"use client";
import { useState, useEffect } from "react";
import { Container } from "@mui/material";
import { useTranslation } from "react-i18next";
import useEmblaCarousel from "embla-carousel-react";
import countries from "i18n-iso-countries";
import enLocale from "i18n-iso-countries/langs/en.json";

import OpportunityItemByGrid from "@/features/opportunity/components/opportunityFrontOffice/OpportunityComponents/OpportunityItemByGrid";
import CustomButton from "@/components/ui/CustomButton";
import { axiosGetJsonSSR } from "@/config/axios";
import { API_URLS, baseURL } from "@/utils/urls";
import { websiteRoutesList } from "@/helpers/routesList";
import Loading from "@/components/loading/Loading";

function LatestJobOffers({ language }) {
  const OPTIONS = { loop: false, align: "start" };
  const [emblaRef] = useEmblaCarousel(OPTIONS);
  const [jobOffers, setJobOffers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const { t } = useTranslation();
  const [query, setQuery] = useState({
    urgent: undefined,
  });

  countries.registerLocale(enLocale);

  const fetchJobOffers = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await axiosGetJsonSSR.get(
        `${baseURL}${API_URLS.opportunity}/urgent`,
        {
          params: {
            urgent: query.urgent,
          },
        }
      );

      if (response.data && response.data.length > 0) {
        const fetchedOffers = response.data.map((offer) => ({
          id: offer._id,
          title: offer.versions[language]?.title || "Titre non disponible",
          industry: offer.industry,
          country: offer.country,
          dateOfExpiration: offer.dateOfExpiration,
          minExperience: offer.minExperience,
          maxExperience: offer.maxExperience,
          existingLanguages: offer.existingLanguages,
          reference: offer.reference,
          urgent: offer.urgent,
          url: offer.versions[language]?.url,
        }));
        setJobOffers(fetchedOffers);
      } else {
        setJobOffers([]);
        setError("No job offers available at the moment.");
      }
    } catch (err) {
      console.error("Error fetching job offers:", err);
      setError("Failed to fetch job offers");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchJobOffers();
  }, [query]);

  const onClickFilter = (data) => {
    if (data.urgent !== undefined) {
      setQuery((prev) => ({
        ...prev,
        urgent: data.urgent,
      }));
    } else {
      setQuery((prev) => ({
        ...prev,
        urgent: undefined,
      }));
    }
  };

  return (
    <Container id="latest-offers" className="custom-max-width">
      <h2 className="heading-h1 text-center">{t("homePage:s3:title")}</h2>
      <div id="filter-btns">
        <CustomButton
          onClick={() => onClickFilter({ urgent: !query.urgent })}
          text={t("homePage:s3:btu")}
          className={`${
            query.urgent ? "btn btn-filter selected" : "btn btn-outlined"
          }`}
        />
        <CustomButton
          text={t("homePage:s3:btlast")}
          onClick={() => onClickFilter({ urgent: undefined })}
          className={`${
            query.urgent === undefined
              ? "btn btn-filter selected"
              : "btn btn-outlined"
          }`}
        />
      </div>

      <section className="embla" id={"jobs__slider"}>
        <div className="embla__viewport" ref={emblaRef}>
          <div className="embla__container">
            {loading ? (
              <Loading />
            ) : jobOffers?.length > 0 ? (
              jobOffers.map((opportunity) => (
                <OpportunityItemByGrid
                  key={opportunity?._id}
                  opportunity={opportunity}
                  language={language}
                  website
                />
              ))
            ) : (
              <p className="no-results-message">
                {t("opportunities:noOpportunitiesFound")}
              </p>
            )}
          </div>
        </div>
      </section>

      <div className="center-div">
        <CustomButton
          text={t("homePage:s3:all")}
          link={`/${websiteRoutesList.opportunities.route}`}
          // onClick={() => onClickFilter({ urgent: undefined })}
          className={"btn btn-filled"}
        />
      </div>
    </Container>
  );
}

export default LatestJobOffers;
