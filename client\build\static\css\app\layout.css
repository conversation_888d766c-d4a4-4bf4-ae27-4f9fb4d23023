/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[13].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[13].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[14].oneOf[13].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[14].oneOf[13].use[5]!./src/styles/globals.scss ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@charset "UTF-8";
@font-face {
  font-family: "Proxima-Nova-Black";
  src: url("/fonts/proxima-nova/WOFF/Proxima-Nova-Black.woff") format("woff");
  font-weight: 900;
  font-display: swap;
}
@font-face {
  font-family: "Proxima-Nova-Extrabold";
  src: url("/fonts/proxima-nova/WOFF/Proxima-Nova-Extrabold.woff") format("woff");
  font-weight: 800;
  font-display: swap;
}
@font-face {
  font-family: "Proxima-Nova-Bold";
  src: url("/fonts/proxima-nova/WOFF/Proxima-Nova-Bold.woff") format("woff");
  font-weight: 700;
  font-display: swap;
}
@font-face {
  font-family: "Proxima-Nova-Semibold";
  src: url("/fonts/proxima-nova/WOFF/Proxima-Nova-Semibold.woff") format("woff");
  font-weight: 600;
  font-display: swap;
}
@font-face {
  font-family: "Proxima-Nova-Medium";
  src: url("/fonts/proxima-nova/WOFF/Proxima-Nova-Medium.woff") format("woff");
  font-weight: 500 !important;
  font-display: swap;
}
@font-face {
  font-family: "Proxima-Nova-Regular";
  src: url("/fonts/proxima-nova/WOFF/Proxima-Nova-Regular.woff") format("woff");
  font-weight: 400;
  font-display: swap;
}
@font-face {
  font-family: "Proxima-Nova-Light";
  src: url("/fonts/proxima-nova/WOFF/Proxima-Nova-Light.woff") format("woff");
  font-weight: 300;
  font-display: swap;
}
@font-face {
  font-family: "Proxima-Nova-Thin";
  src: url("/fonts/proxima-nova/WOFF/Proxima-Nova-Thin.woff") format("woff");
  font-weight: 100;
  font-display: swap;
}
#pentabell-header {
  background: rgba(35, 71, 145, 0.1490196078);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  transition: "background 0.3s ease";
}
#pentabell-header .icon-open-menu svg path {
  stroke: #ffffff;
}
#pentabell-header .pentabell-logo {
  display: flex;
  margin: 5px 0;
}
#pentabell-header .pentabell-logo img {
  height: auto;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #pentabell-header .pentabell-logo img {
    margin-right: 15px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #pentabell-header .pentabell-logo img {
    margin-right: 15px;
  }
}
#pentabell-header .css-1oom1vg-MuiToolbar-root {
  padding: 8px 0;
}
#pentabell-header.scroll {
  background: rgba(35, 71, 145, 0.1490196078);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
}
#pentabell-header .navbar-link,
#pentabell-header .dropdown-toggle-link {
  text-transform: capitalize;
  font-size: 16px;
  height: auto;
  color: #ffffff;
  text-decoration: none;
}
@media only screen and (min-width: 769px) and (max-width: 1024px) {
  #pentabell-header .navbar-link,
  #pentabell-header .dropdown-toggle-link {
    font-size: 14px;
  }
}
#pentabell-header .navbar-link svg path,
#pentabell-header .dropdown-toggle-link svg path {
  fill: #ffffff;
}
#pentabell-header .navbar-link:hover,
#pentabell-header .dropdown-toggle-link:hover {
  background-color: transparent;
  font-family: "Proxima-Nova-Semibold" !important;
}
#pentabell-header .navbar-link.whiteBg,
#pentabell-header .dropdown-toggle-link.whiteBg {
  color: #234791;
}
#pentabell-header .navbar-link.whiteBg svg path,
#pentabell-header .dropdown-toggle-link.whiteBg svg path {
  fill: #234791 !important;
}
#pentabell-header .navbar-link.active,
#pentabell-header .dropdown-toggle-link.active {
  color: #ffca00;
  font-family: "Proxima-Nova-Semibold" !important;
}
#pentabell-header .navbar-link.active svg path,
#pentabell-header .dropdown-toggle-link.active svg path {
  fill: #ffca00 !important;
}
#pentabell-header .dropdown-toggle-link {
  display: contents;
  margin: auto;
}
#pentabell-header .navbar-link:nth-last-child(2) {
  border: 1px solid #ffffff !important;
  border-radius: 1px;
}
#pentabell-header .navbar-link:nth-last-child(2).whiteBg {
  border: 1px solid #234791 !important;
}
#pentabell-header .navbar-link:nth-last-child(2).active {
  border-color: #ffca00 !important;
}
#pentabell-header .menu {
  display: flex;
  align-items: center;
}
#pentabell-header .dropdown-menu {
  width: auto;
}

.flag-lang {
  display: flex;
  margin: auto;
}

.my-profile-img {
  width: 48px;
  height: 48px;
  border-radius: 100%;
}
.my-profile-img img {
  width: 48px;
  height: 48px;
  border-radius: 100%;
}

.sub-dropdown-menu {
  width: 100%;
}
.sub-dropdown-menu button svg path {
  fill: #234791 !important;
}
.sub-dropdown-menu button {
  width: 100%;
  justify-content: space-between;
}

.dropdown-item {
  padding: 0 !important;
}
.dropdown-item button.dropdown-toggle {
  padding: 0 !important;
}
.dropdown-item svg {
  margin-right: 5px;
}
.dropdown-item button,
.dropdown-item a {
  padding: 6px 16px;
}
.dropdown-item button:hover,
.dropdown-item a:hover {
  background: transparent;
}

#fade-menu ul {
  background-color: #e4effc;
  padding: 10px 0;
  display: flex;
  flex-direction: column;
  align-items: stretch;
}
#fade-menu .dropdown-item-link,
#fade-menu .dropdown-toggle-link {
  color: #234791 !important;
  text-transform: capitalize;
  text-decoration: none;
  font-size: 16px;
  width: -webkit-fill-available;
  display: flex;
  align-items: center;
  justify-content: left;
}
#fade-menu .dropdown-item-link .color-industry,
#fade-menu .dropdown-toggle-link .color-industry {
  width: 14px;
  height: 14px;
  margin-right: 4px;
}
#fade-menu .dropdown-item-link .color-industry.oil-gaz,
#fade-menu .dropdown-toggle-link .color-industry.oil-gaz {
  background-color: #d69b19;
  color: #ffffff;
}
#fade-menu .dropdown-item-link .color-industry.energy,
#fade-menu .dropdown-toggle-link .color-industry.energy {
  background-color: #cc3233;
  color: #ffffff;
}
#fade-menu .dropdown-item-link .color-industry.banking-insurance,
#fade-menu .dropdown-toggle-link .color-industry.banking-insurance {
  background-color: #0b3051;
  color: #ffffff;
}
#fade-menu .dropdown-item-link .color-industry.transport,
#fade-menu .dropdown-toggle-link .color-industry.transport {
  background-color: #009966;
  color: #ffffff;
}
#fade-menu .dropdown-item-link .color-industry.it-telecom,
#fade-menu .dropdown-toggle-link .color-industry.it-telecom {
  background-color: #743794;
  color: #ffffff;
}
#fade-menu .dropdown-item-link .color-industry.other,
#fade-menu .dropdown-toggle-link .color-industry.other {
  background-color: #234791;
  color: #ffffff;
}
#fade-menu .dropdown-item-link .color-industry.pharmaceutical,
#fade-menu .dropdown-toggle-link .color-industry.pharmaceutical {
  background-color: #2bbfad;
  color: #ffffff;
}
#fade-menu .dropdown-item-link.active,
#fade-menu .dropdown-toggle-link.active {
  color: #ffca00 !important;
  font-family: "Proxima-Nova-Medium" !important;
  font-weight: 500;
}
#fade-menu .dropdown-item-link.active svg path,
#fade-menu .dropdown-toggle-link.active svg path {
  fill: #ffca00 !important;
}
#fade-menu .dropdown-toggle-link {
  width: auto;
}

#pentabell-footer {
  background-color: #234791;
  color: #ffffff;
  padding-bottom: 34px;
  padding-top: 50px;
}
#pentabell-footer .pentabell-logo {
  height: auto;
}
#pentabell-footer .margin-footer {
  margin: 20px 0;
}
#pentabell-footer .certificate-icons {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
#pentabell-footer .certificate-icons img {
  background-color: #dbe8f6;
  border-radius: 100%;
}
#pentabell-footer form {
  width: 100%;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #pentabell-footer .location-footer {
    padding-top: 0 !important;
  }
}
#pentabell-footer .btn-filled {
  border: 0 !important;
}
#pentabell-footer .btn-filled:hover {
  background-color: #0b3051;
}
#pentabell-footer .container {
  justify-content: space-between;
}
#pentabell-footer .title {
  font-family: "Proxima-Nova-Medium" !important;
  font-size: 20px;
  margin: 20px 0;
}
#pentabell-footer .paragraph {
  margin: 10px 0;
  display: flex;
  flex-wrap: wrap;
}
#pentabell-footer .paragraph.link {
  font-family: "Proxima-Nova-Semibold" !important;
  font-size: 18px;
  color: #ffffff;
}
#pentabell-footer .link {
  text-decoration: none;
  color: #ffffff;
  margin-right: 5px;
}
#pentabell-footer .link:hover {
  text-decoration: underline;
}
#pentabell-footer .bottom-links {
  padding-bottom: 10px;
  margin-top: 15px;
}
#pentabell-footer .bottom-links .website-links {
  padding-top: 10px;
  border-top: 1px solid #ffffff;
  display: flex;
  justify-content: space-between;
}
#pentabell-footer .bottom-links .link {
  font-size: 20px;
  font-family: "Proxima-Nova-Semibold" !important;
}
#pentabell-footer .white-point {
  width: 5px;
  height: 5px;
  content: "";
  border-radius: 100%;
  background-color: #ffffff;
  margin: auto 10px auto 5px;
}
#pentabell-footer .input-pentabell .MuiInputBase-root {
  background: #e5f0fc !important;
}

#social-media-links {
  padding-top: 10px;
  display: flex;
}
#social-media-links .link {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: row;
}
#social-media-links .link svg path {
  fill: #ffffff;
}
#social-media-links .link svg:hover path {
  fill: #ffca00;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #social-media-links {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
  }
}

@media only screen and (min-width: 320px) and (max-width: 480px) {
  #newsletter-form {
    margin: 16px 0 0 16px;
  }
}
#newsletter-form form {
  display: flex;
}
#newsletter-form form .btn-filled {
  height: 50px;
}
#newsletter-form .form-group {
  height: 50px;
}
#newsletter-form .css-fzx5yo-MuiPaper-root-MuiAlert-root {
  transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 4px;
  box-shadow: var(--Paper-shadow);
  background-image: var(--Paper-overlay);
  font-family: "Roboto", "Helvetica", "Arial", sans-serif;
  font-weight: 500 !important;
  font-size: 0.875rem;
  line-height: 1.43;
  letter-spacing: 0.01071em;
  display: flex;
  padding: 0px 10px;
  background-color: #e5f0fc;
  color: #cc3233;
  width: 100%;
  margin: 12px 0;
}

.MuiPaper-root.MuiPaper-elevation.MuiPaper-elevation16.MuiDrawer-paper.MuiDrawer-paperAnchorLeft.css-nsg84x {
  background: #234791 !important;
}

#mobile-menu .btn-ghost.stroke-svg svg path {
  stroke: #ffffff !important;
}
#mobile-menu .MuiPaper-root.MuiPaper-elevation {
  background: #234791 !important;
  width: 65% !important;
}
#mobile-menu .menu-item {
  color: #ffffff;
  font-size: 16px;
  font-family: "Proxima-Nova-Medium" !important;
  font-weight: 500;
  text-transform: capitalize;
  text-decoration: none;
}
#mobile-menu .menu-item svg {
  margin-right: 5px;
}
#mobile-menu .menu-item .color-industry {
  width: 14px;
  height: 14px;
  margin-right: 5px;
  border: 1px solid #ffffff;
}
#mobile-menu .menu-item .color-industry.oil-gaz {
  background-color: #d69b19;
  color: #ffffff;
}
#mobile-menu .menu-item .color-industry.energy {
  background-color: #cc3233;
  color: #ffffff;
}
#mobile-menu .menu-item .color-industry.banking-insurance {
  background-color: #0b3051;
  color: #ffffff;
}
#mobile-menu .menu-item .color-industry.transport {
  background-color: #009966;
  color: #ffffff;
}
#mobile-menu .menu-item .color-industry.it-telecom {
  background-color: #743794;
  color: #ffffff;
}
#mobile-menu .menu-item .color-industry.other {
  background-color: #234791;
  color: #ffffff;
}
#mobile-menu .menu-item .color-industry.pharmaceutical {
  background-color: #2bbfad;
  color: #ffffff;
}
#mobile-menu .menu-item.sub-menu {
  display: flex;
  flex-direction: column;
  align-items: stretch;
}
#mobile-menu .menu-item.sub-menu #collapse-section .menu-item {
  padding: 6px 16px;
  font-size: 14px;
}
#mobile-menu .menu-item.sub-menu .dropdown-menu {
  padding: 0;
}
#mobile-menu nav > a:last-child {
  text-align: center;
  border: 1px solid #ffffff;
  margin: auto 16px;
}
#mobile-menu .dropdown-menu {
  display: flex;
  justify-content: space-between;
  flex-direction: row;
}
#mobile-menu .top-section {
  display: flex;
  flex-direction: column;
  align-items: center;
}
#mobile-menu .items-list {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 75%;
}

#copyrights {
  padding: 10px;
  background-color: rgba(228, 239, 252, 0.7);
  text-align: center;
  font-size: 14px;
  color: #234791;
  font-family: "Proxima-Nova-Medium" !important;
}
#copyrights .link {
  color: #234791;
  text-decoration: underline;
}

.pagination {
  padding: 10px;
  margin: auto;
  text-align: center;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

#page-not-found {
  height: 100vh;
  width: 100%;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  background-image: url(/_next/static/media/bg404.e311f65c.png);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #page-not-found {
    background-image: url(/_next/static/media/bg404-mobile.d4bc3e2b.png);
  }
}
#page-not-found .btn, #page-not-found .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer #page-not-found button {
  width: -moz-fit-content;
  width: fit-content;
  text-align: center;
  margin: 0 auto;
}

.industries-filter.color-industry {
  width: 14px;
  height: 14px;
  margin-right: 4px;
}
.industries-filter.color-industry.oil-gaz {
  background-color: #d69b19;
  color: #ffffff;
}
.industries-filter.color-industry.energy {
  background-color: #cc3233;
  color: #ffffff;
}
.industries-filter.color-industry.banking-insurance {
  background-color: #0b3051;
  color: #ffffff;
}
.industries-filter.color-industry.transport {
  background-color: #009966;
  color: #ffffff;
}
.industries-filter.color-industry.it-telecom {
  background-color: #743794;
  color: #ffffff;
}
.industries-filter.color-industry.other {
  background-color: #234791;
  color: #ffffff;
}
.industries-filter.color-industry.pharmaceutical {
  background-color: #2bbfad;
  color: #ffffff;
}

#sidebar-component::-webkit-scrollbar {
  border-radius: 0px 4px 4px 0px;
  background: #dbe8f6;
  width: 5px;
  height: 80%;
}

#sidebar-component::-webkit-scrollbar-thumb {
  background: #234791;
  border-radius: 0px 4px 4px 0px;
}

#dashboard-layout {
  background-color: rgba(228, 239, 252, 0.7);
  height: 100vh;
}
#dashboard-layout #main-component {
  transition: all 0.3s ease;
  float: right;
  background-color: rgba(228, 239, 252, 0.7);
  width: 100%;
  max-height: 100%;
  height: -webkit-fill-available;
  padding-top: 15px;
  padding-right: 15px;
  padding-left: calc(220px + 30px);
  margin-bottom: 40px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #dashboard-layout #main-component {
    padding: 8px 8px 0 8px;
    padding-left: calc(220px + 15px);
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #dashboard-layout #main-component {
    padding: 8px 8px 0 8px;
    padding-left: calc(220px + 15px);
  }
}
#dashboard-layout #main-component .display-inline {
  margin-top: 10px;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #dashboard-layout #main-component .display-inline {
    flex-wrap: wrap;
  }
  #dashboard-layout #main-component .display-inline .btn, #dashboard-layout #main-component .display-inline .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer #dashboard-layout #main-component .display-inline button {
    margin-bottom: 20px;
  }
}
#dashboard-layout #main-component .container {
  margin: auto;
}
#dashboard-layout #main-component #outlet {
  width: 100%;
  height: 100%;
  display: contents;
}
#dashboard-layout #main-component #outlet .heading-h2 {
  margin-top: 0 !important;
}
#dashboard-layout #main-component.toggled {
  transition: all 0.3s ease;
  padding-left: calc(55px + 30px);
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #dashboard-layout #main-component.toggled {
    padding-left: calc(55px + 15px);
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #dashboard-layout #main-component.toggled {
    padding-left: calc(55px + 15px);
  }
}
#dashboard-layout #dashboard-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  padding: 0;
  max-height: 65px;
}
#dashboard-layout #dashboard-header .btn-link {
  margin-right: auto;
  margin-bottom: auto;
  background: #dbe8f6;
  padding: 10px;
  border-radius: 100%;
  height: 45px;
  width: 45px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
#dashboard-layout #dashboard-header .btn-link svg {
  margin-left: 0 !important;
  transform: scale(1.08);
}
#dashboard-layout #dashboard-header .dropdown-toggle {
  padding: 0;
}
#dashboard-layout #dashboard-header .dropdown-menu svg path {
  fill: #234791;
}
#dashboard-layout #dashboard-header .my-profile-img {
  min-width: 36px;
  height: auto;
  max-width: 48px;
}
#dashboard-layout #dashboard-header .my-profile-img img {
  margin-right: 13px;
  background: #dbe8f6;
}
#dashboard-layout #dashboard-footer {
  background-color: rgba(228, 239, 252, 0.7);
  float: right;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  height: 30px;
  position: fixed;
  bottom: 0;
}
#dashboard-layout #dashboard-footer .paragraph {
  font-size: 12px !important;
  color: #798ba3;
}
#dashboard-layout #dashboard-footer a {
  color: #234791;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #dashboard-layout #dashboard-footer {
    height: 30px;
  }
}
#dashboard-layout #sidebar-component {
  background-color: #ffffff;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  width: 220px;
  display: block;
  z-index: 3;
  margin: 15px;
  margin-bottom: 40px;
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 15px 0;
  overflow-y: auto;
  transition: all 0.3s ease;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #dashboard-layout #sidebar-component {
    margin: 8px;
    margin-bottom: 40px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #dashboard-layout #sidebar-component {
    margin: 8px;
    margin-bottom: 40px;
  }
}
#dashboard-layout #sidebar-component .pentabell-logo {
  width: 90%;
  margin: auto;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
#dashboard-layout #sidebar-component .pentabell-logo .logo {
  width: 60px;
  height: 60px;
}
#dashboard-layout #sidebar-component .pentabell-logo img {
  margin-left: auto;
  max-width: 130px;
  cursor: pointer;
  height: -moz-fit-content;
  height: fit-content;
  width: 100%;
}
#dashboard-layout #sidebar-component.toggled {
  transition: all 0.3s ease;
  width: 55px;
}
#dashboard-layout #sidebar-component.toggled .menu-title {
  display: none;
}
#dashboard-layout #sidebar-component.toggled .menu-icon {
  margin: auto;
}
#dashboard-layout #sidebar-component.toggled .menu-icon.avatar {
  margin-right: auto;
}
#dashboard-layout #sidebar-component .menu-icon.avatar {
  margin-right: 8px;
  background-color: #dbe8f6;
}
#dashboard-layout #sidebar-component #sidebar-menu {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: space-between;
  background-color: #ffffff;
}
#dashboard-layout #sidebar-component .toggle-menu {
  display: flex;
  margin: 0;
  padding: 0 !important;
}
#dashboard-layout #sidebar-component .toggle-menu .menu-icon svg path {
  fill: #234791;
}
#dashboard-layout #sidebar-component .toggle-menu svg {
  margin-left: 0;
}
#dashboard-layout #sidebar-component .nav-link {
  margin: 2px 0;
  border-radius: 4px;
  text-transform: capitalize;
  display: flex;
  align-items: center;
  color: #798ba3;
  text-decoration: none;
  font-size: 16px;
  font-family: "Proxima-Nova-Semibold" !important;
}
#dashboard-layout #sidebar-component .nav-link svg path {
  fill: #798ba3;
}
#dashboard-layout #sidebar-component .nav-link.active {
  color: #ffffff;
  background-color: #234791;
}
#dashboard-layout #sidebar-component .nav-link.active svg path {
  fill: #ffffff;
}

.settings {
  margin-bottom: 20px;
}

.notif-section {
  position: relative;
}
.notif-section .btn-notif {
  padding: 0 4px 0 0 !important;
}
.notif-section .notif-nbr {
  background: #cc3233;
  padding: 0px;
  border: 1px solid #ffffff;
  color: #ffffff;
  border-radius: 50%;
  font-size: 12px;
  width: 20px;
  height: 20px;
  font-weight: 700;
  position: absolute;
  bottom: 30px;
  left: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}
.notif-section .notif-nbr-header {
  background: #cc3233;
  padding: 2px;
  border: 1px solid #ffffff;
  color: #ffffff;
  border-radius: 50%;
  font-size: 12px;
  width: 12px;
  height: 12px;
  font-weight: 700;
  position: absolute;
  bottom: 15px;
  left: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

#auth-layout {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}
#auth-layout .container {
  background: linear-gradient(#0b3051 0%, #234791 100%);
  color: #ffffff;
  width: 50%;
  margin: 100px auto;
  padding: 30px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #auth-layout .container {
    width: 90%;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #auth-layout .container {
    width: 75%;
  }
}

#apply-layout {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}
#apply-layout .apply-section {
  margin: 0 0 50px 0;
}
#apply-layout .opportunity-info .title {
  font-size: 24px;
  font-family: "Proxima-Nova-Semibold" !important;
}
#apply-layout .opportunity-info .apply-title {
  font-size: 40px;
  font-family: "Proxima-Nova-Medium" !important;
}
#apply-layout .opportunity-info .job-ref {
  font-size: 20px;
  color: #ffffff;
  margin-bottom: 20px;
  margin-top: 10px;
}
#apply-layout .opportunity-info .flex {
  display: flex;
  flex-direction: column;
}
#apply-layout .opportunity-info .flex .job-info {
  display: flex;
  flex-direction: row;
  margin: 10px 0;
}
#apply-layout .opportunity-info .flex.children-componenent p {
  color: #ffffff;
  font-size: 14px;
}
#apply-layout .opportunity-info .flex.children-componenent svg {
  margin-right: 8px;
  transform: scale(1.3);
  margin: auto 8px auto 0;
}
#apply-layout .opportunity-info .flex.children-componenent svg path {
  fill: #ffffff;
}
#apply-layout .form-section {
  width: 100% !important;
  padding: 5px 0;
}
#apply-layout .btn-continue {
  text-align: -webkit-right;
}
#apply-layout .container {
  background: linear-gradient(#0b3051 0%, #234791 100%);
  color: #ffffff;
  width: 100%;
  min-height: 750px;
  margin: 0px auto 100px auto;
  padding: 30px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #apply-layout .container {
    width: 90%;
    margin: 0px auto 0px auto !important;
    padding: 20px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #apply-layout .container {
    width: 75%;
    padding: 20px;
    margin: 0px auto 0px auto !important;
  }
}
#apply-layout .container-opportunity-item {
  background-color: #dbe8f6;
  color: #ffffff;
  width: 100%;
  margin: 150px auto 0px auto;
  padding: 30px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #apply-layout .container-opportunity-item {
    width: 90%;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #apply-layout .container-opportunity-item {
    width: 75%;
  }
}
#apply-layout .container-apply-authenticated {
  background: linear-gradient(#0b3051 0%, #234791 100%);
  color: #ffffff;
  width: 50%;
  margin: 100px auto;
  padding: 30px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #apply-layout .container-apply-authenticated {
    width: 90%;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #apply-layout .container-apply-authenticated {
    width: 75%;
  }
}
#apply-layout .resume-title {
  margin: 10px 0 20px;
  display: flex;
  justify-content: space-between;
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #apply-layout .resume-title {
    display: block;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #apply-layout .resume-title {
    display: block;
  }
}
#apply-layout .resume-title .title {
  font-size: 18px !important;
}

#choose-resume {
  display: flex;
  justify-content: space-between;
  border: 1px solid #699bd4;
  padding: 5px;
  margin: 0px 0px 20px;
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #choose-resume {
    display: block;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #choose-resume {
    display: block;
  }
}
#choose-resume .resume-info {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0 5px;
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #choose-resume .resume-info {
    display: block;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #choose-resume .resume-info {
    display: block;
  }
}
#choose-resume .resume-name {
  font-size: 16px;
  padding: 0px 15px;
}
#choose-resume .light svg {
  color: #ffca00;
}
#choose-resume .light .css-r5in8e-MuiInputBase-root-MuiInput-root::before,
#choose-resume .light .css-10yf2i8-MuiInputBase-root-MuiInput-root::before {
  border-bottom-color: #ffffff !important;
}
#choose-resume .light .css-r5in8e-MuiInputBase-root-MuiInput-root::after, #choose-resume .light .css-r5in8e-MuiInputBase-root-MuiInput-root:focus, #choose-resume .light .css-r5in8e-MuiInputBase-root-MuiInput-root:hover, #choose-resume .light .css-r5in8e-MuiInputBase-root-MuiInput-root::before,
#choose-resume .light .css-10yf2i8-MuiInputBase-root-MuiInput-root::after,
#choose-resume .light .css-10yf2i8-MuiInputBase-root-MuiInput-root:focus,
#choose-resume .light .css-10yf2i8-MuiInputBase-root-MuiInput-root:hover,
#choose-resume .light .css-10yf2i8-MuiInputBase-root-MuiInput-root::before {
  border-bottom-color: #ffffff !important;
}
#choose-resume .light .css-r5in8e-MuiInputBase-root-MuiInput-root:hover:not(.Mui-disabled,
.Mui-error):before,
#choose-resume .light .css-10yf2i8-MuiInputBase-root-MuiInput-root:hover:not(.Mui-disabled,
.Mui-error):before {
  border-bottom-color: #ffffff;
}

.logo-container {
  text-align: center;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.logo-picto {
  width: 35px;
  height: auto;
}

.flip-box {
  width: 135px;
  height: 35px;
  position: relative;
  transform-style: preserve-3d;
  animation: rotate-vertical 8s infinite linear;
}

.flip-box-inner {
  position: relative;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
}

.flip-box-face {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  color: #ffffff;
  backface-visibility: hidden;
  transition: transform 0.3s;
  /* Add transition for smoother effect */
}
.flip-box-face img {
  max-width: 100px;
  width: 100%;
  display: flex;
  height: auto;
}

.front {
  transform: rotateX(0deg) translateZ(25px);
  /* Initial position */
}

.back {
  transform: rotateX(180deg) translateZ(25px);
}

.top {
  transform: rotateX(90deg) translateZ(25px);
}

.bottom {
  transform: rotateX(-90deg) translateZ(25px);
  /* Bottom face */
}

@keyframes rotate-vertical {
  /* Front face */
  0% {
    transform: rotateX(0deg);
  }
  /* Start at front */
  8.33% {
    transform: rotateX(0deg);
  }
  /* Pause on front for 0.5s (8.33% of 6s) */
  /* Transition to top face */
  16.67% {
    transform: rotateX(90deg);
  }
  /* Move to top */
  /* Top face */
  25% {
    transform: rotateX(90deg);
  }
  /* Pause on top for 0.5s */
  /* Transition to back face */
  33.33% {
    transform: rotateX(180deg);
  }
  /* Move to back */
  /* Back face */
  41.67% {
    transform: rotateX(180deg);
  }
  /* Pause on back for 0.5s */
  /* Transition to bottom face */
  50% {
    transform: rotateX(270deg);
  }
  /* Move to bottom */
  /* Bottom face */
  58.33% {
    transform: rotateX(270deg);
  }
  /* Pause on bottom for 0.5s */
  /* Transition back to front face */
  66.67% {
    transform: rotateX(360deg);
  }
  /* Move back to front */
  /* Front face */
  75% {
    transform: rotateX(360deg);
  }
  /* Pause on front for 0.5s */
  /* End of animation */
  100% {
    transform: rotateX(360deg);
  }
  /* Ensure smooth loop */
}
.btn, .newsletter-table .MuiDataGrid-toolbarContainer button {
  font-family: "Proxima-Nova-Medium" !important;
  font-size: 16px;
  text-align: center;
  color: #234791;
  padding: 15px 35px;
  border-color: transparent;
  text-decoration: none;
  background-color: transparent;
  border: 1px solid transparent;
  height: -moz-fit-content;
  height: fit-content;
  display: flex;
  align-items: center;
}
.btn.dash-search, .newsletter-table .MuiDataGrid-toolbarContainer button.dash-search {
  padding: 8px 10px !important;
}
.btn.dash-search.btn.right-icon svg, .newsletter-table .MuiDataGrid-toolbarContainer button.dash-search.right-icon svg {
  margin-left: 0px !important;
}
.btn.p-0, .newsletter-table .MuiDataGrid-toolbarContainer button.p-0 {
  padding: 0;
  text-align: inherit;
}
.btn.p-save, .newsletter-table .MuiDataGrid-toolbarContainer button.p-save {
  padding: 10px auto;
  text-align: inherit;
}
.btn.full-width, .newsletter-table .MuiDataGrid-toolbarContainer button.full-width {
  width: -webkit-fill-available;
  display: flex;
  justify-content: center;
}
.btn.yellow, .newsletter-table .MuiDataGrid-toolbarContainer button.yellow {
  color: #ffca00;
}
.btn.yellow svg path, .newsletter-table .MuiDataGrid-toolbarContainer button.yellow svg path {
  fill: #ffca00;
}
.btn.left-icon svg, .newsletter-table .MuiDataGrid-toolbarContainer button.left-icon svg {
  margin-right: 10px !important;
}
.btn.right-icon svg, .newsletter-table .MuiDataGrid-toolbarContainer button.right-icon svg {
  margin-left: 10px !important;
}
.btn:hover, .newsletter-table .MuiDataGrid-toolbarContainer button:hover {
  color: #ffca00;
}
.btn:hover svg path, .newsletter-table .MuiDataGrid-toolbarContainer button:hover svg path {
  fill: #ffca00;
}
.btn-filled {
  background-color: #ffca00;
  border: 1px solid #ffca00;
}
.btn-filled:hover {
  background-color: #234791;
}
.btn-filled.blue {
  background-color: #234791;
  color: #ffca00;
  border: 1px solid #ffffff;
}
.btn-filled.blue:hover {
  background-color: #ffca00;
  color: #234791;
  border-color: #234791;
}
.btn-outlined {
  background-color: transparent;
  border: 1px solid #234791;
}
.btn-outlined:hover {
  background-color: #ffca00;
  color: #234791;
}
.btn-outlined:hover svg path {
  fill: #234791;
}
.btn-outlined.white {
  color: #ffffff;
  border: 1px solid #ffffff;
}
.btn-outlined.white svg path {
  fill: #ffffff;
}
.btn-outlined.no-hover:hover {
  background-color: transparent;
  border: 1px solid #234791;
}
.btn-outlined.no-hover:hover svg path {
  fill: #234791;
}
.btn-ghost {
  background-color: transparent;
  border: none;
  padding: 15px 35px 15px 0;
  text-align: left !important;
}
.btn-ghost.white {
  justify-content: flex-start;
  color: #ffffff;
}
.btn-ghost.white svg path {
  fill: #ffffff;
}
.btn-small {
  background-color: transparent;
  border: none;
  font-family: "Proxima-Nova-Medium" !important;
}
.btn.text-yellow, .newsletter-table .MuiDataGrid-toolbarContainer button.text-yellow {
  color: #ffca00;
}
.btn.text-yellow svg path, .newsletter-table .MuiDataGrid-toolbarContainer button.text-yellow svg path {
  fill: #ffca00;
}
.btn.text-white, .newsletter-table .MuiDataGrid-toolbarContainer button.text-white {
  color: #ffffff;
  border-color: #ffffff;
}
.btn.text-white.no-hover:hover, .newsletter-table .MuiDataGrid-toolbarContainer button.text-white.no-hover:hover {
  border: 1px solid #ffffff;
}
.btn.text-white.no-hover:hover svg path, .newsletter-table .MuiDataGrid-toolbarContainer button.text-white.no-hover:hover svg path {
  fill: #ffffff;
}
.btn.text-white svg path, .newsletter-table .MuiDataGrid-toolbarContainer button.text-white svg path {
  fill: #ffffff;
}
.btn.text-center, .newsletter-table .MuiDataGrid-toolbarContainer button.text-center {
  text-align: center !important;
  justify-content: center;
}
.btn.text-blue, .newsletter-table .MuiDataGrid-toolbarContainer button.text-blue {
  color: #234791 !important;
}
.btn.text-blue svg path, .newsletter-table .MuiDataGrid-toolbarContainer button.text-blue svg path {
  fill: #234791 !important;
}
.btn.stroke-svg svg path, .newsletter-table .MuiDataGrid-toolbarContainer button.stroke-svg svg path {
  stroke: #234791 !important;
}
.btn-filter {
  color: #234791;
  border: 1px solid #234791;
}
.btn-filter:hover, .btn-filter.selected {
  color: #ffffff !important;
  background-color: #234791 !important;
}
.btn-zone {
  color: #ffca00;
  border: 1px solid #ffca00;
  justify-content: center;
}
.btn-country {
  justify-content: center;
  color: #ffffff;
  background-color: rgba(29, 90, 159, 0.2);
  padding: 10px 24px;
  border: 1px solid rgba(29, 90, 159, 0.2);
  text-align: center;
  font-family: "Proxima-Nova-Semibold" !important;
  margin: auto;
}
.btn-country.selected, .btn-country:hover {
  color: #ffffff;
  border: 1px solid #ffffff;
}
.btn.edit-blog, .newsletter-table .MuiDataGrid-toolbarContainer button.edit-blog {
  padding: 0px 5px !important;
  cursor: pointer;
}

.btn-social-media {
  font-size: 18px;
  text-align: center;
  text-transform: capitalize;
  padding: 10px 24px;
  font-family: "Proxima-Nova-Medium" !important;
  font-weight: 500;
  border-color: #ffffff;
  color: #ffffff;
  text-decoration: none;
  background-color: transparent;
  height: -moz-fit-content;
  height: fit-content;
  display: flex;
  align-items: center;
  width: 80%;
  margin: 12px 5px;
  justify-content: center;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .btn-social-media {
    width: 100%;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .btn-social-media {
    justify-content: center;
    margin-top: 8px;
  }
}
.btn-social-media svg {
  margin-right: 10px;
}
.btn-social-media:hover {
  color: #ffca00;
  border-color: #ffca00;
}

.btn-switch-favoris {
  padding: 10px 20px;
  margin-right: 10px;
  cursor: pointer;
  border: 1px solid #234791;
  display: flex;
  justify-content: space-around;
}

.favoris-button {
  background-color: white;
  border: none;
  height: 50px;
}

.btn-favoris {
  font-size: 30px;
  color: #234791;
  font-family: "Proxima-Nova-Semibold" !important;
  display: flex;
  align-items: center;
  gap: 0px;
  justify-content: flex-start;
}

.btn1 {
  background-color: #ffca00;
  padding: 5px 25px;
  color: #234791;
  text-transform: capitalize;
  font-family: "Proxima-Nova-Medium" !important;
}

.btn-switch-favoris {
  padding: 10px 20px;
  margin-right: 10px;
  cursor: pointer;
  border: 1px solid #234791 !important;
  background-color: transparent;
  display: flex;
  align-content: flex-start;
  align-items: center;
}

.button-grid {
  margin-left: 0px !important;
}

.toggle-button1 {
  height: 39px;
  width: 180px;
  border: 1px solid #453bd8;
  background-color: #ffffff;
  color: #245097;
  font-family: "Pentabell-Bold";
  font-size: 20px;
  cursor: pointer;
  padding: 10px 30px;
}

.toggle-button1.active {
  height: 39px;
  width: 180px;
  border: none;
  background-color: #245097;
  color: white;
  cursor: pointer;
  font-family: "Pentabell-Bold";
  font-size: 20px;
  padding: 10px 30px;
}

@media (max-width: 768px) {
  .button-grid {
    width: 100% !important;
    margin-left: 0px !important;
  }
  .button-settings {
    display: flex;
    flex-direction: column;
    align-items: center !important;
    border-radius: inherit;
    margin-right: 100px;
  }
  .btn-dashbord {
    font-family: "Proxima-Nova-Medium" !important;
    font-size: 11px !important;
    padding: 8px 35px !important;
    text-align: center;
    text-transform: capitalize;
    color: #234791;
    width: 99% !important;
    margin-right: 10px !important;
  }
  .btn-filled:hover {
    background-color: #234791;
  }
  .btn-dashbord:hover {
    color: #ffca00;
  }
  .btn_dashboard {
    width: 100% !important;
  }
  * .button-group {
    display: flex;
    justify-content: center;
    gap: 0;
  }
  .button-group .btn-details,
  .button-group .btn-delete {
    margin: 0;
  }
  .btn-switch-favoris {
    background-color: #ffffff;
    border: 1px solid #234791;
    padding: 5px 35px;
    color: #234791;
    text-align: center;
    width: 99% !important;
    text-transform: capitalize;
    font-family: "Proxima-Nova-Medium" !important;
    display: flex;
    align-items: center;
  }
}
.buttonD {
  padding: 7px 25px;
  margin-right: 20px;
}

.fix-Button {
  display: flex;
  align-items: center !important;
}

.espace {
  margin-left: 20px;
  border-radius: "20px" !important;
  border: "1px solid ";
  width: 25px;
  height: 20px;
}

.delete-button,
.job-details-button {
  background-color: transparent;
  border: none;
  border-radius: 5px;
  padding: 0px 5px;
  cursor: pointer;
}

.btn-round {
  width: 30px;
  height: 30px;
  font-size: 16px;
  border-radius: 20px;
}

.btn-social-media {
  font-size: 18px;
  text-align: center;
  text-transform: capitalize;
  padding: 10px 24px;
  font-family: "Proxima-Nova-Medium" !important;
  font-weight: 500;
  border-color: #ffffff;
  color: #ffffff;
  text-decoration: none;
  background-color: transparent;
  height: -moz-fit-content;
  height: fit-content;
  display: flex;
  align-items: center;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .btn-social-media {
    justify-content: center;
    margin-top: 8px;
  }
}
.btn-social-media svg {
  margin-right: 10px;
}
.btn-social-media:hover {
  color: #ffca00;
  border-color: #ffca00;
}

.btn-logout {
  margin: auto 16px 0;
}

.button-settings {
  display: flex;
  flex-direction: column;
  align-items: center !important;
  border-radius: inherit;
}

.btn-add {
  background-color: #ffffff !important;
  border: none;
}

.button-group14 {
  margin-top: auto 0 auto auto;
  display: flex;
  justify-content: flex-end;
}
.button-group14 .btn-outlined {
  margin-right: 20px;
}

.button-settings-profile {
  font-family: "Proxima-Nova-Medium" !important;
  font-size: 16px;
  padding: 15px 35px;
  text-align: center;
  color: #234791;
  border-color: #234791;
  text-decoration: none;
  background-color: transparent;
  border: 2px solid #234791;
  height: -moz-fit-content;
  height: fit-content;
  display: flex;
  align-items: center;
  margin-right: 20px;
}

.btn-settings-jaune {
  font-family: "Proxima-Nova-Medium" !important;
  font-size: 16px;
  padding: 10px 35px;
  text-align: center;
  color: #234791;
  background-color: #ffca00;
  border: 1px solid #ffca00;
  text-decoration: none;
  height: -moz-fit-content;
  height: fit-content;
  display: flex;
  align-items: center;
}

@media (max-width: 768px) {
  .button-settings-profile {
    display: flex;
    flex-direction: column;
    align-items: center !important;
    border-radius: inherit;
    /* margin-right: 100px; */
  }
}
.button-sliders {
  display: flex;
  flex-direction: rows;
}
.button-sliders .btn-add-sliders {
  margin-right: 20px !important;
}

.btn-homepage {
  padding: 7px 15px !important;
  margin-bottom: 10px;
}

.btn-filter-stat {
  height: 40px;
  padding: 5px 15px;
}

.css-1x6bjyf-MuiAutocomplete-root .MuiAutocomplete-tag {
  background-color: white !important;
}

#profile-percentage {
  background-color: #fff;
  width: 100% !important;
  height: 100% !important;
  border-radius: 10px;
}

.css-9tivnm-MuiGauge-container > svg {
  color: #234791 !important;
}

.stat {
  display: flex;
  justify-content: center;
}

.css-1ov6x74-MuiGrid-root > .MuiGrid-item {
  padding-right: 52px !important;
}

.multiple-select .css-1x6bjyf-MuiAutocomplete-root .MuiAutocomplete-tag {
  background-color: white !important;
}
.multiple-select.light .css-15cv2x7-MuiInputBase-root-MuiInput-root:focus, .multiple-select.light .css-15cv2x7-MuiInputBase-root-MuiInput-root::after, .multiple-select.light .css-15cv2x7-MuiInputBase-root-MuiInput-root:hover, .multiple-select.light .css-15cv2x7-MuiInputBase-root-MuiInput-root::before {
  border-bottom-color: #ffffff !important;
}

.data-picker-width .MuiFormControl-root {
  min-width: 50px;
}

.input-date {
  width: 100%;
  overflow: hidden !important;
}
.input-date .MuiInputBase-formControl {
  background-color: transparent;
}
.input-date .MuiOutlinedInput-notchedOutline {
  border-width: 0 0 1px 0 !important;
  border-color: rgba(0, 0, 0, 0.4196078431);
}
.input-date .css-jupps9-MuiInputBase-root-MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline,
.input-date .css-jupps9-MuiInputBase-root-MuiOutlinedInput-root:hover .MuiOutlinedInput-notchedOutline {
  border-width: 0 0 2px 0 !important;
}
.input-date input {
  height: 26px;
  padding: 5px;
  border-width: 0 0 1px 0 !important;
  width: 100%;
  background-color: transparent !important;
  color: #798ba3 !important;
}

.maps path {
  fill: rgb(121, 139, 163);
}

.input-pentabell {
  width: 100%;
  height: 100%;
}
.input-pentabell .MuiInputBase-root {
  background-color: transparent;
  height: 100%;
}
.input-pentabell .css-5lvf42-MuiSelect-select-MuiInputBase-input-MuiInput-input {
  padding: 0px 5px;
}
.input-pentabell.react-international-phone-input-container .react-international-phone-country-selector-button {
  background-color: transparent;
  border: 0px;
  border-radius: 0;
  border-bottom: 1px solid #798ba3;
}
.input-pentabell.react-international-phone-input-container .react-international-phone-input {
  border: 0px;
  border-radius: 0;
  color: #798ba3;
  background-color: transparent;
  border-bottom: 1px solid #798ba3;
}
.input-pentabell input {
  height: 26px;
  padding-left: 10px;
  width: 100%;
  color: #798ba3;
}
.input-pentabell.light.react-international-phone-input-container .react-international-phone-country-selector-button {
  background-color: transparent !important;
  border: 0px;
  border-bottom: 1px solid #0d2849 !important;
  border-radius: 0;
}
.input-pentabell.light.react-international-phone-input-container .react-international-phone-input {
  border-radius: 0;
  background-color: transparent !important;
  border: 0px;
  border-bottom: 1px solid #0d2849 !important;
}
.input-pentabell.light::placeholder {
  color: #ffffff;
}
.input-pentabell.light .MuiInputBase-root {
  background-color: transparent;
}
.input-pentabell.light input {
  color: #ffffff;
  background-color: transparent;
}
.input-pentabell.light input:-webkit-autofill,
.input-pentabell.light input:-webkit-autofill:hover,
.input-pentabell.light input:-webkit-autofill:focus,
.input-pentabell.light input:-webkit-autofill:active {
  -webkit-transition: background-color 9999s ease-in-out 0s !important;
  transition: background-color 9999s ease-in-out 0s !important;
  -webkit-text-fill-color: #ffffff !important;
  background-color: transparent !important;
  box-shadow: 0 0 0px 1000px transparent inset !important;
  font-family: "Pentabell-SemiBold";
}

.select-pentabell {
  width: 100%;
  height: 100%;
}
.select-pentabell .MuiInputBase-root,
.select-pentabell .css-5lvf42-MuiSelect-select-MuiInputBase-input-MuiInput-input.css-5lvf42-MuiSelect-select-MuiInputBase-input-MuiInput-input.css-5lvf42-MuiSelect-select-MuiInputBase-input-MuiInput-input {
  height: 39px !important;
  height: 100%;
}
.select-pentabell .css-5lvf42-MuiSelect-select-MuiInputBase-input-MuiInput-input {
  padding: 0px 5px;
}
.select-pentabell input {
  height: 26px;
  padding-left: 10px;
  width: 100%;
  color: #798ba3;
}
.select-pentabell.light.react-international-phone-input-container .react-international-phone-country-selector-button {
  background-color: transparent;
  border: 0px;
  border-bottom: 1px solid #0d2849;
}
.select-pentabell.light input:-webkit-autofill,
.select-pentabell.light input:-webkit-autofill:hover,
.select-pentabell.light input:-webkit-autofill:focus,
.select-pentabell.light input:-webkit-autofill:active {
  -webkit-transition: background-color 9999s ease-in-out 0s !important;
  transition: background-color 9999s ease-in-out 0s !important;
  -webkit-text-fill-color: #ffffff !important;
  background-color: transparent !important;
  box-shadow: 0 0 0px 1000px transparent inset !important;
  font-family: "Pentabell-SemiBold";
}
.select-pentabell.light.react-international-phone-input-container .react-international-phone-input {
  border: 0px;
  border-radius: 0;
  border-bottom: 1px solid #0d2849;
}
.select-pentabell.light::placeholder {
  color: #ffffff;
}
.select-pentabell.light .MuiInputBase-root {
  background-color: transparent;
}
.select-pentabell.light input {
  color: #ffffff;
  background-color: transparent;
}

.textArea-pentabell {
  width: 100%;
  height: 100%;
}
.textArea-pentabell .MuiInputBase-root {
  height: 100%;
  padding: 0px 8px;
}
.textArea-pentabell input {
  height: 26px;
  padding-left: 10px;
  width: 100%;
  color: #798ba3;
}
.textArea-pentabell.light.react-international-phone-input-container .react-international-phone-country-selector-button {
  background-color: transparent;
  border: 0px;
  border-bottom: 1px solid #0d2849;
}
.textArea-pentabell.light input:-webkit-autofill,
.textArea-pentabell.light input:-webkit-autofill:hover,
.textArea-pentabell.light input:-webkit-autofill:focus,
.textArea-pentabell.light input:-webkit-autofill:active {
  -webkit-transition: background-color 9999s ease-in-out 0s !important;
  transition: background-color 9999s ease-in-out 0s !important;
  -webkit-text-fill-color: #234791 !important;
  background-color: transparent !important;
  box-shadow: 0 0 0px 1000px transparent inset !important;
  font-family: "Pentabell-SemiBold";
}
.textArea-pentabell.light.react-international-phone-input-container .react-international-phone-input {
  border: 0px;
  border-bottom: 1px solid #0d2849;
}
.textArea-pentabell.light::placeholder {
  color: #ffffff;
}
.textArea-pentabell.light .MuiInputBase-root {
  background-color: transparent;
}
.textArea-pentabell.light input {
  color: #ffffff;
  background-color: transparent;
}

#filter {
  padding: 10px 0;
  align-items: flex-end;
}
#filter .css-sc8y68-MuiInputBase-root-MuiOutlinedInput-root-MuiSelect-root {
  max-height: 40px !important;
}
#filter .css-10o2lyd-MuiStack-root {
  padding-top: 0px !important;
}

.css-r5in8e-MuiInputBase-root-MuiInput-root::after, .css-r5in8e-MuiInputBase-root-MuiInput-root:focus, .css-r5in8e-MuiInputBase-root-MuiInput-root:hover,
.css-10yf2i8-MuiInputBase-root-MuiInput-root::after,
.css-10yf2i8-MuiInputBase-root-MuiInput-root:focus,
.css-10yf2i8-MuiInputBase-root-MuiInput-root:hover {
  border-bottom-color: #234791;
}

.css-r5in8e-MuiInputBase-root-MuiInput-root:hover:not(.Mui-disabled,
.Mui-error):before,
.css-10yf2i8-MuiInputBase-root-MuiInput-root:hover:not(.Mui-disabled,
.Mui-error):before {
  border-bottom-color: #234791;
}

.is-invalid {
  background-color: ivory;
  border-bottom: 1px solid red;
  border-radius: 5px;
}

.saved {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-content: space-between;
  justify-content: space-evenly;
  align-items: flex-start;
}

.label-pentabell {
  font-size: 20px !important;
  font-family: "Proxima-Nova-Semibold" !important;
  text-transform: capitalize !important;
  color: #234791 !important;
}
.label-pentabell.light {
  color: #ffffff !important;
}

.label-form {
  font-size: 15px;
  font-family: "Proxima-Nova-Semibold" !important;
  text-transform: capitalize;
  color: #234791;
}
.label-form.light {
  color: #ffffff;
}

.css-1vam7s3-MuiGrid-root {
  display: flex;
  justify-content: space-evenly;
  align-items: flex-start;
  align-content: stretch;
  flex-wrap: wrap;
  flex-direction: row;
}

.label-error {
  font-size: 15px;
  text-transform: capitalize;
  color: red;
}
.label-error.light {
  color: #ffffff;
}

.pentabell-form {
  display: flex;
  flex-direction: column;
}
.pentabell-form .form-content {
  margin-bottom: 20px;
}
.pentabell-form .btn-reset-pwd {
  color: #798ba3;
  display: flex;
  justify-content: flex-end;
  padding: 0;
}
.pentabell-form .form-group {
  margin: 15px 0;
}
.pentabell-form .light svg {
  color: #ffca00;
}
.pentabell-form .light .css-r5in8e-MuiInputBase-root-MuiInput-root::before,
.pentabell-form .light .css-10yf2i8-MuiInputBase-root-MuiInput-root::before {
  border-bottom-color: #ffffff !important;
}
.pentabell-form .light .css-r5in8e-MuiInputBase-root-MuiInput-root::after, .pentabell-form .light .css-r5in8e-MuiInputBase-root-MuiInput-root:focus, .pentabell-form .light .css-r5in8e-MuiInputBase-root-MuiInput-root:hover, .pentabell-form .light .css-r5in8e-MuiInputBase-root-MuiInput-root::before,
.pentabell-form .light .css-10yf2i8-MuiInputBase-root-MuiInput-root::after,
.pentabell-form .light .css-10yf2i8-MuiInputBase-root-MuiInput-root:focus,
.pentabell-form .light .css-10yf2i8-MuiInputBase-root-MuiInput-root:hover,
.pentabell-form .light .css-10yf2i8-MuiInputBase-root-MuiInput-root::before {
  border-bottom-color: #ffffff !important;
}
.pentabell-form .light:-webkit-autofill, .pentabell-form .light:-webkit-autofill:hover, .pentabell-form .light:-webkit-autofill:focus, .pentabell-form .light:-webkit-autofill:active {
  -webkit-transition: background-color 9999s ease-in-out 0s !important;
  transition: background-color 9999s ease-in-out 0s !important;
  -webkit-text-fill-color: #ffffff !important;
  background-color: transparent !important;
  font-family: "Pentabell-SemiBold";
}
.pentabell-form .light .css-r5in8e-MuiInputBase-root-MuiInput-root:hover:not(.Mui-disabled,
.Mui-error):before,
.pentabell-form .light .css-10yf2i8-MuiInputBase-root-MuiInput-root:hover:not(.Mui-disabled,
.Mui-error):before {
  border-bottom-color: #ffffff;
}
.pentabell-form .css-fzx5yo-MuiPaper-root-MuiAlert-root {
  transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 4px;
  box-shadow: var(--Paper-shadow);
  background-image: var(--Paper-overlay);
  font-family: "Roboto", "Helvetica", "Arial", sans-serif;
  font-weight: 500 !important;
  font-size: 0.875rem;
  line-height: 1.43;
  letter-spacing: 0.01071em;
  display: flex;
  padding: 0px 10px;
  background-color: #e5f0fc;
  color: #cc3233;
  width: -webkit-fill-available;
}
.pentabell-form .checkbox-pentabell .MuiTypography-root {
  font-family: "Proxima-Nova-Thin" !important;
  font-size: 14px;
}
.pentabell-form.light .checkbox-pentabell .MuiTypography-root {
  color: #fff;
}
.pentabell-form .btn-submit.full-width {
  margin: 20px 0;
  width: 100%;
  font-family: "Proxima-Nova-Semibold" !important;
  justify-content: center;
  font-size: 16px;
  display: flex;
}
.pentabell-form .btn-submit span {
  width: 30px;
  height: 30px;
  color: #1e4384;
}

input:-internal-autofill-selected {
  background-color: transparent;
}

.error-span {
  margin: 10px 0;
  color: #ff3434;
  display: block;
}

.checkbox-pentabell {
  align-items: center !important;
}
.checkbox-pentabell .MuiCheckbox-root {
  background-color: transparent;
  color: #234791;
}
.checkbox-pentabell .MuiCheckbox-root.Mui-checked {
  color: #ffca00;
}
.checkbox-pentabell.light .MuiCheckbox-root {
  background-color: transparent;
  color: #ffffff;
}
.checkbox-pentabell.light .MuiTypography-root {
  color: #ffffff;
}
.checkbox-pentabell .MuiTypography-root {
  font-family: "Proxima-Nova-Thin" !important;
  font-size: 14px;
  color: #234791;
}

.MuiInputAdornment-root {
  margin: auto 5px;
}

.checkbox-pentabell-filter.blue .MuiCheckbox-root {
  color: #234791;
}
.checkbox-pentabell-filter.blue .MuiCheckbox-root.Mui-checked {
  color: #ecf2fa;
}
.checkbox-pentabell-filter.blue .MuiCheckbox-root.Mui-checked::before {
  content: "";
  width: 16px;
  height: 17px;
  position: absolute;
  border: 2px solid #234791;
  border-radius: 2px;
  border-top-left-radius: 2px;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 2px;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background-color: #234791;
  z-index: -1;
}

.search-input-table-Dashbord {
  display: flex;
  align-items: center;
  color: #234791;
  border: 1.9px solid #234791;
  border-radius: 5px !important;
  height: 35px;
  font-size: 15px;
  justify-content: space-between;
  width: 100%;
}

.css-1edfpdg-MuiTypography-root {
  margin: 0;
  font-weight: 400;
  line-height: 1.5;
  letter-spacing: 0.00938em;
  align-self: center;
}

@media (max-width: 768px) {
  .search-input-table-Dashbord {
    color: #234791;
    border: 1px solid #234791;
    border-radius: 5px !important;
    height: 35px;
    margin-top: 5px;
    font-family: "Proxima-Nova-Semibold" !important;
    font-size: 15px;
    align-items: center;
    margin-right: 100px;
    justify-content: center;
    margin-bottom: 10px;
  }
  .password-section {
    display: flex;
    align-items: baseline;
    justify-content: flex-start;
    flex-wrap: wrap;
    align-content: stretch;
    flex-direction: column;
  }
  .password-section .scale-password {
    margin-top: 10px !important;
  }
}
.eye-icon {
  position: absolute;
  right: 10px;
  cursor: pointer;
  margin-top: 5px;
}

.settings {
  padding: 10px;
  display: flex;
  flex-direction: column;
}

.successMsgBanner {
  margin-top: 25px;
  font-size: 16px;
  line-height: 1.43;
  letter-spacing: 0.01071em;
  display: flex;
  font-weight: 500;
  background-color: #009966;
  color: #fff;
  padding: 15px 20px;
}

.errorMsgBanner {
  margin-top: 25px;
  font-size: 16px;
  line-height: 1.43;
  letter-spacing: 0.01071em;
  display: flex;
  font-weight: 500;
  background-color: #cc3233;
  color: #fff;
  padding: 15px 20px;
}

#settings {
  margin-bottom: 20px;
}
#settings .container-settings {
  width: 100%;
  margin-left: auto;
  box-sizing: border-box;
  margin-right: auto;
  padding-left: 16px;
  padding-right: 16px;
}
#settings .input-pentabell input {
  background-color: transparent !important;
}
#settings .input-pentabell .MuiInputBase-root {
  background-color: transparent !important;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #settings .btn, #settings .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer #settings button {
    padding: 10px 26px !important;
  }
}

.notification-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 0px;
}

.notification-text {
  font-weight: bold;
  margin-bottom: 10px;
}

.checkbox-group {
  display: flex;
  justify-content: flex-start;
  gap: 10px;
}

.checkbox-column {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.checkbox-column span {
  font-weight: bold;
  margin-bottom: 5px;
}

.settings-input {
  background-color: transparent !important;
}

.password-section {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
}

.label-form {
  font-size: 15px;
  font-family: "Proxima-Nova-Semibold" !important;
  text-transform: capitalize;
}

.label-form {
  font-size: 15px;
  font-family: "Proxima-Nova-Semibold" !important;
  text-transform: capitalize;
  color: #798ba3;
}

#Notifications-dashboard .css-imrjgg-MuiButtonBase-root-MuiCheckbox-root.Mui-checked,
#Notifications-dashboard .css-imrjgg-MuiButtonBase-root-MuiCheckbox-root.MuiCheckbox-indeterminate {
  color: #234791;
}
#Notifications-dashboard .css-imrjgg-MuiButtonBase-root-MuiCheckbox-root {
  color: #234791;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #Notifications-dashboard .btn, #Notifications-dashboard .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer #Notifications-dashboard button {
    padding: 10px 26px !important;
  }
}

.css-1usuzwp-MuiButtonBase-root-MuiTab-root.Mui-selected {
  color: #234791 !important;
}

.blue-text {
  color: #234791 !important;
}

.numbers-pentabell {
  font-size: xxx-large !important;
  font-weight: bold;
}

.hover-rect:hover {
  fill: #ffca00;
}

.input-guide {
  background-color: white;
  border: none;
}

.heading-h1 {
  font-family: "Proxima-Nova-Semibold" !important;
  font-weight: 500;
  font-size: 50px;
  color: #234791;
  margin-top: 20px !important;
  margin-bottom: 20px !important;
}
.heading-h1.bold {
  font-family: "Proxima-Nova-Bold" !important;
  font-weight: 600 !important;
}
.heading-h1.text-white {
  color: #ffffff;
}
.heading-h1.text-banking {
  color: #0b3051;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .heading-h1 {
    font-size: 32px;
    margin-bottom: 0 !important;
  }
}
.heading-h1.mb-0 {
  margin-bottom: 0 !important;
}
.heading-h1.mt-0 {
  margin-top: 0 !important;
}

.heading-h2 {
  font-weight: 400;
  color: #234791;
  margin-top: 20px !important;
  margin-bottom: 20px !important;
  font-size: 32px;
}
.heading-h2.semi-bold {
  font-family: "Proxima-Nova-Medium" !important;
  font-weight: 500 !important;
}
.heading-h2.bold {
  font-family: "Proxima-Nova-SemiBold" !important;
  font-weight: 600 !important;
}
.heading-h2.text-white {
  color: #ffffff;
}
.heading-h2.text-banking {
  color: #0b3051;
}
.heading-h2.mt-0 {
  margin-top: 0 !important;
}
.heading-h2.mb-0 {
  margin-bottom: 0 !important;
}

.heading-h3 {
  font-weight: 400;
  color: #234791;
  margin-top: 15px !important;
  margin-bottom: 15px !important;
  font-size: 24px;
}
.heading-h3.text-white {
  color: #ffffff;
}
.heading-h3.text-banking {
  color: #0b3051;
}
.heading-h3.mb-0 {
  margin-bottom: 0 !important;
}
.heading-h3.mt-0 {
  margin-top: 0 !important;
}
.heading-h3.semi-bold {
  font-family: "Proxima-Nova-Medium" !important;
}
.heading-h3.bold {
  font-family: "Proxima-Nova-SemiBold" !important;
}

.sub-heading {
  font-weight: 400;
  font-size: 24px;
  margin: 20px 0;
  color: #798ba3;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .sub-heading {
    font-size: 20px;
  }
}
.sub-heading a {
  color: #798ba3;
}
.sub-heading.text-slide {
  font-size: 24px;
}
.sub-heading.text-grey {
  color: #445469;
}
.sub-heading.text-grey a {
  color: #445469;
}
.sub-heading.semi-bold {
  font-family: "Proxima-Nova-Medium" !important;
  font-weight: 500 !important;
}
.sub-heading.text-white {
  color: #ffffff;
}
.sub-heading.text-white a {
  color: #ffffff;
}
.sub-heading.text-banking {
  color: #0b3051;
}
.sub-heading.text-banking a {
  color: #0b3051;
}
.sub-heading.text-blue {
  color: #234791;
}
.sub-heading.text-blue a {
  color: #234791;
}
.sub-heading.update-span {
  font-size: 18px;
}
.sub-heading.bold {
  font-family: "Proxima-Nova-Bold" !important;
  font-weight: 600;
}
.sub-heading.mb-0 {
  margin-bottom: 0 !important;
}
.sub-heading.mt-0 {
  margin-top: 0 !important;
}

.paragraph {
  font-weight: 400;
  font-size: 16px;
}
.paragraph.mb-0 {
  margin-bottom: 0 !important;
}
.paragraph.text-white {
  color: #ffffff;
}
.paragraph.text-blue {
  color: #234791;
}
.paragraph.text-yellow {
  color: #ffca00;
}
.paragraph.semi-bold {
  font-family: "Proxima-Nova-Medium" !important;
}
.paragraph.bold {
  font-family: "Proxima-Nova-Bold" !important;
}
.paragraph.link {
  font-family: "Proxima-Nova-Semibold" !important;
  font-weight: 600;
  font-size: 14px;
}
.paragraph.grey {
  color: #798ba3;
}

* .status {
  width: 20%;
  font-family: "Proxima-Nova-Semibold" !important;
  font-weight: 600;
  vertical-align: middle;
  text-align: center;
  color: #0b3051;
  font-size: 14px;
}

.job-opportunite {
  height: 24px;
  width: 24px;
  margin-right: 10px;
}

@media (max-width: 768px) {
  .status {
    width: auto;
  }
  .grid-title {
    font-size: 24px !important;
    color: #234791;
    font-family: "Proxima-Nova-Semibold" !important;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.imgtime {
  margin-right: 5px;
  margin-bottom: -2px;
}

.text-yellow {
  color: #ffca00;
}

.title-article {
  vertical-align: middle;
  text-align: center;
  font-family: "Proxima-Nova-Semibold" !important;
  font-weight: 600;
}

.input-text-settings {
  font-family: "Proxima-Nova-SemiBold" !important;
  color: #798ba3;
  font-size: 16px;
}

#Notifications-dashboard .input-text-settings {
  font-family: "Proxima-Nova-SemiBold" !important;
  color: #798ba3;
  font-size: 16px;
}

#Notifications-dashboard .input-text-settings-section {
  font-family: "Proxima-Nova-Regular" !important;
  color: #1d5a9f;
  font-size: 16px;
}

.last-word {
  background-color: #ffca00;
  color: #234791;
}

#home__slider {
  overflow: hidden;
}
#home__slider .embla__container {
  display: flex;
}
#home__slider .embla__slide__content {
  width: 80%;
  display: flex;
  flex-direction: column;
  align-content: center;
  align-items: flex-start;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #home__slider .embla__slide__content {
    width: 100%;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #home__slider .embla__slide__content {
    width: 100%;
  }
}
#home__slider .embla__slide {
  flex: 0 0 100%;
  min-width: 0;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  height: 88vh;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #home__slider .embla__slide {
    height: 85dvh;
  }
}
#home__slider .embla__slide img {
  position: absolute;
  width: 100%;
  height: 88dvh;
  object-fit: cover;
  z-index: -1;
  max-height: 800px;
}
@media (min-height: 800px) {
  #home__slider .embla__slide img {
    max-height: 1320px;
  }
}
@media only screen and (min-width: 1601px) {
  #home__slider .embla__slide img {
    max-height: 1450px;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #home__slider .embla__slide img {
    height: 84dvh;
    max-height: 1000px;
  }
}
#home__slider .embla__slide .slide__container {
  height: 100%;
  padding-bottom: 20px;
  margin-bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #home__slider .embla__slide .slide__container {
    padding-bottom: 20%;
  }
}
#home__slider .embla__slide .explore-btn {
  background-color: #234791;
  color: #ffffff;
  font-size: 16px;
  font-weight: 147;
  padding: 12px 12px;
  display: flex;
  align-items: center;
  gap: 12px;
  font-family: "Proxima-Nova-SemiBold" !important;
  text-transform: none;
  text-decoration: none;
}
#home__slider .embla__slide .explore-btn:hover {
  background-color: #ffca00;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #home__slider .embla__slide .explore-btn {
    font-size: 14px;
    margin-top: 10px !important;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #home__slider .embla__slide .explore-btn {
    font-size: 14px;
    margin-top: 10px !important;
  }
}
#home__slider .embla__slide .embla__slide__title {
  color: #ffffff;
  font-family: "Proxima-Nova-Bold" !important;
  font-size: 24px;
  margin-bottom: 5%;
  position: relative;
  padding-left: 15px;
  font-weight: 483px;
  margin-bottom: 30px;
  white-space: normal;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #home__slider .embla__slide .embla__slide__title {
    font-size: 14px;
    margin-bottom: 0px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #home__slider .embla__slide .embla__slide__title {
    font-size: 14px;
    margin-bottom: 0px;
  }
}
#home__slider .embla__slide .embla__slide__title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 7px;
  background-color: #ffca00;
}
#home__slider .embla__controls {
  bottom: 13%;
  z-index: 1;
  float: inline-end;
  right: 5%;
  position: absolute;
  width: auto;
  text-align: right;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #home__slider .embla__controls {
    bottom: 17dvh;
    right: auto;
    width: -webkit-fill-available;
    text-align: center;
  }
}
#home__slider .embla__controls .embla__button {
  background: rgba(35, 71, 145, 0.1490196078);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border-radius: 100%;
  width: 82px;
  height: 82px;
  margin: auto 8px;
  border: 1px solid rgba(228, 239, 252, 0.7);
}
#home__slider .embla__controls .embla__button:hover {
  box-shadow: 0px 0px 10px 0px rgba(228, 239, 252, 0.7);
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #home__slider .embla__controls .embla__button {
    width: 42px;
    height: 42px;
    padding: 0;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #home__slider .embla__controls .embla__button {
    width: 42px;
    height: 42px;
    padding: 0;
  }
}

#discover-ai-sourcing #aisourcing__slider {
  overflow: hidden;
}
#discover-ai-sourcing #aisourcing__slider .embla__container {
  display: flex;
}
#discover-ai-sourcing #aisourcing__slider .embla__slide {
  flex: 0 0 30%;
  min-width: 0;
  height: 430px;
  margin: 20px 23px 20px 0;
  padding: 20px;
  border: 2px solid transparent;
  border-image: linear-gradient(to top, #234791, #699bd4);
  border-image-slice: 1;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #discover-ai-sourcing #aisourcing__slider .embla__slide {
    height: 370px;
    margin: 12px 12px 12px 0;
    padding: 15px;
    flex: 0 0 64%;
  }
}
#discover-ai-sourcing #aisourcing__slider .embla__slide.oil-gaz {
  border-image: linear-gradient(to top, #d69b19, #699bd4);
  border-image-slice: 1;
}
#discover-ai-sourcing #aisourcing__slider .embla__slide.energy {
  border-image: linear-gradient(to top, #cc3233, #699bd4);
  border-image-slice: 1;
}
#discover-ai-sourcing #aisourcing__slider .embla__slide.banking-insurance {
  border-image: linear-gradient(to top, #0b3051, #699bd4);
  border-image-slice: 1;
}
#discover-ai-sourcing #aisourcing__slider .embla__slide.transport {
  border-image: linear-gradient(to top, #009966, #699bd4);
  border-image-slice: 1;
}
#discover-ai-sourcing #aisourcing__slider .embla__slide.it-telecom {
  border-image: linear-gradient(to top, #743794, #699bd4);
  border-image-slice: 1;
}
#discover-ai-sourcing #aisourcing__slider .embla__slide.other {
  border-image: linear-gradient(to top, #234791, #699bd4);
  border-image-slice: 1;
}
#discover-ai-sourcing #aisourcing__slider .embla__slide .slide__container {
  height: 100%;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  padding-left: 20px;
  padding-right: 20px;
}
#discover-ai-sourcing #aisourcing__slider .embla__slide .slide__container .embla__slide__content {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: flex-end;
  align-items: flex-start;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #discover-ai-sourcing #aisourcing__slider .embla__slide .slide__container .embla__slide__content {
    margin-top: "30% !important";
    display: flex;
    justify-content: space-between;
  }
}
#discover-ai-sourcing #aisourcing__slider .embla__slide .slide__container .embla__slide__content .embla__slide__industry {
  font-size: 14px;
  width: -moz-fit-content;
  width: fit-content;
  font-family: "Proxima-Nova-Semibold" !important;
  text-transform: capitalize;
  padding: 6px 18px;
  margin: 10px 0;
  border-radius: 20px;
  background-color: #ffca00;
  color: #234791;
  display: flex;
  align-items: center;
  margin-top: 20px;
}
#discover-ai-sourcing #aisourcing__slider .embla__slide .slide__container .embla__slide__content .embla__slide__industry svg {
  margin-right: 6px;
}
#discover-ai-sourcing #aisourcing__slider .embla__slide .slide__container .embla__slide__content .embla__slide__industry.oil-gaz {
  background-color: #d69b19;
  color: #ffffff;
}
#discover-ai-sourcing #aisourcing__slider .embla__slide .slide__container .embla__slide__content .embla__slide__industry.energy {
  background-color: #cc3233;
  color: #ffffff;
}
#discover-ai-sourcing #aisourcing__slider .embla__slide .slide__container .embla__slide__content .embla__slide__industry.banking-insurance {
  background-color: #0b3051;
  color: #ffffff;
}
#discover-ai-sourcing #aisourcing__slider .embla__slide .slide__container .embla__slide__content .embla__slide__industry.transport {
  background-color: #009966;
  color: #ffffff;
}
#discover-ai-sourcing #aisourcing__slider .embla__slide .slide__container .embla__slide__content .embla__slide__industry.it-telecom {
  background-color: #743794;
  color: #ffffff;
}
#discover-ai-sourcing #aisourcing__slider .embla__slide .slide__container .embla__slide__content .embla__slide__industry.other {
  background-color: #234791;
  color: #ffffff;
}
#discover-ai-sourcing #aisourcing__slider .embla__slide .slide__container .embla__slide__content .embla__slide__industry.pharmaceutical {
  background-color: #2bbfad;
  color: #ffffff;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #discover-ai-sourcing #aisourcing__slider .embla__slide .slide__container .embla__slide__content .embla__slide__industry {
    padding: 4px 12px;
    font-size: 12px;
    margin-top: 20px;
  }
}
#discover-ai-sourcing #aisourcing__slider .embla__slide .slide__container .embla__slide__content .embla__slide__description,
#discover-ai-sourcing #aisourcing__slider .embla__slide .slide__container .embla__slide__content .embla__slide__jobTitle {
  color: #ffffff;
  font-size: 14px;
  font-family: "Proxima-Nova-Semibold" !important;
  text-transform: capitalize;
  margin-bottom: 15px;
}
#discover-ai-sourcing #aisourcing__slider .embla__slide .slide__container .embla__slide__content .embla__slide__name {
  color: #ffffff;
  font-size: 24px;
  font-family: "Proxima-Nova-Semibold" !important;
  text-transform: capitalize;
  margin-bottom: 5px;
}
#discover-ai-sourcing #aisourcing__slider .embla__slide .slide__container .embla__slide__content .bottom {
  margin: 10px 0 20px 0;
}
#discover-ai-sourcing .embla__controls {
  display: none;
  bottom: 20px;
  z-index: 1;
  float: inline-end;
  right: 15%;
  width: auto;
  text-align: right;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #discover-ai-sourcing .embla__controls {
    display: block;
    right: auto;
    width: -webkit-fill-available;
    text-align: center;
  }
}
#discover-ai-sourcing .embla__controls .embla__button {
  background: rgba(35, 71, 145, 0.1490196078);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border-radius: 100%;
  width: 82px;
  height: 82px;
  margin: auto 8px;
  border: 1px solid rgba(228, 239, 252, 0.7);
}
#discover-ai-sourcing .embla__controls .embla__button:hover {
  box-shadow: 0px 0px 10px 0px rgba(228, 239, 252, 0.7);
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #discover-ai-sourcing .embla__controls .embla__button {
    width: 42px;
    height: 42px;
    padding: 0;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #discover-ai-sourcing .embla__controls .embla__button {
    width: 42px;
    height: 42px;
    padding: 0;
  }
}
#discover-ai-sourcing .btn, #discover-ai-sourcing .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer #discover-ai-sourcing button {
  padding: 6px 8px !important;
}

#jobs__slider {
  overflow: hidden;
  margin: 20px 0;
}
#jobs__slider .embla__slide {
  flex: 0 0 26%;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #jobs__slider .embla__slide {
    flex: 0 0 64%;
  }
}
#jobs__slider .embla__container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

#expert-sourcing {
  background-color: #234791;
  overflow: hidden;
  padding-top: 50px;
  padding-bottom: 50px;
}
#expert-sourcing .sub-heading {
  color: #ffffff;
}
#expert-sourcing .top-section {
  display: flex;
}
#expert-sourcing .top-section .heading {
  width: 80%;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #expert-sourcing .top-section .heading {
    width: 100%;
  }
}
#expert-sourcing .top-section .mobile-btn {
  width: 20%;
  text-align: end;
}
#expert-sourcing .heading-h1 {
  margin: 0 !important;
}
#expert-sourcing .slide__container {
  padding-left: 0;
}
#expert-sourcing .embla__container {
  display: flex;
}
#expert-sourcing .embla__slide {
  flex: 0 0 20%;
  margin: 15px 0;
  padding: 0;
  border: 2px solid transparent;
}
#expert-sourcing img.team-pic {
  border-radius: 5px;
}
#expert-sourcing .btn.text-left.yellow, #expert-sourcing .newsletter-table .MuiDataGrid-toolbarContainer button.text-left.yellow, .newsletter-table .MuiDataGrid-toolbarContainer #expert-sourcing button.text-left.yellow {
  text-align: left !important;
  padding: 4px 0;
}
#expert-sourcing .embla__slide__name {
  color: #ffffff;
  font-style: 20px;
  margin: 10px 0 5px 0;
}
#expert-sourcing .country-flag {
  width: max-content;
  display: flex;
  align-items: center;
  background-color: rgba(36, 72, 145, 0.6);
  border-radius: 16px;
  font-size: 12px;
  color: #ffffff;
  padding: 5px 8px;
  position: absolute;
  margin: 6px;
}
#expert-sourcing .country-flag img {
  margin-right: 5px;
}
#expert-sourcing .embla__controls {
  z-index: 1;
  width: auto;
  text-align: center;
}
#expert-sourcing .embla__controls .embla__buttons {
  width: max-content;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #expert-sourcing .embla__controls .embla__buttons {
    margin: auto;
  }
}
#expert-sourcing .embla__controls .embla__button {
  background: transparent;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border-radius: 100%;
  padding: 0;
  margin: auto 8px;
  border: 1px solid transparent;
}
#expert-sourcing .embla__controls .embla__button svg path {
  fill: #ffffff;
}

#embla-carousel-thumbs-team-pic {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
#embla-carousel-thumbs-team-pic.embla {
  margin: auto;
  --slide-height: 19rem;
  --slide-spacing: 1rem;
  --slide-size: 100%;
}
#embla-carousel-thumbs-team-pic .embla__viewport {
  overflow: hidden;
}
#embla-carousel-thumbs-team-pic .embla__container {
  display: flex;
  touch-action: pan-y pinch-zoom;
  margin-left: calc(var(--slide-spacing) * -1);
}
#embla-carousel-thumbs-team-pic .embla__slide {
  transform: translate3d(0, 0, 0);
  flex: 0 0 var(--slide-size);
  min-width: 0;
  padding-left: var(--slide-spacing);
}
#embla-carousel-thumbs-team-pic .embla__slide__pic {
  border-radius: 1.8rem;
  font-size: 4rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  height: auto;
  -webkit-user-select: none;
          user-select: none;
}
#embla-carousel-thumbs-team-pic .embla__slide__pic img {
  width: 100%;
  height: auto;
}
#embla-carousel-thumbs-team-pic .embla-thumbs {
  --thumbs-slide-spacing: 0.8rem;
  --thumbs-slide-height: 6rem;
  margin-top: var(--thumbs-slide-spacing);
}
#embla-carousel-thumbs-team-pic .embla-thumbs__viewport {
  overflow: hidden;
}
#embla-carousel-thumbs-team-pic .embla-thumbs__container {
  display: flex;
  flex-direction: row;
  margin-left: calc(var(--thumbs-slide-spacing) * -1);
}
#embla-carousel-thumbs-team-pic .embla-thumbs__slide {
  flex: 0 0 34%;
  min-width: 0;
  padding-left: var(--thumbs-slide-spacing);
}
#embla-carousel-thumbs-team-pic .embla-thumbs__slide__number {
  -webkit-tap-highlight-color: rgba(var(--text-high-contrast-rgb-value), 0.5);
  -webkit-appearance: none;
          appearance: none;
  touch-action: manipulation;
  display: inline-flex;
  text-decoration: none;
  cursor: pointer;
  border: 0;
  padding: 0;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: auto;
}
#embla-carousel-thumbs-team-pic .embla-thumbs__slide__number img {
  width: 100%;
  height: auto;
}

.file-labels {
  display: block;
  border-style: dashed;
  border-width: 2.5px;
  border-color: #245097;
  font-family: "Proxima-Nova-Semibold" !important;
  width: auto;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  background-color: rgba(228, 239, 252, 0.7) !important;
}

.file-input {
  display: none;
}

.upload-area {
  display: flex;
  align-items: center;
}

.upload-area p {
  text-align: start;
  margin-bottom: 0px;
  color: #234791;
  font-family: "Proxima-Nova-Semibold" !important;
}

.upload-text {
  font-size: 15px;
}

.upload-description {
  font-size: 10px;
}

.iconPlus {
  height: 35px;
  width: 35px;
  margin-right: 20px;
}

.icon-pic {
  height: 40px;
  width: 40px;
  margin-right: 20px;
  border-radius: 15px;
}

@media all and (max-width: 990px) {
  .uploads {
    display: block;
    flex-direction: column;
  }
}
.upload-resume {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.upload-resume:hover {
  background-color: aliceblue;
}
.upload-resume .btn-link {
  padding: 5px 10px;
  text-transform: none !important;
}
.upload-resume .btn-link:hover {
  color: #ffca00;
}

.spinner {
  width: 50px;
  height: 50px;
  margin: 150px auto;
}

.loading-spinner {
  text-align: center;
  margin: 100px 0;
}

.modal {
  top: 0;
  position: fixed;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 3, 0, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.date-modal {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 400px;
  max-height: 70vh;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
  padding: 16px;
  overflow-y: auto;
}

#toggle .dialog-paper {
  color: #f8f8f8;
}
#toggle .dialog-content {
  flex: 1 1 auto;
  overflow-y: auto;
  padding: 16px 24px;
  border-top: 1px solid transparent !important;
  border-bottom: 1px solid transparent !important;
}
#toggle .dialog-actions {
  display: flex;
  align-items: center !important;
  justify-content: center !important;
  padding: 8px;
}
#toggle .css-1cennmq-MuiBackdrop-root-MuiDialog-backdrop {
  background-color: rgba(0, 0, 0, 0.14) !important;
}

.css-10d30g3-MuiPaper-root-MuiDialog-paper {
  box-shadow: 0px 11px 15px -7px rgba(0, 0, 0, 0), 0px 24px 38px 3px rgba(0, 0, 0, 0), 0px 9px 46px 8px rgba(0, 0, 0, 0) !important;
}

.css-6hw5d-MuiButtonBase-root-MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track {
  background-color: #234791 !important;
}

.modal-content-popup {
  background: linear-gradient(#0b3051 0%, #234791 100%);
  position: fixed;
  padding: 18px;
  color: #ffffff;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  width: 40%;
  min-height: 150px;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
}
.modal-content-popup .message {
  display: flex;
  justify-content: center;
}

.popup-icon {
  display: flex;
  justify-content: center;
}

.btn-container1 {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.btn-popup {
  font-family: "Proxima-Nova-Regular" !important;
  font-size: 14px;
  padding: 10px 40px;
  text-align: center;
  color: #234791;
  border-color: transparent;
  text-decoration: none;
  background-color: transparent;
  border: 1px solid transparent;
  height: -moz-fit-content;
  height: fit-content;
  display: flex;
  align-items: center;
  margin: 0px 5px;
  background-color: #ffca00;
  border: 1px solid #ffca00;
}

.btn-outlined-popup {
  font-family: "Proxima-Nova-Regular" !important;
  font-size: 14px;
  padding: 10px 40px;
  text-align: center;
  color: #ffffff;
  border-color: transparent;
  text-decoration: none;
  background-color: transparent;
  border: 1px solid transparent;
  height: -moz-fit-content;
  height: fit-content;
  display: flex;
  align-items: center;
  background-color: transparent;
  border: 1px solid #f7f7f7;
}

.btn-container-popup {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.label-pentabell-popup {
  font-size: 25px !important;
  font-family: "Proxima-Nova-Regular" !important;
  text-transform: capitalize !important;
  color: #ffffff !important;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-content: stretch;
  justify-content: center;
}

.label-form-delete {
  font-size: 16px;
  font-family: "Proxima-Nova-Regular" !important;
  text-transform: capitalize;
  color: #ffffff;
  display: flex;
  flex-wrap: nowrap;
  flex-direction: column;
  justify-content: space-evenly;
  align-items: center;
}

.select-pentabell-delete {
  width: 100%;
  height: 100%;
}
.select-pentabell-delete .MuiInputBase-root,
.select-pentabell-delete .css-5lvf42-MuiSelect-select-MuiInputBase-input-MuiInput-input.css-5lvf42-MuiSelect-select-MuiInputBase-input-MuiInput-input.css-5lvf42-MuiSelect-select-MuiInputBase-input-MuiInput-input {
  height: 29px !important;
  background-color: transparent;
  height: 100%;
  border: 1px solid #ffffff;
  color: #ffffff;
}
.select-pentabell-delete .css-5lvf42-MuiSelect-select-MuiInputBase-input-MuiInput-input {
  padding: 0px 5px;
}
.select-pentabell-delete input {
  height: 26px;
  padding-left: 10px;
  width: 100%;
  color: #798ba3;
}
.select-pentabell-delete input .select-pentabell {
  width: 100%;
  height: 100%;
}
.select-pentabell-delete input .select-pentabell .MuiInputBase-root,
.select-pentabell-delete input .select-pentabell .css-5lvf42-MuiSelect-select-MuiInputBase-input-MuiInput-input.css-5lvf42-MuiSelect-select-MuiInputBase-input-MuiInput-input.css-5lvf42-MuiSelect-select-MuiInputBase-input-MuiInput-input {
  height: 29px !important;
  background-color: rgba(228, 239, 252, 0.7);
  height: 100%;
}
.select-pentabell-delete input .select-pentabell .css-5lvf42-MuiSelect-select-MuiInputBase-input-MuiInput-input {
  padding: 0px 5px;
}
.select-pentabell-delete input .select-pentabell input {
  height: 26px;
  background-color: rgba(228, 239, 252, 0.7);
  padding-left: 10px;
  width: 100%;
  color: #798ba3;
}
.select-pentabell-delete input .select-pentabell.light.react-international-phone-input-container .react-international-phone-country-selector-button {
  background-color: transparent;
  border: 0px;
  border-radius: 0;
  border-bottom: 1px solid #0d2849;
}
.select-pentabell-delete input .select-pentabell.light.react-international-phone-input-container .react-international-phone-input {
  border: 0px;
  border-radius: 0;
  border-bottom: 1px solid #0d2849;
}
.select-pentabell-delete input .select-pentabell.light::placeholder {
  color: #ffffff;
}
.select-pentabell-delete input .select-pentabell.light .MuiInputBase-root {
  background-color: transparent;
}
.select-pentabell-delete input .select-pentabell.light input {
  color: #ffffff;
  background-color: transparent;
}
.select-pentabell-delete.light.react-international-phone-input-container .react-international-phone-country-selector-button {
  background-color: transparent;
  border: 0px;
  border-radius: 0;
  border-radius: 0;
  border-bottom: 1px solid #0d2849;
}
.select-pentabell-delete.light.react-international-phone-input-container .react-international-phone-input {
  border: 0px;
  border-radius: 0;
  border-bottom: 1px solid #0d2849;
}
.select-pentabell-delete.light::placeholder {
  color: #ffffff;
}
.select-pentabell-delete.light .MuiInputBase-root {
  background-color: transparent;
}
.select-pentabell-delete.light input {
  color: #ffffff;
  background-color: transparent;
}

.label-popup {
  color: #ffca00;
}

.close-icon {
  position: absolute;
  top: 18px;
  right: 0px;
  border: none;
  width: 24px;
  height: 24px;
  background-color: transparent;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}
.close-icon .hover {
  opacity: 1;
}

@media only screen and (min-width: 320px) and (max-width: 480px) {
  .modal-content-popup {
    width: 95%;
    padding: 12px;
  }
  .btn-container-popup {
    flex-direction: column;
    gap: 5px;
  }
  .message {
    font-size: 12px;
    padding: 18px;
  }
}
.confirmation-center {
  display: flex;
  align-content: stretch;
  justify-content: center;
  align-items: baseline;
  margin-bottom: 32px !important;
}

.confirmation-dialog-icon {
  text-align: center !important;
  display: flex !important;
  align-content: stretch;
  justify-content: center !important;
  align-items: baseline;
  margin-bottom: 10px !important;
}

.text-confirmation-h1 {
  font-family: "Proxima-Nova-Medium" !important;
  text-align: center !important;
  font-size: 24px;
  display: flex;
  align-content: stretch;
  justify-content: center;
  align-items: baseline;
  margin-bottom: 10px !important;
}

.text-confirmation-second {
  font-family: "Proxima-Nova-Medium" !important;
  text-align: center !important;
  font-size: 16px;
  display: flex;
  align-content: stretch;
  justify-content: center;
  align-items: baseline;
  margin-bottom: 13px !important;
}

#notifications * {
  font-family: "Proxima-Nova-Semibold" !important;
  color: #0b3051;
  box-sizing: border-box;
  margin: 0;
  font-size: 20px;
}

#notifications .notification-description-message {
  font-size: 13px;
  color: #6b7c93;
  margin-top: 3px;
}

#notifications .container-alerts {
  width: 350px;
  margin: 20px auto;
  background-color: #6b7280;
  overflow: hidden;
}

#notifications .notifications-alerts {
  background: #e4effc;
  padding: 15px;
  padding-top: 0px;
}

#notifications .header-alert {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}

#notifications .header-alert h2 {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.25rem;
}

#notifications .header-alert .unread-notification-number {
  background: #234791;
  color: #ffffff;
  padding: 2px 6px;
  font-size: 0.68rem;
}

#notifications .header-alert p {
  cursor: pointer;
  color: #0b3051;
  font-family: "Proxima-Nova-Semibold" !important;
  font-size: 14px;
  display: flex;
}

#notifications .header-alert p:hover {
  color: #234791;
}

#notifications .message-notif {
  color: #234791;
  font-family: "Proxima-Nova-Semibold" !important;
  font-size: 14px;
}

#notifications .body-alert {
  margin-top: 10px;
}

.notif-details {
  display: flex;
  flex-direction: row;
}

#notifications .notification {
  display: flex;
  align-items: center;
  padding: 10px;
  border-bottom: 2px solid #e0e0e0;
  cursor: pointer;
  transition: background-color 0.3s;
}

#notifications .notification:hover {
  background-color: rgba(181, 197, 218, 0.7882352941);
}

#notifications .notification .avatar img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

#notifications .notification .text {
  margin-left: 15px;
  flex: 1 1;
}

#notifications .text .profil-name {
  font-weight: 700;
  color: #6b7280;
}

#notifications .text-bottom {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 4px;
}

#notifications .unread-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  background-color: #234791;
  border-radius: 50%;
  margin-top: 8px;
  margin-left: 10px;
}

#notifications .see-all {
  width: 100%;
  padding: 12px;
  background-color: #234791;
  color: #ffffff;
  font-size: 16px;
  font-family: "Proxima-Nova-Semibold";
  cursor: pointer;
  text-align: center;
  border: none;
  transition: background-color 0.3s;
}

#notifications .see-all:hover {
  background-color: #234791;
}

@media screen and (max-width: 375px) {
  #notifications .container-alerts {
    width: 90%;
  }
  #notifications .notification .text {
    margin-left: 10px;
  }
  #notifications .header-alert h2 {
    font-size: 1rem;
  }
}
#notifications .text-top {
  display: flex;
  justify-content: space-between;
}

#Notifications-dashboard .notif-details {
  display: flex;
  flex-wrap: nowrap;
  flex-direction: row;
}

#notifications .notification-timestamp {
  margin-left: 10px !important;
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 4px;
}

#get-in-touch {
  padding-bottom: 50px;
  padding-top: 50px;
}
#get-in-touch .text-center {
  width: -moz-fit-content;
  width: fit-content;
  text-align: center;
  justify-self: center;
}
#get-in-touch img {
  margin: 20px 0;
}

.service-image {
  margin: auto;
  border: 1px solid #699bd4;
  padding: 20px;
  height: 332px;
  width: 332px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .service-image {
    height: 250px;
    width: 250px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .service-image {
    max-width: 300px;
  }
}
.service-image > div {
  width: 100%;
  height: 100%;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

.ksa {
  max-width: 100% !important;
}

#global-hr-services-website {
  padding: 50px 0px 100px;
  background: linear-gradient(71deg, #0b3051 0%, #234791 100%);
  max-width: 1240px;
  place-self: center;
  display: flex;
  overflow-x: hidden;
  position: relative;
  flex-wrap: wrap;
  place-content: center;
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #global-hr-services-website {
    max-width: 80%;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #global-hr-services-website {
    max-width: 80%;
    padding: 50px 0px;
    margin: 40px;
  }
}
#global-hr-services-website .services-list {
  margin: 20px;
  flex-wrap: wrap;
  justify-content: space-evenly;
  display: flex;
  white-space: nowrap;
}
#global-hr-services-website .services-list.ksa {
  margin: 0px !important;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #global-hr-services-website .services-list {
    flex-wrap: nowrap;
    overflow-x: auto;
    gap: 16px;
    padding: 8px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #global-hr-services-website .services-list {
    flex-wrap: nowrap;
    overflow-x: auto;
    gap: 16px;
    padding: 8px;
  }
}
#global-hr-services-website .service-item {
  color: white;
  cursor: pointer;
  margin: 0px 10px;
  display: flex;
  align-items: center;
  padding: 5px 20px;
  background: transparent;
  border: none;
  font-size: 16px;
}
#global-hr-services-website .service-item svg {
  margin-right: 5px;
}
#global-hr-services-website .service-item path {
  fill: white;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #global-hr-services-website .service-item {
    padding: 5px 10px;
    margin: 0;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #global-hr-services-website .service-item {
    padding: 5px 10px;
    margin: 0;
  }
}
#global-hr-services-website .service-item-yellow {
  color: #234791;
  background: #ffca00;
  border-radius: 5px;
  margin: 0px 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  padding: 5px 20px;
  border: none;
  font-size: 16px;
}
#global-hr-services-website .service-item-yellow svg {
  margin-right: 5px;
}
#global-hr-services-website .service-item-yellow path {
  fill: #234791;
}
#global-hr-services-website .service-item-yellow .service-title {
  font-weight: 600;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #global-hr-services-website .service-item-yellow {
    padding: 5px 10px;
    margin: 0;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #global-hr-services-website .service-item-yellow {
    padding: 5px 10px;
    margin: 0;
  }
}
#global-hr-services-website .service {
  display: flex;
  max-width: 820px;
  margin: 40px 0px 0px;
  padding: 30px;
  background: #143d6f;
  box-shadow: 0px 0px 5px -2px;
  z-index: 1;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #global-hr-services-website .service {
    display: block;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #global-hr-services-website .service {
    display: block;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #global-hr-services-website .service {
    max-width: 280px;
    margin: 20px;
  }
}
#global-hr-services-website .svg-service-icon {
  position: absolute;
  left: 30px;
  top: 300px;
}
#global-hr-services-website .svg-service-icon.ksa {
  left: 13% !important;
  top: 200px;
}
@media only screen and (min-width: 1601px) {
  #global-hr-services-website .svg-service-icon.ksa {
    left: 20% !important;
  }
}
#global-hr-services-website .list-arrow {
  list-style: none;
  padding-inline-start: 0px;
}
#global-hr-services-website .item-arrow {
  font-size: 17px;
  margin: 5px 0;
}
#global-hr-services-website .item-arrow::before {
  content: "➤"; /* Use an arrow character */
  color: #ffffff; /* Optional: Change arrow color */
  margin-right: 8px; /* Add some space between arrow and text */
}
#global-hr-services-website .service-subtitle {
  color: #ffffff;
  font-size: 17px;
  font-family: "Proxima-Nova-SemiBold" !important;
}
#global-hr-services-website .service-content-title {
  font-size: 25px;
  color: #ffca00;
  font-weight: 600;
  margin-bottom: 10px;
}
#global-hr-services-website .service-content {
  color: white;
  font-size: 20px;
  max-width: 50%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
#global-hr-services-website .service-content a {
  font-size: 20px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #global-hr-services-website .service-content {
    max-width: 100%;
  }
  #global-hr-services-website .service-content .btn, #global-hr-services-website .service-content .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer #global-hr-services-website .service-content button {
    padding: 20px 0;
  }
}
#global-hr-services-website .service-content-image {
  margin: auto;
  padding-left: 20px;
  height: 332px;
  width: 332px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #global-hr-services-website .service-content-image {
    height: 250px;
    width: 250px;
    padding-left: 0px;
    margin-top: 10px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #global-hr-services-website .service-content-image {
    max-width: 350px;
  }
}
#global-hr-services-website .service-content-image > div {
  width: 100%;
  height: 100%;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

#global-hr-services {
  padding: 50px 0;
  background: linear-gradient(#0b3051 0%, #234791 100%);
}
#global-hr-services .custom-max-width {
  max-width: 1300px !important;
  margin-left: auto;
  margin-right: auto;
}
#global-hr-services .css-fgq1ej-MuiPaper-root-MuiAccordion-root::before {
  height: 0;
  display: none;
}
#global-hr-services .services-accordion {
  background-color: transparent;
}
#global-hr-services .services-accordion-header {
  background-color: transparent;
}
#global-hr-services .services-accordion .service-title {
  color: #ffffff;
  font-size: 24px;
  font-family: "Proxima-Nova-Semibold" !important;
}
#global-hr-services .services-accordion .service-title:before {
  content: "";
  width: 12px;
  height: 12px;
  display: inline-flex;
  background: #ffffff;
  margin-right: 10px;
}
#global-hr-services .services-accordion .Mui-expanded:before {
  display: "none";
}
#global-hr-services .services-accordion .Mui-expanded .service-title:before {
  background: #ffca00;
}
#global-hr-services .services-accordion .service-description {
  color: #ffffff;
}

#what-they-say {
  padding: 50px 0;
  background: linear-gradient(#0b3051 0%, #234791 100%);
}
#what-they-say .css-fgq1ej-MuiPaper-root-MuiAccordion-root::before {
  height: 0;
  display: none;
}
#what-they-say .say-section {
  display: flex;
  overflow-x: hidden;
  width: 100%;
  position: relative;
  overflow-y: hidden;
  margin-top: 20px;
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #what-they-say .say-section {
    overflow-x: scroll;
    margin: auto 0px auto 30px;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #what-they-say .say-section {
    overflow-x: scroll;
    margin: auto 0px auto 30px;
  }
}
#what-they-say .say-item {
  background: #234574;
  border-radius: 5px;
  height: auto;
  max-width: 27%;
  margin: 10px 20px;
  padding: 25px;
  flex: 0 0 auto;
  transition: transform 0.3s ease-in-out;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #what-they-say .say-item {
    min-width: 70%;
    margin: 50px 20px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #what-they-say .say-item {
    min-width: 50%;
    margin: 50px 20px;
  }
}
@media only screen and (min-width: 769px) and (max-width: 1024px) {
  #what-they-say .say-item {
    max-width: 24%;
  }
}
#what-they-say .say-item.scale-up {
  transform: scale(1.1);
  z-index: 2;
}
#what-they-say .say-item.scale-down {
  transform: scale(0.85);
  z-index: 2;
}
#what-they-say .say-content {
  color: white;
  line-height: 25px;
}
#what-they-say .guillemet {
  margin-bottom: 20px;
}
#what-they-say .buttons {
  margin: 40px auto 0px;
}
#what-they-say .scroll-button {
  border: none;
  cursor: pointer;
  background: transparent;
}
#what-they-say .scroll-button.left {
  margin-left: 5px;
}
#what-they-say .scroll-button.right {
  margin-left: 5px;
}
#what-they-say .star-fill {
  display: flex;
  margin: 20px 0px;
  scale: 0.7;
  -webkit-transform-origin-x: left;
}
#what-they-say .author {
  display: flex;
  align-items: center;
}
#what-they-say .author .css-pfutxk-MuiAvatar-root {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 1.25rem;
  line-height: 1;
  border-radius: 50%;
  overflow: hidden;
  -webkit-user-select: none;
          user-select: none;
  color: #fff;
  background-color: #bdbdbd;
  width: 50px;
  height: 50px;
}
#what-they-say .author-icon {
  color: #234791;
  text-transform: uppercase;
  font-size: 30px;
  font-weight: 600;
}
#what-they-say .info-author {
  margin-left: 12px;
  color: white;
}
#what-they-say .name {
  font-size: 16px;
}
#what-they-say .quality {
  font-size: 14px;
  font-weight: 600;
}

#global-map {
  padding-top: 50px;
  padding-bottom: 50px;
  background-color: transparent;
}
#global-map .sub-heading {
  color: #ffffff;
}
#global-map .container {
  display: flex;
  justify-content: center;
}
#global-map .buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}
#global-map .buttons button {
  padding: 10px 20px;
  cursor: pointer;
}
#global-map .infoSection {
  display: flex;
  justify-content: center;
  width: 100%;
}
#global-map .infoSection .infoBox {
  padding: 20px;
  border: 1px solid #ccc;
  background-color: #f9f9f9;
  width: 300px;
  text-align: center;
}
#global-map #btns-zone {
  display: flex;
  flex-direction: row;
  padding-top: 10px;
  padding-bottom: 25px;
  flex-wrap: wrap;
  justify-content: center;
}
#global-map #btns-zone .btn-zone {
  width: auto;
  white-space: nowrap;
  margin: 0 10px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #global-map #btns-zone .btn-zone {
    width: 100%;
    margin: 10px 0;
  }
}
#global-map #btns-country {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  grid-gap: 10px;
  gap: 10px;
  padding-bottom: 15px;
  overflow-x: auto;
  white-space: nowrap;
  /* Scrollbar Styling */
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #global-map #btns-country {
    grid-template-columns: repeat(3, 1fr);
  }
}
#global-map #btns-country::-webkit-scrollbar {
  height: 5px;
}
#global-map #btns-country::-webkit-scrollbar-track {
  background-color: #dbe8f6;
  border-radius: 10px;
}
#global-map #btns-country::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: #234791;
}
#global-map #btns-country .btn-country {
  width: 100%;
  white-space: nowrap;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #global-map #btns-country .btn-country {
    padding: 10px !important;
    width: -webkit-fill-available;
  }
}

.gradient-blue, #glossary-page-details #content-table, #blog-page-details #content-table {
  background-image: linear-gradient(to bottom, #0b3051, #234791);
}

#map-frame,
#map-frame-mobile {
  background-color: transparent;
  height: auto;
  width: -webkit-fill-available;
  margin: auto;
}
#map-frame .box-info,
#map-frame-mobile .box-info {
  opacity: 0;
  z-index: -1;
  display: none;
}
#map-frame .box-info.selected,
#map-frame-mobile .box-info.selected {
  display: block;
  opacity: 1;
  position: relative;
  z-index: 9999;
}
#map-frame path.pin.selected,
#map-frame-mobile path.pin.selected {
  fill: #ffca00;
}
#map-frame path.pin.selected.oil-gaz,
#map-frame-mobile path.pin.selected.oil-gaz {
  fill: #d69b19;
}
#map-frame path.pin.selected.energy,
#map-frame-mobile path.pin.selected.energy {
  fill: #cc3233;
}
#map-frame path.pin.selected.banking-insurance,
#map-frame-mobile path.pin.selected.banking-insurance {
  fill: #0b3051;
}
#map-frame path.pin.selected.transport,
#map-frame-mobile path.pin.selected.transport {
  fill: #009966;
}
#map-frame path.pin.selected.it-telecom,
#map-frame-mobile path.pin.selected.it-telecom {
  fill: #743794;
}
#map-frame path.pin.selected.pharmaceutical,
#map-frame-mobile path.pin.selected.pharmaceutical {
  fill: #2bbfad;
}

.numbers {
  display: flex;
  justify-content: center;
  padding: 0;
  margin-bottom: 50px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .numbers {
    margin-bottom: 20px;
  }
}
.numbers .img-nbr {
  margin-bottom: 50px;
  width: 100%;
  height: auto;
}
.numbers svg {
  display: flex;
  margin: auto;
}

.responsive-row-title-text {
  padding-top: 50px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .responsive-row-title-text {
    flex-direction: column;
  }
}
.responsive-row-title-text .grid-item {
  width: 48%;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .responsive-row-title-text .grid-item {
    width: 100%;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .responsive-row-title-text {
    padding-bottom: 50px;
    padding-top: 10px;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .responsive-row-title-text .section {
    max-width: 330px;
  }
}

.dark-bg {
  background: linear-gradient(#0b3051 0%, #234791 100%);
}
.dark-bg .content {
  background-color: transparent !important;
}

.service-row {
  padding-top: 50px;
  padding-bottom: 50px;
}
.service-row .label.yellow {
  color: #ffca00;
  font-size: 16px;
  font-family: "Proxima-Nova-Medium" !important;
}
.service-row .reverse {
  flex-direction: row-reverse;
}
.service-row .reverse .feauture-img {
  margin-left: 0;
  margin-right: 20px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .service-row .reverse .feauture-img {
    margin: 10px auto;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .service-row .reverse .feauture-img {
    margin: 10px auto;
  }
}
.service-row .MuiGrid-item {
  display: flex;
}
.service-row .content,
.service-row .feauture-img {
  display: flex;
  justify-content: center;
  padding: 50px;
  flex-direction: column;
  width: 100%;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .service-row .content,
  .service-row .feauture-img {
    padding: 20px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .service-row .content,
  .service-row .feauture-img {
    padding: 20px;
  }
}
.service-row .content {
  background-color: #0b3051;
}
.service-row .feauture-img {
  margin-left: 20px;
  border: 2px solid #699bd4;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .service-row .feauture-img {
    max-height: 300px;
    width: 100%;
    text-align: center;
    margin: 10px auto;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .service-row .feauture-img {
    max-height: 300px;
    width: 100%;
    text-align: center;
    margin: 10px auto;
  }
}
.service-row .feauture-img img {
  height: 100%;
  width: 100%;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .service-row .feauture-img img {
    max-height: 300px;
    width: auto;
    margin: auto;
    text-align: center;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .service-row .feauture-img img {
    max-height: 300px;
    width: auto;
    margin: auto;
    text-align: center;
  }
}
.service-row .data {
  margin: 30px 0;
}
.service-row .data .data-item {
  font-size: 18px;
  color: #234791;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  margin: 10px 0;
}
.service-row .data .data-item.text-white {
  color: #ffffff;
}
.service-row .data .data-item svg {
  width: 50px;
  margin-right: 5px;
}
.service-row .data-item-paragraph .sub-heading {
  display: flex;
  align-items: center;
  font-family: "Proxima-Nova-Medium" !important;
}
.service-row .data-item-paragraph svg {
  width: 50px;
  margin-right: 5px;
}
.service-row .btn, .service-row .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer .service-row button {
  width: -moz-fit-content;
  width: fit-content;
}

#services-page #expert-sourcing {
  margin-bottom: 50px;
  margin-top: 50px;
}

#our-industries {
  padding: 30px 0;
}
#our-industries .heading-h1 {
  margin-bottom: 20px !important;
  margin-top: 0 !important;
}
#our-industries #industries__slider {
  overflow: hidden;
}
#our-industries #industries__slider .embla__container {
  display: flex;
}
#our-industries #industries__slider .embla__slide {
  flex: 0 0 25%;
  min-width: 0;
  height: 280px;
  margin: 6px auto;
  padding: 0;
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #our-industries #industries__slider .embla__slide {
    margin: 5px 5px 5px 0;
    flex: 0 0 31%;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #our-industries #industries__slider .embla__slide {
    margin: 5px 5px 5px 0;
    flex: 0 0 64%;
  }
}
#our-industries #industries__slider .embla__slide .btn-ghost {
  padding: 15px 0;
}
#our-industries #industries__slider .embla__slide .smart-square {
  width: 216px;
  height: 268px;
  padding: 10px 16px !important;
}
#our-industries #industries__slider .embla__slide .slide__container.oil-gaz {
  background-color: #d69b19;
  color: #ffffff;
}
#our-industries #industries__slider .embla__slide .slide__container.energy {
  background-color: #cc3233;
  color: #ffffff;
}
#our-industries #industries__slider .embla__slide .slide__container.banking-insurance {
  background-color: #0b3051;
  color: #ffffff;
}
#our-industries #industries__slider .embla__slide .slide__container.transport {
  background-color: #009966;
  color: #ffffff;
}
#our-industries #industries__slider .embla__slide .slide__container.it-telecom {
  background-color: #743794;
  color: #ffffff;
}
#our-industries #industries__slider .embla__slide .slide__container.other {
  background-color: #234791;
  color: #ffffff;
}
#our-industries #industries__slider .embla__slide .slide__container.pharmaceutical {
  background-color: #2bbfad;
  color: #ffffff;
}
#our-industries #industries__slider .embla__slide .slide__container .embla__slide__content {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: flex-start;
}
#our-industries #industries__slider .embla__slide .slide__container .embla__slide__content .embla__slide__description {
  color: #ffffff;
  font-size: 14px;
  margin-top: 10px;
}
#our-industries #industries__slider .embla__slide .slide__container .embla__slide__content .embla__slide__industry-icon {
  justify-content: center;
  display: flex;
  height: -moz-fit-content;
  height: fit-content;
  width: -moz-fit-content;
  width: fit-content;
  margin: 20px auto 20px auto;
  transform: scale(2, 2);
}
#our-industries #industries__slider .embla__slide .slide__container .embla__slide__content .embla__slide__industry-name,
#our-industries #industries__slider .embla__slide .slide__container .embla__slide__content .embla__slide__industry-icon {
  text-align: center;
  color: #ffffff;
  font-size: 16px;
  text-transform: capitalize !important;
}

.stats-container-pentabell {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-gap: 0;
  gap: 0;
  /* Assign specific grid positions */
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .stats-container-pentabell {
    grid-template-columns: repeat(3, 1fr);
    margin: auto;
    scale: 0.75;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .stats-container-pentabell {
    grid-template-columns: repeat(3, 1fr);
    margin: auto;
    scale: 0.75;
  }
}
.stats-container-pentabell .stat-box {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: transparent;
  padding: 20px;
  text-align: center;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 2px solid #234791;
  aspect-ratio: 1/1;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .stats-container-pentabell .stat-box {
    padding: 10px;
  }
}
.stats-container-pentabell .stat-box h2 {
  margin: 0;
  font-size: 3rem;
  color: #234791;
  font-weight: bold;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .stats-container-pentabell .stat-box h2 {
    font-size: 2rem;
  }
}
.stats-container-pentabell .stat-box p {
  margin: 5px 0 0;
  font-size: 1.4rem;
  color: #234791;
}
.stats-container-pentabell .corner {
  position: absolute;
  width: 16px;
  height: 16px;
  background-color: currentColor;
}
.stats-container-pentabell .purple {
  z-index: 8;
  color: #743794;
  border-color: #743794;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .stats-container-pentabell .purple {
    margin-left: 1px;
    height: 150px;
    width: 150px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .stats-container-pentabell .purple {
    margin-left: 1px;
    height: 150px;
    width: 150px;
  }
}
.stats-container-pentabell .purple .bottom-right {
  display: block;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .stats-container-pentabell .purple .bottom-right {
    display: none;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .stats-container-pentabell .purple .bottom-right {
    display: none;
  }
}
.stats-container-pentabell .blue,
.stats-container-pentabell .red,
.stats-container-pentabell .green {
  margin-top: -2px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .stats-container-pentabell .blue,
  .stats-container-pentabell .red,
  .stats-container-pentabell .green {
    margin-top: 0;
    height: 150px;
    width: 150px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .stats-container-pentabell .blue,
  .stats-container-pentabell .red,
  .stats-container-pentabell .green {
    margin-top: 0;
    height: 150px;
    width: 150px;
  }
}
.stats-container-pentabell .red {
  margin-left: -3px;
  z-index: 7;
  color: #cc3233;
  border-color: #cc3233;
}
.stats-container-pentabell .red .bottom-left {
  display: block;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .stats-container-pentabell .red .bottom-left {
    display: none;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .stats-container-pentabell .red .bottom-left {
    display: none;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .stats-container-pentabell .red {
    margin-left: -3px;
    height: 150px;
    width: 150px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .stats-container-pentabell .red {
    margin-left: -3px;
    height: 150px;
    width: 150px;
  }
}
.stats-container-pentabell .yellow {
  margin-left: -6px;
  color: #d69b19;
  border-color: #d69b19;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .stats-container-pentabell .yellow {
    z-index: 9;
    margin-left: -1px;
    margin-top: -2px;
    height: 150px;
    width: 150px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .stats-container-pentabell .yellow {
    z-index: 9;
    margin-left: -1px;
    margin-top: -2px;
    height: 150px;
    width: 150px;
  }
}
.stats-container-pentabell .yellow .bottom-left,
.stats-container-pentabell .yellow .bottom-right {
  display: block;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .stats-container-pentabell .yellow .bottom-left,
  .stats-container-pentabell .yellow .bottom-right {
    display: none;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .stats-container-pentabell .yellow .bottom-left,
  .stats-container-pentabell .yellow .bottom-right {
    display: none;
  }
}
.stats-container-pentabell .green {
  margin-left: -8px;
  z-index: 9;
  color: #009966;
  border-color: #009966;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .stats-container-pentabell .green {
    margin-left: 1px;
    margin-top: -4px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .stats-container-pentabell .green {
    margin-left: 1px;
    margin-top: -4px;
  }
}
.stats-container-pentabell .green .top-right,
.stats-container-pentabell .green .bottom-right {
  display: none;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .stats-container-pentabell .green .top-right,
  .stats-container-pentabell .green .bottom-right {
    display: block;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .stats-container-pentabell .green .top-right,
  .stats-container-pentabell .green .bottom-right {
    display: block;
  }
}
.stats-container-pentabell .blue {
  margin-left: -10px;
  z-index: 10;
  color: #0b3051;
  border-color: #0b3051;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .stats-container-pentabell .blue {
    margin-left: -3px;
    margin-top: -4px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .stats-container-pentabell .blue {
    margin-left: -3px;
    margin-top: -4px;
  }
}
.stats-container-pentabell .top-left {
  top: -9px;
  left: -9px;
}
.stats-container-pentabell .top-right {
  top: -8px;
  right: -8px;
}
.stats-container-pentabell .bottom-left {
  bottom: -8px;
  left: -8px;
}
.stats-container-pentabell .bottom-right {
  bottom: -8px;
  right: -8px;
}
.stats-container-pentabell .stat-box:nth-child(1) {
  grid-column: 1;
  /* First column */
  grid-row: 1;
  /* First row */
}
.stats-container-pentabell .stat-box:nth-child(2) {
  grid-column: 2;
  /* Second column */
  grid-row: 2;
  /* Second row */
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .stats-container-pentabell .stat-box:nth-child(2) {
    grid-column: 3;
    grid-row: 1;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .stats-container-pentabell .stat-box:nth-child(2) {
    grid-column: 3;
    grid-row: 1;
  }
}
.stats-container-pentabell .stat-box:nth-child(3) {
  grid-column: 3;
  /* Second column */
  grid-row: 1;
  /* First row */
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .stats-container-pentabell .stat-box:nth-child(3) {
    grid-column: 2;
    grid-row: 2;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .stats-container-pentabell .stat-box:nth-child(3) {
    grid-column: 2;
    grid-row: 2;
  }
}
.stats-container-pentabell .stat-box:nth-child(4) {
  grid-column: 4;
  /* First column */
  grid-row: 2;
  /* Second row */
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .stats-container-pentabell .stat-box:nth-child(4) {
    grid-column: 1;
    grid-row: 3;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .stats-container-pentabell .stat-box:nth-child(4) {
    grid-column: 1;
    grid-row: 3;
  }
}
.stats-container-pentabell .stat-box:nth-child(5) {
  grid-column: 5;
  /* First column */
  grid-row: 2;
  /* Third row */
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .stats-container-pentabell .stat-box:nth-child(5) {
    grid-column: 3;
    grid-row: 3;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .stats-container-pentabell .stat-box:nth-child(5) {
    grid-column: 3;
    grid-row: 3;
  }
}

#insights-section {
  padding-bottom: 50px;
  padding-top: 50px;
}
#insights-section .btn-view-more .btn, #insights-section .btn-view-more .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer #insights-section .btn-view-more button {
  margin: 50px auto 0;
  width: -moz-fit-content;
  width: fit-content;
}
#insights-section .MuiGrid-container {
  margin-top: 20px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #insights-section .MuiGrid-container {
    overflow-x: auto;
    flex-wrap: nowrap;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #insights-section .MuiGrid-container {
    overflow-x: auto;
    flex-wrap: nowrap;
  }
}
#insights-section .title {
  text-transform: none;
}

.guide-category {
  display: flex !important;
  flex-direction: row-reverse !important;
  flex-wrap: wrap !important;
  align-content: stretch;
  justify-content: space-around;
  align-items: flex-start;
  margin-left: 142px;
}

.label-category {
  font-size: 14px;
  width: -moz-fit-content;
  width: fit-content;
  font-family: "Proxima-Nova-Semibold" !important;
  text-transform: capitalize;
  padding: 6px 18px;
  position: absolute;
  margin: 10px 0 0 10px;
  border-radius: 20px;
  background-color: #ffca00;
  color: #234791;
}
.label-category.oil-gaz {
  background-color: #d69b19;
  color: #ffffff;
}
.label-category.energy {
  background-color: #cc3233;
  color: #ffffff;
}
.label-category.banking-insurance {
  background-color: #0b3051;
  color: #ffffff;
}
.label-category.transport {
  background-color: #009966;
  color: #ffffff;
}
.label-category.it-telecom {
  background-color: #743794;
  color: #ffffff;
}
.label-category.other {
  background-color: #234791;
  color: #ffffff;
}
.label-category.pharmaceutical {
  background-color: #2bbfad;
  color: #ffffff;
}

.label-category-guide {
  font-size: 14px;
  width: -moz-fit-content;
  width: fit-content;
  font-family: "Proxima-Nova-Semibold" !important;
  text-transform: capitalize;
  padding: 6px 18px;
  border-radius: 20px;
  background-color: #ffca00;
  color: #234791;
}
.label-category-guide.oil-gaz {
  background-color: #d69b19;
  color: #ffffff;
}
.label-category-guide.energy {
  background-color: #cc3233;
  color: #ffffff;
}
.label-category-guide.banking-insurance {
  background-color: #0b3051;
  color: #ffffff;
}
.label-category-guide.transport {
  background-color: #009966;
  color: #ffffff;
}
.label-category-guide.it-telecom {
  background-color: #743794;
  color: #ffffff;
}
.label-category-guide.other {
  background-color: #234791;
  color: #ffffff;
}
.label-category-guide.pharmaceutical {
  background-color: #2bbfad;
  color: #ffffff;
}

.labels-container {
  position: relative;
  display: flex;
  position: absolute;
  margin-top: 10px;
  margin-left: 8px;
  gap: 10px !important;
  align-items: flex-start !important;
  flex-wrap: wrap;
}

.download-label {
  font-size: 14px;
  width: -moz-fit-content;
  width: fit-content;
  font-family: "Proxima-Nova-Semibold" !important;
  text-transform: capitalize;
  background-color: transparent;
  padding: 6px 18px;
  position: absolute;
  margin: 10px 0 0 10px;
  border: none;
  position: absolute;
  bottom: 45%;
  right: 1px;
}

.blog-item {
  background-color: transparent;
}
.blog-item .card-content {
  padding: 8px !important;
}
.blog-item.MuiGrid-item {
  padding-top: 10px;
}
.blog-item a {
  text-decoration: none;
}
.blog-item .card-image {
  cursor: pointer;
}
.blog-item .card {
  transition: transform 0.3s ease, background-color 0.3s ease;
}
.blog-item .card:hover {
  transform: scale(1.05);
  background-color: #dbe8f6;
}
.blog-item .card {
  background-color: transparent;
  display: flex;
  flex-direction: column;
  height: 100%;
  box-shadow: none;
  position: relative;
}
.blog-item .card-content {
  padding: 0;
}
.blog-item .card-content .blog-title {
  margin: 15px 0;
  font-size: 24px;
  font-family: "Proxima-Nova-Medium" !important;
  color: #234791;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .blog-item .card-content .blog-title {
    font-size: 1.75rem;
  }
}
.blog-item .card-content .blog-description {
  font-size: 16px;
  margin: 0 0 10px 0;
  color: #3c3b3b;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}
.blog-item .card-image {
  height: 252px;
  background-color: #dbe8f6;
  object-fit: cover;
}
.blog-item .card-bottom {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.blog-item .card .blog-time {
  font-size: 14px;
  color: #798ba3;
  display: flex;
  align-items: center;
}
.blog-item .card .blog-time svg {
  margin-right: 5px;
}
.blog-item .card .btn, .blog-item .card .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer .blog-item .card button {
  margin: 0 0 2px 0;
  padding: 8px !important;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .blog-item {
    min-width: 305px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .blog-item {
    min-width: 305px;
  }
}
.blog-item .btn-guide {
  margin-top: 20px !important;
  font-family: "Proxima-Nova-Medium" !important;
}
.blog-item .guide-ReadMore {
  margin-top: 10px !important;
}

.guide-item {
  background-color: transparent;
}
.guide-item .card-content {
  padding: 8px !important;
}
.guide-item.MuiGrid-item {
  padding-top: 10px;
}
.guide-item a {
  text-decoration: none;
}
.guide-item .card-image {
  cursor: pointer;
}
.guide-item .card {
  transition: transform 0.3s ease, background-color 0.3s ease;
}
.guide-item .card:hover {
  transform: scale(1.05);
  background-color: #dbe8f6;
}
.guide-item .card {
  background-color: transparent;
  display: flex;
  flex-direction: column;
  height: 100%;
  box-shadow: none;
  position: relative;
}
.guide-item .card-content {
  padding: 0;
}
.guide-item .card-content .blog-title {
  margin: 15px 0;
  font-size: 24px;
  font-family: "Proxima-Nova-Medium" !important;
  color: #234791;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .guide-item .card-content .blog-title {
    font-size: 1.75rem;
  }
}
.guide-item .card-content .blog-description {
  font-size: 16px;
  margin: 0 0 10px 0;
  color: #3c3b3b;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}
.guide-item .card-image {
  height: 252px;
  background-color: #dbe8f6;
  object-fit: cover;
}
.guide-item .card-bottom {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
  align-items: center;
}
.guide-item .card .blog-time {
  font-size: 14px;
  color: #798ba3;
  display: flex;
  align-items: center;
}
.guide-item .card .blog-time svg {
  margin-right: 5px;
}
.guide-item .card .btn, .guide-item .card .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer .guide-item .card button {
  margin: 0 0 2px 0;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .guide-item {
    min-width: 305px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .guide-item {
    min-width: 305px;
  }
}
.guide-item .btn-guide {
  margin-top: 20px !important;
  font-family: "Proxima-Nova-Medium" !important;
}
.guide-item .guide-ReadMore {
  margin-top: 10px !important;
}

#latest-offers {
  padding: 50px 0;
}
#latest-offers .container {
  justify-content: center;
}
#latest-offers .items {
  margin: 20px 0;
}
#latest-offers .no-results-message {
  margin: 80px 0;
  text-align: center;
  font-size: 18px;
}
#latest-offers .spinner {
  margin: 80px 0;
}
#latest-offers .center-div {
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}
#latest-offers .job-item {
  margin: 10px;
  border-radius: 12px;
  padding: 15px !important;
  background-color: #dbe8f6;
  display: flex;
}
#latest-offers .job-item .left-section {
  width: 60%;
  justify-content: space-between;
}
#latest-offers .job-item .right-section {
  width: 40%;
  display: "flex";
  flex-direction: "column";
  align-items: "flex-end";
  justify-content: center;
}
#latest-offers .job-item .job-title {
  font-size: 18px;
  color: #234791;
  font-family: "Proxima-Nova-Semibold" !important;
}
#latest-offers .job-item .job-ref {
  font-size: 14px;
  color: #798ba3;
}
#latest-offers .job-item .job-industry {
  font-size: 12px;
  width: -moz-fit-content;
  width: fit-content;
  font-family: "Proxima-Nova-Semibold" !important;
  text-transform: capitalize;
  color: #234791;
  display: flex;
  align-items: center;
}
#latest-offers .job-item .job-industry.oil-gaz {
  color: #d69b19;
}
#latest-offers .job-item .job-industry.energy {
  color: #cc3233;
}
#latest-offers .job-item .job-industry.banking-insurance {
  color: #0b3051;
}
#latest-offers .job-item .job-industry.transport {
  color: #009966;
}
#latest-offers .job-item .job-industry.it-telecom {
  color: #743794;
}
#latest-offers .job-item .job-industry.other {
  color: #234791;
}
#latest-offers .job-item .job-industry.pharmaceutical {
  color: #2bbfad;
}
#latest-offers .job-item .btn.apply, #latest-offers .job-item .newsletter-table .MuiDataGrid-toolbarContainer button.apply, .newsletter-table .MuiDataGrid-toolbarContainer #latest-offers .job-item button.apply {
  padding: 8px 18px !important;
  margin-top: 10px;
  width: -moz-fit-content;
  width: fit-content;
}
#latest-offers .job-item .map-img {
  height: auto;
  margin: auto;
  max-height: 200px;
  max-width: -moz-fit-content;
  max-width: fit-content;
}

.map-home-page {
  color: transparent;
  width: 100px;
  height: 80px;
}

#filter-btns {
  display: flex;
  justify-content: center;
}
#filter-btns .btn, #filter-btns .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer #filter-btns button {
  margin: 10px;
}

#search-bar-opportunities, #search-bar-glossary, #search-bar-blogs {
  background-color: #dbe8f6;
  padding-top: 10px;
  padding-bottom: 25px;
}
#search-bar-opportunities .sub-heading, #search-bar-glossary .sub-heading, #search-bar-blogs .sub-heading {
  font-family: "Proxima-Nova-Medium" !important;
}
#search-bar-opportunities .container, #search-bar-glossary .container, #search-bar-blogs .container {
  padding: 0;
}
#search-bar-opportunities input, #search-bar-glossary input, #search-bar-blogs input {
  padding: 15px;
}
#search-bar-opportunities .MuiInputAdornment-root, #search-bar-glossary .MuiInputAdornment-root, #search-bar-blogs .MuiInputAdornment-root {
  margin-left: 10px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #search-bar-opportunities .btns-filter, #search-bar-glossary .btns-filter, #search-bar-blogs .btns-filter {
    flex-direction: row;
    display: flex;
    background-color: transparent !important;
    /* justify-content: center; */
    /* align-items: stretch; */
    /* flex-direction: column; */
  }
}
#search-bar-opportunities .filter-inputs, #search-bar-glossary .filter-inputs, #search-bar-blogs .filter-inputs {
  box-shadow: 0px 10px 40px rgba(10, 48, 82, 0.0784313725);
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #search-bar-opportunities .filter-inputs, #search-bar-glossary .filter-inputs, #search-bar-blogs .filter-inputs {
    box-shadow: none;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #search-bar-opportunities .filter-inputs .MuiGrid-item, #search-bar-glossary .filter-inputs .MuiGrid-item, #search-bar-blogs .filter-inputs .MuiGrid-item {
    margin: 10px 0;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #search-bar-opportunities .filter-inputs .MuiGrid-item:first-child, #search-bar-glossary .filter-inputs .MuiGrid-item:first-child, #search-bar-blogs .filter-inputs .MuiGrid-item:first-child,
  #search-bar-opportunities .filter-inputs .MuiGrid-item:nth-child(2),
  #search-bar-glossary .filter-inputs .MuiGrid-item:nth-child(2),
  #search-bar-blogs .filter-inputs .MuiGrid-item:nth-child(2) {
    border-right: 0;
    padding-right: 0;
  }
}
#search-bar-opportunities .MuiInputBase-root.Mui-focused::before, #search-bar-opportunities .MuiInputBase-root::after, #search-bar-glossary .MuiInputBase-root::after, #search-bar-blogs .MuiInputBase-root::after, #search-bar-opportunities .MuiInputBase-root:focus, #search-bar-glossary .MuiInputBase-root:focus, #search-bar-blogs .MuiInputBase-root:focus, #search-bar-opportunities .MuiInputBase-root::before, #search-bar-glossary .MuiInputBase-root::before, #search-bar-blogs .MuiInputBase-root::before, #search-bar-opportunities .MuiInputBase-root:hover, #search-bar-glossary .MuiInputBase-root:hover, #search-bar-blogs .MuiInputBase-root:hover,
#search-bar-opportunities .css-r5in8e-MuiInputBase-root-MuiInput-root.Mui-focused::before,
#search-bar-opportunities .css-r5in8e-MuiInputBase-root-MuiInput-root::after,
#search-bar-glossary .css-r5in8e-MuiInputBase-root-MuiInput-root::after,
#search-bar-blogs .css-r5in8e-MuiInputBase-root-MuiInput-root::after,
#search-bar-opportunities .css-r5in8e-MuiInputBase-root-MuiInput-root:focus,
#search-bar-glossary .css-r5in8e-MuiInputBase-root-MuiInput-root:focus,
#search-bar-blogs .css-r5in8e-MuiInputBase-root-MuiInput-root:focus,
#search-bar-opportunities .css-r5in8e-MuiInputBase-root-MuiInput-root::before,
#search-bar-glossary .css-r5in8e-MuiInputBase-root-MuiInput-root::before,
#search-bar-blogs .css-r5in8e-MuiInputBase-root-MuiInput-root::before,
#search-bar-opportunities .css-r5in8e-MuiInputBase-root-MuiInput-root:hover,
#search-bar-glossary .css-r5in8e-MuiInputBase-root-MuiInput-root:hover,
#search-bar-blogs .css-r5in8e-MuiInputBase-root-MuiInput-root:hover,
#search-bar-opportunities .css-10yf2i8-MuiInputBase-root-MuiInput-root.Mui-focused::before,
#search-bar-opportunities .css-10yf2i8-MuiInputBase-root-MuiInput-root::after,
#search-bar-glossary .css-10yf2i8-MuiInputBase-root-MuiInput-root::after,
#search-bar-blogs .css-10yf2i8-MuiInputBase-root-MuiInput-root::after,
#search-bar-opportunities .css-10yf2i8-MuiInputBase-root-MuiInput-root:focus,
#search-bar-glossary .css-10yf2i8-MuiInputBase-root-MuiInput-root:focus,
#search-bar-blogs .css-10yf2i8-MuiInputBase-root-MuiInput-root:focus,
#search-bar-opportunities .css-10yf2i8-MuiInputBase-root-MuiInput-root::before,
#search-bar-glossary .css-10yf2i8-MuiInputBase-root-MuiInput-root::before,
#search-bar-blogs .css-10yf2i8-MuiInputBase-root-MuiInput-root::before,
#search-bar-opportunities .css-10yf2i8-MuiInputBase-root-MuiInput-root:hover,
#search-bar-glossary .css-10yf2i8-MuiInputBase-root-MuiInput-root:hover,
#search-bar-blogs .css-10yf2i8-MuiInputBase-root-MuiInput-root:hover {
  border-bottom: 0;
}

.btns-filter {
  align-items: center;
  display: flex;
  justify-content: space-evenly;
}
.btns-filter.dashboard {
  background: transparent;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: row;
}
.btns-filter.dashboard .btn-filled {
  width: 80%;
  text-align: center;
  display: block;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .btns-filter {
    background-color: transparent !important;
    justify-content: center;
    align-items: stretch;
    flex-direction: column;
  }
}
.btns-filter .btn, .btns-filter .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer .btns-filter button {
  padding: 10px 16px !important;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .btns-filter .btn, .btns-filter .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer .btns-filter button {
    margin-bottom: 10px;
    display: flex;
    justify-content: center;
  }
}
.btns-filter .btn-search {
  padding: 10px 55px !important;
}
.btns-filter .btn-refresh svg {
  margin: 0 !important;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .btns-filter {
    margin-top: 10px;
    margin-right: 10px;
  }
}

.job-title {
  font-size: 18px;
  color: #234791;
  font-family: "Proxima-Nova-Semibold" !important;
}

.job-ref {
  font-size: 14px;
  color: #798ba3;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .job-ref {
    margin-bottom: 10px;
  }
}

.job-industry {
  font-size: 12px;
  width: -moz-fit-content;
  width: fit-content;
  font-family: "Proxima-Nova-Semibold" !important;
  text-transform: capitalize;
  color: #234791;
  display: flex;
  align-items: center;
}
.job-industry.oil-gaz {
  color: #d69b19;
}
.job-industry.energy {
  color: #cc3233;
}
.job-industry.banking-insurance {
  color: #0b3051;
}
.job-industry.transport {
  color: #009966;
}
.job-industry.it-telecom {
  color: #743794;
}
.job-industry.other {
  color: #234791;
}
.job-industry.pharmaceutical {
  color: #2bbfad;
}
.job-industry.banner {
  color: #ffffff !important;
  font-size: 14px;
  margin-left: 0 !important;
}
.job-industry.banner.oil-gaz {
  background-color: #d69b19;
  color: #ffffff;
}
.job-industry.banner.energy {
  background-color: #cc3233;
  color: #ffffff;
}
.job-industry.banner.banking-insurance {
  background-color: #0b3051;
  color: #ffffff;
}
.job-industry.banner.transport {
  background-color: #009966;
  color: #ffffff;
}
.job-industry.banner.it-telecom {
  background-color: #743794;
  color: #ffffff;
}
.job-industry.banner.other {
  background-color: #234791;
  color: #ffffff;
}
.job-industry.banner.pharmaceutical {
  background-color: #2bbfad;
  color: #ffffff;
}
.job-industry.border {
  margin-left: 10px;
  padding: 1px 10px;
  border-radius: 20px;
  border: 1px solid;
}
.job-industry.border.oil-gaz {
  border-color: #d69b19;
}
.job-industry.border.energy {
  border-color: #cc3233;
}
.job-industry.border.banking-insurance {
  border-color: #0b3051;
}
.job-industry.border.transport {
  border-color: #009966;
}
.job-industry.border.it-telecom {
  border-color: #743794;
}
.job-industry.border.other {
  border-color: #234791;
}
.job-industry.border.pharmaceutical {
  border-color: #2bbfad;
}

#opportunities, #one-opportunity-page .related-opportunities {
  margin: 50px 0;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #opportunities, #one-opportunity-page .related-opportunities {
    margin: 20px 0;
  }
}
#opportunities .items, #one-opportunity-page .related-opportunities .items {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #opportunities .items, #one-opportunity-page .related-opportunities .items {
    margin: auto;
  }
}
#opportunities .grid, #one-opportunity-page .related-opportunities .grid {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-content: flex-start;
  justify-content: space-between;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #opportunities .grid, #one-opportunity-page .related-opportunities .grid {
    margin-top: 20px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #opportunities .grid, #one-opportunity-page .related-opportunities .grid {
    margin-top: 20px;
  }
}
#opportunities .sub-heading, #one-opportunity-page .related-opportunities .sub-heading {
  font-family: "Proxima-Nova-Medium" !important;
}

.opportunities-nbr {
  margin-left: 10px;
  background-color: #ffca00;
  padding: 2px 4px;
}

.button-pointer {
  display: contents;
}

.opportunity-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0px 10px 40px rgba(10, 48, 82, 0.0784313725);
  padding: 30px;
  max-width: 100%;
  margin: 10px auto;
}
.opportunity-item .apply {
  font-size: 16px;
  padding: 10px 15px;
  cursor: pointer;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .opportunity-item .apply {
    margin-top: 15px;
  }
}
.opportunity-item .item-image {
  padding-right: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .opportunity-item .item-image.opportunity-apply {
    margin-top: 20px;
  }
}
.opportunity-item .item-image .map-img {
  width: "110px";
  height: "70px";
  margin: auto;
  max-height: "64px";
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .opportunity-item .item-image .map-img {
    text-align: center;
    display: flex;
    max-height: 108px;
  }
}
.opportunity-item .margin-section-item {
  margin-bottom: 12px;
}
.opportunity-item .job-ref {
  font-size: 14px;
  padding: 2px;
  color: #798ba3;
}
.opportunity-item .location {
  border: 1px solid #234791;
  border-radius: 20px;
  padding: 2px 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: -moz-fit-content;
  width: fit-content;
  margin-left: 10px;
  text-decoration: none;
}
.opportunity-item .location .location-text {
  font-size: 12px;
  margin-left: 5px;
  color: #234791;
}
.opportunity-item .job-description {
  font-size: 14px;
  color: #234791;
  max-width: 80%;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .opportunity-item .job-description {
    max-width: 100%;
  }
}
.opportunity-item .flex {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .opportunity-item .flex {
    margin-bottom: 5px;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .opportunity-item .flex.mobile-col {
    flex-direction: column;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .opportunity-item .flex.mobile-col .border {
    margin-top: 5px;
    margin-left: 0;
  }
}
.opportunity-item .flex.row {
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: space-between;
}
.opportunity-item .item-content,
.opportunity-item .item-btns {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .opportunity-item .item-content.opportunity-apply,
  .opportunity-item .item-btns.opportunity-apply {
    margin-top: 20px;
  }
}
.opportunity-item .job-contract {
  font-size: 12px;
  display: flex;
  align-items: center;
  background: rgba(61, 184, 78, 0.1);
  padding: 5px 15px;
  color: #3db84e;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .opportunity-item .job-contract {
    margin-bottom: 10px;
  }
}
.opportunity-item .job-contract svg {
  margin-right: 5px;
}
.opportunity-item .job-contract svg path {
  fill: #3db84e;
}
.opportunity-item .job-time {
  margin-left: 10px;
  font-size: 12px;
  display: flex;
  align-items: center;
  background: rgba(29, 90, 159, 0.18);
  padding: 5px 15px;
  color: #1d5a9f;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .opportunity-item .job-time {
    margin-left: 0px;
  }
}
.opportunity-item .job-time svg {
  margin-right: 5px;
}
.opportunity-item .job-time svg path {
  fill: #1d5a9f;
}
.opportunity-item .job-time-apply,
.opportunity-item .job-contract-apply {
  font-size: 14px;
  display: flex;
  align-items: center;
  background: "transparent";
  padding: 2px;
  margin-right: 10px;
  color: #798ba3;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .opportunity-item .job-time-apply,
  .opportunity-item .job-contract-apply {
    margin-bottom: 10px;
    margin-left: 0px !important;
  }
}
.opportunity-item .job-time-apply svg,
.opportunity-item .job-contract-apply svg {
  margin-right: 5px;
}
.opportunity-item .job-time-apply svg path,
.opportunity-item .job-contract-apply svg path {
  fill: #798ba3;
}
.opportunity-item .job-deadline {
  margin-left: 10px;
  font-size: 12px;
  display: flex;
  align-items: center;
  background: rgba(252, 1, 1, 0.16);
  padding: 5px 15px;
  color: #fc0103;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .opportunity-item .job-deadline {
    margin-left: 0px;
  }
}
.opportunity-item .job-deadline svg {
  margin-right: 5px;
}
.opportunity-item .job-deadline svg path {
  fill: #fc0103;
}
.opportunity-item .item-btns {
  align-items: flex-end;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .opportunity-item .item-btns {
    flex-direction: row-reverse;
    justify-content: space-between;
  }
}
.opportunity-item .btn-outlined {
  padding: 10px 15px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .opportunity-item .btn-outlined {
    text-align: center;
    display: flex;
    justify-content: center;
    width: -webkit-fill-available;
  }
}
.opportunity-item .bookmark {
  padding: 0px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .opportunity-item .bookmark {
    padding: 5px;
  }
}

.opportunity-grid-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0px 10px 40px rgba(10, 48, 82, 0.0784313725);
  padding: 15px;
  max-width: 49%;
  margin: 10px 0;
}
.opportunity-grid-item.website {
  max-width: 40%;
  margin: 10px;
  background: #dbe8f6;
  box-shadow: none;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .opportunity-grid-item.website {
    max-width: 100%;
    align-items: flex-start;
    margin: 10px 30px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .opportunity-grid-item.website {
    max-width: 100%;
    align-items: flex-start;
    margin: 10px 30px;
  }
}
@media only screen and (min-width: 769px) and (max-width: 1024px) {
  .opportunity-grid-item.website {
    max-width: 100%;
    align-items: flex-start;
    margin: 10px 30px;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .opportunity-grid-item {
    max-width: 100%;
    align-items: flex-start;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .opportunity-grid-item {
    max-width: 100%;
    align-items: flex-start;
  }
}
@media only screen and (min-width: 769px) and (max-width: 1024px) {
  .opportunity-grid-item {
    max-width: 100%;
    align-items: flex-start;
  }
}
.opportunity-grid-item .apply {
  font-size: 14px;
  padding: 5px 15px;
  cursor: pointer;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .opportunity-grid-item .apply {
    font-size: 16px;
    margin-top: 0px;
  }
}
.opportunity-grid-item .item-image {
  padding-right: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .opportunity-grid-item .item-image {
    justify-content: flex-start;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .opportunity-grid-item .item-image {
    justify-content: flex-start;
    padding-left: 15px;
  }
}
.opportunity-grid-item .item-image .map-img {
  width: "110px";
  height: "70px";
  margin: auto;
  max-height: "64px";
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .opportunity-grid-item .item-image .map-img {
    text-align: center;
    display: flex;
    max-height: 108px;
  }
}
.opportunity-grid-item .margin-section-item {
  margin-bottom: 12px;
}
.opportunity-grid-item .job-ref {
  font-size: 14px;
  padding: 2px;
  color: #798ba3;
}
.opportunity-grid-item .location {
  border: 1px solid #234791;
  border-radius: 20px;
  padding: 2px 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: -moz-fit-content;
  width: fit-content;
  text-decoration: none;
}
.opportunity-grid-item .location .location-text {
  font-size: 14px;
  margin-left: 5px;
  color: #234791;
}
.opportunity-grid-item .job-description {
  font-size: 14px;
  color: #234791;
  max-width: 80%;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .opportunity-grid-item .job-description {
    max-width: 100%;
  }
}
.opportunity-grid-item .flex {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .opportunity-grid-item .flex {
    justify-content: space-between;
    margin-bottom: 0px;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .opportunity-grid-item .flex.contract {
    margin-top: 5px;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .opportunity-grid-item .flex.mobile-col {
    flex-direction: column;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .opportunity-grid-item .flex.mobile-col .border {
    margin-top: 5px;
    margin-left: 0;
  }
}
.opportunity-grid-item .flex.row {
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: space-between;
  align-items: center;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .opportunity-grid-item .flex.row {
    align-items: flex-start;
  }
}
.opportunity-grid-item .item-content,
.opportunity-grid-item .item-btns {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .opportunity-grid-item .item-apply {
    display: flex;
    justify-content: space-between;
  }
}
.opportunity-grid-item .job-contract,
.opportunity-grid-item .job-deadline {
  font-size: 14px;
  display: flex;
  align-items: center;
  background: "transparent";
  padding: 2px;
  margin-left: 10px;
  color: #798ba3;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .opportunity-grid-item .job-contract,
  .opportunity-grid-item .job-deadline {
    margin-bottom: 10px;
    margin-left: 0px !important;
  }
}
.opportunity-grid-item .job-contract svg,
.opportunity-grid-item .job-deadline svg {
  margin-right: 5px;
}
.opportunity-grid-item .job-contract svg path,
.opportunity-grid-item .job-deadline svg path {
  fill: #798ba3;
}
.opportunity-grid-item .item-btns {
  align-items: flex-end;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .opportunity-grid-item .item-btns {
    flex-direction: row-reverse;
    justify-content: space-between;
  }
}
.opportunity-grid-item .btn-outlined {
  padding: 5px 15px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .opportunity-grid-item .btn-outlined {
    text-align: center;
    display: flex;
    justify-content: center;
    width: -webkit-fill-available;
    padding: 15px;
  }
}
.opportunity-grid-item .bookmark {
  padding: 0px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .opportunity-grid-item .bookmark {
    padding: 0px;
  }
}

.sidebar .section {
  background-color: #e5f0fc;
  padding: 15px;
  margin-bottom: 20px;
}
.sidebar .section .title {
  font-size: 20px;
  font-family: "Proxima-Nova-Semibold" !important;
  color: #0b3051;
  margin: 10px 0;
}
.sidebar .section .description {
  color: #0b3051;
  margin-bottom: 10px;
  font-size: 16px;
}
.sidebar .section .btn, .sidebar .section .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer .sidebar .section button {
  margin-top: 20px;
  width: -webkit-fill-available;
  text-align: center;
  display: flex;
  justify-content: center;
}
.sidebar .section .last-blog {
  position: relative;
}
.sidebar .section .last-blog::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to top, #0b3051, rgba(11, 48, 81, 0));
  opacity: 0.6;
  pointer-events: none;
  /* Ensure gradient doesn't interfere with clicks */
}
.sidebar .section .last-blog .card-media {
  height: 185px;
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .sidebar .section .last-blog .card-media {
    height: 136px;
  }
}
.sidebar .section .last-blog .card-text {
  color: #ffffff;
  position: absolute;
  bottom: 10px;
  left: 10px;
  font-size: 16px;
  padding: 0px 2px;
  font-family: "Proxima-Nova-Semibold" !important;
}
.sidebar .section .last-blog .card-text button {
  margin: 0;
  padding: 10px 0px;
  justify-content: left;
}
.sidebar .section .last-blog .card-text a {
  margin: 0;
  padding: 10px 0px;
  justify-content: left;
}

#one-opportunity-page .container {
  margin-top: 20px;
}
#one-opportunity-page .job-info {
  display: flex;
  flex-direction: row;
  margin: 10px 0;
}
#one-opportunity-page #banner-job-info {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
}
#one-opportunity-page #banner-job-info .job-info {
  margin: 4px 20px 4px 0 !important;
}
#one-opportunity-page #banner-job-info .job-info a {
  text-decoration: none;
}
#one-opportunity-page .children-componenent p,
#one-opportunity-page #banner-job-info p {
  color: #ffffff;
  font-size: 18px;
}
#one-opportunity-page .children-componenent svg,
#one-opportunity-page #banner-job-info svg {
  margin-right: 8px;
  transform: scale(1.4);
  margin: auto 8px auto 0;
}
#one-opportunity-page .children-componenent svg path,
#one-opportunity-page #banner-job-info svg path {
  fill: #ffffff;
}
#one-opportunity-page .flex {
  display: flex;
  flex-direction: column;
}
#one-opportunity-page .flex-apply {
  display: flex;
  flex-direction: column;
}
#one-opportunity-page .related-opportunities {
  margin-top: 20px !important;
  margin-bottom: 20px !important;
}
#one-opportunity-page .opportunity-content {
  padding: 15px;
  box-shadow: 0px 10px 40px rgba(10, 48, 82, 0.0784313725);
}
#one-opportunity-page .opportunity-content p,
#one-opportunity-page .opportunity-content ul,
#one-opportunity-page .opportunity-content div,
#one-opportunity-page .opportunity-content li {
  background: rgba(228, 239, 252, 0.7) !important;
}
#one-opportunity-page .apply-section {
  margin-top: 20px;
  box-shadow: 0px 10px 40px rgba(10, 48, 82, 0.0784313725);
  background-color: #234791;
  padding: 20px;
}
#one-opportunity-page .apply-section > div:first-child {
  margin: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  width: 60%;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #one-opportunity-page .apply-section > div:first-child {
    width: 100%;
  }
}
#one-opportunity-page .apply-section .btns {
  flex-direction: row;
  display: flex;
}
#one-opportunity-page .apply-section .btns .btn, #one-opportunity-page .apply-section .btns .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer #one-opportunity-page .apply-section .btns button {
  margin: auto 10px;
}
#one-opportunity-page .opportunity-info {
  margin-bottom: 20px;
  background: linear-gradient(to bottom, #0b3051, #234791);
  padding: 15px;
  color: #ffffff;
}
#one-opportunity-page .opportunity-info .title {
  margin-top: 10px !important;
  font-size: 24px;
  font-family: "Proxima-Nova-Semibold" !important;
}
#one-opportunity-page .opportunity-info .job-ref {
  font-size: 20px;
  color: #ffffff;
  margin-bottom: 20px;
  margin-top: 10px;
}
#one-opportunity-page .job-btns {
  display: flex;
  width: 100%;
  justify-content: space-between;
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #one-opportunity-page .job-btns {
    flex-wrap: wrap;
  }
}
#one-opportunity-page .job-btns .btn, #one-opportunity-page .job-btns .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer #one-opportunity-page .job-btns button {
  margin: 8px 0;
  width: -webkit-fill-available;
  text-align: center;
  display: flex;
  justify-content: center;
}
#one-opportunity-page .job-btns .btn:first-child, #one-opportunity-page .job-btns .newsletter-table .MuiDataGrid-toolbarContainer button:first-child, .newsletter-table .MuiDataGrid-toolbarContainer #one-opportunity-page .job-btns button:first-child {
  margin-right: 8px;
}
#one-opportunity-page .job-btns .btn:last-child, #one-opportunity-page .job-btns .newsletter-table .MuiDataGrid-toolbarContainer button:last-child, .newsletter-table .MuiDataGrid-toolbarContainer #one-opportunity-page .job-btns button:last-child {
  margin-left: 8px;
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #one-opportunity-page .job-btns .btn, #one-opportunity-page .job-btns .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer #one-opportunity-page .job-btns button {
    margin: 8px 0 !important;
  }
}
#one-opportunity-page .job-btns .btn.apply, #one-opportunity-page .job-btns .newsletter-table .MuiDataGrid-toolbarContainer button.apply, .newsletter-table .MuiDataGrid-toolbarContainer #one-opportunity-page .job-btns button.apply {
  margin: 8px 0;
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #one-opportunity-page .job-btns .btn.apply, #one-opportunity-page .job-btns .newsletter-table .MuiDataGrid-toolbarContainer button.apply, .newsletter-table .MuiDataGrid-toolbarContainer #one-opportunity-page .job-btns button.apply {
    margin: 8px 0 !important;
  }
}

#last-opportunities-inhouse-section {
  padding-top: 50px;
  padding-bottom: 50px;
}
#last-opportunities-inhouse-section .heading-h1 {
  margin-bottom: 50px !important;
}
#last-opportunities-inhouse-section .view-more {
  margin: 30px auto auto auto !important;
  width: -moz-fit-content;
  width: fit-content;
}

.flex-apply {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .flex-apply {
    margin-bottom: 5px;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .flex-apply.mobile-col {
    flex-direction: column;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .flex-apply.mobile-col .border {
    margin-top: 5px;
    margin-left: 0;
  }
}
.flex-apply.row {
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: space-between;
}

.flex-item {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 5px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .flex-item {
    margin-bottom: 5px;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .flex-item.mobile-col {
    flex-direction: column;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .flex-item.mobile-col .border {
    margin-top: 5px;
    margin-left: 0;
  }
}
.flex-item.row {
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: space-between;
}

@media only screen and (min-width: 480px) and (max-width: 768px) {
  .job-contrat-time {
    display: flex;
    flex-direction: column;
    align-content: stretch;
    flex-wrap: wrap;
    align-items: baseline;
    align-items: flex-start;
  }
  .job-time {
    margin-top: 10px;
    margin-left: 0px !important;
  }
  .map-img {
    max-height: 102;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .job-contrat-time {
    display: flex;
    flex-direction: column;
    align-content: stretch;
    flex-wrap: wrap;
    align-items: baseline;
    align-items: flex-start;
  }
  .job-time {
    margin-top: 10px;
    margin-left: 0px !important;
  }
  .map-img {
    max-height: 102;
  }
}
.job-contrat-time {
  display: flex;
  flex-wrap: wrap;
  align-content: stretch;
  justify-content: flex-start;
}
.filter-options {
  display: flex;
  flex-direction: column;
}

.filter-popup-content {
  display: flex;
  border: 1px;
  box-shadow: 0px 10px 40px rgba(10, 48, 82, 0.0784313725);
  /* padding: 10px; */
  margin: 10px 4px 0px 4px;
  flex-direction: column;
  height: auto;
  overflow: auto;
  /* scroll-behavior: smooth; */
  scrollbar-width: none;
}
.filter-popup-content .css-7iurs7-MuiAccordionSummary-content {
  margin: 0px 0 !important;
}

.title-filter {
  font-size: 18px;
  color: #0b3051;
  font-family: "Proxima-Nova-Semibold" !important;
}

.label-filter {
  font-size: 16px;
  color: #798ba3;
  font-family: "Proxima-Nova-Medium" !important;
}

.chip {
  background-color: transparent;
  border-color: #1d5a9f !important;
  padding: 8px 20px !important;
  border: 1px solid;
  margin-top: 10px !important;
}
.chip .css-1dybbl5-MuiChip-label {
  color: #1d5a9f !important;
  font-family: "Proxima-Nova-Medium" !important;
  font-size: 14px;
}

.display {
  display: flex;
  justify-content: space-between;
}

.titre {
  margin-right: 26%;
}

.opportunity-chip {
  display: flex;
  flex-direction: column;
}

.grid-list-buttons {
  display: flex;
  width: 70px;
  justify-content: space-between;
  align-items: center;
}
.grid-list-buttons button {
  padding: 0px;
}
.grid-list-buttons button.active svg path {
  fill: #ffca00;
}
.grid-list-buttons svg:hover path {
  fill: #ffca00;
}

#filter-actions .filter-actions {
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
  padding: 10px;
}
#filter-actions .filter-actions .btn, #filter-actions .filter-actions .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer #filter-actions .filter-actions button {
  padding: 10px 35px !important;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #filter-actions .filter-actions {
    flex-direction: column;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) and (min-width: 320px) and (max-width: 480px) {
  #filter-actions .filter-actions .btn, #filter-actions .filter-actions .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer #filter-actions .filter-actions button {
    margin-bottom: 10px;
    display: flex;
    padding: 10px;
    justify-content: center;
  }
}
#filter-actions .css-1ll44ll-MuiOutlinedInput-notchedOutline {
  box-shadow: 0px 10px 40px rgba(10, 48, 82, 0.0784313725) !important;
  border-color: transparent !important;
}

.MuiCheckbox-root {
  z-index: 1;
}

#guide-item .blog-description {
  margin-bottom: 20px !important;
}

#connect-social-media .top-section {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin: 20px 0;
}
#connect-social-media .top-section .title {
  text-wrap: nowrap;
  margin: 0 20px;
  text-align: center;
  font-family: "Proxima-Nova-Medium" !important;
  font-weight: 500;
  font-size: 16px;
  color: #798ba3;
}
#connect-social-media .top-section .line {
  content: "";
  width: 100%;
  border-bottom: 1px solid #798ba3;
}
#connect-social-media .btns-section {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #connect-social-media .btns-section {
    display: flex;
    flex-direction: column;
    align-items: stretch;
  }
}
#connect-social-media .btns-section .btn, #connect-social-media .btns-section .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer #connect-social-media .btns-section button {
  text-align: center;
}
#connect-social-media .bottom-section {
  display: flex;
  flex-direction: row;
  align-items: center;
  text-align: center;
  justify-content: center;
  margin-top: 30px;
  color: #ffffff;
  font-size: 16px;
}
#connect-social-media .bottom-section .btn, #connect-social-media .bottom-section .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer #connect-social-media .bottom-section button {
  margin-left: 8px;
  padding: 0;
}

#banner-component, #glossary-banner, #join-us-banner, #our-industries {
  background-color: rgba(228, 239, 252, 0.7);
  min-height: 50vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}
#banner-component .heading-h1, #glossary-banner .heading-h1, #join-us-banner .heading-h1, #our-industries .heading-h1,
#banner-component .sub-heading,
#glossary-banner .sub-heading,
#join-us-banner .sub-heading,
#our-industries .sub-heading {
  width: 70%;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #banner-component .heading-h1, #glossary-banner .heading-h1, #join-us-banner .heading-h1, #our-industries .heading-h1,
  #banner-component .sub-heading,
  #glossary-banner .sub-heading,
  #join-us-banner .sub-heading,
  #our-industries .sub-heading {
    width: 100%;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #banner-component .heading-h1, #glossary-banner .heading-h1, #join-us-banner .heading-h1, #our-industries .heading-h1,
  #banner-component .sub-heading,
  #glossary-banner .sub-heading,
  #join-us-banner .sub-heading,
  #our-industries .sub-heading {
    width: 100%;
  }
}
#banner-component.center-banner, .center-banner#glossary-banner, .center-banner#join-us-banner, .center-banner#our-industries {
  text-align: center;
}
#banner-component.center-banner .heading-h1, .center-banner#glossary-banner .heading-h1, .center-banner#join-us-banner .heading-h1, .center-banner#our-industries .heading-h1,
#banner-component.center-banner .sub-heading,
.center-banner#glossary-banner .sub-heading,
.center-banner#join-us-banner .sub-heading,
.center-banner#our-industries .sub-heading {
  margin: auto;
}
#banner-component.expert-care-banner, .expert-care-banner#glossary-banner, .expert-care-banner#join-us-banner, .expert-care-banner#our-industries {
  align-items: flex-end;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #banner-component.expert-care-banner, .expert-care-banner#glossary-banner, .expert-care-banner#join-us-banner, .expert-care-banner#our-industries {
    text-align: center;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #banner-component.expert-care-banner .btn, #banner-component.expert-care-banner .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer #banner-component.expert-care-banner button, .expert-care-banner#glossary-banner .btn, .expert-care-banner#glossary-banner .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer .expert-care-banner#glossary-banner button, .expert-care-banner#join-us-banner .btn, .expert-care-banner#join-us-banner .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer .expert-care-banner#join-us-banner button, .expert-care-banner#our-industries .btn, .expert-care-banner#our-industries .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer .expert-care-banner#our-industries button {
    margin: auto;
  }
}
#banner-component.expert-care-banner .sub-heading, .expert-care-banner#glossary-banner .sub-heading, .expert-care-banner#join-us-banner .sub-heading, .expert-care-banner#our-industries .sub-heading {
  width: 100%;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #banner-component.expert-care-banner .sub-heading, .expert-care-banner#glossary-banner .sub-heading, .expert-care-banner#join-us-banner .sub-heading, .expert-care-banner#our-industries .sub-heading {
    display: none;
  }
}

#join-us-banner {
  flex-direction: column;
}
#join-us-banner .top-section {
  margin-top: 150px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #join-us-banner .top-section {
    margin-top: 100px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #join-us-banner .top-section {
    margin-top: 100px;
  }
}
#join-us-banner .btn, #join-us-banner .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer #join-us-banner button {
  width: -moz-fit-content;
  width: fit-content;
  margin: 10px auto;
}
#join-us-banner .avatars {
  overflow: hidden;
  display: flex;
  flex-direction: column;
  width: 100%;
  margin: auto auto 20px auto;
  min-height: 80px;
  justify-content: flex-end;
  background-repeat: repeat-x;
  background-position: 0 0;
  background-size: auto 100%;
  height: 80px;
  animation: moveBackground 10s linear infinite;
}
#join-us-banner .avatars.last-one {
  margin-top: 0 !important;
  animation: moveBackgroundLeft 10s linear infinite;
}
@keyframes moveBackgroundLeft {
  from {
    background-position-x: 0;
  }
  to {
    background-position-x: 100%; /* Negative value to create continuous scroll */
  }
}
@keyframes moveBackground {
  from {
    background-position-x: 0;
  }
  to {
    background-position-x: -100%; /* Negative value to create continuous scroll */
  }
}

#glossary-banner {
  flex-direction: column;
}
#glossary-banner .top-section {
  margin-bottom: 260px;
}
@media only screen and (min-width: 1601px) {
  #glossary-banner .top-section {
    margin-bottom: 250px;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #glossary-banner .top-section {
    margin-top: 75px;
  }
  #glossary-banner .top-section .page-title {
    margin-bottom: 20px !important;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #glossary-banner .top-section {
    margin-top: 30px;
  }
}
#glossary-banner .top-section svg {
  scale: 0.8;
}
#glossary-banner .top-section .glossary-search {
  display: ruby;
}
#glossary-banner .top-section .glossary-search-input {
  background: white;
  border-radius: 25px;
  height: 50px;
  width: 100%;
  max-width: 650px;
  padding: 4px 20px;
  margin: auto;
}
#glossary-banner .top-section .glossary-search-input:before {
  border-bottom: none;
}
#glossary-banner .top-section .glossary-search-input:after {
  border-bottom: none;
}
#glossary-banner .top-section .letters {
  display: flex;
  justify-self: center;
  margin-top: 20px;
  flex-wrap: wrap;
}
#glossary-banner .top-section .letters .letter {
  margin: 0px 6px;
  font-size: 20px;
  text-decoration: none;
  color: #ffffff;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #glossary-banner .top-section .letters .letter {
    font-size: 18px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #glossary-banner .top-section .letters .letter {
    font-size: 18px;
  }
}
#glossary-banner .top-section .letters .letter.selected {
  color: #ffca00;
}
#glossary-banner .top-section .btn-search {
  background-color: #234791;
  padding: 10px 0px;
  position: absolute;
  margin: 6px -55px 0px;
  border-radius: 20px;
  min-width: 40px;
}
#glossary-banner .top-section .btn-search svg {
  scale: 1;
}
#glossary-banner .top-section .btn-search svg path {
  stroke: #ffffff;
}
#glossary-banner .btn, #glossary-banner .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer #glossary-banner button {
  width: -moz-fit-content;
  width: fit-content;
  margin: 10px auto;
}

#africa-banner {
  background-color: rgba(228, 239, 252, 0.7);
  min-height: 50vh;
  display: flex;
  justify-content: center;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}
#africa-banner .container {
  flex-direction: row;
  display: flex;
  align-items: center;
  justify-content: space-between;
  align-content: center;
  height: 100%;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #africa-banner .container {
    align-content: flex-end;
  }
}
#africa-banner .right-section {
  padding: 30px 20px;
  background-color: rgba(11, 48, 81, 0.68);
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #africa-banner .right-section {
    margin-top: 20px;
    margin-bottom: 20px;
    padding: 16px;
  }
}
#africa-banner .right-section .bold {
  margin-top: 20px;
  font-family: "Proxima-Nova-Semibold" !important;
}
#africa-banner .right-section .btns {
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
#africa-banner .right-section .btns .btn, #africa-banner .right-section .btns .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer #africa-banner .right-section .btns button {
  font-family: "Proxima-Nova-Semibold" !important;
  text-transform: none;
  padding: 12px;
}
#africa-banner .right-section .btns .btn:first-child, #africa-banner .right-section .btns .newsletter-table .MuiDataGrid-toolbarContainer button:first-child, .newsletter-table .MuiDataGrid-toolbarContainer #africa-banner .right-section .btns button:first-child {
  margin-right: 10px;
}
#africa-banner .right-section .btns .btn:last-child, #africa-banner .right-section .btns .newsletter-table .MuiDataGrid-toolbarContainer button:last-child, .newsletter-table .MuiDataGrid-toolbarContainer #africa-banner .right-section .btns button:last-child {
  margin-left: 10px;
}

#expert-care-benefits .grid-item .sub-heading {
  display: flex;
  align-items: center;
  flex-direction: row;
}
#expert-care-benefits .grid-item svg {
  margin-right: 10px;
}
#expert-care-benefits .grid-item svg path {
  stroke: green;
}

#service-page-form {
  padding: 50px 0;
  background: linear-gradient(#0b3051 0%, #234791 100%);
}
#service-page-form .flex-end {
  display: flex;
  justify-content: flex-end;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #service-page-form .flex-end {
    justify-content: center;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #service-page-form .flex-end .btn, #service-page-form .flex-end .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer #service-page-form .flex-end button {
    width: 100%;
    display: flex;
    justify-content: center;
  }
}

.flex-row-center {
  display: flex;
  flex-direction: row !important;
  align-items: center;
}
.flex-row-center .label-pentabell {
  margin-right: 40px;
}

.custom-file-upload {
  border: 1px solid #699bd4;
  padding: 20px;
  text-align: center;
  cursor: pointer;
}
.custom-file-upload .spinner {
  width: 50px;
  height: 50px;
  margin: 50px auto;
}
.custom-file-upload > div {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.custom-file-upload .btn, .custom-file-upload .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer .custom-file-upload button {
  margin: auto;
  cursor: pointer;
  margin-bottom: 10px;
}
.custom-file-upload .sub-label {
  color: #798ba3;
  margin-bottom: 15px;
}

.custom-file-upload-wbg {
  border: 1px solid #798ba3;
  padding: 20px;
  text-align: center;
  cursor: pointer;
}
.custom-file-upload-wbg:hover {
  border: 1px solid #172541;
}
.custom-file-upload-wbg .spinner {
  width: 50px;
  height: 50px;
  margin: 50px auto;
}
.custom-file-upload-wbg > div {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.custom-file-upload-wbg .btn, .custom-file-upload-wbg .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer .custom-file-upload-wbg button {
  margin: auto;
  cursor: pointer;
  margin-bottom: 10px;
  border: 1px solid #798ba3;
  color: #234791;
}
.custom-file-upload-wbg .sub-label {
  color: #798ba3;
  margin-bottom: 15px;
}
.custom-file-upload-wbg .label-pentabell {
  color: #234791 !important;
}

#search-bar-blogs .btn, #search-bar-blogs .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer #search-bar-blogs button {
  margin: 0 5px;
}
#search-bar-blogs .btn svg, #search-bar-blogs .newsletter-table .MuiDataGrid-toolbarContainer button svg, .newsletter-table .MuiDataGrid-toolbarContainer #search-bar-blogs button svg {
  margin: 0 !important;
}

.blog-content p,
.blog-content span {
  font-size: 18px !important;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .blog-content p,
  .blog-content span {
    font-size: 17px !important;
  }
}
.blog-content h2 {
  font-size: 26px !important;
  color: #234791;
}
.blog-content h3 {
  font-size: 22px !important;
  color: rgb(73, 69, 69);
}
.blog-content h4 {
  font-size: 20;
  color: rgb(112, 108, 108);
}
.blog-content ul {
  list-style: none;
  padding-left: 1em;
}
.blog-content ul > li::before {
  content: "";
  display: inline-block;
  width: 12px;
  height: 12px;
  background-color: #ffca00;
  margin-right: 0.5em;
}
.blog-content p,
.blog-content ul,
.blog-content div,
.blog-content span,
.blog-content li {
  background: transparent !important;
  font-size: 18px;
}
.blog-content figure {
  width: 100% !important;
  margin: 10px 0;
  height: auto;
  min-width: 300px;
  border: 1px solid #699bd4;
  background-color: transparent;
}
.blog-content figure img {
  width: -webkit-fill-available !important;
  height: auto !important;
  padding: 10px;
}
.blog-content img {
  width: -webkit-fill-available !important;
  height: auto;
}
.blog-content table {
  width: -webkit-fill-available !important;
}
.blog-content .summary {
  margin: 15px 0;
}
.blog-content .summary .dropdown-header {
  display: flex;
  align-items: center;
}
.blog-content .summary svg path {
  fill: #234791;
}
.blog-content button {
  display: inline-block;
  background: linear-gradient(to right, #5ce6e6, #0052cc);
  color: white;
  padding: 17px 24px;
  border-radius: 999px;
  text-decoration: none;
  font-family: Arial, sans-serif;
  font-size: 14px;
  font-weight: bold;
  transition: background 0.3s ease;
  border: none;
}
.blog-content button:hover {
  opacity: 0.9;
}

#blogs-page {
  padding-bottom: 50px;
}
#blogs-page .pagination {
  margin-top: 20px;
}
#blogs-page .featured-title {
  font-size: 2.5rem;
  font-family: "Proxima-Nova-SemiBold" !important;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #blogs-page .featured-title {
    font-size: 1.75rem !important;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #blogs-page .featured-title {
    font-size: 1.75rem !important;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #blogs-page {
    padding-bottom: 20px;
  }
}
#blogs-page .btn.white, #blogs-page .newsletter-table .MuiDataGrid-toolbarContainer button.white, .newsletter-table .MuiDataGrid-toolbarContainer #blogs-page button.white {
  color: #ffffff;
  border-color: #ffffff;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #blogs-page .btn.white, #blogs-page .newsletter-table .MuiDataGrid-toolbarContainer button.white, .newsletter-table .MuiDataGrid-toolbarContainer #blogs-page button.white {
    width: auto;
    display: flex;
    justify-content: center;
  }
}
#blogs-page .first-blog {
  display: flex;
  justify-content: space-between;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #blogs-page .first-blog {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    flex-direction: column-reverse;
    align-content: flex-start;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #blogs-page .first-blog {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    flex-direction: column-reverse;
    align-content: flex-start;
  }
}
#blogs-page .blog-img-section {
  border: 1px solid #699bd4;
  background-color: transparent;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: auto;
  min-width: 675px;
  box-shadow: none;
  position: relative;
  margin: 10px 0px 20px 30px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #blogs-page .blog-img-section {
    min-width: 200px;
    margin: 50px 0px 20px 0px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #blogs-page .blog-img-section {
    min-width: 200px;
    margin: 50px 0px 20px 0px;
  }
}
#blogs-page .blog-img-section .img-section {
  background-size: cover;
  background-position: center;
  padding: 100px;
  margin: 15px;
  background-color: #dbe8f6;
  height: 100%;
  min-height: 150px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #blogs-page .blog-img-section .img-section {
    min-height: 100px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #blogs-page .blog-img-section .img-section {
    min-height: 100px;
  }
}
#blogs-page .last-blog {
  min-height: 220px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  margin-bottom: 20px;
}
#blogs-page .last-blog .description {
  font-size: 18px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #blogs-page .last-blog .description {
    font-size: 16px;
  }
}
#blogs-page .last-blog .title,
#blogs-page .last-blog .description {
  color: #234791;
  margin-bottom: 20px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #blogs-page .last-blog .title,
  #blogs-page .last-blog .description {
    margin-bottom: 20px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #blogs-page .last-blog .title,
  #blogs-page .last-blog .description {
    margin-bottom: 20px;
  }
}

#last-blog {
  margin-top: 24px;
  width: -webkit-fill-available;
  min-height: 270px;
  padding: 45px 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #last-blog {
    padding: 24px 20px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #last-blog {
    padding: 24px 20px;
  }
}
#last-blog .title {
  font-size: 36px;
  color: #ffffff;
  font-family: "Proxima-Nova-Medium" !important;
  margin-bottom: 15px;
}
#last-blog .description {
  font-size: 20px;
  color: #ffffff;
  margin-bottom: 15px;
}

#blog__categories__slider {
  overflow: hidden;
}
#blog__categories__slider .embla__container {
  display: flex;
}
#blog__categories__slider .embla__slide {
  background-color: antiquewhite;
  flex: 0 0 20%;
  min-width: 0;
  height: 216px;
  margin: 6px 10px;
  padding: 10px;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #blog__categories__slider .embla__slide {
    margin: 15px 15px 15px 0;
    flex: 0 0 30%;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #blog__categories__slider .embla__slide {
    margin: 15px 15px 15px 0;
    flex: 0 0 56%;
    height: 190px;
  }
}
#blog__categories__slider .embla__slide .slide__container {
  text-decoration: none;
}
#blog__categories__slider .embla__slide .slide__container .embla__slide__category-title {
  color: #ffffff;
  font-size: 24px;
  font-family: "Proxima-Nova-Medium" !important;
}

#social-media-share {
  background-color: #dbe8f6;
  padding: 15px;
  margin-bottom: 20px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #social-media-share {
    margin-top: 20px;
  }
}
#social-media-share .title {
  text-align: center;
  font-size: 20px;
  font-family: "Proxima-Nova-Semibold" !important;
  color: #0b3051;
  margin: 10px 0;
}
#social-media-share div {
  display: flex;
}
#social-media-share div .btn, #social-media-share div .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer #social-media-share div button {
  margin: auto;
  padding: 10px;
}
#social-media-share div .btn svg, #social-media-share div .newsletter-table .MuiDataGrid-toolbarContainer button svg, .newsletter-table .MuiDataGrid-toolbarContainer #social-media-share div button svg {
  margin: 0 0 0 0 !important;
}

#blog__categories__slider {
  margin-top: 20px;
}

.categories-list {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
}
.categories-list .category-2 {
  flex: 0 0 13%;
  min-width: max-content;
}
.categories-list .category-2 a {
  text-align: center;
  display: block;
  text-decoration: none;
  font-size: 20px;
  margin-right: 10px;
  color: #798ba3;
  border: 1px solid #798ba3;
  border-radius: 18px;
  padding: 8px 20px;
  margin-bottom: 5px;
}
.categories-list .category a {
  display: block;
  text-decoration: none;
  font-size: 14px;
  margin-right: 10px;
  color: #798ba3;
  border: 1px solid #798ba3;
  border-radius: 18px;
  padding: 4px 10px;
  margin-bottom: 5px;
}
.categories-list .category a.light {
  border: 1px solid #ffffff;
  color: #ffffff;
}

#one-blog-details .categories-path {
  padding: 15px 0;
  display: flex;
  align-items: center;
}
#one-blog-details .categories-path svg {
  transform: scale(0.5);
}
#one-blog-details .categories-path svg path {
  fill: #798ba3;
}
#one-blog-details .categories-path .link {
  font-size: 18px;
  color: #798ba3;
}
#one-blog-details .categories-path .link:last-child {
  color: #0b3051;
}
#one-blog-details .blog-content {
  padding: 15px;
  box-shadow: 0px 10px 40px rgba(10, 48, 82, 0.0784313725);
}
#one-blog-details .blog-content p {
  font-size: 18px;
}
#one-blog-details .blog-content p,
#one-blog-details .blog-content ul,
#one-blog-details .blog-content div,
#one-blog-details .blog-content li {
  background: transparent !important;
  font-size: 18px !important;
}
#one-blog-details .blog-content figure {
  margin: 10px 0;
}
#one-blog-details .blog-content table {
  width: -webkit-fill-available !important;
}
#one-blog-details .blog-content .summary {
  margin: 15px 0;
}
#one-blog-details .blog-content .summary .dropdown-header {
  display: flex;
  align-items: center;
}
#one-blog-details .blog-content .summary svg path {
  fill: #234791;
}
#one-blog-details .date {
  font-size: 16px;
  display: flex;
  align-items: center;
}
#one-blog-details .date svg {
  margin-right: 10px;
}
#one-blog-details .date svg path {
  fill: #ffffff;
}
#one-blog-details .sidebar .last-blog {
  margin-bottom: 10px;
}
#one-blog-details .sidebar .last-blog .btn-ghost.white {
  margin-top: 10px;
  padding: 4px 0;
  width: 100%;
}

.btn-filled-yellow {
  fill: #ffca00 !important;
}

#blog-page-details {
  background-color: white !important;
}
#blog-page-details #blog-header {
  padding-top: 10%;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  padding-bottom: 15px;
  background-color: #dbe8f6;
}
#blog-page-details #blog-header .heading-h1 {
  font-size: 2.75rem !important;
  margin-top: 5px !important;
}
#blog-page-details #blog-header .css-o1xwpy-MuiGrid-root > .MuiGrid-item {
  padding-left: 7% !important;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #blog-page-details #blog-header {
    align-items: center;
    padding-top: 20%;
  }
  #blog-page-details #blog-header .css-zow5z4-MuiGrid-root > .MuiGrid-item {
    padding-top: 0 !important;
  }
  #blog-page-details #blog-header .heading-h1 {
    font-size: 1.75rem !important;
  }
}
#blog-page-details #blog-header .MuiGrid-container {
  align-items: center;
}
#blog-page-details .blog-content p,
#blog-page-details .blog-content span {
  font-size: 18px !important;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #blog-page-details .blog-content p,
  #blog-page-details .blog-content span {
    font-size: 17px !important;
  }
}
#blog-page-details .blog-content h2 {
  font-size: 26px !important;
  color: #234791;
}
#blog-page-details .blog-content h3 {
  font-size: 22px !important;
  color: rgb(73, 69, 69);
}
#blog-page-details .blog-content h4 {
  font-size: 20;
  color: rgb(112, 108, 108);
}
#blog-page-details .blog-content ul {
  list-style: none;
  padding-left: 1em;
}
#blog-page-details .blog-content ul > li::before {
  content: "";
  display: inline-block;
  width: 12px;
  height: 12px;
  background-color: #ffca00;
  margin-right: 0.5em;
}
#blog-page-details .blog-content p,
#blog-page-details .blog-content ul,
#blog-page-details .blog-content div,
#blog-page-details .blog-content span,
#blog-page-details .blog-content li {
  background: transparent !important;
  font-size: 18px;
}
#blog-page-details .blog-content figure {
  width: 100% !important;
  margin: 10px 0;
  height: auto;
  min-width: 300px;
  border: 1px solid #699bd4;
  background-color: transparent;
}
#blog-page-details .blog-content figure img {
  width: -webkit-fill-available !important;
  height: auto !important;
  padding: 10px;
}
#blog-page-details .blog-content img {
  width: -webkit-fill-available !important;
  height: auto;
}
#blog-page-details .blog-content table {
  width: -webkit-fill-available !important;
}
#blog-page-details .blog-content .summary {
  margin: 15px 0;
}
#blog-page-details .blog-content .summary .dropdown-header {
  display: flex;
  align-items: center;
}
#blog-page-details .blog-content .summary svg path {
  fill: #234791;
}
#blog-page-details .blog-content button {
  display: inline-block;
  background: linear-gradient(to right, #5ce6e6, #0052cc);
  color: white;
  padding: 17px 24px;
  border-radius: 999px;
  text-decoration: none;
  font-family: Arial, sans-serif;
  font-size: 14px;
  font-weight: bold;
  transition: background 0.3s ease;
  border: none;
}
#blog-page-details .blog-content button:hover {
  opacity: 0.9;
}
#blog-page-details .categories-list {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  margin-top: 20px;
}
#blog-page-details .categories-list .category-2 {
  flex: 0 0 13%;
  min-width: max-content;
}
#blog-page-details .categories-list .category-2 a {
  text-align: center;
  display: block;
  text-decoration: none;
  font-size: 20px;
  margin-right: 10px;
  color: #234791;
  border: 1px solid #234791;
  border-radius: 18px;
  padding: 8px 20px;
  margin-bottom: 5px;
}
#blog-page-details .categories-list .category a {
  display: block;
  text-decoration: none;
  font-size: 14px;
  margin-right: 10px;
  color: #234791;
  border: 1px solid #234791;
  border-radius: 18px;
  padding: 4px 10px;
  margin-bottom: 5px;
}
#blog-page-details .categories-list .category a.light {
  border: 1px solid #ffffff;
  color: #ffffff;
}
#blog-page-details .date {
  font-size: 16px;
  display: flex;
  align-items: center;
  color: #234791;
  font-weight: 500;
}
#blog-page-details .date svg {
  margin-right: 10px;
}
#blog-page-details .date svg path {
  fill: #234791;
}
#blog-page-details .date svg:last-child {
  margin-left: 20px;
}
#blog-page-details .blog-img {
  background-color: #234791;
  max-width: 90%;
  height: auto;
  max-height: 80%;
  margin-bottom: 20px;
}
#blog-page-details .blog-img img {
  height: auto;
  width: 100%;
  margin: auto;
  margin-left: 20px;
  margin-bottom: -20px;
  margin-top: 20px;
}
#blog-page-details .last-word {
  background-color: #ffca00;
}
#blog-page-details .categories-path {
  padding: 15px 0;
  display: flex;
  align-items: center;
}
#blog-page-details .categories-path svg {
  transform: scale(0.5);
}
#blog-page-details .categories-path svg path {
  fill: #798ba3;
}
#blog-page-details .categories-path .link {
  font-size: 18px;
  color: #798ba3;
  text-decoration: none;
}
#blog-page-details .categories-path .link:last-child {
  color: #0b3051;
}
#blog-page-details .section-related-blog {
  margin-bottom: 20px;
}
#blog-page-details .section-related-blog .last-blog .card {
  position: relative;
}
#blog-page-details .section-related-blog .last-blog .card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to top, #0b3051, rgba(11, 48, 81, 0));
  opacity: 0.6;
  pointer-events: none;
}
#blog-page-details .section-related-blog .last-blog .card .card-media {
  height: 185px;
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #blog-page-details .section-related-blog .last-blog .card .card-media {
    height: 136px;
  }
}
#blog-page-details .section-related-blog .last-blog .card .card-text {
  color: #ffffff;
  position: absolute;
  bottom: 10px;
  left: 10px;
  font-size: 18px;
  padding: 0px 2px;
  font-family: "Proxima-Nova-Semibold" !important;
}
#blog-page-details .section-related-blog .last-blog .card .card-text button {
  margin: 0;
  padding: 5px 0px;
  justify-content: left;
}
#blog-page-details .section-related-blog .last-blog .card .card-text a {
  margin: 0;
  padding: 5px 0px;
  justify-content: left;
}
#blog-page-details .section-related-blog .btn-filled.blue {
  width: max-content;
  margin: 15px auto;
}
#blog-page-details .section {
  background-color: #dbe8f6;
  padding: 15px;
  margin-bottom: 20px;
}
#blog-page-details .section .title {
  font-size: 20px;
  font-family: "Proxima-Nova-Semibold" !important;
  color: #0b3051;
  margin: 10px 0;
}
#blog-page-details .section .description {
  color: #0b3051;
  margin-bottom: 10px;
  font-size: 16px;
}
#blog-page-details .section .btn, #blog-page-details .section .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer #blog-page-details .section button {
  margin-top: 20px;
  width: -webkit-fill-available;
  text-align: center;
  display: flex;
  justify-content: center;
}
#blog-page-details #sticky-sidebar {
  padding-left: 7%;
  position: sticky;
  top: 110px;
  left: 0;
  width: 100%;
  z-index: 1000;
  max-height: 80vh;
}
#blog-page-details #content-table {
  padding: 10px;
  max-height: 50vh;
  margin-bottom: 20px;
  overflow: auto;
}
#blog-page-details #content-table::-webkit-scrollbar {
  border-radius: 8px;
  background: #ffffff;
  width: 12px;
}
#blog-page-details #content-table::-webkit-scrollbar-thumb {
  background: #ffca00;
  border-radius: 8px;
}
#blog-page-details #content-table::-webkit-scrollbar-track {
  background: transparent;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #blog-page-details #content-table {
    max-height: 65vh;
  }
}
#blog-page-details #content-table ul {
  list-style: none;
  padding-left: 1em;
}
#blog-page-details #content-table ul li {
  margin-bottom: 10px;
}
#blog-page-details #content-table ul li a {
  color: #ffffff;
  text-decoration: none;
  font-family: "Proxima-Nova-Medium";
  font-size: 16px;
}
#blog-page-details #content-table ul > li::before {
  content: "";
  display: inline-block;
  width: 12px;
  height: 12px;
  background-color: #ffca00;
  margin-right: 0.5em;
}

#faq-container {
  background-color: #ebece6;
  padding: 32px;
  border-radius: 4px;
  box-shadow: 0px 2px 12px rgba(0, 0, 0, 0.1);
  margin: 24px 0px 12px;
}
#faq-container .faq-title {
  font-weight: 600;
  color: #d6990b;
  font-size: 24px !important;
  margin-bottom: 18px;
}
#faq-container .faq-item {
  border: none;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #faq-container .faq-item {
    padding: 5px 0px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #faq-container .faq-item {
    padding: 5px 0px;
  }
}
#faq-container .faq-question {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
  background-color: transparent;
  cursor: pointer;
  margin-bottom: 12px;
}
#faq-container .faq-icon svg {
  scale: 1.2;
}
#faq-container .faq-icon svg path {
  fill: #019967;
}
#faq-container .faq-question-text {
  font-weight: 600;
  font-size: 18px;
  color: #019967;
}
#faq-container .faq-number {
  background-color: #019967;
  color: white;
  padding: 8px;
  border-radius: 20px;
  font-size: 14px;
  margin-right: 5px;
}
#faq-container .faq-answer {
  padding: 0px 25px 16px 45px;
  background-color: transparent;
  font-size: 16px;
  color: #36546e;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #faq-container .faq-answer {
    padding: 0px 0px 16px 0px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #faq-container .faq-answer {
    padding: 0px 0px 16px 0px;
  }
}

#faq-header {
  background: rgba(228, 239, 252, 0.7019607843);
  border-radius: 10px;
  border: none;
}
#faq-header .accordion-faq-title {
  color: #234791;
  font-size: 16px;
  font-family: "Proxima-Nova-Semibold" !important;
}

#faq-header:before {
  background: none;
  border: none;
}

#create-comment-blog {
  margin-top: 20px;
  margin-bottom: 20px;
  padding: 15px;
  box-shadow: 0px 10px 40px rgba(10, 48, 82, 0.0784313725);
}
#create-comment-blog .btn, #create-comment-blog .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer #create-comment-blog button {
  margin: 10px 0 auto auto;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #create-comment-blog .btn, #create-comment-blog .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer #create-comment-blog button {
    margin: 10px auto;
  }
}

#comment-list-blog {
  padding: 15px;
  margin: 0 0 30px;
  box-shadow: 0px 10px 40px rgba(10, 48, 82, 0.0784313725);
}
#comment-list-blog .show-more {
  padding: 10px;
  margin: auto;
}
#comment-list-blog .sub-heading {
  font-family: "Proxima-Nova-Medium" !important;
}
#comment-list-blog .comments-nbr {
  margin-left: 10px;
  background-color: #ffca00;
  padding: 2px 4px;
}
#comment-list-blog .comment-header {
  display: flex;
  flex-direction: row;
}
#comment-list-blog .one-comment-item {
  border-left: 1px solid #798ba3;
  padding: 10px;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  margin-bottom: 6px;
}
#comment-list-blog .one-comment-item .user-img {
  width: 40px;
  height: 40px;
  border-radius: 100%;
  margin-right: 5px;
}
#comment-list-blog .one-comment-item .comment-content {
  margin-left: 10px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
#comment-list-blog .one-comment-item .username {
  color: #234791;
  font-size: 16px;
  font-family: "Proxima-Nova-Bold" !important;
  margin-right: 10px;
}
#comment-list-blog .one-comment-item .text {
  color: #0b3051;
  font-size: 18px;
}
#comment-list-blog .one-comment-item .date {
  color: #798ba3;
  font-size: 12px;
}
#comment-list-blog .one-comment-item .btn.reply, #comment-list-blog .one-comment-item .newsletter-table .MuiDataGrid-toolbarContainer button.reply, .newsletter-table .MuiDataGrid-toolbarContainer #comment-list-blog .one-comment-item button.reply {
  padding: 8px 0 8px 8px;
  font-size: 14px;
  color: #798ba3;
  cursor: pointer;
}
#comment-list-blog .one-comment-item .grey {
  padding: 8px 0 0px 2px !important;
}
#comment-list-blog .one-comment-item .flex {
  display: flex;
  flex-direction: row;
}
#comment-list-blog .one-comment-item .replies-list {
  font-size: 14px;
  width: 100%;
  text-wrap: nowrap;
}
#comment-list-blog .one-comment-item .replies-list:after {
  content: "";
  border-bottom: 1px solid #798ba3;
  width: 100%;
  margin-left: 10px;
}
#comment-list-blog .one-comment-item .reply-form {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
#comment-list-blog .one-comment-item .reply-form .MuiFormControl-root {
  width: 100%;
  margin-right: 6px;
}
#comment-list-blog .one-comment-item .reply-form .MuiInputBase-root {
  width: 100%;
  border-color: #234791 !important;
  border-radius: 0;
}
#comment-list-blog .one-comment-item .reply-form .MuiInputBase-root:hover {
  border-color: #234791 !important;
}
#comment-list-blog .one-comment-item .reply-form input {
  width: 100%;
  margin-left: 10px;
  padding: 12px;
  border-color: #234791;
}
#comment-list-blog .one-comment-item .reply-form input:focus-visible {
  outline: -webkit-focus-ring-color auto 0;
}

#about-us-page .intro-section {
  padding: 50px 15px;
  background: linear-gradient(to bottom, #0b3051, #234791);
}
#about-us-page .quote-section {
  margin-top: 80px;
  border: 1px solid #699bd4;
  padding: 20px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: start;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #about-us-page .quote-section {
    flex-direction: column;
    padding: 10px;
  }
}
#about-us-page .quote-section .sub-heading,
#about-us-page .quote-section .paragraph {
  margin: 0;
  padding: 0;
  margin-top: 5px;
  color: #ffffff;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #about-us-page .quote-section .sub-heading,
  #about-us-page .quote-section .paragraph {
    text-align: center;
  }
}
#about-us-page .quote {
  font-family: "Proxima-Nova-Medium" !important;
  font-weight: 500;
  font-size: 18px;
  margin-left: 20px;
  color: #ffffff;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #about-us-page .quote {
    margin-left: 0;
    margin-top: 10px;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #about-us-page .first-row {
    display: flex;
    justify-content: center;
    flex-direction: column;
  }
}
#about-us-page .user-img {
  margin-top: 10px;
  width: auto;
  height: auto;
  margin-right: 5px;
  max-width: 150px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #about-us-page .user-img {
    max-height: 140px;
    margin: auto;
  }
}
#about-us-page .user-img.sign {
  margin: auto 0 auto auto !important;
  text-align: right;
  display: flex;
}

#what-we-beleive-section {
  padding: 50px 15px;
}
#what-we-beleive-section img {
  margin: auto;
  width: 100%;
  height: auto;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #what-we-beleive-section img {
    margin: auto;
    height: auto;
  }
}
#what-we-beleive-section .heading-h1 {
  margin-bottom: 30px;
  font-family: "Proxima-Nova-Bold" !important;
}
#what-we-beleive-section .vision-mission-section {
  margin: 50px auto auto auto;
  padding: 15px;
  background: linear-gradient(to bottom, #0b3051, #234791);
  height: 100%;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #what-we-beleive-section .vision-mission-section {
    margin: 20px auto auto auto;
    height: -webkit-fill-available;
  }
}

#our-values {
  margin-top: 50px;
  margin-bottom: 50px;
}
#our-values .values {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 20px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #our-values .values {
    flex-direction: column;
  }
}
#our-values .values .value-item {
  width: 15%;
  padding: 10px;
  min-height: 125px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
#our-values .values .value-item.oil-gaz {
  background-color: #d69b19;
  color: #ffffff;
}
#our-values .values .value-item.energy {
  background-color: #cc3233;
  color: #ffffff;
}
#our-values .values .value-item.banking-insurance {
  background-color: #0b3051;
  color: #ffffff;
}
#our-values .values .value-item.transport {
  background-color: #009966;
  color: #ffffff;
}
#our-values .values .value-item.it-telecom {
  background-color: #743794;
  color: #ffffff;
}
#our-values .values .value-item.other {
  background-color: #234791;
  color: #ffffff;
}
#our-values .values .value-item.pharmaceutical {
  background-color: #2bbfad;
  color: #ffffff;
}
#our-values .values .value-item .sub-heading {
  margin: 0 0 10px 0 !important;
  font-size: 24px !important;
  font-family: "Proxima-Nova-Medium" !important;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #our-values .values .value-item {
    width: 100%;
    width: auto;
    margin: 10px 0;
    min-height: 100px;
  }
}

#offices-section {
  padding-top: 50px;
  padding-bottom: 50px;
  background-image: url(/_next/static/media/bgOffices.9e75c663.png);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #offices-section {
    background-image: url(/_next/static/media/bgOfficesMobile.07a3b26a.png);
  }
}
#offices-section .MuiGrid-root.MuiGrid-item.MuiGrid-grid-xs-12 {
  padding: 0;
}
#offices-section .align-items-center {
  align-items: center;
}
#offices-section .align-items-center.container {
  margin-bottom: 50px;
}
#offices-section .offices-img {
  width: 100%;
  height: auto;
}
#offices-section .container {
  width: auto;
  margin: auto;
}
#offices-section .text-section {
  height: -moz-fit-content;
  height: fit-content;
  padding: 15px !important;
  margin-right: 20px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #offices-section .text-section {
    margin-right: 0;
    margin-bottom: 20px;
  }
}
#offices-section .text-section-img {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(228, 239, 252, 0.7);
  padding: 15px !important;
}
#offices-section .text-section-img img {
  width: 100%;
  height: auto;
  margin: auto;
}
#offices-section .text-section.banking-color {
  background-color: #0b3051;
}
#offices-section .text-section.blue-color {
  background-color: #234791;
}

#explore-more-section {
  background-color: #234791;
  padding: 15px;
  margin-top: 50px;
  margin-bottom: 50px;
}
#explore-more-section .btns {
  display: flex;
  flex-direction: row;
  margin-top: 20px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #explore-more-section .btns {
    flex-direction: column;
  }
}
#explore-more-section .btns .btn.btn-outlined.white, #explore-more-section .btns .newsletter-table .MuiDataGrid-toolbarContainer button.btn-outlined.white, .newsletter-table .MuiDataGrid-toolbarContainer #explore-more-section .btns button.btn-outlined.white {
  text-align: center;
  display: flex;
  justify-content: center;
}
#explore-more-section .btns .btn.btn-outlined.white.first-child, #explore-more-section .btns .newsletter-table .MuiDataGrid-toolbarContainer button.btn-outlined.white.first-child, .newsletter-table .MuiDataGrid-toolbarContainer #explore-more-section .btns button.btn-outlined.white.first-child {
  margin-right: 30px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #explore-more-section .btns .btn.btn-outlined.white.first-child, #explore-more-section .btns .newsletter-table .MuiDataGrid-toolbarContainer button.btn-outlined.white.first-child, .newsletter-table .MuiDataGrid-toolbarContainer #explore-more-section .btns button.btn-outlined.white.first-child {
    margin-right: 0;
    margin-bottom: 20px;
  }
}
#explore-more-section .text-section {
  height: -moz-fit-content;
  height: fit-content;
  padding: 15px !important;
  display: flex;
  flex-direction: column;
  width: 60%;
  margin: auto;
  align-items: center;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #explore-more-section .text-section {
    margin-right: 0;
    margin-bottom: 20px;
    width: auto;
  }
}

.ksa {
  margin: 40px 0;
}

#our-partners #partners__slider, #coporate-profile-partners #partners__slider {
  overflow: hidden;
}
#our-partners #partners__slider .embla__container, #coporate-profile-partners #partners__slider .embla__container {
  display: flex;
}
#our-partners #partners__slider .slide__container, #coporate-profile-partners #partners__slider .slide__container {
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  padding: 0;
  height: 100%;
  max-height: 80px;
}
#our-partners #partners__slider .embla__slide, #coporate-profile-partners #partners__slider .embla__slide {
  flex: 0 0 10%;
  min-width: 0;
  min-height: 80px;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin: 6px 18px 0px 0;
  transition: filter 0.3s ease;
  height: auto;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #our-partners #partners__slider .embla__slide, #coporate-profile-partners #partners__slider .embla__slide {
    margin: 10px 10px 10px 0;
    flex: 0 0 25%;
  }
}

#contact-page-form {
  margin-top: 100px;
  margin-bottom: 50px;
}

#locations-section, #approach-payroll-section, #business-tunisia, #hr-solution-africa-section {
  margin-top: 100px;
}
#locations-section .locations, #approach-payroll-section .locations, #business-tunisia .locations, #hr-solution-africa-section .locations {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: stretch;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #locations-section .locations, #approach-payroll-section .locations, #business-tunisia .locations, #hr-solution-africa-section .locations {
    flex-direction: column;
  }
}
#locations-section .location-item, #approach-payroll-section .location-item, #business-tunisia .location-item, #hr-solution-africa-section .location-item {
  width: 30%;
  padding: 15px;
  border: 1px solid #234791;
  margin: 10px;
  border-image: linear-gradient(-45deg, #0b3051, #699bd4);
  border-image-slice: 1;
}
#locations-section .location-item .label, #approach-payroll-section .location-item .label, #business-tunisia .location-item .label, #hr-solution-africa-section .location-item .label {
  font-size: 20px;
  font-family: "Proxima-Nova-Medium" !important;
  color: #234791;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
#locations-section .location-item .label svg, #approach-payroll-section .location-item .label svg, #business-tunisia .location-item .label svg, #hr-solution-africa-section .location-item .label svg {
  margin-right: 10px;
}
#locations-section .location-item .value, #approach-payroll-section .location-item .value, #business-tunisia .location-item .value, #hr-solution-africa-section .location-item .value {
  color: #234791;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #locations-section .location-item, #approach-payroll-section .location-item, #business-tunisia .location-item, #hr-solution-africa-section .location-item {
    width: auto;
    margin: 10px 0;
  }
}

#offices-list-contact-page {
  padding-top: 130px;
  padding-bottom: 50px;
  background: linear-gradient(to bottom, #0b3051, #234791);
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #offices-list-contact-page {
    padding-top: 100px;
  }
}
#offices-list-contact-page .center-img {
  margin-top: -40px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #offices-list-contact-page .center-img {
    margin-top: -10px;
  }
}
#offices-list-contact-page .center-img img {
  width: 100%;
  height: auto;
}
#offices-list-contact-page .top-section,
#offices-list-contact-page .contact-items-section {
  z-index: 10;
  position: relative;
  display: block;
}
#offices-list-contact-page .contact-items-section {
  margin-top: -100px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #offices-list-contact-page .contact-items-section {
    margin-top: -25px;
    flex-wrap: nowrap;
    overflow: auto;
  }
}
#offices-list-contact-page .link-offices-page {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #offices-list-contact-page .link-offices-page {
    display: flex;
    flex-direction: row;
    align-items: center;
    overflow: scroll;
  }
}
#offices-list-contact-page .btn, #offices-list-contact-page .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer #offices-list-contact-page button {
  padding: 10px 20px;
}
#offices-list-contact-page .btn.offices, #offices-list-contact-page .newsletter-table .MuiDataGrid-toolbarContainer button.offices, .newsletter-table .MuiDataGrid-toolbarContainer #offices-list-contact-page button.offices {
  width: -moz-fit-content;
  width: fit-content;
  margin: auto;
}
#offices-list-contact-page .btn.offices svg, #offices-list-contact-page .newsletter-table .MuiDataGrid-toolbarContainer button.offices svg, .newsletter-table .MuiDataGrid-toolbarContainer #offices-list-contact-page button.offices svg {
  transform: rotate(90deg);
}

.contact-items-section {
  margin-top: -40px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .contact-items-section {
    margin-top: -25px;
    flex-wrap: nowrap;
    overflow: auto;
  }
}
.contact-items-section .contact-item {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background-color: hsla(220, 60%, 35%, 0.2);
  color: #ffffff;
  flex: 0 0 20%;
  margin: 15px 0;
  align-items: stretch;
  padding: 15px;
  border: 1px solid rgba(228, 239, 252, 0.7);
  border-radius: 5px;
  min-height: 280px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .contact-items-section .contact-item {
    margin: 15px 15px 15px 0;
  }
}
.contact-items-section .contact-item .title {
  margin-bottom: 10px !important;
  font-size: 16px;
  font-family: "Proxima-Nova-Semibold" !important;
}
.contact-items-section .contact-item .row-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin: 10px 0;
  font-size: 14px;
}
.contact-items-section .contact-item svg {
  margin-right: 5px;
}
.contact-items-section .contact-item svg rect {
  fill: transparent;
}
.contact-items-section .contact-item svg path {
  stroke: #ffca00;
}
.contact-items-section .contact-item .btn, .contact-items-section .contact-item .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer .contact-items-section .contact-item button {
  margin: auto 0 0 0;
  text-align: center;
  display: flex;
  justify-content: center;
}

#cta-contact {
  margin-bottom: 40px;
}
#cta-contact .container {
  background-color: #0b3051;
  padding: 30px 20px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #cta-contact {
    margin: 10px auto;
    padding: 20px 10px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #cta-contact {
    margin: 10px auto;
    padding: 20px 10px;
  }
}
#cta-contact .left-section {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
}
#cta-contact .left-section .paragraph {
  margin-bottom: 20px;
}
#cta-contact .africa-img {
  text-align: end;
}
#cta-contact .africa-img img {
  width: auto !important;
  height: auto !important;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #cta-contact .africa-img img {
    width: 100%;
    margin: auto;
  }
}

#approach-payroll-section, #business-tunisia, #hr-solution-africa-section {
  margin-top: 50px !important;
  margin-bottom: 20px;
}
#approach-payroll-section .locations, #business-tunisia .locations, #hr-solution-africa-section .locations {
  flex-wrap: wrap;
}
#approach-payroll-section .location-item, #business-tunisia .location-item, #hr-solution-africa-section .location-item {
  width: 26%;
}
#approach-payroll-section .location-item.four-items, #business-tunisia .location-item.four-items, #hr-solution-africa-section .location-item.four-items {
  width: 19%;
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #approach-payroll-section .location-item, #business-tunisia .location-item, #hr-solution-africa-section .location-item {
    width: 40% !important;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #approach-payroll-section .location-item, #business-tunisia .location-item, #hr-solution-africa-section .location-item {
    width: auto !important;
    margin: 10px 0;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #approach-payroll-section, #business-tunisia, #hr-solution-africa-section {
    margin-top: 0 !important;
  }
}

#how-it-works, #comprehensive-support-section, #candidate-journey-section {
  padding-top: 20px;
  padding-bottom: 20px;
  color: #ffffff;
  background-color: #0b3051;
}
#how-it-works .heading-h1, #comprehensive-support-section .heading-h1, #candidate-journey-section .heading-h1 {
  margin-bottom: 40px !important;
}

.multi-step {
  padding: 0;
  display: flex;
  justify-content: center;
}
.multi-step .title {
  text-align: left;
  font-size: 18px;
}
.multi-step .paragraph {
  text-align: justify;
  font-size: 14px;
}
.multi-step .content {
  width: 100%;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .multi-step .content {
    margin-top: 30px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .multi-step .content {
    margin-top: 30px;
  }
}
.multi-step .content-even {
  margin: 10px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .multi-step .content-even {
    width: 45%;
    text-align: left;
    display: flex;
    flex-direction: column;
    margin: auto 0 auto auto;
    margin-top: -30px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .multi-step .content-even {
    width: 45%;
    text-align: left;
    display: flex;
    flex-direction: column;
    margin: auto 0 auto auto;
    margin-top: -30px;
  }
}
.multi-step .content-odd {
  margin: 10px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .multi-step .content-odd {
    width: 45%;
    text-align: left;
    display: flex;
    flex-direction: column;
    margin: auto auto auto 0;
    margin-top: -30px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .multi-step .content-odd {
    width: 45%;
    text-align: left;
    display: flex;
    flex-direction: column;
    margin: auto auto auto 0;
    margin-top: -30px;
  }
}
.multi-step li {
  list-style-type: none;
  width: 25%;
  float: left;
  font-size: 16px;
  position: relative;
  text-align: center;
  padding: 0 40px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .multi-step li {
    padding: 40px 0;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .multi-step li {
    padding: 40px 0;
  }
}
.multi-step li:after {
  width: 100%;
  height: 2px;
  content: "";
  position: absolute;
  top: 15px;
  left: -50%;
  z-index: 1;
  background: linear-gradient(to left, #0b3051 0%, #699bd4 46%, #0b3051 100%);
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .multi-step li:after {
    width: 2px;
    height: 100%;
    left: 50%;
    top: 0;
    transform: translateX(-50%);
    margin: 40px 0;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .multi-step li:after {
    width: 2px;
    height: 100%;
    left: 50%;
    top: 0;
    transform: translateX(-50%);
    margin: 40px 0;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .multi-step li:last-child:after {
    content: none;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .multi-step li:last-child:after {
    content: none;
  }
}
.multi-step li:first-child:after {
  content: none;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .multi-step li:first-child:after {
    content: "";
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .multi-step li:first-child:after {
    content: "";
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .multi-step li {
    width: 100%;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .multi-step li {
    width: 100%;
  }
}
.multi-step .step {
  width: 30px;
  height: 30px;
  content: "";
  counter-increment: step;
  line-height: 30px;
  border: 2px solid #699bd4;
  display: block;
  text-align: center;
  margin: 0 auto 0 auto;
  background-color: transparent;
  position: relative;
  z-index: 2;
  background-color: #0b3051;
}
.multi-step .step:before {
  width: 20px;
  height: 20px;
  content: "";
  margin: 5px;
  background-color: #699bd4;
  display: flex;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .multi-step {
    flex-direction: column;
    align-items: flex-start;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .multi-step {
    flex-direction: column;
    align-items: flex-start;
  }
}

#cta-payroll {
  padding-top: 20px;
  padding-bottom: 20px;
  background-color: #0b3051;
}
#cta-payroll.light {
  background-color: transparent;
}
#cta-payroll .MuiContainer-root {
  margin: 20px auto;
}
#cta-payroll .div {
  padding: 10px 20px;
  border: 1px solid #699bd4;
}
#cta-payroll #last-blog {
  margin: 0;
}

#why-pentabell-section {
  padding-top: 20px;
  padding-bottom: 20px;
  background: linear-gradient(to bottom, #0b3051, #234791);
}
#why-pentabell-section .reasons {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
#why-pentabell-section .reason {
  margin: 15px 0;
  padding: 20px 10px;
  background-color: rgba(217, 217, 217, 0.06);
  height: -webkit-fill-available;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
#why-pentabell-section .center-img {
  margin: 15px auto;
  align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
}
#why-pentabell-section .center-img .img {
  display: block;
  height: auto;
  width: 100%;
  margin: auto;
}
#why-pentabell-section .center-img .img img {
  height: auto;
  width: 100%;
}
#why-pentabell-section .center-img .txt {
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 1;
  color: white;
  transform: translate(-50%, -50%);
}

#ask-questions-section, #labor-tn-laws {
  padding-top: 20px;
  padding-bottom: 20px;
}
#ask-questions-section .container, #labor-tn-laws .container {
  margin-top: 20px;
}
#ask-questions-section .services-accordion, #labor-tn-laws .services-accordion {
  background-color: transparent;
  border-bottom: 1px solid #699bd4;
  border-radius: 0;
}
#ask-questions-section .services-accordion-header, #labor-tn-laws .services-accordion-header {
  padding-left: 0;
  background-color: transparent;
}
#ask-questions-section .services-accordion svg, #labor-tn-laws .services-accordion svg {
  transform: scale(1.8);
}
#ask-questions-section .services-accordion svg path, #labor-tn-laws .services-accordion svg path {
  fill: #234791;
}
#ask-questions-section .services-accordion .service-title, #labor-tn-laws .services-accordion .service-title {
  color: #234791;
  font-size: 20px;
  font-family: "Proxima-Nova-Medium" !important;
  font-weight: 500;
}

#comprehensive-support-section .support-reasons, #candidate-journey-section .support-reasons {
  margin-top: 20px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #comprehensive-support-section .support-reasons, #candidate-journey-section .support-reasons {
    flex-direction: column;
  }
}
#comprehensive-support-section .support-reason, #candidate-journey-section .support-reason {
  width: 25%;
  display: flex;
  flex-direction: column;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #comprehensive-support-section .support-reason, #candidate-journey-section .support-reason {
    width: 100%;
    margin-bottom: 10px;
  }
}
#comprehensive-support-section .support-reason-title, #candidate-journey-section .support-reason-title {
  margin: 15px 0px;
  color: #ffca00;
  font-size: 20px;
  font-family: "Proxima-Nova-Semibold" !important;
}
#comprehensive-support-section .support-reason ul, #candidate-journey-section .support-reason ul {
  margin: 0;
}
#comprehensive-support-section .support-reason-item, #candidate-journey-section .support-reason-item {
  color: #699bd4;
  font-size: 16px;
}
#comprehensive-support-section .support-reason img, #candidate-journey-section .support-reason img {
  width: 100%;
  height: auto;
}

.ai-sourcing-details {
  padding-top: 50px;
  padding-bottom: 50px;
}
.ai-sourcing-details .industry-label {
  font-size: 14px;
  width: -moz-fit-content;
  width: fit-content;
  font-family: "Proxima-Nova-Semibold" !important;
  text-transform: capitalize;
  padding: 6px 18px;
  margin: 10px 0;
  border-radius: 20px;
  background-color: #ffca00;
  color: #234791;
  display: flex;
  align-items: center;
  margin-top: 20px;
}
.ai-sourcing-details .industry-label svg {
  margin-right: 6px;
}
.ai-sourcing-details .industry-label.oil-gaz {
  background-color: #d69b19;
  color: #ffffff;
}
.ai-sourcing-details .industry-label.energy {
  background-color: #cc3233;
  color: #ffffff;
}
.ai-sourcing-details .industry-label.banking-insurance {
  background-color: #0b3051;
  color: #ffffff;
}
.ai-sourcing-details .industry-label.transport {
  background-color: #009966;
  color: #ffffff;
}
.ai-sourcing-details .industry-label.it-telecom {
  background-color: #743794;
  color: #ffffff;
}
.ai-sourcing-details .industry-label.other {
  background-color: #234791;
  color: #ffffff;
}
.ai-sourcing-details .industry-label.pharmaceutical {
  background-color: #2bbfad;
  color: #ffffff;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .ai-sourcing-details .industry-label {
    padding: 4px 12px;
    font-size: 12px;
    margin-top: 20px;
  }
}
.ai-sourcing-details .btn, .ai-sourcing-details .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer .ai-sourcing-details button {
  margin-top: 10px;
  width: -moz-fit-content;
  width: fit-content;
}
.ai-sourcing-details .reverse {
  flex-direction: row-reverse;
}
.ai-sourcing-details .reverse .feauture-img {
  margin-left: 0;
  margin-right: 20px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .ai-sourcing-details .reverse .feauture-img {
    margin: 10px auto;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .ai-sourcing-details .reverse .feauture-img {
    margin: 10px auto;
  }
}
.ai-sourcing-details .MuiGrid-item {
  display: flex;
}
.ai-sourcing-details .content,
.ai-sourcing-details .feauture-img {
  display: flex;
  justify-content: space-between;
  padding: 20px 30px;
  flex-direction: column;
  width: 100%;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .ai-sourcing-details .content,
  .ai-sourcing-details .feauture-img {
    padding: 20px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .ai-sourcing-details .content,
  .ai-sourcing-details .feauture-img {
    padding: 20px;
  }
}
.ai-sourcing-details .content {
  background-color: #0b3051;
}
.ai-sourcing-details .feauture-img {
  margin-left: 20px;
  border: 2px solid #699bd4;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .ai-sourcing-details .feauture-img {
    max-height: 300px;
    width: 100%;
    text-align: center;
    margin: 10px auto;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .ai-sourcing-details .feauture-img {
    max-height: 300px;
    width: 100%;
    text-align: center;
    margin: 10px auto;
  }
}
.ai-sourcing-details .feauture-img img {
  height: 100%;
  width: 100%;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .ai-sourcing-details .feauture-img img {
    max-height: 300px;
    width: auto;
    margin: auto;
    text-align: center;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .ai-sourcing-details .feauture-img img {
    max-height: 300px;
    width: auto;
    margin: auto;
    text-align: center;
  }
}
.ai-sourcing-details .data {
  margin: 30px 0;
}
.ai-sourcing-details .data .data-item {
  font-size: 18px;
  color: #234791;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  margin: 10px 0;
}
.ai-sourcing-details .data .data-item.text-white {
  color: #ffffff;
}
.ai-sourcing-details .data .data-item svg {
  width: 50px;
  margin-right: 5px;
}

#candidate-journey-section.light-bg {
  background-color: transparent;
  color: #234791 !important;
}
#candidate-journey-section.light-bg .support-reason-title {
  color: #234791 !important;
}
#candidate-journey-section .support-reason {
  width: 20%;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #candidate-journey-section .support-reason {
    width: 80%;
    margin: auto auto 10px auto;
  }
}
#candidate-journey-section .img {
  min-height: 120px;
  width: auto;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}
#candidate-journey-section .img img {
  height: auto;
  width: auto;
}

#team {
  padding-top: 20px;
  color: #ffffff;
  background-color: #0b3051;
}
#team .heading-h1 {
  padding: 15px;
}
#team .team-img {
  width: 100%;
  display: flex;
  height: 100%;
}
#team .team-img img {
  width: 100%;
  height: auto;
}

#our-culture-location {
  padding-top: 50px;
  padding-bottom: 20px;
}
#our-culture-location .MuiGrid-root.MuiGrid-container {
  justify-content: space-between;
}
#our-culture-location .MuiGrid-item {
  padding: 20px !important;
  border: 1px solid #699bd4;
  display: flex;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #our-culture-location .MuiGrid-item {
    margin: 10px auto;
  }
}
#our-culture-location .btn, #our-culture-location .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer #our-culture-location button {
  margin-top: 20px;
}
#our-culture-location .location-section,
#our-culture-location .culture-section {
  background-color: #0b3051;
  padding: 30px 20px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
}
#our-culture-location .location-section .paragraph,
#our-culture-location .culture-section .paragraph {
  margin: 10px 0;
}

#privacy-terms,
#terms-conditions {
  padding-top: 20px;
  padding-bottom: 20px;
}

#labor-tn-laws {
  margin-bottom: 20px;
}
#labor-tn-laws .service-title {
  font-family: "Proxima-Nova-SemiBold" !important;
  margin: 0;
}
#labor-tn-laws .list-arrow {
  list-style: none; /* Remove default bullet points */
  padding: 0; /* Remove padding if necessary */
}
#labor-tn-laws .item-arrow::before {
  content: "➤"; /* Use an arrow character */
  color: #234791; /* Optional: Change arrow color */
  margin-right: 8px; /* Add some space between arrow and text */
}
#labor-tn-laws .service-sub-title {
  font-family: "Proxima-Noava-Medium";
  color: #234791;
}
#labor-tn-laws .service-description {
  margin-top: 10px;
  color: #798ba3;
}
#labor-tn-laws .services-accordion .item:not(:last-child) {
  margin-bottom: 10px;
}
#labor-tn-laws .payroll-tn {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  flex-wrap: wrap;
}
#labor-tn-laws .payroll-tn-item {
  background: linear-gradient(to right, rgb(116, 55, 148), rgb(35, 71, 145));
  padding: 15px;
  width: 20%;
  color: #ffffff;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  min-height: 110px;
  margin: 10px 0;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #labor-tn-laws .payroll-tn-item {
    width: 100%;
  }
}
#labor-tn-laws .payroll-tn-item .title {
  font-family: "Proxima-Nova-Medium" !important;
  width: 50%;
  margin-bottom: 10px;
}
#labor-tn-laws .payroll-tn-item .date {
  width: 50%;
  text-align: right;
  font-family: "Proxima-Nova-Light" !important;
  margin-bottom: 10px;
}
#labor-tn-laws .payroll-tn-item .paragraph {
  font-family: "Proxima-Nova-Light" !important;
  width: 100%;
}
#labor-tn-laws .holidays-dates {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(152px, 1fr)); /* Adjust the width as needed */
  grid-gap: 1rem;
  gap: 1rem;
}
#labor-tn-laws .holidays-dates .item:nth-last-child(-n+3) { /* Adjust 3 based on your column count */
  justify-self: start; /* Align items to the start */
}
#labor-tn-laws .holidays-dates .item {
  color: #ffffff;
  background-color: #0b3051;
  padding: 15px;
  margin: 10px 0;
  width: 142px;
  min-height: 72px;
  display: flex;
  flex-direction: column;
}
#labor-tn-laws .holidays-dates .item .title {
  color: #ffca00;
  font-family: "Proxima-Nova-Medium";
  display: flex;
  flex-direction: row;
  align-items: center;
}
#labor-tn-laws .holidays-dates .item .paragraph {
  margin-top: 15px;
}
#labor-tn-laws .holidays-dates .item svg {
  transform: scale(1.4);
  margin-right: 10px;
}
#labor-tn-laws .holidays-dates .item svg path {
  fill: none;
}

#africa-locations {
  margin-top: 50px !important;
  margin-bottom: 20px;
}
#africa-locations .heading-h1 {
  margin-top: 0 !important;
}
#africa-locations .sub-heading {
  font-family: "Proxima-Nova-Medium" !important;
  margin-bottom: 0;
}
#africa-locations .contact-items-section {
  margin-top: 20px !important;
}
#africa-locations .contact-items-section .country-img {
  width: 100%;
  position: relative;
}
#africa-locations .contact-items-section .country-img img {
  width: 100%;
  height: auto;
}
#africa-locations .contact-items-section .country-img .country-label {
  width: max-content;
  display: flex;
  align-items: center;
  background-color: rgba(36, 72, 145, 0.6);
  border-radius: 16px;
  font-size: 12px;
  color: #ffffff;
  padding: 5px 8px;
  position: absolute;
  margin: 6px;
  bottom: 6px;
}
#africa-locations .contact-items-section .country-img .country-label img {
  margin-right: 5px;
  width: auto;
}
#africa-locations .contact-items-section .contact-item {
  flex: 0 0 27% !important;
  background-color: transparent;
  color: #234791;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #africa-locations .contact-items-section .contact-item {
    flex: 0 0 76% !important;
  }
}
#africa-locations .contact-items-section .contact-item .btns {
  display: flex;
  align-items: center;
  flex-direction: row;
  justify-content: space-between;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #africa-locations .contact-items-section .contact-item .btns {
    justify-content: center;
    flex-wrap: wrap;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #africa-locations .contact-items-section .contact-item .btns {
    justify-content: center;
    flex-wrap: wrap;
  }
}
#africa-locations .contact-items-section .contact-item .btn, #africa-locations .contact-items-section .contact-item .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer #africa-locations .contact-items-section .contact-item button {
  padding: 10px !important;
}
#africa-locations .contact-items-section .contact-item .btn svg path, #africa-locations .contact-items-section .contact-item .newsletter-table .MuiDataGrid-toolbarContainer button svg path, .newsletter-table .MuiDataGrid-toolbarContainer #africa-locations .contact-items-section .contact-item button svg path {
  stroke: transparent !important;
}

#navigate-african-market-section {
  padding-top: 20px;
  padding-bottom: 20px;
}
#navigate-african-market-section .container {
  display: flex;
  align-items: center;
  flex-direction: row;
  justify-content: space-between;
}
#navigate-african-market-section .container .heading-h1,
#navigate-african-market-section .container .paragraph {
  margin-bottom: 30px;
}
#navigate-african-market-section .container img {
  height: auto;
  width: 100%;
}

#business-tunisia .business-ksa {
  margin-top: 13% !important;
}
#business-tunisia .business-ksa .highlight-text {
  position: absolute;
  color: white;
  font-size: 20px;
  font-family: "Proxima-Nova-SemiBold";
  padding: 10px 16px;
  border-radius: 4px;
  font-weight: bold;
  transition: opacity 0.5s ease-in-out;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #business-tunisia .business-ksa .highlight-text {
    font-size: 14px;
    padding: 5px;
  }
}
#business-tunisia .business-ksa .business-image {
  max-width: 100%;
}
#business-tunisia .business-ksa .bg-purple {
  background-color: #743794;
  margin-left: 20%;
  margin-bottom: 30px;
}
#business-tunisia .business-ksa .bg-yellow {
  background-color: #ffca00;
  color: #234791;
  margin-left: -14%;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #business-tunisia .business-ksa .bg-yellow {
    margin-left: -24%;
  }
}
#business-tunisia .business-ksa .bg-green {
  background-color: #2bbfad;
  margin-left: 15%;
}
#business-tunisia .business-ksa .bg-teal {
  background-color: #009966;
  margin-left: -8%;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #business-tunisia .business-ksa .bg-teal {
    margin-left: -25%;
  }
}
#business-tunisia .business-ksa .bg-blue {
  background-color: #234791;
  margin-left: 12%;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #business-tunisia .business-ksa .bg-blue {
    margin-left: 0%;
  }
}
#business-tunisia .business-ksa .bg-red {
  background-color: #cc3233;
  margin-left: -15%;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #business-tunisia .business-ksa .bg-red {
    margin-left: -33%;
  }
}
#business-tunisia .business-ksa .bg-darkblue {
  background-color: #0b3051;
  margin-left: 8%;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #business-tunisia .business-ksa .bg-darkblue {
    margin-left: -5%;
  }
}

#eor-services-tn {
  margin-top: 50px;
  margin-bottom: 50px;
}
#eor-services-tn .container {
  display: flex;
  flex-direction: row;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #eor-services-tn .container {
    flex-direction: row-reverse;
    flex-direction: column;
  }
}
#eor-services-tn .team-thumbs,
#eor-services-tn .right-section {
  height: 100%;
}
#eor-services-tn .right-section {
  background-color: #0b3051;
  display: flex;
  flex-direction: column;
  padding: 0 20px;
  justify-content: center;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #eor-services-tn .right-section {
    padding: 20px;
  }
}
#eor-services-tn .right-section .paragraph {
  margin: 10px 0 !important;
}

#office-info-ksa {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: stretch;
  flex-wrap: wrap;
  padding: 10px;
  border-radius: 12px;
  background-color: #0b3051;
  position: absolute;
  top: 80%;
  width: 80%;
  left: 10%;
}
@media only screen and (min-width: 1601px) {
  #office-info-ksa {
    top: 84%;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #office-info-ksa {
    flex-direction: row !important;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #office-info-ksa {
    margin: 20px 0;
    flex-direction: column;
    position: initial;
    width: auto;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #office-info-ksa {
    margin: 20px 0;
    flex-direction: column;
    position: initial;
    width: auto;
  }
}
#office-info-ksa .info {
  width: 26%;
  padding: 20px 5px;
  display: flex;
  flex-direction: row;
  align-items: center;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #office-info-ksa .info {
    padding: 5px;
    width: auto;
    margin: auto auto auto 0;
  }
}
#office-info-ksa .info .icon {
  margin-right: 10px;
}
#office-info-ksa .info .label,
#office-info-ksa .info .value {
  margin: 5px 0 !important;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #office-info-ksa .info .label,
  #office-info-ksa .info .value {
    font-size: 16px !important;
  }
}

#office-info-tn {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: stretch;
  flex-wrap: wrap;
  padding: 10px;
  background-color: #0b3051;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #office-info-tn {
    flex-direction: column;
  }
}
#office-info-tn .info {
  width: 26%;
  padding: 20px 5px;
  display: flex;
  flex-direction: row;
  align-items: center;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #office-info-tn .info {
    width: auto;
    margin: auto auto auto 0;
  }
}
#office-info-tn .info .icon {
  margin-right: 10px;
}
#office-info-tn .info .label,
#office-info-tn .info .value {
  margin: 5px 0 !important;
}

#office-location-map {
  margin-top: 50px;
  margin-bottom: 50px;
}
#office-location-map .container {
  display: flex;
  flex-direction: row;
  background: linear-gradient(71deg, #0b3051 0%, #234791 100%);
  align-items: center;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #office-location-map .container {
    flex-direction: column;
    align-items: stretch;
  }
}
#office-location-map .content,
#office-location-map .map-frame {
  height: 100%;
}
#office-location-map .content {
  display: flex;
  flex-direction: column;
  padding: 0 20px;
  justify-content: center;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #office-location-map .content {
    padding: 20px;
  }
}
#office-location-map .content .btn, #office-location-map .content .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer #office-location-map .content button {
  margin-top: 20px;
  margin-bottom: 20px;
}
#office-location-map .content .paragraph {
  display: flex;
  flex-direction: row;
  align-items: center;
}
#office-location-map .content svg {
  margin-right: 5px;
}
#office-location-map .content svg rect {
  fill: transparent;
}
#office-location-map .content svg path {
  stroke: #ffca00;
}
#office-location-map .map-frame {
  display: flex;
  padding: 0 20px 20px 20px;
  flex-direction: column;
  justify-content: center;
}
#office-location-map iframe {
  border: 0;
  height: 100%;
  margin: auto;
  width: 100%;
  min-height: 340px;
}

#office-location-map-ksa {
  margin-top: 50px;
  margin-bottom: 50px;
}
#office-location-map-ksa .heading-h2 {
  margin-bottom: 0px !important;
  font-family: "Proxima-Nova-Bold" !important;
}
#office-location-map-ksa .container {
  display: flex;
  flex-direction: row;
  align-items: center;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #office-location-map-ksa .container {
    flex-direction: column;
    align-items: stretch;
  }
}
#office-location-map-ksa .content {
  display: flex;
  flex-direction: column;
  padding: 0 20px;
  position: absolute;
  top: 48%;
  left: 14%;
  background: linear-gradient(to bottom, rgba(11, 48, 81, 0.5960784314), #234791);
  width: 70%;
  justify-content: center;
  align-items: center;
}
#office-location-map-ksa .content .phone-wrapper {
  margin: 0px 25px;
  display: flex;
  flex-direction: column;
}
#office-location-map-ksa .content .phone-wrapper .paragraph {
  align-items: center !important;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #office-location-map-ksa .content {
    padding: 20px;
  }
}
#office-location-map-ksa .content .btn, #office-location-map-ksa .content .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer #office-location-map-ksa .content button {
  margin-top: 20px;
  margin-bottom: 20px;
}
#office-location-map-ksa .content .paragraph {
  display: flex;
  flex-direction: row;
  align-items: center;
  max-width: 300px;
}
#office-location-map-ksa .content svg {
  margin-right: 5px;
}
#office-location-map-ksa .content svg rect {
  fill: transparent;
}
#office-location-map-ksa .content svg path {
  stroke: #ffca00;
}
#office-location-map-ksa .content .infos {
  display: flex;
  justify-content: center;
}
#office-location-map-ksa .map-frame {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 600px;
}
#office-location-map-ksa iframe {
  border: 0;
  height: 100%;
  margin: auto;
  width: 100%;
  min-height: 340px;
}

.recent-application-pentabell .top-section .btn, .recent-application-pentabell .top-section .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer .recent-application-pentabell .top-section button, .newsletter-list-page .top-section .btn, .newsletter-list-page .top-section .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer .newsletter-list-page .top-section button {
  width: -moz-fit-content;
  width: fit-content;
  margin: auto 0 auto auto;
}

.filter-table-pentabell {
  justify-content: space-between;
  align-items: center;
}

.newsletter-list-page .download-csv {
  margin: auto 0 auto auto;
}

#new-year-countdown {
  padding: 20px 0;
  background: linear-gradient(90deg, #0b3051 0%, #234791 100%);
}
#new-year-countdown .element {
  background-image: url(/_next/static/media/ISO.693b0aef.svg);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  margin: auto;
  width: 453px;
  height: 450px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #new-year-countdown .element {
    width: 300px;
    height: 300px;
    margin-top: 30px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #new-year-countdown .element {
    width: 300px;
    height: 300px;
    margin-top: 30px;
  }
}
#new-year-countdown .text-section {
  max-width: 52%;
  width: 85%;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #new-year-countdown .text-section {
    max-width: 100%;
    width: 100%;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #new-year-countdown .text-section {
    max-width: 100%;
    width: 100%;
  }
}
@media only screen and (min-width: 769px) and (max-width: 1024px) {
  #new-year-countdown .text-section {
    max-width: 44%;
    width: 85%;
  }
}
#new-year-countdown .text-section .sm-title {
  color: #ffca00;
  font-size: 20px;
}
#new-year-countdown .text-section .bg-title {
  color: white;
  font-size: 30px;
  margin: 10px 0 30px;
  font-weight: 600;
  width: 70%;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #new-year-countdown .text-section .bg-title {
    width: 100%;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #new-year-countdown .text-section .bg-title {
    width: 100%;
  }
}
#new-year-countdown .text-section .content {
  color: white;
  font-size: 18px;
  width: 85%;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #new-year-countdown .text-section .content {
    width: 100%;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #new-year-countdown .text-section .content {
    width: 100%;
  }
}
#new-year-countdown .new-year-container {
  max-width: 1500px !important;
  margin: 20px auto;
}
#new-year-countdown .new-year-content {
  display: flex !important;
  justify-content: space-between;
  flex-wrap: nowrap;
  align-items: center;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #new-year-countdown .new-year-content {
    display: flex !important;
    width: auto;
    justify-items: left;
    flex-direction: column-reverse;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #new-year-countdown .new-year-content {
    display: flex !important;
    width: auto;
    justify-items: left;
    flex-direction: column-reverse;
  }
}

#events-page .event-path {
  padding: 15px 0;
  display: flex;
  align-items: center;
}
#events-page .event-path svg {
  transform: scale(0.5);
}
#events-page .event-path svg path {
  fill: #ffffff;
}
#events-page .event-path .link {
  font-size: 18px;
  color: #ffffff;
  text-decoration: none;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #events-page .event-path .link {
    font-size: 16px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #events-page .event-path .link {
    font-size: 16px;
  }
}
#events-page .event-path .link:last-child {
  color: #ffffff;
}
#events-page #event-page .event-section {
  color: #cc3233;
  font-size: 24px;
  text-align: center;
  margin-top: 40px;
  margin-bottom: 10px;
  font-family: "Proxima-Nova-Medium";
}
#events-page #event-page .event-slider {
  position: absolute;
  top: 60%;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #events-page #event-page .event-slider {
    position: initial;
    margin-top: 20px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #events-page #event-page .event-slider {
    position: initial;
    margin-top: 20px;
  }
}
#events-page #event-page .event-slider .event-item {
  height: 90%;
}
#events-page #event-page .event-slider .carousel {
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%;
}
#events-page #event-page .event-slider .viewport {
  overflow: hidden;
}
#events-page #event-page .event-slider .container {
  display: flex;
  margin-bottom: 30px;
}
#events-page #event-page .event-slider .slide {
  flex: 0 0 30%;
  padding: 0 10px;
}
#events-page #event-page .event-slider .prevButton,
#events-page #event-page .event-slider .nextButton {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.6);
  color: white;
  border: none;
  cursor: pointer;
  padding: 10px 15px;
  z-index: 10;
}
#events-page #event-page .event-slider .prevButton {
  left: 10px;
}
#events-page #event-page .event-slider .nextButton {
  right: 10px;
}
#events-page #event-page .featured-event {
  font-family: "Proxima-Nova-Bold" !important;
  font-weight: 500;
  font-size: 48px;
  color: #234791;
  margin-bottom: 20px !important;
  text-align: left;
  margin-top: 40px;
}
#events-page #event-page .featured-event-wrapper {
  background-color: #234791;
  padding: 30px 40px 100px 20px;
  position: relative;
  margin-left: 6.5%;
  margin-bottom: 25%;
}
#events-page #event-page .featured-event-wrapper .card:hover {
  transform: scale(1) !important;
  background-color: #d7e7f8;
}
#events-page #event-page .featured-event-wrapper .heading-h2 {
  margin-bottom: 0px !important;
}
#events-page #event-page .featured-event-wrapper .label-pentabell {
  margin-top: 20px;
  font-family: "Proxima-Nova-Regular" !important;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #events-page #event-page .featured-event-wrapper {
    padding: 30px 40px 0px 20px;
    margin-left: 0%;
    margin-bottom: 20%;
  }
}
#events-page #event-page .quote-box {
  position: relative;
  max-width: 800px;
  margin: 40px auto;
  padding: 30px;
  background-color: transparent;
  font-family: Arial, sans-serif;
}
#events-page #event-page .quote-border {
  height: 0px;
  background-color: #234791;
  margin: 30px 0;
}
#events-page #event-page .quote-content {
  text-align: center;
  padding: 0 10px;
  color: #234791;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #events-page #event-page .quote-icon-top {
    top: 48px !important;
    left: -11px !important;
    position: absolute !important;
    width: 40px !important;
    height: 40px !important;
  }
  #events-page #event-page .quote-icon-bottom {
    top: 193px !important;
    right: -8px !important;
    position: absolute !important;
    width: 40px !important;
    height: 40px !important;
  }
}
#events-page #event-page .quote-content p {
  font-size: 1.1rem;
  font-family: "Proxima-Nova-Bold" !important;
  font-size: 22px !important;
  line-height: 1.6;
  margin: 0;
}
#events-page #event-page .quote-author {
  margin-top: 10px;
  font-weight: bold;
  font-size: 0.95rem;
}
#events-page #event-page .quote-author span {
  font-weight: normal;
  font-style: italic;
}
#events-page #event-page .quote-icon-top {
  top: 14px;
  left: -52px;
  position: absolute;
  width: 40px;
  height: 40px;
}
#events-page #event-page .quote-icon-bottom {
  position: absolute;
  width: 40px;
  height: 40px;
  bottom: 50px;
  right: -20px;
}
#events-page #event-page .event-date {
  font-size: 14px;
  width: -moz-fit-content;
  width: fit-content;
  font-family: "Proxima-Nova-Medium" !important;
  font-size: 16px;
  text-transform: capitalize;
  padding: 6px 18px;
  margin: 10px 0 0 0;
  border-radius: 20px;
  background-color: #ffca00;
  color: #000000;
}
#events-page #event-page .event-date.oil-gaz {
  background-color: #d69b19;
  color: #ffffff;
}
#events-page #event-page .event-date.energy {
  background-color: #cc3233;
  color: #ffffff;
}
#events-page #event-page .event-date.banking-insurance {
  background-color: #0b3051;
  color: #ffffff;
}
#events-page #event-page .event-date.transport {
  background-color: #009966;
  color: #ffffff;
}
#events-page #event-page .event-date.it-telecom {
  background-color: #743794;
  color: #ffffff;
}
#events-page #event-page .event-date.other {
  background-color: #234791;
  color: #ffffff;
}
#events-page #event-page .event-date.pharmaceutical {
  background-color: #2bbfad;
  color: #ffffff;
}
#events-page #event-page .date-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
#events-page #event-page .location-container {
  display: flex;
  align-items: center;
  flex-direction: row;
}
#events-page #event-page .posting-date {
  margin: 10px 0 0 0;
  font-size: 12;
  font-family: "Proxima-Nova-Regular";
  color: #000000;
}
#events-page #event-page .event-item {
  margin-bottom: 30px;
  background-color: transparent;
}
#events-page #event-page .event-item.MuiGrid-item {
  padding-top: 10px;
}
#events-page #event-page .event-item a {
  text-decoration: none;
}
#events-page #event-page .event-item .card-image {
  cursor: pointer;
  border-radius: 5px;
  min-height: 200px;
}
#events-page #event-page .event-item .card {
  transition: transform 0.3s ease, background-color 0.3s ease;
}
#events-page #event-page .event-item .card:hover {
  transform: scale(1.05);
  background-color: #d7e7f8;
}
#events-page #event-page .event-item .card {
  padding: 15px;
  background-color: #d7e7f8;
  display: flex;
  flex-direction: column;
  height: 100%;
  box-shadow: none;
  position: relative;
  border-radius: 12px;
}
#events-page #event-page .event-item .card.featured {
  padding: 10px 10px 0 10px;
  border-radius: 5px;
}
#events-page #event-page .event-item .card-content {
  padding: 0 !important;
}
#events-page #event-page .event-item .card-content .blog-title {
  margin: 15px 0;
  font-size: 24px;
  font-family: "Proxima-Nova-Medium" !important;
  color: #234791;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #events-page #event-page .event-item .card-content .blog-title {
    font-size: 16px;
  }
}
#events-page #event-page .event-item .card-content .blog-description {
  font-size: 16px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
#events-page #event-page .event-item .card-content .blog-description.ksa {
  color: #234791 !important;
  margin: 0 5px !important;
}
#events-page #event-page .event-item .card-image {
  background-color: #dbe8f6;
  object-fit: cover;
}
#events-page #event-page .event-item .card-bottom {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
#events-page #event-page .event-item .card .blog-time {
  font-size: 14px;
  color: #798ba3;
  display: flex;
  align-items: center;
}
#events-page #event-page .event-item .card .blog-time svg {
  margin-right: 5px;
}
#events-page #event-page .event-item .card .btn, #events-page #event-page .event-item .card .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer #events-page #event-page .event-item .card button {
  margin: 0 0 2px 0;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #events-page #event-page .event-item {
    min-width: 305px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #events-page #event-page .event-item {
    min-width: 305px;
  }
}
#events-page #event-page .oil-gaz {
  color: #d69b19;
}
#events-page #event-page .energy {
  color: #cc3233;
}
#events-page #event-page .banking-insurance {
  color: #0b3051;
}
#events-page #event-page .transport {
  color: #009966;
}
#events-page #event-page .it-telecom {
  color: #743794;
}
#events-page #event-page .pharma {
  color: #2bbfad;
}
#events-page #event-page #event-detail {
  margin-top: 50px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #events-page #event-page #event-detail {
    margin-top: 0px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #events-page #event-page #event-detail {
    margin-top: 0px;
  }
}
#events-page #event-page #event-detail .more-events-section {
  margin-bottom: 50px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #events-page #event-page #event-detail .more-events-section {
    overflow-x: auto;
    flex-wrap: nowrap;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #events-page #event-page #event-detail .more-events-section {
    overflow-x: auto;
    flex-wrap: nowrap;
  }
}
#events-page #event-page #event-detail .heading-h1 {
  margin: 40px 0 !important;
  font-size: 40px !important;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #events-page #event-page #event-detail .heading-h1 {
    font-size: 24px !important;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #events-page #event-page #event-detail .heading-h1 {
    font-size: 24px !important;
  }
}
#events-page #event-page #event-detail .text {
  margin: 15px 0;
  color: #3b3b3b !important;
  font-size: 20px !important;
  font-family: "Proxima-Nova-Regular" !important;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #events-page #event-page #event-detail .text {
    font-size: 16px !important;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #events-page #event-page #event-detail .text {
    font-size: 16px !important;
  }
}
#events-page #event-page #event-detail .infoSection {
  background: linear-gradient(to top, #20284b, #234791);
  color: white;
  padding: 16px 50px;
  border-radius: 24px;
  margin: 50px 0;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #events-page #event-page #event-detail .infoSection {
    padding: 20px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #events-page #event-page #event-detail .infoSection {
    padding: 20px;
  }
}
#events-page #event-page #event-detail .item {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  margin-bottom: 8px;
}
#events-page #event-page #event-detail .label {
  font-size: 22px;
  font-family: "Proxima-Nova-Bold" !important;
  color: #ffca00;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #events-page #event-page #event-detail .label {
    font-size: 17px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #events-page #event-page #event-detail .label {
    font-size: 17px;
  }
}
#events-page #event-page #event-detail .value {
  font-size: 22px;
  font-family: "puceProxima-Nova-Regular";
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #events-page #event-page #event-detail .value {
    font-size: 16px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #events-page #event-page #event-detail .value {
    font-size: 16px;
  }
}
#events-page #event-page #event-detail .divider {
  border-left: 2px solid rgba(255, 255, 255, 0.979);
  margin-top: 20px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #events-page #event-page #event-detail .divider {
    display: none;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #events-page #event-page #event-detail .divider {
    display: none;
  }
}
#events-page #event-page #event-detail .bg-title {
  font-family: "Proxima-Nova-Bold";
  color: white;
  font-size: 30px;
  margin: 10px 0 30px;
  font-weight: 600;
  width: 70%;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #events-page #event-page #event-detail .bg-title {
    width: 100%;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #events-page #event-page #event-detail .bg-title {
    width: 100%;
  }
}
#events-page #event-page #event-detail .content {
  font-family: "Proxima-Nova-Regular";
  color: white;
  font-size: 18px;
  width: 85%;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #events-page #event-page #event-detail .content {
    width: 100%;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #events-page #event-page #event-detail .content {
    width: 100%;
  }
}
#events-page #event-page #event-detail .left-side {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #events-page #event-page #event-detail .right-side {
    padding-top: 0px !important;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #events-page #event-page #event-detail .right-side {
    padding-top: 0px !important;
  }
}
#events-page .icon-map {
  position: absolute;
  bottom: 15%;
  right: 8%;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #events-page .icon-map {
    position: initial;
    margin-top: 10%;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #events-page .icon-map {
    position: initial;
  }
}
#events-page .icon-leap {
  position: absolute;
  bottom: 20%;
  left: 10%;
}
#events-page #banner-component, #events-page #our-industries, #events-page #join-us-banner, #events-page #glossary-banner {
  position: relative;
}
#events-page #banner-component .continer_banner_event, #events-page #our-industries .continer_banner_event, #events-page #join-us-banner .continer_banner_event, #events-page #glossary-banner .continer_banner_event {
  margin-top: -5%;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #events-page #banner-component .continer_banner_event, #events-page #our-industries .continer_banner_event, #events-page #join-us-banner .continer_banner_event, #events-page #glossary-banner .continer_banner_event {
    margin-top: auto !important;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #events-page #banner-component .continer_banner_event, #events-page #our-industries .continer_banner_event, #events-page #join-us-banner .continer_banner_event, #events-page #glossary-banner .continer_banner_event {
    margin-top: auto !important;
  }
}
#events-page .sub-heading {
  margin-bottom: 20px !important;
  margin-top: 0 !important;
  font-size: 25px;
  font-family: "Proxima-Nova-SemiBold" !important;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #events-page .sub-heading {
    font-size: 16px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #events-page .sub-heading {
    font-size: 16px;
  }
}
#events-page .heading-h1 {
  margin-top: 0px !important;
  font-family: "Proxima-Nova-Bold" !important;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #events-page .heading-h1 {
    font-size: 35px !important;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #events-page .heading-h1 {
    font-size: 35px !important;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #events-page .custom-max-width {
    padding-left: 20px;
    padding-right: 20px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #events-page .custom-max-width {
    padding-left: 20px;
    padding-right: 20px;
  }
}

#guide-header {
  padding-top: 5%;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  padding-bottom: 15px;
  /*     background-color: $lightBlue2;  */
}
#guide-header .heading-h1 {
  font-size: 2.75rem !important;
  margin-top: 5px !important;
}

#sticky-sidebar-guide {
  padding-left: 7%;
  position: sticky;
  top: 110px;
  left: 0;
  width: 100%;
  z-index: 1000;
  border-radius: 15px;
  max-height: 105vh;
}

.section-guide {
  background-color: #e5f0fc;
  padding: 26px;
  margin-bottom: 20px;
  border-radius: 25px !important;
  width: inherit;
}
.section-guide .form-success {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.section-guide .form-success .sub-heading {
  color: #234791;
  text-align: center;
  font-size: 18px;
  margin: 5px 0 !important;
}
.section-guide .form-success .heading-h2 {
  margin: 5px 0 0 0 !important;
  font-family: "Proxima-Nova-Semibold" !important;
  font-size: 20px;
}
.section-guide .title {
  font-size: 20px;
  font-family: "Proxima-Nova-Semibold" !important;
  color: #234791;
  margin: 10px 0;
}
.section-guide .btn, .section-guide .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer .section-guide button {
  margin-top: 20px;
  text-align: center;
  display: flex;
  justify-content: center;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .section-guide {
    padding: 20px !important;
  }
}
.section-guide .css-16wblaj-MuiInputBase-input-MuiOutlinedInput-input,
.section-guide .css-1pk1fka {
  height: 14px !important;
}

@media only screen and (min-width: 320px) and (max-width: 480px) {
  .section-guide {
    padding: 20px !important;
  }
}

.description-guide {
  color: #0b3051;
  font-family: "Proxima-Nova-Regular" !important;
  font-size: 12px;
  padding-top: 15px !important;
}

.item-list-guide {
  border-radius: 12px;
  background-color: rgba(228, 239, 252, 0.7019607843);
  padding: 20px;
  margin-top: 20px;
}
.item-list-guide .subtitle-item {
  font-weight: 600;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
}
.item-list-guide .remove-button {
  color: #c50000;
  text-transform: capitalize;
  font-weight: 600;
}
.item-list-guide .remove-button:hover {
  background-color: #c50000;
  color: white;
  text-transform: capitalize;
  font-weight: 600;
}
.item-list-guide .subtitle-title,
.item-list-guide .subtitle-description {
  margin-bottom: 5px;
}

#search-bar-glossary .btn, #search-bar-glossary .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer #search-bar-glossary button {
  margin: 0 5px;
}
#search-bar-glossary .btn svg, #search-bar-glossary .newsletter-table .MuiDataGrid-toolbarContainer button svg, .newsletter-table .MuiDataGrid-toolbarContainer #search-bar-glossary button svg {
  margin: 0 !important;
}

#glossary-page {
  display: flex;
  padding-bottom: 50px;
}
#glossary-page .custom-max-width {
  padding-left: 70px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #glossary-page .custom-max-width {
    padding-left: 28px;
    padding-right: 28px;
  }
}
#glossary-page .letters {
  margin: 35px 0px;
  scroll-margin-top: 80px;
}
#glossary-page .letters .length {
  margin-left: 40px;
  font-weight: 600;
  background: #ffca00;
  width: 20px;
  padding: 5px;
  border-radius: 20px;
  text-align: center;
}
#glossary-page .letters .letter {
  margin-bottom: 20px;
  background-color: #234791;
  border-radius: 5px;
  text-align: center;
  width: 40px;
  color: white;
  font-size: 30px;
}
#glossary-page .letters .words {
  display: flex;
  flex-direction: column;
}
#glossary-page .letters .word {
  margin: 5px 0;
  text-decoration: none;
  color: black;
}
#glossary-page .letters .word:hover {
  color: #234791;
}
#glossary-page .letters .glossary-button {
  text-transform: none;
  border: solid 1px;
  border-radius: 20px;
  padding: 5px 10px;
  margin-top: 5px;
  color: #234791;
}
#glossary-page .pagination {
  margin-top: 20px;
}
#glossary-page .featured-title {
  font-size: 2.5rem;
  font-family: "Proxima-Nova-SemiBold" !important;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #glossary-page .featured-title {
    font-size: 1.75rem !important;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #glossary-page .featured-title {
    font-size: 1.75rem !important;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #glossary-page {
    padding-bottom: 20px;
  }
}
#glossary-page .btn.white, #glossary-page .newsletter-table .MuiDataGrid-toolbarContainer button.white, .newsletter-table .MuiDataGrid-toolbarContainer #glossary-page button.white {
  color: #ffffff;
  border-color: #ffffff;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #glossary-page .btn.white, #glossary-page .newsletter-table .MuiDataGrid-toolbarContainer button.white, .newsletter-table .MuiDataGrid-toolbarContainer #glossary-page button.white {
    width: auto;
    display: flex;
    justify-content: center;
  }
}
#glossary-page .first-blog {
  display: flex;
  justify-content: space-between;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #glossary-page .first-blog {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    flex-direction: column-reverse;
    align-content: flex-start;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #glossary-page .first-blog {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    flex-direction: column-reverse;
    align-content: flex-start;
  }
}
#glossary-page .blog-img-section {
  border: 1px solid #699bd4;
  background-color: transparent;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: auto;
  min-width: 675px;
  box-shadow: none;
  position: relative;
  margin: 10px 0px 20px 30px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #glossary-page .blog-img-section {
    min-width: 200px;
    margin: 50px 0px 20px 0px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #glossary-page .blog-img-section {
    min-width: 200px;
    margin: 50px 0px 20px 0px;
  }
}
#glossary-page .blog-img-section .img-section {
  background-size: cover;
  background-position: center;
  padding: 100px;
  margin: 15px;
  background-color: #dbe8f6;
  height: 100%;
  min-height: 150px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #glossary-page .blog-img-section .img-section {
    min-height: 100px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #glossary-page .blog-img-section .img-section {
    min-height: 100px;
  }
}
#glossary-page .last-blog {
  min-height: 220px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  margin-bottom: 20px;
}
#glossary-page .last-blog .description {
  font-size: 18px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #glossary-page .last-blog .description {
    font-size: 16px;
  }
}
#glossary-page .last-blog .title,
#glossary-page .last-blog .description {
  color: #234791;
  margin-bottom: 20px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #glossary-page .last-blog .title,
  #glossary-page .last-blog .description {
    margin-bottom: 20px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #glossary-page .last-blog .title,
  #glossary-page .last-blog .description {
    margin-bottom: 20px;
  }
}

#social-media-share {
  background-color: #dbe8f6;
  padding: 15px;
  margin-bottom: 20px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #social-media-share {
    margin-top: 20px;
  }
}
#social-media-share .title {
  text-align: center;
  font-size: 20px;
  font-family: "Proxima-Nova-Semibold" !important;
  color: #0b3051;
  margin: 10px 0;
}
#social-media-share div {
  display: flex;
}
#social-media-share div .btn, #social-media-share div .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer #social-media-share div button {
  margin: auto;
  padding: 10px;
}
#social-media-share div .btn svg, #social-media-share div .newsletter-table .MuiDataGrid-toolbarContainer button svg, .newsletter-table .MuiDataGrid-toolbarContainer #social-media-share div button svg {
  margin: 0 0 0 0 !important;
}

#blog__categories__slider {
  margin-top: 20px;
}

.categories-list {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
}
.categories-list .category-2 {
  flex: 0 0 13%;
  min-width: max-content;
}
.categories-list .category-2 a {
  text-align: center;
  display: block;
  text-decoration: none;
  font-size: 20px;
  margin-right: 10px;
  color: #798ba3;
  border: 1px solid #798ba3;
  border-radius: 18px;
  padding: 8px 20px;
  margin-bottom: 5px;
}
.categories-list .category a {
  display: block;
  text-decoration: none;
  font-size: 14px;
  margin-right: 10px;
  color: #798ba3;
  border: 1px solid #798ba3;
  border-radius: 18px;
  padding: 4px 10px;
  margin-bottom: 5px;
}
.categories-list .category a.light {
  border: 1px solid #ffffff;
  color: #ffffff;
}

#one-blog-details .categories-path {
  padding: 15px 0;
  display: flex;
  align-items: center;
}
#one-blog-details .categories-path svg {
  transform: scale(0.5);
}
#one-blog-details .categories-path svg path {
  fill: #798ba3;
}
#one-blog-details .categories-path .link {
  font-size: 18px;
  color: #798ba3;
}
#one-blog-details .categories-path .link:last-child {
  color: #0b3051;
}
#one-blog-details .blog-content {
  padding: 15px;
  box-shadow: 0px 10px 40px rgba(10, 48, 82, 0.0784313725);
}
#one-blog-details .blog-content p {
  font-size: 18px;
}
#one-blog-details .blog-content p,
#one-blog-details .blog-content ul,
#one-blog-details .blog-content div,
#one-blog-details .blog-content li {
  background: transparent !important;
  font-size: 18px !important;
}
#one-blog-details .blog-content figure {
  width: 100% !important;
  margin: 10px 0;
  height: auto;
  min-width: 300px;
}
#one-blog-details .blog-content figure img {
  width: -webkit-fill-available !important;
  height: auto;
}
#one-blog-details .blog-content img {
  min-width: 300px;
  width: -webkit-fill-available !important;
  height: auto;
}
#one-blog-details .blog-content table {
  width: -webkit-fill-available !important;
}
#one-blog-details .blog-content .summary {
  margin: 15px 0;
}
#one-blog-details .blog-content .summary .dropdown-header {
  display: flex;
  align-items: center;
}
#one-blog-details .blog-content .summary svg path {
  fill: #234791;
}
#one-blog-details .date {
  font-size: 16px;
  display: flex;
  align-items: center;
}
#one-blog-details .date svg {
  margin-right: 10px;
}
#one-blog-details .date svg path {
  fill: #ffffff;
}
#one-blog-details .sidebar .last-blog {
  margin-bottom: 10px;
}
#one-blog-details .sidebar .last-blog .btn-ghost.white {
  margin-top: 10px;
  padding: 4px 0;
  width: 100%;
}

.btn-filled-yellow {
  fill: #ffca00 !important;
}

#glossary-page-details {
  background-color: #dbe8f6;
}
#glossary-page-details #glossary-header {
  padding-top: 10%;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  padding-bottom: 15px;
  background-color: #dbe8f6;
}
#glossary-page-details #glossary-header .letter {
  color: #234791;
  font-size: 100px;
  font-weight: 800;
}
#glossary-page-details #glossary-header .heading-h1 {
  font-size: 2.75rem !important;
  margin-top: 5px !important;
}
#glossary-page-details #glossary-header .css-o1xwpy-MuiGrid-root > .MuiGrid-item {
  padding-left: 7% !important;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #glossary-page-details #glossary-header {
    align-items: center;
    padding-top: 20%;
  }
  #glossary-page-details #glossary-header .css-zow5z4-MuiGrid-root > .MuiGrid-item {
    padding-top: 0 !important;
  }
  #glossary-page-details #glossary-header .heading-h1 {
    font-size: 1.75rem !important;
  }
}
#glossary-page-details #glossary-header .MuiGrid-container {
  align-items: center;
}
#glossary-page-details .pentabell-company {
  background: linear-gradient(180deg, #234791 0%, #20284b 100%);
  border-radius: 24px;
  padding: 40px;
  margin: 40px 0px;
  display: flex;
  align-items: center;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #glossary-page-details .pentabell-company {
    flex-direction: column;
    margin: 10px 0px 40px;
    padding: 40px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #glossary-page-details .pentabell-company {
    flex-direction: column;
    margin: 10px 0px 40px;
    padding: 40px;
  }
}
#glossary-page-details .pentabell-company .content {
  color: white;
  padding-right: 20px;
}
#glossary-page-details .pentabell-company .content .title {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 30px;
}
#glossary-page-details .pentabell-company .content .description {
  font-size: 18px;
  font-weight: 300;
  margin-bottom: 30px;
}
#glossary-page-details .pentabell-company .content .btn-filled-yellow {
  background: #ffca00;
  color: #234791;
  padding: 10px 30px;
  text-transform: none;
  border-radius: 0;
  font-size: 16px;
}
#glossary-page-details .pentabell-company .pentabell-offices-icon {
  padding-left: 20px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #glossary-page-details .pentabell-company .pentabell-offices-icon {
    padding-left: 0;
    margin-top: 40px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #glossary-page-details .pentabell-company .pentabell-offices-icon {
    padding-left: 0;
    margin-top: 40px;
  }
}
#glossary-page-details .glossary-social-media-icons {
  padding: 120px 40px;
  display: flex;
  flex-direction: column;
  align-items: end;
  justify-content: left;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #glossary-page-details .glossary-social-media-icons {
    padding: 10px 0;
    align-items: center;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
  }
  #glossary-page-details .glossary-social-media-icons svg {
    margin: 0px 20px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #glossary-page-details .glossary-social-media-icons {
    padding: 10px 0;
    align-items: center;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
  }
  #glossary-page-details .glossary-social-media-icons svg {
    margin: 0px 20px;
  }
}
#glossary-page-details .glossary-social-media-icons svg {
  margin-bottom: 10px;
  border-radius: 50%;
  border: 1px solid;
  cursor: pointer;
  padding: 15px;
}
#glossary-page-details .glossary-social-media-icons svg:hover {
  border: 1px solid #234791;
}
#glossary-page-details .glossary-social-media-icons svg:hover path {
  fill: #234791;
}
#glossary-page-details .date {
  font-size: 16px;
  display: flex;
  align-items: center;
  color: #234791;
  font-weight: 500;
}
#glossary-page-details .date svg {
  margin-right: 10px;
}
#glossary-page-details .date svg path {
  fill: #234791;
}
#glossary-page-details .date svg:last-child {
  margin-left: 20px;
}
#glossary-page-details .blog-img {
  background-color: #234791;
  max-width: 90%;
  height: auto;
  max-height: 80%;
  margin-bottom: 20px;
}
#glossary-page-details .blog-img img {
  height: auto;
  width: 100%;
  margin: auto;
  margin-left: 20px;
  margin-bottom: -20px;
  margin-top: 20px;
}
#glossary-page-details .last-word {
  background-color: #ffca00;
}
#glossary-page-details .glossary-path {
  padding: 15px 0;
  display: flex;
  align-items: center;
}
#glossary-page-details .glossary-path svg {
  transform: scale(0.5);
}
#glossary-page-details .glossary-path svg path {
  fill: #798ba3;
}
#glossary-page-details .glossary-path .link {
  font-size: 18px;
  color: #798ba3;
  text-decoration: none;
}
#glossary-page-details .glossary-path .link:last-child {
  color: #0b3051;
}
#glossary-page-details .glossary-path .word {
  font-size: 18px;
  color: "black";
  text-decoration: none;
}
#glossary-page-details .section-related-blog {
  margin-bottom: 20px;
}
#glossary-page-details .section-related-blog .last-blog .card {
  position: relative;
}
#glossary-page-details .section-related-blog .last-blog .card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to top, #0b3051, rgba(11, 48, 81, 0));
  opacity: 0.6;
  pointer-events: none;
}
#glossary-page-details .section-related-blog .last-blog .card .card-media {
  height: 185px;
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #glossary-page-details .section-related-blog .last-blog .card .card-media {
    height: 136px;
  }
}
#glossary-page-details .section-related-blog .last-blog .card .card-text {
  color: #ffffff;
  position: absolute;
  bottom: 10px;
  left: 10px;
  font-size: 18px;
  padding: 0px 2px;
  font-family: "Proxima-Nova-Semibold" !important;
}
#glossary-page-details .section-related-blog .last-blog .card .card-text button {
  margin: 0;
  padding: 5px 0px;
  justify-content: left;
}
#glossary-page-details .section-related-blog .last-blog .card .card-text a {
  margin: 0;
  padding: 5px 0px;
  justify-content: left;
}
#glossary-page-details .section-related-blog .btn-filled.blue {
  width: max-content;
  margin: 15px auto;
}
#glossary-page-details .section {
  background-color: #dbe8f6;
  padding: 15px;
  margin-bottom: 20px;
}
#glossary-page-details .section .title {
  font-size: 20px;
  font-family: "Proxima-Nova-Semibold" !important;
  color: #0b3051;
  margin: 10px 0;
}
#glossary-page-details .section .description {
  color: #0b3051;
  margin-bottom: 10px;
  font-size: 16px;
}
#glossary-page-details .section .btn, #glossary-page-details .section .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer #glossary-page-details .section button {
  margin-top: 20px;
  width: -webkit-fill-available;
  text-align: center;
  display: flex;
  justify-content: center;
}
#glossary-page-details #sticky-sidebar {
  padding-left: 7%;
  position: sticky;
  top: 110px;
  left: 0;
  width: 100%;
  z-index: 1000;
  max-height: 80vh;
}
#glossary-page-details #content-table {
  padding: 10px;
  max-height: 50vh;
  margin-bottom: 20px;
  overflow: auto;
}
#glossary-page-details #content-table::-webkit-scrollbar {
  border-radius: 8px;
  background: #ffffff;
  width: 12px;
}
#glossary-page-details #content-table::-webkit-scrollbar-thumb {
  background: #ffca00;
  border-radius: 8px;
}
#glossary-page-details #content-table::-webkit-scrollbar-track {
  background: transparent;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #glossary-page-details #content-table {
    max-height: 65vh;
  }
}
#glossary-page-details #content-table ul {
  list-style: none;
  padding-left: 1em;
}
#glossary-page-details #content-table ul li {
  margin-bottom: 10px;
}
#glossary-page-details #content-table ul li a {
  color: #ffffff;
  text-decoration: none;
  font-family: "Proxima-Nova-Medium";
  font-size: 16px;
}
#glossary-page-details #content-table ul > li::before {
  content: "";
  display: inline-block;
  width: 12px;
  height: 12px;
  background-color: #ffca00;
  margin-right: 0.5em;
}

#back-to-top .btn-filled-yellow {
  background: #ffca00;
  color: #234791;
  padding: 10px 30px;
  text-transform: none;
  border-radius: 0;
  font-size: 16px;
}

.filter-popup {
  width: 100%;
  height: auto;
  transition: all 0.3s ease;
  overflow: hidden;
  max-height: 0;
  opacity: 0;
}
.filter-popup.open {
  max-height: 2000px;
  opacity: 1;
}
.filter-popup .filter-popup-content {
  padding: 15px;
}
.filter-popup .filter-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}
.filter-popup .filter-actions .btn, .filter-popup .filter-actions .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer .filter-popup .filter-actions button {
  width: 100%;
  justify-content: center;
  min-width: 100px;
}

.filter-toggle {
  margin-left: 15px;
}

@media (max-width: 768px) {
  .filter-popup.open {
    max-height: 3000px;
  }
}
.coporate-profile-banner {
  align-items: flex-end !important;
  height: 100vh;
  min-height: 100vh;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .coporate-profile-banner {
    align-items: flex-end !important;
    padding-top: 80px;
    height: auto !important;
    min-height: auto !important;
  }
}
.coporate-profile-banner .earth-img {
  animation: scaleUp 1s ease-out forwards;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .coporate-profile-banner .earth-img {
    width: 290px;
  }
}
.coporate-profile-banner #success-nbrs {
  color: #ffffff;
  align-items: center;
  justify-content: center;
}
.coporate-profile-banner #success-nbrs .nbr-element {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.coporate-profile-banner #success-nbrs .nbr-element .nbr {
  opacity: 0;
  animation: fadeInUp 1.2s ease-out forwards;
  animation-delay: 0.4s;
}
.coporate-profile-banner #success-nbrs .nbr-element .nbr-txt {
  opacity: 0;
  animation: fadeInUp 1.2s ease-out forwards;
  animation-delay: 0.6s;
}
.coporate-profile-banner #success-nbrs .nbr-element .line {
  opacity: 0;
  animation: fadeInUp 1.2s ease-out forwards;
  animation-delay: 0.8s;
}
.coporate-profile-banner #success-nbrs .nbr-element.align-end {
  align-self: end;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .coporate-profile-banner #success-nbrs .nbr-element.align-end {
    align-self: flex-start;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .coporate-profile-banner #success-nbrs .nbr-element.align-end {
    margin-bottom: -34px;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .coporate-profile-banner #success-nbrs .nbr-element.align-end-mobile {
    margin-top: 50px;
  }
}
.coporate-profile-banner #success-nbrs .line {
  height: 72px;
  width: 2px;
  margin: 0 auto;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0) 100%);
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .coporate-profile-banner #success-nbrs .line {
    height: 52px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .coporate-profile-banner #success-nbrs .line {
    height: 52px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .coporate-profile-banner #success-nbrs {
    justify-content: space-between;
  }
}
@media only screen and (min-width: 769px) and (max-width: 1024px) {
  .coporate-profile-banner #success-nbrs {
    justify-content: space-between;
  }
}
.coporate-profile-banner .d-block.d-sm-none {
  display: none;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .coporate-profile-banner .d-block.d-sm-none {
    display: block;
  }
}
.coporate-profile-banner #overview-section {
  background-color: rgba(36, 72, 145, 0.39);
  padding: 20px 60px;
  border-radius: 5px;
  margin: 20px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .coporate-profile-banner #overview-section {
    padding: 20px;
    margin: 10px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .coporate-profile-banner #overview-section {
    padding: 20px;
    margin: 10px;
  }
}
.coporate-profile-banner #overview-section .center-btn {
  align-self: center;
  margin: auto 0 auto auto;
  display: flex;
  justify-content: flex-end;
  width: 100%;
}
.coporate-profile-banner #overview-section .center-btn svg {
  transform: scale(1.5);
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .coporate-profile-banner #overview-section .center-btn {
    padding-top: 15px;
    justify-content: center;
  }
}
.coporate-profile-banner #overview-section .btn-filled {
  padding: 15px 15px !important;
}
.coporate-profile-banner #overview-section .btn-filled.bold {
  font-weight: 600;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .coporate-profile-banner #overview-section .btn-filled {
    width: 100% !important;
    text-align: center;
    display: flex;
    justify-content: center;
  }
}
.coporate-profile-banner #overview-section .btn-filled:hover {
  color: #ffca00;
}
.coporate-profile-banner #overview-section .btn-filled:hover svg path {
  fill: #ffca00 !important;
}
@keyframes scaleUp {
  from {
    transform: scale(0.8);
    opacity: 0.2;
  }
  to {
    transform: scale(1.2);
    opacity: 1;
  }
}
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.custom-sub-heading {
  font-size: 18px !important;
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .custom-sub-heading {
    font-size: 16px !important;
  }
}

#who-we-are-section {
  height: auto;
  padding-bottom: 0;
  padding-top: 50px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #who-we-are-section {
    padding-bottom: 20px;
  }
}
#who-we-are-section .z-index {
  position: relative;
}
#who-we-are-section .mt-2 {
  margin-top: 20px !important;
}
#who-we-are-section #team-images {
  position: relative;
  margin-bottom: 50px;
}
#who-we-are-section #team-images .team-img {
  z-index: 10;
  position: inherit;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #who-we-are-section #team-images .team-img .MuiGrid-item:nth-child(2) img {
    display: none;
  }
}
#who-we-are-section #team-images .team-img .image {
  transition: opacity 0.5s ease-in-out;
  opacity: 1;
}
#who-we-are-section #team-images .team-img .fade-out {
  opacity: 0;
}
#who-we-are-section #team-images .team-img .fade-in {
  opacity: 1;
}
#who-we-are-section .fixed-element {
  z-index: 0;
  position: absolute;
  height: auto;
}
#who-we-are-section .fixed-element.green {
  bottom: -50px;
  left: -50px;
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #who-we-are-section .fixed-element.green {
    left: -20px;
    bottom: -20px;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #who-we-are-section .fixed-element.green {
    bottom: -26px;
    left: 9px;
    max-width: 77px;
  }
}
#who-we-are-section .fixed-element.yellow {
  bottom: -160px;
  right: -50px;
  z-index: 1;
  max-width: 150px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #who-we-are-section .fixed-element.yellow {
    right: 0;
    z-index: 10;
    max-width: 55px;
    bottom: -50px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #who-we-are-section .fixed-element.yellow {
    right: 0;
    z-index: 10;
    max-width: 55px;
    bottom: -50px;
  }
}
#who-we-are-section .country-data {
  margin-top: -150px;
  margin-bottom: 30px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #who-we-are-section .country-data {
    margin-top: 0;
    margin-bottom: 0;
    padding-bottom: 0 !important;
  }
}
#who-we-are-section #graph-map {
  margin-top: 40px !important;
}
#who-we-are-section #graph-map .custom-max-width.p0 {
  padding-left: 0 !important;
  padding-right: 0 !important;
  margin: 0 !important;
}
#who-we-are-section #graph-map .gloabl-sites {
  margin-bottom: -100px;
  max-height: 750px;
  width: auto;
  margin: auto;
  display: flex;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #who-we-are-section #graph-map .gloabl-sites {
    margin-top: 20px;
    min-width: 300px;
    max-width: 350px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #who-we-are-section #graph-map .gloabl-sites {
    margin-bottom: 0;
    max-height: auto;
    width: 100%;
  }
}
@media only screen and (min-width: 769px) and (max-width: 1024px) {
  #who-we-are-section #graph-map .gloabl-sites {
    margin-bottom: 0;
    max-height: auto;
    width: 100%;
  }
}

#coporate-profile-partners {
  margin-top: -15px;
  padding: 20px 10px;
  padding-top: 50px;
  padding-bottom: 50px;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0) 20%, rgba(35, 71, 145, 0.5) 30%, rgb(35, 71, 145) 100%);
  pointer-events: none;
  z-index: 10;
}
#coporate-profile-partners .white-bg {
  background-color: rgba(219, 232, 246, 0.8196078431);
  border-radius: 15px;
  padding: 12px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #coporate-profile-partners .white-bg {
    padding: 0;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #coporate-profile-partners {
    background: #dbe8f6 !important;
    padding-top: 10px;
    margin: 20px 0 0 !important;
    padding-bottom: 10px;
  }
}

#coporate-profile-services .nowrap-section {
  flex-direction: row;
  align-items: center;
  flex-wrap: nowrap;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #coporate-profile-services .nowrap-section {
    flex-direction: column;
    flex-wrap: wrap;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #coporate-profile-services .nowrap-section {
    flex-direction: column;
    flex-wrap: wrap;
  }
}
#coporate-profile-services .relative-section {
  position: relative;
  margin-top: 35px;
}
#coporate-profile-services .sub-heading {
  font-size: 18px;
}
#coporate-profile-services .fixed-element {
  z-index: 0;
  position: absolute;
  height: auto;
  top: -20px;
  left: -50px;
  max-width: 100px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #coporate-profile-services .fixed-element {
    max-width: 73px;
    height: auto;
    top: -28px;
    left: 0;
    max-height: 318px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #coporate-profile-services .fixed-element {
    max-width: 73px;
    height: auto;
    top: -28px;
    left: 0;
    max-height: 318px;
  }
}
#coporate-profile-services .contents {
  margin-left: -20px;
  background: linear-gradient(to bottom, #0b3051 0%, #234791 100%);
  filter: drop-shadow(0 0 40px rgba(11, 48, 81, 0.57));
  padding: 15px 25px;
}
#coporate-profile-services .contents .paragraph {
  margin-top: 15px;
  margin-bottom: 30px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #coporate-profile-services .contents .paragraph {
    margin-bottom: 10px;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #coporate-profile-services .contents {
    margin-left: 0;
    padding: 10px 15px;
    margin-top: -150px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #coporate-profile-services .contents {
    margin-left: 0;
    padding: 10px 15px;
    margin-top: -150px;
  }
}

#coporate-profile-testimonials {
  margin: auto;
  padding: 40px 0;
  max-width: 100%;
  overflow: hidden;
}
#coporate-profile-testimonials .embla__container {
  display: flex;
}
#coporate-profile-testimonials .embla__viewport {
  overflow: hidden;
  width: 100%;
}
#coporate-profile-testimonials .embla__slide__content {
  width: 80%;
  display: flex;
  flex-direction: column;
  align-content: center;
  align-items: flex-start;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #coporate-profile-testimonials .embla__slide__content {
    width: 100%;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #coporate-profile-testimonials .embla__slide__content {
    width: 100%;
  }
}
#coporate-profile-testimonials .embla__slide {
  flex: 0 0 100%;
  min-width: 0;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  height: auto;
}
#coporate-profile-testimonials .embla__controls {
  width: auto;
  text-align: center;
}
#coporate-profile-testimonials .embla__controls .embla__button {
  background: rgba(35, 71, 145, 0.1490196078);
  margin: auto 8px;
  border: 0px solid rgba(228, 239, 252, 0.7);
  border-radius: 100%;
  width: 62px;
  height: 62px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #coporate-profile-testimonials .embla__controls .embla__button {
    width: 42px;
    height: 42px;
    padding: 0;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #coporate-profile-testimonials .embla__controls .embla__button {
    width: 42px;
    height: 42px;
    padding: 0;
  }
}
#coporate-profile-testimonials .union-img {
  max-width: 50px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #coporate-profile-testimonials .union-img {
    max-width: 25px;
  }
}
#coporate-profile-testimonials .union-img-rotate {
  rotate: -180deg;
}
#coporate-profile-testimonials .user-info {
  flex-direction: row;
  display: flex;
  align-items: center;
  margin-top: 20px;
  justify-content: flex-end;
  font-style: italic;
}
#coporate-profile-testimonials .user-picture {
  border-radius: 100%;
  border: 1px solid #ffca00;
  background-color: #dbe8f6;
  height: 54px;
  width: 54px;
}
#coporate-profile-testimonials .ml-2 {
  margin-left: 20px;
}

#coporate-profile-industries .single-industry {
  width: 100%;
  min-height: 100px;
  padding: 30px 50px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #coporate-profile-industries .single-industry {
    padding: 15px;
    min-height: 50px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #coporate-profile-industries .single-industry {
    padding: 10px 20px;
  }
}
#coporate-profile-industries .single-industry .align-right {
  display: flex;
  justify-content: flex-end;
}
#coporate-profile-industries .single-industry .display-decription {
  background: rgba(35, 71, 145, 0.1490196078);
  margin: auto 8px;
  border-radius: 100%;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  width: 62px;
  height: 62px;
  text-align: center;
  border: 0px solid #ffffff;
  rotate: -90deg;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: transform 0.3s ease;
  transform-origin: center center;
}
#coporate-profile-industries .single-industry .display-decription svg {
  transform: scale(1.8);
}
#coporate-profile-industries .single-industry .display-decription svg path {
  stroke: #ffffff;
}
#coporate-profile-industries .single-industry .display-decription svg rect {
  stroke: transparent;
}
#coporate-profile-industries .single-industry .display-decription.rotate {
  rotate: 90deg;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #coporate-profile-industries .single-industry .display-decription {
    width: 62px;
    height: 62px;
  }
}
#coporate-profile-industries .single-industry .description p {
  font-size: 20px !important;
}
#coporate-profile-industries .single-industry .paragraph {
  margin-top: 20px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #coporate-profile-industries .single-industry .paragraph {
    margin-top: 10px;
  }
}

#expert-care-section {
  padding-top: 0px;
  padding-bottom: 40px;
}
#expert-care-section .mb-1 {
  margin-bottom: 10px;
  margin-top: 10px;
}
#expert-care-section .heading-h1 {
  font-size: 36px !important;
}

.why-matters-section {
  z-index: 1;
}
.why-matters-section.d-none-sm {
  padding: 10px 20px !important;
  background-color: #234791;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .why-matters-section.d-none-sm {
    display: none;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .why-matters-section.d-none-sm {
    display: none;
  }
}
.why-matters-section.d-block-sm {
  display: none;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .why-matters-section.d-block-sm {
    display: inline;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .why-matters-section.d-block-sm {
    display: inline;
  }
}

#sustinability-section {
  position: relative;
  min-height: 94vh;
  max-height: 200vh;
  padding: 20px 0;
  width: 100%;
  overflow: hidden;
  display: flex;
  align-items: center;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #sustinability-section {
    max-height: 80vh;
    min-height: 36vh;
  }
}
#sustinability-section .m-auto {
  margin: auto;
}
#sustinability-section .sustinability-slides {
  position: absolute;
  inset: 0;
  z-index: 0;
  transition: background-image 0.5s ease-in-out;
  background-size: cover;
  background-position: center;
}
#sustinability-section .relative-section {
  position: relative;
  z-index: 1;
  height: 100%;
  display: flex;
  align-items: center;
  margin: auto;
  display: flex;
}
#sustinability-section .relative-section .sdg-img, #sustinability-section .relative-section .iso-img {
  width: auto;
  min-height: 36px;
  height: 36px;
  max-height: 50px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #sustinability-section .relative-section {
    margin-bottom: 0;
    align-items: flex-end;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #sustinability-section .relative-section {
    margin-bottom: 0;
    align-items: flex-end;
  }
}
#sustinability-section .relative-div {
  position: relative;
}
#sustinability-section .relative-div .heading-h2 {
  text-shadow: 1px 1px 2px #172541;
}
#sustinability-section .slider-control {
  background: transparent;
  margin: auto 8px;
  border: 0px solid rgba(228, 239, 252, 0.7);
  border-radius: 100%;
  width: 62px;
  height: 62px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #sustinability-section .slider-control {
    width: 42px;
    height: 42px;
    padding: 0;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #sustinability-section .slider-control {
    width: 42px;
    height: 42px;
    padding: 0;
  }
}

#coporate-profile-csr {
  padding-top: 20px;
  padding-bottom: 20px;
  align-items: center !important;
  height: 100%;
  min-height: 50vh;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #coporate-profile-csr {
    align-items: flex-end !important;
    padding-top: 20px;
    height: auto !important;
    min-height: auto !important;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #coporate-profile-csr .text-white {
    text-align: center;
  }
}
#coporate-profile-csr .values {
  flex-wrap: nowrap;
  overflow-x: hidden;
  margin-top: 30px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #coporate-profile-csr .values {
    overflow-x: auto;
  }
}
#coporate-profile-csr .values .value-div {
  min-width: 276px;
  width: 100%;
  margin-right: 15px;
  height: 374px;
  border-radius: 15px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: 15px;
}
#coporate-profile-csr .values .value-div:last-child {
  margin-right: 0;
}
#coporate-profile-csr .values .value-div .content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  border-radius: 15px;
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
  justify-content: space-between;
  height: auto;
  min-height: 0;
  transition: min-height 6s ease-out;
}
#coporate-profile-csr .values .value-div .paragraph {
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 16ch;
  /* Show only first 10 characters */
  transition: max-width 0.3s ease;
}
#coporate-profile-csr .values .value-div:hover {
  padding: 0;
}
#coporate-profile-csr .values .value-div:hover .paragraph {
  white-space: normal;
  /* allow full text to wrap */
  max-width: 1000px;
  /* or 100% */
}
#coporate-profile-csr .values .value-div:hover .content {
  min-height: -webkit-fill-available;
  height: -webkit-fill-available !important;
  transition: height 6s ease-out;
}

#hunter-section {
  margin-top: 40px;
}
#hunter-section .video-container {
  position: relative;
  margin: 40px auto 20px;
  width: 100%;
  min-width: 240px;
  max-width: 800px;
  z-index: 10;
}
#hunter-section .video-container video {
  width: 96%;
  margin: auto;
  height: auto;
  display: block;
  border-radius: 10px;
  border: 4px solid #ffffff;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}
#hunter-section .text-hunter {
  font-size: 40px !important;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #hunter-section .text-hunter {
    font-size: 32px !important;
  }
}
#hunter-section .play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 70px;
  height: 70px;
  background-color: transparent;
  border-radius: 100%;
  cursor: pointer;
  transition: transform 0.2s ease-in-out;
  z-index: 2;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}
#hunter-section .play-button:hover {
  transform: translate(-50%, -50%) scale(1.1);
}
#hunter-section .hunter-content {
  margin-bottom: 50px;
  margin-top: 50px;
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #hunter-section .hunter-content {
    margin-top: 20px;
    margin-bottom: 20px;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #hunter-section .hunter-content {
    margin-top: 20px;
    margin-bottom: 20px;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #hunter-section .hunter-content .d-block-sm {
    display: none;
  }
}
#hunter-section .hunter-content .paragraph {
  margin-bottom: 50px;
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #hunter-section .hunter-content .paragraph {
    margin-bottom: 20px;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #hunter-section .hunter-content .paragraph {
    margin-bottom: 20px;
  }
}
#hunter-section .hunter-content .why-hunter {
  margin: 10px;
  display: flex;
  flex-direction: row;
  align-items: center;
  z-index: 99;
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #hunter-section .hunter-content .why-hunter img {
    width: 40px;
    height: 15px;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #hunter-section .hunter-content .why-hunter img {
    width: 40px;
    height: 15px;
  }
}
#hunter-section .hunter-content .why-hunter.left-side:first-child {
  transform: translateX(40px);
}
#hunter-section .hunter-content .why-hunter.left-side:nth-child(2) {
  transform: translateX(20px);
}
#hunter-section .hunter-content .why-hunter.right-side img {
  rotate: -180deg;
}
#hunter-section .hunter-content .why-hunter.right-side:first-child {
  transform: translateX(-40px);
}
#hunter-section .hunter-content .why-hunter.right-side:nth-child(2) {
  transform: translateX(-20px);
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #hunter-section .hunter-content .why-hunter {
    justify-content: center;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #hunter-section .hunter-content .why-hunter {
    justify-content: center;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #hunter-section .hunter-content .why-hunter {
    transform: translateX(0) !important;
  }
}
#hunter-section .hunter-content .why-hunter .content {
  border-radius: 10px;
  width: 348px;
  padding: 20px;
  box-shadow: inset 0 4px 6px rgba(0, 0, 0, 0.2);
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #hunter-section .hunter-content .why-hunter .content {
    max-width: 200px;
    margin-bottom: 0;
    padding: 10px;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #hunter-section .hunter-content .why-hunter .content {
    max-width: 200px;
    margin-bottom: 0;
    padding: 10px;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #hunter-section .hunter-content .why-hunter .content {
    font-size: 14px !important;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #hunter-section .hunter-content .why-hunter .content .paragraph {
    margin-bottom: 20px;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #hunter-section .hunter-content .why-hunter .content .paragraph {
    margin-bottom: 20px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #hunter-section .hunter-content .why-hunter .content span {
    display: none;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #hunter-section .hunter-content .why-hunter .content span {
    display: none;
  }
}
#hunter-section .hunter-content .center-section {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  margin: auto;
  align-items: center;
  justify-content: center;
}
#hunter-section .hunter-content .center-section .relative-img {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-height: 700px;
  max-width: 700px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #hunter-section .hunter-content .center-section .relative-img {
    max-width: 300px;
    max-height: 350px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #hunter-section .hunter-content .center-section .relative-img {
    max-width: 300px;
    max-height: 350px;
  }
}
#hunter-section .hunter-content .center-section .yellow-label {
  padding: 15px 30px;
  font-weight: 800;
  font-size: 32px;
  text-align: center;
  color: #234791;
  background-color: #ffca00;
  border-radius: 50px;
  margin: 40px auto;
  transform: rotate(-15deg);
  filter: drop-shadow(0 0 40px rgba(255, 202, 0, 0.7));
  z-index: 10;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #hunter-section .hunter-content .center-section .yellow-label {
    padding: 10px 15px;
    font-size: 28px;
    margin: 20px auto;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #hunter-section .hunter-content .center-section .yellow-label {
    padding: 10px 15px;
    font-size: 28px;
    margin: 20px auto;
  }
}
#hunter-section .hunter-content .center-section .white-label {
  margin: 20px auto;
  padding: 10px 25px;
  z-index: 10;
  color: #234791;
  text-align: center;
  font-weight: 700;
  font-size: 18px;
  border-radius: 50px;
  background-color: #ffffff;
  width: auto;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #hunter-section .hunter-content .center-section .white-label {
    padding: 10px 15px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #hunter-section .hunter-content .center-section .white-label {
    display: none;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #hunter-section .hunter-content .center-section .white-label {
    display: none;
  }
}

#coporateProfileForm {
  position: relative;
  padding: 20px 0;
  width: 100%;
  overflow: hidden;
  display: flex;
  align-items: center;
}
#coporateProfileForm .coporate-form {
  position: absolute;
  inset: 0;
  z-index: 0;
  transition: background-image 0.5s ease-in-out;
  background-size: cover;
  background-position: center;
}
#coporateProfileForm .accept-terms {
  color: #ffffff;
}
#coporateProfileForm .pentabell-form .form-group {
  margin: 10px 0 !important;
}
#coporateProfileForm .relative-section {
  position: relative;
  z-index: 1;
  width: 100%;
  padding: 0 16px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #coporateProfileForm .relative-section {
    padding: 0 30px;
  }
}
#coporateProfileForm .coporate-grid {
  display: flex;
  align-items: center;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #coporateProfileForm .coporate-grid {
    flex-direction: column;
  }
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #coporateProfileForm .coporate-grid .left-section {
    padding-left: 0 !important;
  }
}
#coporateProfileForm .heading-h1 {
  margin-top: 0px !important;
  margin-bottom: 0px !important;
}
#coporateProfileForm .right-section {
  display: flex;
  justify-content: center;
  align-items: center;
}
#coporateProfileForm .right-section .coporate-image {
  max-width: 100%;
  height: auto;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #coporateProfileForm .right-section .coporate-image {
    width: 250px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #coporateProfileForm .right-section .coporate-image {
    width: 250px;
  }
}
#coporateProfileForm .input-pentabell input {
  color: #ffffff;
  opacity: 1;
}
#coporateProfileForm .input-pentabell input::placeholder {
  color: #ffffff;
  opacity: 0.8;
  font-family: "Proxima-Nova-Regular" !important;
  font-size: 14px;
}
#coporateProfileForm .input-pentabell input:-webkit-autofill, #coporateProfileForm .input-pentabell input:-webkit-autofill:hover, #coporateProfileForm .input-pentabell input:-webkit-autofill:focus, #coporateProfileForm .input-pentabell input:-webkit-autofill:active {
  -webkit-transition: background-color 9999s ease-in-out 0s !important;
  transition: background-color 9999s ease-in-out 0s !important;
  -webkit-text-fill-color: #ffffff !important;
  background-color: transparent !important;
  font-family: "Pentabell-SemiBold";
}
#coporateProfileForm .input-pentabell .MuiInput-underline::before {
  border-bottom: 1px solid #ffffff;
}
#coporateProfileForm .input-pentabell .MuiInput-underline:hover:not(.Mui-disabled)::before {
  border-bottom: 1px solid #ffffff;
}
#coporateProfileForm .input-pentabell .MuiInput-underline::after {
  border-bottom: 2px solid #ffffff;
}
#coporateProfileForm .btn-section .btn, #coporateProfileForm .btn-section .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer #coporateProfileForm .btn-section button {
  align-self: center;
  margin: auto 0;
  display: flex;
  justify-content: flex-end;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #coporateProfileForm .btn-section .btn, #coporateProfileForm .btn-section .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer #coporateProfileForm .btn-section button {
    justify-content: center;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #coporateProfileForm .btn-section .btn, #coporateProfileForm .btn-section .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer #coporateProfileForm .btn-section button {
    justify-content: center;
  }
}
#coporateProfileForm .btn-section .btn svg, #coporateProfileForm .btn-section .newsletter-table .MuiDataGrid-toolbarContainer button svg, .newsletter-table .MuiDataGrid-toolbarContainer #coporateProfileForm .btn-section button svg {
  transform: scale(1.5);
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #coporateProfileForm .btn-section .btn, #coporateProfileForm .btn-section .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer #coporateProfileForm .btn-section button {
    padding-top: 15px;
    justify-content: center;
  }
}

.text-banner {
  font-size: 36px !important;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .text-banner {
    font-size: 22px !important;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .text-banner {
    font-size: 22px !important;
  }
}

.text-p {
  font-size: 25px !important;
}

#our-partners #partners__slider .embla__slide, #coporate-profile-partners #partners__slider .embla__slide {
  min-height: 58px !important;
  margin: 6px 8px 0px 0;
  flex: 0 0 12% !important;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #our-partners #partners__slider .embla__slide, #coporate-profile-partners #partners__slider .embla__slide {
    min-height: 38px !important;
    flex: 0 0 20% !important;
  }
}

#corporate-profile-page .heading-h1 {
  font-size: 36px !important;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #corporate-profile-page .heading-h1 {
    font-size: 22px !important;
  }
}
#corporate-profile-page .heading-h3 {
  font-size: 34px !important;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #corporate-profile-page .heading-h3 {
    font-size: 20px !important;
  }
}
#corporate-profile-page .heading-h2 {
  font-size: 38px !important;
  font-family: "Proxima-Nova-Bold" !important;
  line-height: 42px !important;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #corporate-profile-page .heading-h2 {
    font-size: 26px !important;
    line-height: 28px !important;
  }
}

.internal-link {
  color: #ffffff;
  text-decoration: underline;
}
.internal-link.yellow {
  color: #ffca00;
}

.filter-popup {
  width: 100%;
  height: auto;
  transition: all 0.3s ease;
  overflow: hidden;
  max-height: 0;
  opacity: 0;
}
.filter-popup.open {
  max-height: 2000px;
  opacity: 1;
}
.filter-popup .filter-popup-content {
  padding: 15px;
}
.filter-popup .filter-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}
.filter-popup .filter-actions .btn, .filter-popup .filter-actions .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer .filter-popup .filter-actions button {
  width: 100%;
  justify-content: center;
  min-width: 100px;
}

.filter-toggle {
  margin-left: 15px;
}

@media (max-width: 768px) {
  .filter-popup.open {
    max-height: 3000px;
  }
}
.main-content {
  height: 100%;
}
.main-content #filter-btns .btn, .main-content #filter-btns .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer .main-content #filter-btns button {
  padding: 10px 35px !important;
}

#container {
  border-radius: 20px;
  background: white;
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 15px;
  overflow: auto;
}

.profile-infos .upload-container {
  margin-bottom: 10px;
}
.profile-infos .upload-area {
  display: flex;
  align-items: center;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .profile-infos .upload-area {
    align-items: stretch;
    flex-direction: column;
  }
}
.profile-infos .icon-pic {
  height: 120px;
  width: 120px;
  margin-right: 20px;
  border-radius: 100%;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .profile-infos .icon-pic {
    height: 65px;
    width: 65px;
    border-radius: 100%;
  }
}
.profile-infos .label {
  font-family: "Proxima-Nova-Semibold" !important;
  font-size: 20px !important;
  color: #234791;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .profile-infos .label {
    font-size: 18px !important;
  }
}
.profile-infos .description {
  color: #798ba3 !important;
  font-family: "Proxima-Nova-Regular" !important;
  font-size: 14px !important;
}
.profile-infos .profile-container {
  display: flex;
  align-items: center;
  margin-right: 15px;
}
.profile-infos .btn-upload {
  padding: 5px 10px;
  border-radius: 25px !important;
  border: 1px solid #234791 !important;
  text-transform: none !important;
}
.profile-infos .btn-upload .label-button {
  font-size: 16px;
  font-family: "Proxima-Nova-Regular";
  color: #234791;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .profile-infos .btn-upload .label-button {
    font-size: 12px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .profile-infos .btn-upload .label-button {
    font-size: 12px;
  }
}
.profile-infos .btn-delete {
  padding: 5px 10px;
  border-radius: 25px !important;
  border: 1px solid #234791 !important;
  text-transform: none !important;
}
.profile-infos .btn-delete .label-button {
  font-size: 16px;
  font-family: "Proxima-Nova-Regular";
  color: #234791;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .profile-infos .btn-delete .label-button {
    font-size: 12px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  .profile-infos .btn-delete .label-button {
    font-size: 12px;
  }
}

#stepper {
  max-width: 650px;
  margin-left: auto;
  margin-right: auto;
  min-width: 290px;
}
#stepper .css-3hpdci-MuiSvgIcon-root-MuiStepIcon-root.Mui-active {
  color: #234791;
}
#stepper .css-3hpdci-MuiSvgIcon-root-MuiStepIcon-root.Mui-completed {
  color: #234791;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #stepper .stepLabel {
    display: none;
  }
}

.commun .btn-ghost {
  padding: 15px 35px 15px 35px;
}
.commun .inline-group {
  display: flex;
  gap: 20px;
  padding: 5px 0px;
}
.commun .inline-group.flex-end {
  align-items: flex-end;
}
.commun .inline-group > div {
  flex: 1 1;
}
.commun .inline-group > div:nth-child(3) {
  flex-basis: 5%;
}
.commun .inline-group > div:nth-child(2) {
  flex-basis: 0%;
}
.commun .btn-container {
  margin: 20px 0px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  flex-wrap: wrap;
}
.commun .css-5bvx1p-MuiSelect-select-MuiInputBase-input-MuiInput-input.MuiSelect-select {
  min-height: 30px;
}
.commun .css-1869usk-MuiFormControl-root {
  margin: 0px;
}
.commun .css-10o2lyd-MuiStack-root {
  padding-top: 0px;
}
@media all and (max-width: 768px) {
  .commun .inline-group {
    display: block;
    flex-direction: column;
  }
  .commun .file-label {
    max-width: 270px;
  }
  .commun .background {
    background-color: white;
  }
  .commun .upload-description {
    display: none;
  }
}
@media (max-width: 600px) {
  .commun .btn-filled {
    padding: 10px 10px;
    width: 100%;
  }
  .commun .btn-outlined {
    padding: 8px 10px;
    width: 100%;
  }
  .commun .btn, .commun .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer .commun button {
    margin: 5px 0px !important;
    display: flex;
    justify-content: center;
  }
  .commun td:nth-child(3) {
    display: none;
  }
}
.commun .btn, .commun .newsletter-table .MuiDataGrid-toolbarContainer button, .newsletter-table .MuiDataGrid-toolbarContainer .commun button {
  margin: 0px 5px;
}
.commun .btn-filled {
  padding: 15px 20px;
}
.commun .btn-outlined {
  padding: 13px 20px;
}
.commun .css-dmmspl-MuiFormGroup-root {
  margin: 10px 0px;
}

#experiences {
  padding: 20px 0;
}
#experiences .clickable-link {
  cursor: pointer;
  color: #234791;
  text-decoration: none;
  display: flex;
  align-items: center;
  width: -moz-fit-content;
  width: fit-content;
}
#experiences .clickable-link:hover {
  color: #234791;
}
#experiences #accordion {
  border-radius: 8px;
  margin: 10px 0px;
}
#experiences #panel-header {
  background-color: rgba(228, 239, 252, 0.7);
  color: #234791;
  font-family: "Proxima-Nova-Semibold" !important;
  font-size: 15px;
  border-radius: 8px;
}
#experiences .Mui-expanded {
  border-bottom: none;
}
#experiences .custom-table {
  border-collapse: collapse;
  width: 100%;
}
#experiences .custom-table td {
  width: 90px;
  text-align: left;
  border: none;
  padding: 0;
}
#experiences .titleTable {
  color: #234791;
  font-family: "Proxima-Nova-Semibold" !important;
  font-size: 16px;
}
#experiences .btn-outlined:hover {
  cursor: pointer;
}
#experiences .update {
  height: 24px;
  padding: 15px 15px;
}
#experiences .delete {
  height: 24px;
  padding: 15px 15px;
  border-color: red;
  color: red;
  background-color: transparent;
}

#addModal .modal-content {
  padding: 35px;
  background-color: white;
  border-radius: 20px;
  height: auto;
  max-width: 60%;
  max-height: 700px;
  overflow: auto;
}

.centered-image {
  display: block;
  margin: 0 auto;
  width: auto;
  height: 100%;
  max-height: 400px;
}

.resume {
  margin: auto;
  display: flex;
  flex-direction: column;
}

#form .text-danger {
  color: red;
}

.datagrid-cell {
  font-weight: 400;
  font-size: 14px;
  color: #234791;
  text-transform: capitalize;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.datagrid-cell a {
  text-transform: none;
  color: #234791;
}

.pentabell-table table {
  width: 100%;
}
.pentabell-table .action-buttons {
  overflow: auto;
  justify-content: center;
  flex-direction: row;
  display: flex;
  align-items: center;
}
.pentabell-table .action-buttons .btn-download path {
  fill: black;
}
.pentabell-table .action-buttons .btn-download:hover path {
  fill: #ffca00;
}
.pentabell-table .datagrid-cell {
  font-weight: 400;
  font-size: 14px;
  color: #234791;
  text-transform: capitalize;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.pentabell-table .datagrid-header {
  font-family: "Proxima-Nova-Medium" !important;
  font-weight: 500;
  font-size: 14px;
  color: #0b3051;
  text-transform: capitalize;
}
.pentabell-table .left-align {
  justify-content: flex-end;
}
.pentabell-table .job-title-cell {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.pentabell-table .job-title-cell svg {
  margin: auto 5px auto 0;
}

.stats-container {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  gap: 20px;
  padding: 0 0 20px 0px;
}

.stats-card {
  flex-grow: 1;
  flex-shrink: 1;
  flex-basis: calc(25% - 20px);
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 20px;
  height: 50px;
  width: 100.5px;
  border-radius: 10px;
  background-color: #fff;
  text-align: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease-in-out;
  align-items: baseline;
  align-content: space-between;
}
.stats-card.cursor {
  cursor: pointer;
}

.stats-icon-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  margin-top: 20px !important;
}

.stats-card:hover {
  transform: translateY(-5px);
}

.stats-job {
  background: conic-gradient(from 100deg, #0b3051, #234791);
}

.stats-expired {
  background: conic-gradient(from 100deg, #aa2728, #cc3233);
}

.stats-active {
  background: conic-gradient(from 100deg, #006241, #009966);
}

.stats-icon-rejected {
  width: 14px;
  height: 15px;
}

.stats-views {
  background: conic-gradient(from 100deg, #f98016, #d86b0c);
}

.stats-application {
  background: conic-gradient(from 100deg, #bc8100, #d69b19);
}

.stats-contact {
  background: conic-gradient(from 100deg, #314259, #798ba3);
}

.stats-icon {
  width: 20.67px;
  height: 23.33;
}

.stats-info {
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  align-items: flex-start;
  width: -webkit-fill-available;
}

.stats-title {
  font-size: 16px;
  color: #fff;
  font-family: "Proxima-Nova-Semibold" !important;
}

.stats-content {
  display: flex;
  align-items: center;
  width: -webkit-fill-available;
}

.stats-value {
  font-size: 24px;
  font-weight: 600;
  color: #fff;
  margin-top: 5px;
  display: flex;
  justify-content: space-between;
  width: -webkit-fill-available;
  align-items: baseline;
}

.active {
  color: #ffca00;
  font-family: "Proxima-Nova-Regular" !important;
  font-size: 18px;
}

.stats-icon1 {
  width: 24.33px;
  height: 24.33px;
}

.stats-icon2 {
  width: 20.67px;
  height: 14.83px;
}

.stats-icon3 {
  width: 26.67px;
  height: 18.83px;
}

.stats-info .stats-title {
  font-size: 18px;
  text-transform: capitalize;
  color: #fff;
  text-align: left;
}

.stats-info .stats-value {
  font-size: 26px;
  color: #fff;
}

.stats-icon-wrapper {
  display: flex;
  /* width: 50px; */
  height: 50px;
  border-radius: 50%;
  margin-right: px;
  flex-direction: column-reverse;
  flex-wrap: wrap;
  justify-content: flex-end;
  /* align-content: space-between; */
  margin: 0px;
  margin-bottom: 7px;
}

@media (max-width: 768px) {
  .stats-card {
    background-color: red;
  }
}
@media only screen and (max-width: 768px) and (min-width: 320px) and (max-width: 480px) {
  .stats-card {
    flex-grow: 1;
    flex-shrink: 1;
    flex-basis: calc(33% - 20px);
    margin-bottom: 10px;
    padding: 10px;
  }
}
@media only screen and (max-width: 768px) and (min-width: 480px) and (max-width: 768px) {
  .stats-card {
    flex-grow: 1;
    flex-shrink: 1;
    flex-basis: calc(33% - 20px);
    margin-bottom: 10px;
    padding: 10px;
  }
}
@media (max-width: 768px) and (max-width: 768px) {
  .stats-card {
    margin-right: -5px;
  }
  .stats-container {
    flex-wrap: wrap;
    gap: 10px;
  }
  .stats-icon-wrapper {
    width: 50px;
    height: 50px;
  }
  .stats-icon {
    width: 25px;
    height: 25px;
  }
  .stats-title {
    font-size: 14px !important;
  }
  .stats-value {
    font-size: 15px !important;
  }
  .stats-content {
    display: flex;
    align-items: center;
    margin-left: -13px;
  }
}
@media (max-width: 768px) {
  .title-Applications-stat {
    align-items: center !important;
    display: flex;
    margin-bottom: 20px;
    justify-content: center;
  }
}
.modal {
  top: 0;
  position: fixed;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 3, 0, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.date-modal {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 400px;
  max-height: 70vh;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
  padding: 16px;
  overflow-y: auto;
}

#toggle .dialog-paper {
  color: #f8f8f8;
}
#toggle .dialog-content {
  flex: 1 1 auto;
  overflow-y: auto;
  padding: 16px 24px;
  border-top: 1px solid transparent !important;
  border-bottom: 1px solid transparent !important;
}
#toggle .dialog-actions {
  display: flex;
  align-items: center !important;
  justify-content: center !important;
  padding: 8px;
}
#toggle .css-1cennmq-MuiBackdrop-root-MuiDialog-backdrop {
  background-color: rgba(0, 0, 0, 0.14) !important;
}

.css-10d30g3-MuiPaper-root-MuiDialog-paper {
  box-shadow: 0px 11px 15px -7px rgba(0, 0, 0, 0), 0px 24px 38px 3px rgba(0, 0, 0, 0), 0px 9px 46px 8px rgba(0, 0, 0, 0) !important;
}

.css-6hw5d-MuiButtonBase-root-MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track {
  background-color: #234791 !important;
}

.modal-content-popup {
  background: linear-gradient(#0b3051 0%, #234791 100%);
  position: fixed;
  padding: 18px;
  color: #ffffff;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  width: 40%;
  min-height: 150px;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
}
.modal-content-popup .message {
  display: flex;
  justify-content: center;
}

.popup-icon {
  display: flex;
  justify-content: center;
}

.btn-container1 {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.btn-popup {
  font-family: "Proxima-Nova-Regular" !important;
  font-size: 14px;
  padding: 10px 40px;
  text-align: center;
  color: #234791;
  border-color: transparent;
  text-decoration: none;
  background-color: transparent;
  border: 1px solid transparent;
  height: -moz-fit-content;
  height: fit-content;
  display: flex;
  align-items: center;
  margin: 0px 5px;
  background-color: #ffca00;
  border: 1px solid #ffca00;
}

.btn-outlined-popup {
  font-family: "Proxima-Nova-Regular" !important;
  font-size: 14px;
  padding: 10px 40px;
  text-align: center;
  color: #ffffff;
  border-color: transparent;
  text-decoration: none;
  background-color: transparent;
  border: 1px solid transparent;
  height: -moz-fit-content;
  height: fit-content;
  display: flex;
  align-items: center;
  background-color: transparent;
  border: 1px solid #f7f7f7;
}

.btn-container-popup {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.label-pentabell-popup {
  font-size: 25px !important;
  font-family: "Proxima-Nova-Regular" !important;
  text-transform: capitalize !important;
  color: #ffffff !important;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-content: stretch;
  justify-content: center;
}

.label-form-delete {
  font-size: 16px;
  font-family: "Proxima-Nova-Regular" !important;
  text-transform: capitalize;
  color: #ffffff;
  display: flex;
  flex-wrap: nowrap;
  flex-direction: column;
  justify-content: space-evenly;
  align-items: center;
}

.select-pentabell-delete {
  width: 100%;
  height: 100%;
}
.select-pentabell-delete .MuiInputBase-root,
.select-pentabell-delete .css-5lvf42-MuiSelect-select-MuiInputBase-input-MuiInput-input.css-5lvf42-MuiSelect-select-MuiInputBase-input-MuiInput-input.css-5lvf42-MuiSelect-select-MuiInputBase-input-MuiInput-input {
  height: 29px !important;
  background-color: transparent;
  height: 100%;
  border: 1px solid #ffffff;
  color: #ffffff;
}
.select-pentabell-delete .css-5lvf42-MuiSelect-select-MuiInputBase-input-MuiInput-input {
  padding: 0px 5px;
}
.select-pentabell-delete input {
  height: 26px;
  padding-left: 10px;
  width: 100%;
  color: #798ba3;
}
.select-pentabell-delete input .select-pentabell {
  width: 100%;
  height: 100%;
}
.select-pentabell-delete input .select-pentabell .MuiInputBase-root,
.select-pentabell-delete input .select-pentabell .css-5lvf42-MuiSelect-select-MuiInputBase-input-MuiInput-input.css-5lvf42-MuiSelect-select-MuiInputBase-input-MuiInput-input.css-5lvf42-MuiSelect-select-MuiInputBase-input-MuiInput-input {
  height: 29px !important;
  background-color: rgba(228, 239, 252, 0.7);
  height: 100%;
}
.select-pentabell-delete input .select-pentabell .css-5lvf42-MuiSelect-select-MuiInputBase-input-MuiInput-input {
  padding: 0px 5px;
}
.select-pentabell-delete input .select-pentabell input {
  height: 26px;
  background-color: rgba(228, 239, 252, 0.7);
  padding-left: 10px;
  width: 100%;
  color: #798ba3;
}
.select-pentabell-delete input .select-pentabell.light.react-international-phone-input-container .react-international-phone-country-selector-button {
  background-color: transparent;
  border: 0px;
  border-radius: 0;
  border-bottom: 1px solid #0d2849;
}
.select-pentabell-delete input .select-pentabell.light.react-international-phone-input-container .react-international-phone-input {
  border: 0px;
  border-radius: 0;
  border-bottom: 1px solid #0d2849;
}
.select-pentabell-delete input .select-pentabell.light::placeholder {
  color: #ffffff;
}
.select-pentabell-delete input .select-pentabell.light .MuiInputBase-root {
  background-color: transparent;
}
.select-pentabell-delete input .select-pentabell.light input {
  color: #ffffff;
  background-color: transparent;
}
.select-pentabell-delete.light.react-international-phone-input-container .react-international-phone-country-selector-button {
  background-color: transparent;
  border: 0px;
  border-radius: 0;
  border-radius: 0;
  border-bottom: 1px solid #0d2849;
}
.select-pentabell-delete.light.react-international-phone-input-container .react-international-phone-input {
  border: 0px;
  border-radius: 0;
  border-bottom: 1px solid #0d2849;
}
.select-pentabell-delete.light::placeholder {
  color: #ffffff;
}
.select-pentabell-delete.light .MuiInputBase-root {
  background-color: transparent;
}
.select-pentabell-delete.light input {
  color: #ffffff;
  background-color: transparent;
}

.label-popup {
  color: #ffca00;
}

.close-icon {
  position: absolute;
  top: 18px;
  right: 0px;
  border: none;
  width: 24px;
  height: 24px;
  background-color: transparent;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}
.close-icon .hover {
  opacity: 1;
}

@media only screen and (min-width: 320px) and (max-width: 480px) {
  .modal-content-popup {
    width: 95%;
    padding: 12px;
  }
  .btn-container-popup {
    flex-direction: column;
    gap: 5px;
  }
  .message {
    font-size: 12px;
    padding: 18px;
  }
}
.confirmation-center {
  display: flex;
  align-content: stretch;
  justify-content: center;
  align-items: baseline;
  margin-bottom: 32px !important;
}

.confirmation-dialog-icon {
  text-align: center !important;
  display: flex !important;
  align-content: stretch;
  justify-content: center !important;
  align-items: baseline;
  margin-bottom: 10px !important;
}

.text-confirmation-h1 {
  font-family: "Proxima-Nova-Medium" !important;
  text-align: center !important;
  font-size: 24px;
  display: flex;
  align-content: stretch;
  justify-content: center;
  align-items: baseline;
  margin-bottom: 10px !important;
}

.text-confirmation-second {
  font-family: "Proxima-Nova-Medium" !important;
  text-align: center !important;
  font-size: 16px;
  display: flex;
  align-content: stretch;
  justify-content: center;
  align-items: baseline;
  margin-bottom: 13px !important;
}

#tags .ReactTags__tags {
  position: relative;
  display: flex;
  align-items: flex-start;
  border-bottom: 1px solid #798ba3;
  max-height: 120px;
  overflow-y: auto;
  flex-wrap: wrap;
  width: 100%;
  /* Custom scrollbar styling */
  /* Firefox scrollbar */
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
  /* Smooth scrolling */
  scroll-behavior: smooth;
  /* Better spacing when scrolling */
  /* Empty state - reset to normal layout */
}
#tags .ReactTags__tags::-webkit-scrollbar {
  width: 6px;
}
#tags .ReactTags__tags::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}
#tags .ReactTags__tags::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}
#tags .ReactTags__tags::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
#tags .ReactTags__tags:not(:empty) {
  padding-bottom: 6px;
}
#tags .ReactTags__tags:empty, #tags .ReactTags__tags:has(.ReactTags__selected:empty) {
  display: block;
  align-items: normal;
  flex-wrap: nowrap;
  max-height: none;
  overflow: visible;
}

#tags .ReactTags__selected {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  max-width: 100%;
  margin-right: 8px;
  /* When empty, don't take up space */
}
#tags .ReactTags__selected:empty {
  display: none;
  margin-right: 0;
}

#tags .ReactTags__clearAll {
  cursor: pointer;
  padding: 10px;
  margin: 10px;
  background: #f88d8d;
  color: #fff;
  border: none;
}

#tags .ReactTags__tagInput {
  border-radius: 2px;
  display: inline-block;
  flex: 1 1;
  min-width: 120px;
  margin-top: 2px;
}

/* When no tags are present, input takes full width */
#tags .ReactTags__tags:has(.ReactTags__selected:empty) .ReactTags__tagInput,
#tags .ReactTags__tags .ReactTags__selected:empty + .ReactTags__tagInput {
  flex: none;
  width: 100%;
  margin-top: 0;
  min-width: 100%;
}

/* Fallback for browsers that don't support :has() */
#tags .ReactTags__tags.no-tags .ReactTags__tagInput {
  flex: none;
  width: 100%;
  margin-top: 0;
  min-width: 100%;
}

#tags .ReactTags__tags.no-tags {
  display: block;
  align-items: normal;
  flex-wrap: nowrap;
  max-height: none;
  overflow: visible;
  padding: 4px 10px;
}

#tags .ReactTags__tagInput input.ReactTags__tagInputField,
#tags .ReactTags__tagInput input.ReactTags__tagInputField:focus {
  height: 31px;
  margin: 0;
  font-size: 12px;
  min-width: 120px;
  width: 100%;
  padding: 0px 10px;
  border: none;
  outline: none;
}

#tags .ReactTags__editInput {
  border-radius: 1px;
}

#tags .ReactTags__editTagInput {
  display: inline-flex;
}

#tags .ReactTags__selected span.ReactTags__tag {
  background: rgba(228, 239, 252, 0.7);
  border-radius: 15px;
  color: #000000;
  font-size: 12px;
  display: flex;
  padding: 5px;
  margin: 2px 0;
  white-space: nowrap;
  align-items: center;
}

#tags button.ReactTags__remove {
  color: #000000;
  margin-left: 5px;
  cursor: pointer;
}

#tags .ReactTags__suggestions {
  position: absolute;
}

#tags .ReactTags__suggestions li {
  border-bottom: 1px solid #ddd;
  padding: 5px 10px;
  margin: 0;
}

#tags .ReactTags__suggestions li mark {
  text-decoration: underline;
  background: none;
  font-weight: 600;
}

#tags .ReactTags__suggestions ul li.ReactTags__activeSuggestion {
  background: #b7cfe0;
  cursor: pointer;
}

#tags .ReactTags__remove {
  border: none;
  cursor: pointer;
  background: none;
  color: #798ba3;
  margin-left: 5px;
}

#tags input {
  border: none;
  outline: none;
}

#tags input:before {
  border: none;
}

@media (max-width: 768px) {
  #tags .ReactTags__tags {
    max-height: 100px;
    padding: 6px 8px;
  }
  #tags .ReactTags__tagInput {
    min-width: 100px;
  }
  #tags .ReactTags__tagInput input.ReactTags__tagInputField {
    font-size: 14px;
    min-width: 100px;
  }
  #tags .ReactTags__selected span.ReactTags__tag {
    font-size: 11px;
    padding: 4px 6px;
  }
}
#dashboard-grid .dashboard-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
  grid-gap: 25px;
  gap: 25px;
  padding: 0 16px 0 0;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #dashboard-grid .dashboard-grid {
    grid-template-columns: 1fr !important;
    grid-template-rows: auto !important;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #dashboard-grid .dashboard-grid {
    grid-template-columns: 1fr !important;
    grid-template-rows: auto !important;
  }
}
#dashboard-grid .table-container {
  width: 100%;
  height: 100%;
  border: 1px solid #e5f0fc;
  border-radius: 8px;
  box-shadow: 0px 0px 10px 1px rgba(229, 240, 251, 0.87);
  overflow: hidden;
  padding: 5px;
  display: flex;
  flex-direction: column;
}
#dashboard-grid .table-container.disable-pagination .MuiDataGrid-footerContainer {
  display: none;
}
#dashboard-grid .flex {
  padding: 10px 0 0 10px;
  display: flex;
  justify-content: space-between;
}
#dashboard-grid #dashboard-layout #main-component #outlet .heading-h2 {
  margin-top: 5px !important;
}

.warning-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 5px;
}

.css-16wblaj-MuiInputBase-input-MuiOutlinedInput-input,
.css-1pk1fka {
  height: 7px !important;
  color: #234791;
}

.css-w76bbz-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input.MuiSelect-select,
.css-mp9f0v {
  height: 7px !important;
  min-height: 7px !important;
  color: #234791 !important;
}

.css-w76bbz-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input.css-w76bbz-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input.css-w76bbz-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input,
.css-mp9f0v.css-mp9f0v.css-mp9f0v {
  padding-right: 32px;
  display: flex;
  align-items: center;
}

.css-113d811-MuiFormLabel-root-MuiInputLabel-root {
  color: #234791;
}

.svg-accordion {
  max-height: 48px !important;
}
.svg-accordion svg {
  transform: scale(1.5);
}
.svg-accordion svg path {
  fill: #234791;
}

.btnPanel {
  background-color: transparent;
  border: none;
  border-bottom: 3px solid rgba(228, 239, 252, 0.7);
  font-family: "Proxima-Nova-Medium" !important;
  font-size: 16px;
  text-align: center;
  color: #234791;
  padding: 10px 20px;
  cursor: pointer;
  width: -webkit-fill-available;
  transition: border-bottom 0.3s ease;
}

.btnPanel-filled {
  border-bottom-color: #234791;
}

.btnPanel:hover {
  border-bottom-color: #ffca00;
}

.progress-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  width: 100%;
  height: 100%;
  margin: 0 auto;
}

.css-1qltlow-MuiTabs-indicator {
  position: absolute;
  height: 2px;
  bottom: 0;
  width: 100%;
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  background-color: #234791 !important;
}

.gauge-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin-bottom: 20px;
}

.profile-title {
  font-size: 20px;
  font-family: "Proxima-Nova-Semibold" !important;
  color: #234791;
  margin-bottom: 10px;
}

.gauge-container {
  position: relative;
  margin: 1rem auto;
}

@media (max-width: 767px) {
  .gauge-container {
    width: 100%;
  }
  .profile-title {
    font-size: 18px;
  }
}
@media (min-width: 768px) and (max-width: 1024px) {
  .gauge-container {
    width: 100%;
  }
  .profile-title {
    font-size: 18px;
  }
}
@media (min-width: 1025px) {
  .gauge-container {
    display: block;
  }
  .profile-title {
    font-size: 22px;
  }
}
.slider-list {
  background-color: #fff;
}

.slider-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 10px;
}

.slider-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-radius: 8px;
  background-color: rgba(228, 239, 252, 0.7);
  transition: background-color 0.3s ease;
  cursor: pointer;
}

.slider-item p {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.slider-item span {
  font-size: 16px;
  color: #234791;
  font-weight: 500;
}

@media (max-width: 768px) {
  .slider-item {
    flex-direction: row;
    display: flex;
    text-align: center;
  }
}
.title-Applications-stat {
  font-size: 24px;
  margin-left: 20px;
  font-weight: 400;
  color: #234791;
  font-family: "Proxima-Nova-Medium" !important;
}

.favorite-opportunities {
  padding: 16px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.title {
  font-size: 1.5rem;
  font-weight: 600;
}

.opportunity-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.opportunity-item-homePage {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 20px;
  padding: 12px;
  gap: 16px;
  margin: 0 0 15px 0;
  padding: 20px;
  height: max-content;
  box-shadow: 0px 10px 40px rgba(10, 48, 82, 0.0784313725);
}
@media (max-width: 768px) {
  .opportunity-item-homePage {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
  }
}

.opportunity-left img {
  border-radius: 50%;
}

.opportunity-center {
  flex: 1 1;
}

.opportunity-title {
  font-size: 24px;
  font-family: "Proxima-Nova-Medium" !important;
  color: #234791;
}

.article-title {
  font-size: 18px;
  font-family: "Proxima-Nova-Medium" !important;
  color: #234791;
}
@media (max-width: 768px) {
  .article-title {
    display: flex;
    align-items: center !important;
  }
}

.empty-button {
  margin-top: 10px;
}

.opportunity-reference {
  color: #798BA3;
  font-size: 12px;
  font-family: "Proxima-Nova-Regular" !important;
}
@media (max-width: 768px) {
  .opportunity-reference {
    display: flex;
    justify-content: space-around;
  }
}

.opportunity-right {
  display: flex;
  align-items: center;
  gap: 5px;
  flex-direction: column;
}

.bookmark-icon {
  color: #f9a825;
}

.read-more-btn {
  background-color: #007bff;
  color: #fff;
  font-size: 0.9rem;
  padding: 8px 16px;
  border-radius: 4px;
  text-transform: uppercase;
}

.list-Home-page {
  border-radius: 16px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  align-content: stretch;
  justify-content: space-between;
  align-items: center;
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 20px;
}

.empty-title-1 {
  display: flex;
  font-size: 18px;
  margin-left: 20px;
}

.empty-title-2 {
  display: flex;
  font-size: 14px;
}

.view-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 10px;
}

.view-container {
  position: relative;
  width: 150px;
  height: 150px;
  margin: 0 auto;
}

.view-background {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.view-info {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center !important;
}

.view-percentage {
  font-size: 25px;
  font-weight: bold;
  color: #234791;
  margin-bottom: 5px;
}

.profile-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  margin-top: 5px;
}

@media (max-width: 768px) {
  .css-gg4vpm {
    display: flex;
    justify-content: space-evenly;
    flex-wrap: nowrap;
    margin: 6px 26px;
    flex-direction: column;
    align-content: stretch;
    align-items: stretch;
    margin-bottom: 10px;
  }
  .gauge-container {
    max-width: 200px;
  }
}
@media (max-width: 1024px) {
  .gauge-container {
    max-width: 200px;
  }
}
@media (max-width: 480px) {
  .gauge-container {
    max-width: 200px;
  }
}
@media (min-width: 900px) {
  .css-10voxkt-MuiGrid-root {
    flex-basis: 25%;
    flex-grow: 0;
    /* max-width: 25%; */
  }
}
.div-wrapper {
  height: 100%;
  padding: 15px;
  overflow: auto;
}

#stats .chart-wrapper {
  display: flex !important;
  align-items: center !important;
  flex-direction: column !important;
  margin-top: 30px !important;
}
#stats .chart-grid {
  display: flex;
  align-items: center;
}
#stats .labelstats-wrapper {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 8px;
  flex-wrap: wrap;
}
#stats .logins-dot {
  background-color: #30b0c7;
  width: 12px;
  height: 12px;
  border-radius: 50%;
}
#stats .newacccounts-dot {
  background-color: #234791;
  width: 12px;
  height: 12px;
  border-radius: 50%;
}
#stats .uploadedresumes-dot {
  background-color: #007aff;
  width: 12px;
  height: 12px;
  border-radius: 50%;
}
#stats .applications-dot {
  background-color: #32ade6;
  width: 12px;
  height: 12px;
  border-radius: 50%;
}
#stats .accepted-dot {
  background-color: #018055;
  width: 12px;
  height: 12px;
  border-radius: 50%;
}
#stats .rejected-dot {
  background-color: #d73232;
  width: 12px;
  height: 12px;
  border-radius: 50%;
}
#stats .pending-dot {
  background-color: #e97611;
  width: 12px;
  height: 12px;
  border-radius: 50%;
}
#stats .public-dot {
  background-color: #234791;
  width: 12px;
  height: 12px;
  border-radius: 50%;
}
#stats .privateopportunity-dot {
  background-color: #d5e5ff;
  width: 12px;
  height: 12px;
  border-radius: 50%;
}
#stats .privatearticles-dot {
  background-color: #ffca00;
  width: 12px;
  height: 12px;
  border-radius: 50%;
}
#stats .draft-dot {
  background-color: #227b94;
  width: 12px;
  height: 12px;
  border-radius: 50%;
}
#stats .newopportunities-dot {
  background-color: #ffcc00;
  width: 12px;
  height: 12px;
  border-radius: 50%;
}
#stats .neswarticles-dot {
  background-color: #ffa135;
  width: 12px;
  height: 12px;
  border-radius: 50%;
}
#stats .newsletters-dot {
  background-color: #ffd985;
  width: 12px;
  height: 12px;
  border-radius: 50%;
}
#stats .newcontact-dot {
  background-color: #ff7700;
  width: 12px;
  height: 12px;
  border-radius: 50%;
}
#stats .label-chart {
  color: #a6a8b1;
  font-size: 14px;
}
#stats .label-wrapper {
  display: flex;
  align-items: center;
  gap: 4px;
}
#stats .barchartfilter-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-direction: row;
}
#stats .card {
  height: 100%;
  border-radius: 20px;
}

#embla .embla {
  margin: 50px auto;
  position: relative;
  max-width: 90%;
  --slide-height: 19rem;
  /* You can adjust this depending on your needs */
  --slide-spacing: 10px;
  /* Space between images */
}
#embla .embla__viewport {
  overflow: hidden;
  position: relative;
}
#embla .embla__container {
  display: flex;
  touch-action: pan-y pinch-zoom;
  margin-left: calc(var(--slide-spacing) * -1);
}
#embla .embla__slide {
  transform: translate3d(0, 0, 0);
  flex: 0 0 auto;
  /* Allow the slide to auto-adjust based on image size */
  min-width: 0;
  padding-left: var(--slide-spacing);
  /* Space between slides */
  margin-right: var(--slide-spacing);
  /* Add space between slides */
}
#embla .embla__slide img {
  position: relative;
  width: 100%;
  height: auto;
  object-fit: cover;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #embla .embla__slide img {
    height: 196px !important;
    width: auto !important;
  }
}
#embla .embla__slide__number {
  box-shadow: inset 0 0 0 0.2rem var(--detail-medium-contrast);
  border-radius: 1.8rem;
  font-size: 4rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  height: var(--slide-height);
  -webkit-user-select: none;
          user-select: none;
}
#embla .embla-thumbs {
  --thumbs-slide-spacing: 0.8rem;
  --thumbs-slide-height: 6rem;
  margin-top: var(--thumbs-slide-spacing);
}
#embla .embla-thumbs__image {
  border-radius: 10px;
}
#embla .embla-thumbs__viewport {
  overflow: hidden;
}
#embla .embla-thumbs__container {
  display: flex;
  flex-direction: row;
  margin-left: calc(var(--thumbs-slide-spacing) * -1);
  justify-content: center;
}
#embla .embla-thumbs__slide {
  flex: 0 0 22%;
  min-width: 0;
  padding-left: var(--thumbs-slide-spacing);
}
@media (min-width: 576px) {
  #embla .embla-thumbs__slide {
    flex: 0 0 15%;
  }
}
#embla .embla-thumbs__slide__number {
  border-radius: 1.8rem;
  -webkit-tap-highlight-color: rgba(var(--text-high-contrast-rgb-value), 0.5);
  -webkit-appearance: none;
  appearance: none;
  background-color: transparent;
  touch-action: manipulation;
  display: inline-flex;
  text-decoration: none;
  cursor: pointer;
  border: 0;
  padding: 0;
  margin: 0;
  box-shadow: inset 0 0 0 0.2rem var(--detail-medium-contrast);
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--detail-high-contrast);
  display: flex;
  align-items: center;
  justify-content: center;
  height: var(--thumbs-slide-height);
  width: 100%;
}
#embla .embla-thumbs__slide--selected .embla-thumbs__slide__number {
  color: var(--text-body);
}
#embla .embla__controls {
  bottom: 55%;
  z-index: 1;
  float: inline-end;
  position: absolute;
  width: auto;
}
#embla .embla__controls.left {
  left: 1%;
}
#embla .embla__controls.right {
  right: 1%;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #embla .embla__controls {
    text-align: center;
    top: 100%;
  }
  #embla .embla__controls.left {
    left: 30%;
    margin-right: 50px;
  }
  #embla .embla__controls.right {
    right: 30%;
    margin-left: 50px;
  }
}
#embla .embla__controls .embla__button {
  background: rgba(28, 45, 78, 0.1490196078);
  -webkit-backdrop-filter: blur(2px);
          backdrop-filter: blur(2px);
  border-radius: 100%;
  width: 70px;
  height: 70px;
  margin: auto 8px;
  border: 1px solid rgba(228, 239, 252, 0.7);
}
#embla .embla__controls .embla__button:hover {
  box-shadow: 0px 0px 10px 0px rgba(228, 239, 252, 0.7);
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #embla .embla__controls .embla__button {
    width: 42px;
    height: 42px;
    padding: 0;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #embla .embla__controls .embla__button {
    width: 42px;
    height: 42px;
    padding: 0;
  }
}

#ksa .iso-wrapper {
  background-color: rgba(29, 90, 159, 0.1176470588);
  margin: 50px 0px;
  justify-content: center;
  padding: 40px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #ksa .iso-wrapper {
    margin: 20px 0px;
    padding: 20px 0 !important;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #ksa .iso-wrapper {
    margin: 20px 0px;
    padding: 20px 0 !important;
  }
}
#ksa .icons-wrapper {
  align-self: center;
}
#ksa .label {
  color: #234791;
  font-family: "Proxima-Nova-Medium";
}
#ksa .icon {
  margin: 0 30px;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #ksa .icon {
    margin: 1px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #ksa .icon {
    margin: 1px;
  }
}
#ksa .divider {
  margin: 20px;
  border: 1px solid rgba(29, 90, 159, 0.2549019608);
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  #ksa .divider {
    margin: 0;
  }
}
@media only screen and (min-width: 480px) and (max-width: 768px) {
  #ksa .divider {
    margin: 0;
  }
}
#ksa .overview {
  display: flex;
  flex-direction: column;
  align-items: center;
}
#ksa .overview .heading-h1 {
  margin-bottom: 0px !important;
}
#ksa .grey {
  color: #798ba3;
  font-family: "Proxima-Nova-Regular";
}
#ksa #potential-component {
  background-color: rgba(228, 239, 252, 0.7);
  min-height: 50vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  margin: 30px 0;
}
#ksa #potential-component .container {
  display: flex;
  background-color: rgba(35, 72, 145, 0.6235294118);
  width: 40%;
  min-height: 30% !important;
  justify-content: center;
  padding: 20px;
}
#ksa #potential-component .container .heading-h2 {
  font-family: "Proxima-Nova-SemiBold" !important;
  font-size: 40px;
}

@media only screen and (min-width: 320px) and (max-width: 480px) {
  .buttons-resume {
    display: flex;
    flex-direction: column;
  }
  .button-resume {
    padding: 2px !important;
  }
  .label-upload-resume {
    font-size: 14px;
  }
  .resume-date, .file-requirements-resume {
    display: none;
  }
}
p,
a,
h1,
h2,
h3,
div,
.css-rizt0-MuiTypography-root {
  font-family: "Proxima-Nova-Regular" !important;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  background-color: rgba(228, 239, 252, 0.7);
  font-family: "Proxima-Nova-Regular", sans-serif;
}

.text-center {
  text-align: center !important;
}

.text-justify {
  text-align: justify !important;
}

p {
  margin-block-start: 0;
  margin-block-end: 0;
}

.custom-max-width {
  max-width: 90% !important;
  margin-left: auto;
  margin-right: auto;
}
@media only screen and (min-width: 320px) and (max-width: 480px) {
  .custom-max-width {
    max-width: 100% !important;
  }
}
