import { FormLabel, Stack, TextField } from "@mui/material";
import Autocomplete from "@mui/material/Autocomplete";
import { ErrorMessage } from "formik";
const SelectMultipleObject = ({
  label,
  name,
  options = [],
  value = [], 
  onChange,
  onSelectCallback = () => {},
  errors,
  touched,
  getOptionLabel = (option) => option.name,
  placeholder = "",
}) => {
  const selectedOptions = options.filter((option) =>
    value.includes(option.id)
  );

  return (
    <FormLabel className="label-form">
      {label}
      <Stack>
        <Autocomplete
          multiple
          id={`${name}-autocomplete`}
          options={options}
          getOptionLabel={getOptionLabel}
          value={selectedOptions}
          onChange={(event, selectedOptions) => {
            const selectedIds = selectedOptions.map((option) => option.id);
            onChange(name, selectedIds);
            onSelectCallback(selectedIds);
          }}
          renderInput={(params) => (
            <TextField
              {...params}
              className="input-pentabell multiple-select"
              variant="standard"
              placeholder={placeholder}
            />
          )}
          className={
            "input-pentabell" +
            (errors && touched ? " is-invalid" : "")
          }
        />
      </Stack>
      <ErrorMessage
        className="label-error"
        name={name}
        component="div"
      />
    </FormLabel>
  );
};

export default SelectMultipleObject;
