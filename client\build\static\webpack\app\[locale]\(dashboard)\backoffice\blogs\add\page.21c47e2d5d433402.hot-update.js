"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/add/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/AddArticleFroala.jsx":
/*!***********************************************************!*\
  !*** ./src/features/blog/components/AddArticleFroala.jsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Accordion/Accordion.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionSummary/AccordionSummary.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionDetails/AccordionDetails.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormGroup/FormGroup.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormLabel/FormLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mui/icons-material/ExpandMore */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/ExpandMore.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! yup */ \"(app-pages-browser)/./node_modules/yup/index.esm.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! formik */ \"(app-pages-browser)/./node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var _utils_urls__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../utils/urls */ \"(app-pages-browser)/./src/utils/urls.js\");\n/* harmony import */ var _AddArticleEN__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./AddArticleEN */ \"(app-pages-browser)/./src/features/blog/components/AddArticleEN.jsx\");\n/* harmony import */ var _AddArticleFR__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./AddArticleFR */ \"(app-pages-browser)/./src/features/blog/components/AddArticleFR.jsx\");\n/* harmony import */ var _opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../opportunity/hooks/opportunity.hooks */ \"(app-pages-browser)/./src/features/opportunity/hooks/opportunity.hooks.js\");\n/* harmony import */ var _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../hooks/blog.hook */ \"(app-pages-browser)/./src/features/blog/hooks/blog.hook.js\");\n/* harmony import */ var react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-datepicker/dist/react-datepicker.css */ \"(app-pages-browser)/./node_modules/react-datepicker/dist/react-datepicker.css\");\n/* harmony import */ var _config_axios__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/config/axios */ \"(app-pages-browser)/./src/config/axios.js\");\n/* harmony import */ var _features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/features/auth/hooks/currentUser.hooks */ \"(app-pages-browser)/./src/features/auth/hooks/currentUser.hooks.js\");\n/* harmony import */ var _components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! lodash/debounce */ \"(app-pages-browser)/./node_modules/lodash/debounce.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_15__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AddArticle = ()=>{\n    _s();\n    const { user } = (0,_features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_13__[\"default\"])();\n    const savedArticle = localStorage.getItem(\"savedArticle\");\n    const useUpdateAutoSaveHook = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useUpdateAutoSave)();\n    const useCreateArticleHook = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useCreateArticle)();\n    const useCreateAutoSaveHook = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useCreateAutoSave)();\n    const useSaveFileHook = (0,_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_9__.useSaveFile)();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const currentYear = new Date().getFullYear();\n    const formikRefAll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [expanded, setExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(`panel`);\n    const [formdataEN, setFormDataEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new FormData());\n    const [formdataFR, setFormDataFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new FormData());\n    const [uuidPhotoFileNameEN, setUuidPhotoFileNameEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [uuidPhotoFileNameFR, setUuidPhotoFileNameFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedLanguages, setSelectedLanguages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        en: true,\n        fr: false\n    });\n    const [categoriesEN, setCategoriesEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categoriesFR, setCategoriesFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredCategoriesEN, setFilteredCategoriesEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredCategoriesFR, setFilteredCategoriesFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCategoriesEN, setSelectedCategoriesEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCategoriesFR, setSelectedCategoriesFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const getCategoriesEN = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories)(\"en\");\n    const getCategoriesFR = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories)(\"fr\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (getCategoriesEN.data?.categories) {\n            const transformedCategories = getCategoriesEN.data.categories.map((category)=>({\n                    id: category.versionscategory[0]?.id,\n                    name: category.versionscategory[0]?.name\n                }));\n            setCategoriesEN(transformedCategories);\n        }\n    }, [\n        getCategoriesEN.data\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (getCategoriesFR.data?.categories) {\n            const transformedCategories = getCategoriesFR.data.categories.map((category)=>({\n                    id: category?.versionscategory[0]?.id,\n                    name: category.versionscategory[0]?.name\n                }));\n            setCategoriesFR(transformedCategories);\n        }\n    }, [\n        getCategoriesFR.data\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedCategoriesEN.length > 0) {\n            fetchTranslatedCategories(selectedCategoriesEN, \"en\");\n        } else if (selectedCategoriesFR.length > 0) {\n            fetchTranslatedCategories(selectedCategoriesFR, \"fr\");\n        }\n    }, [\n        selectedCategoriesEN,\n        selectedCategoriesFR\n    ]);\n    const fetchTranslatedCategories = async (selectedCategories, language)=>{\n        try {\n            const response = await _config_axios__WEBPACK_IMPORTED_MODULE_12__.axiosGetJson.get(`${_utils_urls__WEBPACK_IMPORTED_MODULE_6__.API_URLS.categories}/${language}/${selectedCategories}`);\n            if (language === \"en\") {\n                const transformedCategories = response.data.map((category)=>({\n                        id: category._id,\n                        name: category.name\n                    }));\n                setFilteredCategoriesFR(transformedCategories);\n            } else {\n                const transformedCategories = response.data.map((category)=>({\n                        id: category._id,\n                        name: category.name\n                    }));\n                setFilteredCategoriesEN(transformedCategories);\n            }\n        } catch (error) {\n            console.error(\"Error fetching translated categories:\", error);\n        }\n    };\n    const handleImageSelect = async (selectedFile, language)=>{\n        if (language === \"en\") {\n            const uuidPhotos = (0,uuid__WEBPACK_IMPORTED_MODULE_16__[\"default\"])().replace(/-/g, \"\");\n            const newFormData = new FormData();\n            newFormData.append(\"file\", selectedFile);\n            setFormDataEN(newFormData);\n            const extension = selectedFile.name.split(\".\").pop();\n            setUuidPhotoFileNameEN(`${uuidPhotos}.${extension}`);\n        } else if (language === \"fr\") {\n            const uuidPhotos = (0,uuid__WEBPACK_IMPORTED_MODULE_16__[\"default\"])().replace(/-/g, \"\");\n            const newFormData = new FormData();\n            newFormData.append(\"file\", selectedFile);\n            setFormDataFR(newFormData);\n            const extension = selectedFile.name.split(\".\").pop();\n            setUuidPhotoFileNameFR(`${uuidPhotos}.${extension}`);\n        }\n    };\n    // Helper function to safely parse localStorage items\n    const getLocalStorageItem = (key)=>{\n        try {\n            const item = localStorage.getItem(key);\n            return item ? JSON.parse(item) : \"\";\n        } catch (error) {\n            console.warn(`Error parsing localStorage item \"${key}\":`, error);\n            return \"\";\n        }\n    };\n    const titleEN = getLocalStorageItem(\"title\");\n    const metaTitleEN = getLocalStorageItem(\"metatitle\");\n    const metaDescriptionEN = getLocalStorageItem(\"metaDescription\");\n    const contentEN = getLocalStorageItem(\"content\");\n    const contentFR = getLocalStorageItem(\"contentfr\");\n    const titleFR = getLocalStorageItem(\"titlefr\");\n    const metaDescriptionFR = getLocalStorageItem(\"metaDescriptionfr\");\n    const metaTitleFR = getLocalStorageItem(\"metatitlefr\");\n    const initialValues = {\n        robotsMeta: \"index\",\n        metaTitleEN: metaTitleEN,\n        metaDescriptionEN: metaDescriptionEN,\n        descriptionEN: \"\",\n        visibilityEN: \"\",\n        categoryEN: [],\n        imageEN: null,\n        keywordsEN: \"\",\n        titleEN: titleEN,\n        urlEN: \"\",\n        altEN: \"\",\n        contentEN: contentEN,\n        highlightsEN: [],\n        publishDateEN: \"\",\n        faqTitleEN: \"\",\n        faqEN: [],\n        titleFR: titleFR,\n        metaTitleFR: metaTitleFR,\n        metaDescriptionFR: metaDescriptionFR,\n        descriptionFR: \"\",\n        visibilityFR: \"\",\n        categoryFR: [],\n        imageFR: null,\n        keywordsFR: \"\",\n        urlFR: \"\",\n        altFR: \"\",\n        contentFR: contentFR,\n        publishDateFR: \"\",\n        highlightsFR: [],\n        faqTitleFR: \"\",\n        faqFR: []\n    };\n    const validationSchema = yup__WEBPACK_IMPORTED_MODULE_3__.object().shape({\n        ...selectedLanguages.en && {\n            metaTitleEN: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            metaDescriptionEN: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            titleEN: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            category: yup__WEBPACK_IMPORTED_MODULE_3__.array().min(1, t(\"validations:minCategory\")),\n            visibilityEN: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\"))\n        },\n        ...selectedLanguages.fr && {\n            metaTitleFR: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            metaDescriptionFR: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            titleFR: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            visibilityFR: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\"))\n        }\n    });\n    const uploadFile = (filename, formData, lang)=>{\n        console.log(`Starting upload for ${lang}:`, {\n            filename,\n            formData\n        });\n        return new Promise((resolve)=>{\n            useSaveFileHook.mutate({\n                resource: \"blogs\",\n                folder: currentYear,\n                filename,\n                body: {\n                    formData,\n                    t\n                }\n            }, {\n                onSuccess: (data)=>{\n                    console.log(`Upload success for ${lang}:`, data);\n                    if (data.message === \"uuid exist\") {\n                        resolve({\n                            lang,\n                            uuid: data.uuid\n                        });\n                    } else {\n                        resolve({\n                            lang,\n                            uuid: data.uuid\n                        });\n                    }\n                },\n                onError: (error)=>{\n                    console.error(`Error uploading ${lang} image:`, error);\n                    react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(`Failed to upload ${lang} image: ${error.message || error}`);\n                    resolve({\n                        lang,\n                        uuid: null\n                    });\n                }\n            });\n        });\n    };\n    const clearLocalStorage = ()=>{\n        const keysToRemove = [\n            \"title\",\n            \"content\",\n            \"titlefr\",\n            \"contentfr\",\n            \"metaDescription\",\n            \"metaDescriptionfr\",\n            \"metatitle\",\n            \"metatitlefr\",\n            \"savedArticle\"\n        ];\n        keysToRemove.forEach((key)=>{\n            try {\n                localStorage.removeItem(key);\n            } catch (error) {\n                console.warn(`Error removing localStorage item \"${key}\":`, error);\n            }\n        });\n    };\n    const handleSubmit = async (values)=>{\n        console.log(\"\\uD83D\\uDE80 handleSubmit called - Starting article save process\");\n        const data = {\n            robotsMeta: values.robotsMeta,\n            versions: []\n        };\n        if (!selectedLanguages.en && !selectedLanguages.fr) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please select at least one version!\");\n            return;\n        }\n        try {\n            let resultEN = {\n                uuid: null\n            }, resultFR = {\n                uuid: null\n            };\n            // Upload files if they exist\n            if (selectedLanguages.en && uuidPhotoFileNameEN && formdataEN) {\n                console.log(\"Uploading English image...\");\n                resultEN = await uploadFile(uuidPhotoFileNameEN, formdataEN, \"English\");\n                console.log(\"English upload result:\", resultEN);\n            } else if (selectedLanguages.en) {\n                console.log(\"No English image to upload, proceeding without image\");\n                resultEN = {\n                    uuid: null\n                };\n            }\n            if (selectedLanguages.fr && uuidPhotoFileNameFR && formdataFR) {\n                console.log(\"Uploading French image...\");\n                resultFR = await uploadFile(uuidPhotoFileNameFR, formdataFR, \"French\");\n                console.log(\"French upload result:\", resultFR);\n            } else if (selectedLanguages.fr) {\n                console.log(\"No French image to upload, proceeding without image\");\n                resultFR = {\n                    uuid: null\n                };\n            }\n            if (selectedLanguages.en) {\n                console.log(\"Creating English version with data:\", {\n                    title: values.titleEN,\n                    image: resultEN?.uuid || null\n                });\n                data.versions.push({\n                    language: \"en\",\n                    metaTitle: values.metaTitleEN,\n                    title: values.titleEN,\n                    metaDescription: values.metaDescriptionEN,\n                    description: values.descriptionEN,\n                    url: values.urlEN,\n                    visibility: values.visibilityEN,\n                    publishDate: values.publishDateEN,\n                    content: values.contentEN,\n                    alt: values.altEN,\n                    keywords: values.keywordsEN,\n                    highlights: values.highlightsEN,\n                    image: resultEN?.uuid || null,\n                    category: values.categoryEN === \"\" ? [] : values.categoryEN,\n                    createdBy: user?.firstName + \" \" + user?.lastName,\n                    faqTitle: values.faqTitleEN || \"\",\n                    faq: values.faqEN || []\n                });\n            }\n            if (selectedLanguages.fr) {\n                console.log(\"Creating French version with data:\", {\n                    title: values.titleFR,\n                    image: resultFR?.uuid || null\n                });\n                data.versions.push({\n                    language: \"fr\",\n                    metaTitle: values.metaTitleFR,\n                    title: values.titleFR,\n                    metaDescription: values.metaDescriptionFR,\n                    url: values.urlFR,\n                    visibility: values.visibilityFR,\n                    description: values.descriptionFR,\n                    publishDate: values.publishDateFR,\n                    content: values.contentFR,\n                    alt: values.altFR,\n                    keywords: values.keywordsFR,\n                    highlights: values.highlightsFR,\n                    image: resultFR?.uuid || null,\n                    category: values.categoryFR === \"\" ? [] : values.categoryFR,\n                    createdBy: user?.firstName + \" \" + user?.lastName,\n                    faqTitle: values.faqTitleFR || \"\",\n                    faq: values.faqFR || []\n                });\n            }\n            if (data.versions.length > 0) {\n                useCreateArticleHook.mutate({\n                    data\n                }, {\n                    onSuccess: ()=>{\n                        clearLocalStorage();\n                        window.location.href = `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.blogs.route}/`;\n                    },\n                    onError: ()=>{\n                        react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to create article. Please try again.\");\n                    }\n                });\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"No article versions were created. Please check your form data and try again.\");\n            }\n        } catch (error) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(`An error occurred while processing: ${error.message || error}`);\n        }\n    };\n    const handleChangeAccordion = (panel)=>(_, newExpanded)=>{\n            setExpanded(newExpanded ? panel : false);\n        };\n    const handleCategoriesENSelect = (selectedCategories)=>{\n        setSelectedCategoriesEN(selectedCategories);\n    };\n    const handleCategoriesFRSelect = (selectedCategories)=>{\n        setSelectedCategoriesFR(selectedCategories);\n    };\n    const [isSavedArticle, setIsSavedArticle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(savedArticle);\n    const isSavedArticleRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(isSavedArticle);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        isSavedArticleRef.current = isSavedArticle;\n    }, [\n        isSavedArticle\n    ]);\n    const autosave = async ()=>{\n        try {\n            const values = formikRefAll.current?.values;\n            if (!values) return;\n            const data = {\n                robotsMeta: values?.robotsMeta,\n                versions: []\n            };\n            if (selectedLanguages.en) {\n                data.versions.push({\n                    language: \"en\",\n                    metaTitle: values.metaTitleEN,\n                    title: values.titleEN,\n                    metaDescription: values.metaDescriptionEN,\n                    url: values.urlEN,\n                    visibility: \"Draft\",\n                    publishDate: values.publishDateEN,\n                    content: values.contentEN,\n                    alt: values.altEN,\n                    keywords: values.keywordsEN,\n                    category: values.categoryEN,\n                    highlights: values.highlightsEN,\n                    faqTitle: values.faqTitleEN || \"\",\n                    faq: values.faqEN || []\n                });\n            }\n            if (selectedLanguages.fr) {\n                data.versions.push({\n                    language: \"fr\",\n                    metaTitle: values.metaTitleFR,\n                    title: values.titleFR,\n                    metaDescription: values.metaDescriptionFR,\n                    url: values.urlFR,\n                    visibility: \"Draft\",\n                    publishDate: values.publishDateFR,\n                    content: values.contentFR,\n                    alt: values.altFR,\n                    keywords: values.keywordsFR,\n                    category: values.categoryFR,\n                    highlights: values.highlightsFR,\n                    faqTitle: values.faqTitleFR || \"\",\n                    faq: values.faqFR || []\n                });\n            }\n            if (isSavedArticleRef.current != \"\") {\n                useUpdateAutoSaveHook.mutate({\n                    data: data,\n                    id: isSavedArticleRef.current\n                });\n            } else {\n                if (data.versions.length > 0) {\n                    useCreateAutoSaveHook.mutate({\n                        data\n                    }, {\n                        onSuccess: (data)=>{\n                            setIsSavedArticle(data.articleId);\n                            localStorage.setItem(\"savedArticle\", data.articleId);\n                        }\n                    });\n                }\n            }\n        } catch (error) {\n            console.warn(\"Auto-save failed :\", error);\n        }\n    };\n    const handleChange = lodash_debounce__WEBPACK_IMPORTED_MODULE_15___default()(autosave, 30000);\n    const handleClear = ()=>{\n        formikRefAll.current?.resetForm();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"heading-h2 semi-bold\",\n                children: t(\"createArticle:addArticle\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                lineNumber: 480,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"container\",\n                className: \"recent-application-pentabell\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `main-content`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"commun\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"label-form\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: selectedLanguages.en,\n                                                onChange: ()=>setSelectedLanguages((prev)=>({\n                                                            ...prev,\n                                                            en: !prev.en\n                                                        }))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"English\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"label-form\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: selectedLanguages.fr,\n                                                onChange: ()=>setSelectedLanguages((prev)=>({\n                                                            ...prev,\n                                                            fr: !prev.fr\n                                                        }))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                lineNumber: 499,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"French\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                        lineNumber: 498,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                lineNumber: 484,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                id: \"experiences\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    id: \"form\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.Formik, {\n                                        initialValues: initialValues,\n                                        validationSchema: validationSchema,\n                                        innerRef: formikRefAll,\n                                        onSubmit: handleSubmit,\n                                        className: \"formik-form\",\n                                        children: (param)=>{\n                                            let { errors, touched, setFieldValue, values } = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.Form, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        id: \"accordion\",\n                                                        disableGutters: true,\n                                                        expanded: expanded === `panel`,\n                                                        onChange: handleChangeAccordion(`panel`),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                    lineNumber: 531,\n                                                                    columnNumber: 39\n                                                                }, void 0),\n                                                                \"aria-controls\": `panel-content`,\n                                                                id: `panel-header`,\n                                                                children: t(\"createArticle:settings\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                lineNumber: 530,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"accordion-detail\",\n                                                                elevation: 0,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"inline-group\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                    className: \"label-form\",\n                                                                                    children: [\n                                                                                        \"Robots meta\",\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                            className: \"select-pentabell\",\n                                                                                            variant: \"standard\",\n                                                                                            sx: {\n                                                                                                m: 1,\n                                                                                                minWidth: 120\n                                                                                            },\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                                value: _utils_constants__WEBPACK_IMPORTED_MODULE_5__.RobotsMeta.filter((option)=>values.robotsMeta === option),\n                                                                                                selected: values.robotsMeta,\n                                                                                                onChange: (event)=>{\n                                                                                                    setFieldValue(\"robotsMeta\", event.target.value);\n                                                                                                },\n                                                                                                children: _utils_constants__WEBPACK_IMPORTED_MODULE_5__.RobotsMeta.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                                        value: item,\n                                                                                                        children: item\n                                                                                                    }, item, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                                        lineNumber: 564,\n                                                                                                        columnNumber: 41\n                                                                                                    }, undefined))\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                                lineNumber: 551,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                            lineNumber: 546,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.ErrorMessage, {\n                                                                                            className: \"label-error\",\n                                                                                            name: \"robotsMeta\",\n                                                                                            component: \"div\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                            lineNumber: 570,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                    lineNumber: 544,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                lineNumber: 543,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.ErrorMessage, {\n                                                                                name: \"robotsMeta\",\n                                                                                component: \"div\",\n                                                                                className: \"label-error\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                lineNumber: 578,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                        lineNumber: 542,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                    lineNumber: 541,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                lineNumber: 537,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, `panel`, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                        lineNumber: 523,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    selectedLanguages.en && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddArticleEN__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        errors: errors,\n                                                        touched: touched,\n                                                        setFieldValue: setFieldValue,\n                                                        values: values,\n                                                        onImageSelect: handleImageSelect,\n                                                        debounce: handleChange,\n                                                        categories: categoriesEN,\n                                                        filteredCategories: filteredCategoriesEN,\n                                                        onCategoriesSelect: handleCategoriesENSelect\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                        lineNumber: 588,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    selectedLanguages.fr && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddArticleFR__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        errors: errors,\n                                                        touched: touched,\n                                                        setFieldValue: setFieldValue,\n                                                        values: values,\n                                                        onImageSelect: handleImageSelect,\n                                                        categories: categoriesFR,\n                                                        filteredCategories: filteredCategoriesFR,\n                                                        onCategoriesSelect: handleCategoriesFRSelect,\n                                                        debounce: handleChange\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                        lineNumber: 601,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"btn-container\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                type: \"button\",\n                                                                text: \"Clear\",\n                                                                className: \"btn btn-filled\",\n                                                                onClick: handleClear\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                lineNumber: 614,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                type: \"submit\",\n                                                                text: \"Save\",\n                                                                className: \"btn btn-filled\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                lineNumber: 620,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                        lineNumber: 613,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 21\n                                            }, undefined);\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                    lineNumber: 513,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                lineNumber: 512,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                        lineNumber: 483,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                    lineNumber: 482,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                lineNumber: 481,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(AddArticle, \"J9fbCVL3GirbPWfQOsSUVKopJIQ=\", false, function() {\n    return [\n        _features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useUpdateAutoSave,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useCreateArticle,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useCreateAutoSave,\n        _opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_9__.useSaveFile,\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories\n    ];\n});\n_c = AddArticle;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AddArticle);\nvar _c;\n$RefreshReg$(_c, \"AddArticle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/AddArticleFroala.jsx\n"));

/***/ })

});