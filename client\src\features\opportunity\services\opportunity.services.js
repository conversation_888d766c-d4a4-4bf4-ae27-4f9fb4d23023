import { API_URLS } from "../../../utils/urls";
import { axiosGetJson, axiosGetJsonSSR, axiosPrivateFile } from "../../../config/axios";
import { toast } from "react-toastify";

export const createOpportunity = (body) => {
  let t = body.t;
  return new Promise(async (resolve, reject) => {
    axiosGetJson
      .post(API_URLS.opportunity, body.data)
      .then((valid) => {
        toast.success("Opportunity added successfully");
        if (valid?.data) {
          resolve(valid.data);
        }
      })
      .catch((err) => {
        if (err && err.response && err.response.data) {
        }
        if (err) {
          reject(err);
        }
      });
  });
};

export const createApplication = (body) => {
  let t = body.t;
  return new Promise(async (resolve, reject) => {
    axiosGetJson
      .post(
        `/opportunities${API_URLS.applications}/${body.opportunityId}`,
        body.data
      )
      .then((valid) => {
        if (valid?.data) {
          resolve(valid.data);
        }
      })
      .catch((err) => {
        if (err) {
          reject(err);
        }
      });
  });
};

export const getOpportunityByUrlAndLanguage = (body) => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await axiosGetJson.get(
        `${API_URLS.baseUrl}/${body.language}/opportunities/${body.urlOpportunity}`
      );
      resolve(response.data);
    } catch (err) {
      if (err && err.response && err.response.data) {
        if (err.response.status === 404) {
        }
      }
      reject(err);
    }
  });
};
export const getOpportunities = (body) => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await axiosGetJson.get(`${API_URLS.opportunity}`, {
        params: {
          paginated: body.paginated,
          language: body.language,
          pageSize: body.pageSize,
          pageNumber: body.pageNumber,
          sortOrder: body.sortOrder,
          keyWord: body.keyWord,
          visibility: body.visibility,
          title: body.title,
          createdAt: body.createdAt,
          publishDate: body.publishDate,
          country: body.country,
          industry: body.industry,
          contractType: body.contractType,
          minExperience: body.minExperience,
          maxExperience: body.maxExperience,
          jobDescriptionLanguages: body.jobDescriptionLanguages,
          reference: body.reference,
          opportunityType: body.opportunityType,
          exclude: body.opportunityType !== "In House" && "true",
          active : body.active
        },
      });
      resolve(response.data);
    } catch (err) {
      reject(err);
    }
  });
};

export const getOpportunitiesrelated = (body) => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await axiosGetJson.get(`${API_URLS.opportunity}`, {
        params: {
          language: body.language,
          pageSize: body.pageSize,
          pageNumber: body.pageNumber,
          sortOrder: body.sortOrder,
          keyWord: body.keyWord,
          visibility: body.visibility,
          createdAt: body.createdAt,
          publishDate: body.publishDate,
          paginated: body.paginated,
          country: body.country,
          industry: body.industry,
        },
      });
      resolve(response.data);
    } catch (err) {
      reject(err);
    }
  });
};

export const getOpportunityById = (id) => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await axiosGetJson.get(`${API_URLS.opportunity}/${id}`);
      resolve(response.data);
    } catch (err) {
      reject(err);
    }
  });
};


export const updateJobDescriptions = async () => {
  const response = await axiosGetJsonSSR.put(`/UpdateJobdescription`);
  return response.data;
};


export const updateOpportunity = ({ data, language, id }) => {
  return new Promise(async (resolve, reject) => {
    axiosGetJson
      .post(`${API_URLS.opportunity}/${language}/${id}`, data)
      .then((valid) => {
        language === "en" &&
          toast.success(`Opportunity english updated successfully`);
        language === "fr" &&
          toast.success(`Opportunity french updated successfully`);

        if (valid?.data) {
          resolve(valid.data);
        }
      })
      .catch((err) => {
        if (err && err.response && err.response.data) {
        }
        if (err) {
          reject(err);
        }
      });
  });
};

export const updateOpportunityAll = ({ data, id }) => {
  return new Promise(async (resolve, reject) => {
    axiosGetJson
      .put(`${API_URLS.opportunity}/${id}`, data)
      .then((valid) => {
        toast.success(`Opportunity Commun fields updated successfully`);

        if (valid?.data) {
          resolve(valid.data);
        }
      })
      .catch((err) => {
        if (err && err.response && err.response.data) {
        }
        if (err) {
          reject(err);
        }
      });
  });
};

export const addToFavourite = ({ id, title, typeOfFavourite }) => {
  return new Promise(async (resolve, reject) => {
    axiosGetJson
      .put(`${API_URLS.baseUrl}/favourite/${id}`, {
        type: typeOfFavourite,
      })
      .then((valid) => {
        toast.success(`${typeOfFavourite} : ${title} saved to your favorites.`);

        if (valid?.data) {
          resolve(valid.data);
        }
      })
      .catch((err) => {
        if (err && err.response && err.response.data) {
          if (err.response.status === 409) {
            toast.warning(` ${title} already in shortlist`);
          }
        }
        if (err) {
          reject(err);
        }
      });
  });
};

export const saveFile = ({ resource, folder, filename, body }) => {
  return new Promise(async (resolve, reject) => {
    axiosPrivateFile
      .post(
        `${API_URLS.files}/uploadResume/${resource}/${folder}/${filename}`,
        body.formData
      )
      .then((valid) => {
        if (valid.data) {
          resolve(valid.data);
        }
      })
      .catch((err) => {
        if (err && err.response && err.response.data) {
          if (err.response.status === 400) {
            if (
              err.response.data.message.includes(
                "The resume lacks essential information"
              )
            ) {
              toast.warn(body.t("messages:requireResume"));
            } else {
              toast.warn(err.response.data.message);
            }
          } else if (err.response.status === 500) {
            toast.error("Internal Server Error");
          }
        }
        if (err) {
          reject(err);
        }
      });
  });
};

export const getSeoOpportunity = () => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await axiosGetJson.get(`${API_URLS.seoOpportunity}`);
      resolve(response.data);
    } catch (err) {
      reject(err);
    }
  });
};

export const desarchivearchiveversions = (language, id, archive) => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await axiosGetJson.put(
        `${API_URLS.opportunity}/${language}/${id}/desarchiver`,
        { archive }
      );
      if (response?.data) {
        toast.success(
          `opportunity ${archive ? "archived" : "desarchived"} successfully`
        );
        resolve(response.data);
      }
    } catch (err) {
      toast.error(
        `Failed to ${archive ? "archive" : "desarchive"} the opportunity.`
      );
      reject(err);
    }
  });
};

export const updateOpportunitySeo = ({ data, id }) => {
  return new Promise(async (resolve, reject) => {
    axiosGetJson
      .put(`${API_URLS.seoOpportunity}/${id}`, data)
      .then((valid) => {
        toast.success(`Opportunity seo updated successfully`);

        if (valid?.data) {
          resolve(valid.data);
        }
      })
      .catch((err) => {
        if (err && err.response && err.response.data) {
        }
        if (err) {
          reject(err);
        }
      });
  });
};
