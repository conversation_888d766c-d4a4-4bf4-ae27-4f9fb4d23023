"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateUserSchema = exports.createUserSchema = exports.createNewUserSchema = void 0;
const constants_1 = require("@/utils/helpers/constants");
const Joi = __importStar(require("joi"));
const educationSchema = Joi.object({
    degree: Joi.string().required(),
    endDate: Joi.string(),
    startDate: Joi.string().required(),
    university: Joi.string().required(),
});
const experienceSchema = Joi.object({
    company: Joi.string(),
    contractType: Joi.string(),
    duration: Joi.string(),
    endDate: Joi.string(),
    location: Joi.string(),
    startDate: Joi.string(),
    title: Joi.string().required(),
});
const certificationSchema = Joi.object({
    academy: Joi.string(),
    endDate: Joi.string(),
    startDate: Joi.string(),
    title: Joi.string(),
});
const createUserSchema = Joi.object({
    certifications: Joi.array().items(certificationSchema),
    experiences: Joi.array().items(experienceSchema).required(),
    experiencesCompany: Joi.array().items(Joi.string()),
    educations: Joi.array().items(educationSchema).required(),
    firstName: Joi.string().min(3).required(),
    lastName: Joi.string().min(3).required(),
    industry: Joi.string(),
    industries: Joi.array().items(Joi.string()),
    skills: Joi.array().items(Joi.string()).required(),
    dateOfBirth: Joi.date(),
    phone: Joi.string()
        .required()
        .custom((value, helpers) => {
        // Regular expressions for the allowed phone number formats
        const allowedFormats = [
            /^\+216\s?\d{8}$/, // Tunisia
            /^\+213\s?\d{9}$/, // Algeria
            /^\+20\s?\d{10}$/, // Egypt
            /^\+212\s?\d{9}$/, // Morocco
            /^\+218\s?\d{9}$/, // Libya
            /^\+966\s?\d{9}$/, // Saudi Arabia
            /^\+33\s?\d{9}$/, // France
            /^\+971\s?\d{9}$/, // UAE
            /^\+41\s?\d{9}$/, // Switzerland
            // Add more countries as needed
        ];
        const isValidFormat = Object.values(allowedFormats).some(regex => regex.test(value));
        if (isValidFormat) {
            return value;
        }
        return helpers.error('any.invalid');
    }, 'Phone number validation'),
    email: Joi.string().required().email(),
    roles: Joi.array().items(Joi.valid(...Object.values(constants_1.Role))),
    country: Joi.valid(...Object.values(constants_1.Countries)),
    gender: Joi.valid(...Object.values(constants_1.Gender)),
    jobTitle: Joi.string(),
    profilePicture: Joi.optional(),
    addressLine: Joi.string().optional(),
}).options({ abortEarly: false });
exports.createUserSchema = createUserSchema;
const createNewUserSchema = Joi.object({
    firstName: Joi.string().min(3).required(),
    lastName: Joi.string().min(3).required(),
    dateOfBirth: Joi.date(),
    phone: Joi.string(),
    email: Joi.string().required().email(),
    roles: Joi.array().items(Joi.valid(...Object.values(constants_1.Role))),
}).options({ abortEarly: false });
exports.createNewUserSchema = createNewUserSchema;
const updateUserSchema = Joi.object({
    //certifications: Joi.array().items(certificationSchema),
    //experiences: Joi.array().items(experienceSchema).required(),
    //  experiencesCompany: Joi.array().items(Joi.string()),
    //   educations: Joi.array().items(educationSchema).required(),
    firstName: Joi.string().min(3).optional(),
    lastName: Joi.string().min(3).optional(),
    industry: Joi.string(),
    industries: Joi.array().items(Joi.string()),
    skills: Joi.array().items(Joi.string()).optional(),
    nationalities: Joi.array().items(Joi.string()).optional(),
    phone: Joi.string().optional(),
    //     .custom((value: string, helpers: Joi.CustomHelpers) => {
    //         // Regular expressions for the allowed phone number formats
    //         const allowedFormats = [
    //             /^\+216\s?\d{8}$/,      // Tunisia
    //             /^\+213\s?\d{9}$/,      // Algeria
    //             /^\+20\s?\d{10}$/,      // Egypt
    //             /^\+212\s?\d{9}$/,      // Morocco
    //             /^\+218\s?\d{9}$/,      // Libya
    //             /^\+966\s?\d{9}$/,      // Saudi Arabia
    //             /^\+33\s?\d{9}$/,       // France
    //             /^\+971\s?\d{9}$/,      // UAE
    //             /^\+41\s?\d{9}$/        // Switzerland
    //             // Add more countries as needed
    //         ];
    //         const isValidFormat = Object.values(allowedFormats).some(regex => regex.test(value));
    //         if (isValidFormat) {
    //             return value;
    //         }
    //         return helpers.error('any.invalid');
    //     }, 'Phone number validation'),
    email: Joi.string().required().email().optional(),
    roles: Joi.array().items(Joi.valid(...Object.values(constants_1.Role))).optional(),
    country: Joi.valid(...Object.values(constants_1.Countries)).required().optional(),
    jobTitle: Joi.string(),
    dateOfBirth: Joi.date(),
    gender: Joi.valid(...Object.values(constants_1.Gender)),
    profilePicture: Joi.optional(),
    addressLine: Joi.string().optional(),
});
exports.updateUserSchema = updateUserSchema;
//# sourceMappingURL=users.validations.js.map