"use client";
import { Container, Grid } from "@mui/material";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import SvgFileText from "@/assets/images/icons/FileText.svg";
import SvgMapPin from "@/assets/images/icons/MapPin.svg";
import SvgTime from "@/assets/images/icons/time.svg";
import RegisterForm from "@/features/auth/component/RegisterForm";
import { axiosGetJson } from "@/config/axios";
import { API_URLS } from "@/utils/urls";
import moment from "moment";

function ApplySecondForm({ data, successMsg, errorMsg, secondForm }) {
  const [errMsg, setErrMsg] = useState("");
  const [registerSuccess, setRegisterSuccess] = useState(false);

  const { t, i18n } = useTranslation();

  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (successMsg) setRegisterSuccess(successMsg);
    if (errorMsg) setErrMsg(errorMsg);
  }, [errorMsg, successMsg]);

  const handleSubmit = async (values, { resetForm }) => {
    setLoading(true);
    try {
      const dataToSend = {
        firstName: values.firstName,
        lastName: values.lastName,
        email: values.email,
        phone: values.phone,
        resume: secondForm,
        country: values.country,
        industry: values.industry,
        password: values.password,
      };

      await axiosGetJson.post(
        `${API_URLS.baseUrl}/opportunities/applications/${data._id}`,
        dataToSend,
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
      resetForm();
      //toast.success("Application submitted successfully!");
      /*   setRegisterSuccess(
        "Application submitted successfully! Please check your email to activate your account, and don't forget to check your spam folder if you can't find it."
      ); */
      setTimeout(() => {
        window.location.href = `/confirm-application/`;
      }, 1000);
    } catch (error) {
      if (error.response.status !== 500) {
        setErrMsg(error?.response?.data?.message);
        if (error.response.data.message.includes("User already exists")) {
          setTimeout(() => {
            window.location.href = "/login";
          }, 2000);
        }
      } else {
        setErrMsg("Internal Server Error");
      }
    }
    setLoading(false);
  };

  const initialValues = {
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    country: "",
    industry: "",
    password: "",
    confirmPassword: "",
    acceptTerms: false,
  };

  return (
    <Container className="container-apply-authenticated">
      <div className="opportunity-info">
        <Grid container spacing={1}>
          <Grid item xs={12} sm={6}>
            <p className="apply-title text-white">
              {t("global:applyForThisJob")}
            </p>
          </Grid>
          <Grid item xs={12} sm={6}>
            <p className="title text-yellow">
              {data?.versions[i18n.language].title}
            </p>
            <p className="job-ref">Ref: {data?.reference}</p>
            <div className="flex children-componenent">
              <Grid container>
                {data?.contractType ? (
                  <Grid item xs={12} sm={6}>
                    <div className="job-info">
                      <SvgFileText />
                      <p>
                        Contract&nbsp;:&nbsp;
                        <span>{data?.contractType}</span>
                      </p>
                    </div>
                  </Grid>
                ) : null}
                <Grid item xs={12} sm={6}>
                  <div className="job-info">
                    <SvgMapPin />
                    <p>
                      Location&nbsp;:&nbsp;
                      <span>{data?.country}</span>
                    </p>
                  </div>
                </Grid>
                <Grid item xs={12} sm={12}>
                  <div className="job-info">
                    <SvgTime />
                    <p>
                      Expiration date&nbsp;:&nbsp;
                      <span>
                        {data?.dateOfExpiration
                          ? moment(data?.dateOfExpiration).format("DD/MM/YYYY")
                          : moment().add(3, "months").format("DD/MM/YYYY")}
                      </span>
                    </p>
                  </div>
                </Grid>
              </Grid>
            </div>
          </Grid>
        </Grid>
        <RegisterForm
          initialValues={initialValues}
          handleSubmit={handleSubmit}
          successMsg={registerSuccess}
          errorMsg={errMsg}
          t={t}
          loading={loading}
          setErrMsg={setErrMsg}
          setRegisterSuccess={setRegisterSuccess}
        />
      </div>
    </Container>
  );
}

export default ApplySecondForm;
