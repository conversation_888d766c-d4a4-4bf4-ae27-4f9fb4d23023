{"c": ["app/[locale]/(website)/glossaries/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/@mui/icons-material/esm/Book.js", "(app-pages-browser)/./node_modules/@mui/icons-material/esm/ErrorOutline.js", "(app-pages-browser)/./node_modules/@mui/icons-material/esm/Search.js", "(app-pages-browser)/./node_modules/@mui/material/Box/Box.js", "(app-pages-browser)/./node_modules/@mui/material/Box/boxClasses.js", "(app-pages-browser)/./node_modules/@mui/material/node_modules/@mui/system/esm/createBox/createBox.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Csrc%5C%5Cassets%5C%5Cimages%5C%5Cwebsite%5C%5Cbanner%5C%5CPentabell-joinUs.webp%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CGlossaryBanner.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Csrc%5C%5Cfeatures%5C%5Cglossary%5C%5Ccomponent%5C%5CGlossariesListWebsite.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!", "(app-pages-browser)/./src/features/glossary/component/GlossariesListWebsite.jsx"]}