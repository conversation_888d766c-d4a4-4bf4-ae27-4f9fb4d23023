import { Container, Grid } from "@mui/material";
import navigateAfricanMarket from "@/assets/images/offices/navigateAfricanMarket.png";
function NavigateAfricanMarket({t}) {
  return (
    <div id="navigate-african-market-section">
      <Container className="custom-max-width">
        <Grid className="container" container columnSpacing={0}>
          <Grid item xs={12} sm={6}>
            <div className="left-section">
              <p className="heading-h1">
              {t("africa:intro:title")}
              </p>
              <p className="paragraph">
              {t("africa:intro:description")}
              </p>
              <p className="sub-heading text-blue bold">
              {t("africa:intro:label")}
              </p>
            </div>
          </Grid>
          <Grid item xs={12} sm={5}>
            <img
              width={307}
              height={324}
              alt= {t("africa:intro:altImg")}
              src={navigateAfricanMarket.src}
              loading="lazy"
            />
          </Grid>
        </Grid>
      </Container>
    </div>
  );
}

export default NavigateAfricanMarket;
