"use client";

import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import nodata from "@/assets/images/No data.png";
import CustomButton from "../../../components/ui/CustomButton";
import { useGetFavourites } from "../../application/hooks/application.hooks";
import CandidateSavedDataChart from "@/features/stats/charts/CandidateSavedDataChart";
import ArticleFavourites from "./ArticleFavourite";
import JobFavourites from "./JobFavourites";

const CandidateFavourites = () => {
  const { t } = useTranslation();

  const [paginationModel, setPaginationModel] = useState({
    pageSize: 5,
    page: 0,
  });

  const [shortLists, setShortLists] = useState([]);
  const [showJobs, setShowJobs] = useState(true);
  const [total, setTotal] = useState(0);
  const [totalArticles, setTotalArticles] = useState(0);
  const [typeOfFavourite, setTypeOfFavourite] = useState("opportunity");

  const getFavourites = useGetFavourites({
    pageSize: paginationModel.pageSize,
    pageNumber: paginationModel.page + 1,
    keyword: "",
    typeOfFavourite,
  });

  useEffect(() => {
    getFavourites.refetch();
    setShortLists(getFavourites?.data?.favourites);
    setTotal(getFavourites?.data?.totalFavouriteOpportunities);
    setTotalArticles(getFavourites?.data?.totalFavouriteArticles);
  }, [paginationModel, typeOfFavourite, getFavourites.data]);

  return (
    <>
      <CandidateSavedDataChart totalOpp={total} totalArticles={totalArticles} />

      <div id="container" className="my-applications-pentabell">
        <div className={`main-content`}>
          <div id="filter-btns">
            <CustomButton
              onClick={() => {
                setShowJobs(true);
                setTypeOfFavourite("opportunity");
              }}
              text={t("global:jobs")}
              className={` btnPanel ${showJobs ? " btnPanel-filled" : ""}`}
            />
            <CustomButton
              text={t("global:articles")}
              className={`btnPanel ${!showJobs ? "btnPanel-filled" : ""}`}
              onClick={() => {
                setShowJobs(false);
                setTypeOfFavourite("article");
              }}
            />{" "}
          </div>
          <div>
            {typeOfFavourite === "opportunity" && showJobs && (
              <>
                {total > 0 ? (
                  <JobFavourites
                    totalOpportunities={total}
                    jobFavourites={shortLists}
                    typeOfFavourite={typeOfFavourite}
                    setShortLists={setShortLists}
                    getFavourites={getFavourites}
                    paginationModel={paginationModel}
                    setPaginationModel={setPaginationModel}
                  />
                ) : (
                  <img
                    src={nodata.src}
                    className="centered-image"
                    alt="Application favorite"
                    loading="lazy"
                  />
                )}
              </>
            )}
            {typeOfFavourite === "article" && !showJobs && (
              <>
                {totalArticles > 0 ? (
                  <ArticleFavourites
                    totalArticles={totalArticles}
                    articleFavourites={shortLists}
                    typeOfFavourite={typeOfFavourite}
                    setShortLists={setShortLists}
                    getFavourites={getFavourites}
                    paginationModel={paginationModel}
                    setPaginationModel={setPaginationModel}
                  />
                ) : (
                  <img
                    src={nodata.src}
                    className="centered-image"
                    alt="Application favorite"
                    loading="lazy"
                  />
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default CandidateFavourites;
