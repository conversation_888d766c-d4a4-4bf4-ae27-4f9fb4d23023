"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.countryContact = exports.GetInTouchFormContact = exports.mainServiceForm = exports.CountryForm = exports.FavouriteType = exports.OpportunityType = exports.JoinUsForm = exports.PayrollServiceForm = exports.technicalAssistanceContact = exports.ConsultingServiceForm = exports.GetInTouchForm = exports.Gender = exports.robotsMeta = exports.Visibility = exports.Language = exports.ApplicationStatus = exports.Type = exports.JobStatus = exports.Genre = exports.candidateStatus = exports.IndustryCandidat = exports.Cible = exports.Countries = exports.Industry = exports.Frequence = exports.Role = exports.languages = void 0;
const Countries = [
    'Congo Dem. Rep.',
    'Afghanistan',
    'Åland Islands',
    'Albania',
    'Algeria',
    'American Samoa',
    'AndorrA',
    'Angola',
    'Anguilla',
    'Antarctica',
    'Antigua and Barbuda',
    'Argentina',
    'Armenia',
    'Aruba',
    'Australia',
    'Austria',
    'Azerbaijan',
    'Bahamas',
    'Bahrain',
    'Bangladesh',
    'Barbados',
    'Belarus',
    'Belgium',
    'Belize',
    'Benin',
    'Bermuda',
    'Bhutan',
    'Bolivia',
    'Bosnia and Herzegovina',
    'Botswana',
    'Bouvet Island',
    'Brazil',
    'British Indian Ocean Territory',
    'Brunei Darussalam',
    'Bulgaria',
    'Burkina Faso',
    'Burundi',
    'Cambodia',
    'Cameroon',
    'Canada',
    'Cape Verde',
    'Cayman Islands',
    'Central African Republic',
    'Chad',
    'Chile',
    'China',
    'Christmas Island',
    'Cocos (Keeling) Islands',
    'Colombia',
    'Comoros',
    'Congo',
    'Congo, The Democratic Republic of the',
    'Cook Islands',
    'Costa Rica',
    "Côte d'Ivoire",
    "Cote D'Ivoire",
    'Croatia',
    'Cuba',
    'Cyprus',
    'Czech Republic',
    'Denmark',
    'Democratic Republic of the Congo',
    'Djibouti',
    'Dominica',
    'Dominican Republic',
    'Ecuador',
    'Egypt',
    'El Salvador',
    'Equatorial Guinea',
    'Eritrea',
    'Estonia',
    'Ethiopia',
    'Falkland Islands (Malvinas)',
    'Faroe Islands',
    'Fiji',
    'Finland',
    'France',
    'French Guiana',
    'French Polynesia',
    'French Southern Territories',
    'Gabon',
    'Gambia',
    'Georgia',
    'Germany',
    'Ghana',
    'Gibraltar',
    'Greece',
    'Greenland',
    'Grenada',
    'Guadeloupe',
    'Guam',
    'Guatemala',
    'Guernsey',
    'Guinea',
    'Guinea-Bissau',
    'Guyana',
    'Haiti',
    'Heard Island and Mcdonald Islands',
    'Holy See (Vatican City State)',
    'Honduras',
    'Hong Kong',
    'Hungary',
    'Iceland',
    'India',
    'Indonesia',
    'Iran, Islamic Republic Of',
    'Iraq',
    'Ireland',
    'Isle of Man',
    'Italy',
    'Ivory Coast',
    'Jamaica',
    'Japan',
    'Jersey',
    'Jordan',
    'Kazakhstan',
    'Kenya',
    'Kiribati',
    "Korea, Democratic People'S Republic of",
    'Korea, Republic of',
    'Kuwait',
    'Kyrgyzstan',
    "Lao People'S Democratic Republic",
    'Latvia',
    'Lebanon',
    'Lesotho',
    'Liberia',
    'Libya',
    'Liechtenstein',
    'Lithuania',
    'Luxembourg',
    'Macao',
    'Macedonia, The Former Yugoslav Republic of',
    'Madagascar',
    'Malawi',
    'Malaysia',
    'Maldives',
    'Mali',
    'Malta',
    'Marshall Islands',
    'Martinique',
    'Mauritania',
    'Mauritius',
    'Mayotte',
    'Mexico',
    'Micronesia, Federated States of',
    'Moldova, Republic of',
    'Monaco',
    'Mongolia',
    'Montserrat',
    'Morocco',
    'Mozambique',
    'Myanmar',
    'Namibia',
    'Nauru',
    'Nepal',
    'Netherlands',
    'Netherlands Antilles',
    'New Caledonia',
    'New Zealand',
    'Nicaragua',
    'Niger',
    'Nigeria',
    'Niue',
    'Norfolk Island',
    'Northern Mariana Islands',
    'Norway',
    'Oman',
    'Pakistan',
    'Palau',
    'Palestine',
    'Panama',
    'Papua New Guinea',
    'Paraguay',
    'Peru',
    'Philippines',
    'Pitcairn',
    'Poland',
    'Portugal',
    'Puerto Rico',
    'Qatar',
    'Reunion',
    'Romania',
    'Russian Federation',
    'RWANDA',
    'Saint Helena',
    'Saint Kitts and Nevis',
    'Saint Lucia',
    'Saint Pierre and Miquelon',
    'Saint Vincent and the Grenadines',
    'Samoa',
    'San Marino',
    'Sao Tome and Principe',
    'Saudi Arabia',
    'Senegal',
    'Serbia',
    'Serbia and Montenegro',
    'Seychelles',
    'Sierra Leone',
    'Singapore',
    'Slovakia',
    'Slovenia',
    'Solomon Islands',
    'Somalia',
    'South Africa',
    'South Georgia and the South Sandwich Islands',
    'South Korea',
    'Spain',
    'Sri Lanka',
    'Sudan',
    'Suriname',
    'Svalbard and Jan Mayen',
    'Swaziland',
    'Sweden',
    'Switzerland',
    'Syrian Arab Republic',
    'Taiwan, Province of China',
    'Tajikistan',
    'Tanzania, United Republic of',
    'Thailand',
    'Timor-Leste',
    'Togo',
    'Tokelau',
    'Tonga',
    'Trinidad and Tobago',
    'Tunisia',
    'Turkey',
    'Turkmenistan',
    'Turks and Caicos Islands',
    'Tuvalu',
    'Uganda',
    'Ukraine',
    'United Arab Emirates',
    'United Kingdom',
    'United States',
    'United States Minor Outlying Islands',
    'Uruguay',
    'Uzbekistan',
    'Vanuatu',
    'Venezuela',
    'Viet Nam',
    'Virgin Islands, British',
    'Virgin Islands, U.S.',
    'Wallis and Futuna',
    'Western Sahara',
    'Yemen',
    'Zambia',
    'Zimbabwe',
];
exports.Countries = Countries;
const languages = ['en', 'fr'];
exports.languages = languages;
var OpportunityType;
(function (OpportunityType) {
    OpportunityType["CONFIDENTIAL"] = "Confidential";
    OpportunityType["DIRECT_HIRE"] = "Direct Hire";
    OpportunityType["TENDER"] = "Tender";
    OpportunityType["CAPABILITY"] = "Capability";
    OpportunityType["PAYROLL"] = "Payroll";
    OpportunityType["IN_HOUSE"] = "In House";
    OpportunityType["RECRUTEMENT"] = "Recrutement";
    OpportunityType["CONSULTING"] = "Consulting";
    OpportunityType["PORTAGE"] = "Portage";
    OpportunityType["NOT_SPECIFIED"] = "Not specified";
})(OpportunityType || (exports.OpportunityType = OpportunityType = {}));
var Role;
(function (Role) {
    Role["ADMIN"] = "Admin";
    Role["CANDIDATE"] = "Candidate";
    Role["RECRUITER"] = "Recruiter";
    Role["EDITEUR"] = "Editor";
})(Role || (exports.Role = Role = {}));
var Visibility;
(function (Visibility) {
    Visibility["Public"] = "Public";
    Visibility["Private"] = "Private";
    Visibility["Draft"] = "Draft";
})(Visibility || (exports.Visibility = Visibility = {}));
var Language;
(function (Language) {
    Language["FRENCH"] = "fr";
    Language["ENGLISH"] = "en";
})(Language || (exports.Language = Language = {}));
var Gender;
(function (Gender) {
    Gender["All"] = "All";
    Gender["Female"] = "Female";
    Gender["Male"] = "Male";
})(Gender || (exports.Gender = Gender = {}));
var robotsMeta;
(function (robotsMeta) {
    robotsMeta["index"] = "index";
    robotsMeta["noindex"] = "noindex";
})(robotsMeta || (exports.robotsMeta = robotsMeta = {}));
var Cible;
(function (Cible) {
    Cible["client"] = "client";
    Cible["consultant"] = "consultant";
})(Cible || (exports.Cible = Cible = {}));
var Industry;
(function (Industry) {
    Industry["TELECOM"] = "It & Telecom";
    Industry["TRANSPORT"] = "Transport";
    Industry["ENERGIES"] = "Energies";
    Industry["BANKING"] = "Banking";
    Industry["OTHERS"] = "Others";
    Industry["PHARMACEUTICAL"] = "Pharmaceutical";
})(Industry || (exports.Industry = Industry = {}));
var IndustryCandidat;
(function (IndustryCandidat) {
    IndustryCandidat["TELECOM"] = "It & Telecom";
    IndustryCandidat["TRANSPORT"] = "Transport";
    IndustryCandidat["OIL_GAS"] = "Oil & gas";
    IndustryCandidat["ENERGY"] = "Energy";
    IndustryCandidat["BANKING"] = "Banking";
    IndustryCandidat["PHARMACEUTICAL"] = "Pharmaceutical";
    IndustryCandidat["OTHERS"] = "Others";
})(IndustryCandidat || (exports.IndustryCandidat = IndustryCandidat = {}));
var Genre;
(function (Genre) {
    Genre["MALE"] = "Male";
    Genre["FEMALE"] = "Female";
    Genre["BOTH"] = "Both";
})(Genre || (exports.Genre = Genre = {}));
var ApplicationStatus;
(function (ApplicationStatus) {
    ApplicationStatus["PENDING"] = "Pending";
    ApplicationStatus["ACCEPTED"] = "Accepted";
    ApplicationStatus["REJECTED"] = "Rejected";
})(ApplicationStatus || (exports.ApplicationStatus = ApplicationStatus = {}));
var candidateStatus;
(function (candidateStatus) {
    candidateStatus["New"] = "New";
    candidateStatus["Active"] = "Active";
    candidateStatus["Inactive"] = "Inactive";
    candidateStatus["Blacklisted"] = "Blacklisted";
})(candidateStatus || (exports.candidateStatus = candidateStatus = {}));
var JobStatus;
(function (JobStatus) {
    JobStatus["ACTIVE"] = "Active";
    JobStatus["EXPIRED"] = "Expired";
})(JobStatus || (exports.JobStatus = JobStatus = {}));
const GetInTouchFormContact = ['type', 'to', 'team', 'fullName', 'email', 'phone', 'youAre', 'subject', 'country', 'message', 'resume', 'field'];
exports.GetInTouchFormContact = GetInTouchFormContact;
const GetInTouchForm = ['type', 'to', 'team', 'firstName', 'lastName', 'email', 'phone', 'message', 'resume', 'field'];
exports.GetInTouchForm = GetInTouchForm;
const technicalAssistanceContact = [
    'type',
    'to',
    'team',
    'fullName',
    'email',
    'phone',
    'youAre',
    'message',
    'companyName',
    'jobTitle',
    'resume',
    'field',
];
exports.technicalAssistanceContact = technicalAssistanceContact;
const ConsultingServiceForm = [
    'type',
    'to',
    'team',
    'fullName',
    'jobTitle',
    'email',
    'phone',
    'companyName',
    'subject',
    'youAre',
    'message',
    'resume',
    'field',
];
exports.ConsultingServiceForm = ConsultingServiceForm;
const PayrollServiceForm = ['type', 'to', 'firstName', 'lastName', 'email', 'phone', 'companyName', 'message', 'team', 'youAre', 'resume', 'field'];
exports.PayrollServiceForm = PayrollServiceForm;
const mainServiceForm = [
    'type',
    'to',
    'firstName',
    'lastName',
    'email',
    'phone',
    'companyName',
    'enquirySelect',
    'message',
    'team',
    'resume',
    'field',
];
exports.mainServiceForm = mainServiceForm;
const countryContact = [
    'type',
    'to',
    'fullName',
    'email',
    'phone',
    'companyName',
    'howToHelp',
    'message',
    'youAre',
    'team',
    'countryName',
    'country',
    'resume',
    'field',
];
exports.countryContact = countryContact;
const CountryForm = ['type', 'fullName', 'email', 'youAre', 'phoneNumber', 'message', 'country', 'resume', 'field'];
exports.CountryForm = CountryForm;
const JoinUsForm = [
    'type',
    'to',
    'team',
    'fullName',
    'jobTitle',
    'email',
    'phone',
    'companyName',
    'subject',
    'field',
    'message',
    'mission',
    'resume',
    'field',
];
exports.JoinUsForm = JoinUsForm;
var Type;
(function (Type) {
    Type["DEFAULT"] = "default";
    Type["APPLICATIONSTATUS"] = "applicationstatus";
    Type["JOBALERT"] = "jobalert";
    Type["COMMENT"] = "comment";
})(Type || (exports.Type = Type = {}));
var FavouriteType;
(function (FavouriteType) {
    FavouriteType["ARTICLE"] = "article";
    FavouriteType["OPPORTUNITY"] = "opportunity";
})(FavouriteType || (exports.FavouriteType = FavouriteType = {}));
var Frequence;
(function (Frequence) {
    Frequence["MONTHLY"] = "monthly";
    Frequence["WEEKLY"] = "weekly";
})(Frequence || (exports.Frequence = Frequence = {}));
//# sourceMappingURL=constants.js.map