"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const downloadsreport_service_1 = __importDefault(require("./downloadsreport.service"));
class DownloadReport {
    constructor() {
        this.path = '/report';
        this.router = (0, express_1.Router)();
        this.downloadreportService = new downloadsreport_service_1.default();
        this.handleReportDownload = async (req, res, next) => {
            try {
                const { fullName, email } = req.body;
                const message = await this.downloadreportService.handleReportDownload(fullName, email);
                return res.status(200).json({ message });
            }
            catch (error) {
                next(error);
            }
        };
        this.getAllDownloadReport = async (req, res, next) => {
            try {
                const queries = req.query;
                const result = await this.downloadreportService.getAllDownloadReports(queries);
                return res.status(200).json(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.initialiseRoutes();
    }
    initialiseRoutes() {
        this.router.post(`${this.path}/download`, this.handleReportDownload);
        this.router.get(`${this.path}`, this.getAllDownloadReport);
    }
}
exports.default = DownloadReport;
//# sourceMappingURL=downloadsreport.controller.js.map