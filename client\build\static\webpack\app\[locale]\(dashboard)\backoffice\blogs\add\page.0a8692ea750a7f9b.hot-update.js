"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/add/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/AddArticleEN.jsx":
/*!*******************************************************!*\
  !*** ./src/features/blog/components/AddArticleEN.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! formik */ \"(app-pages-browser)/./node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-datepicker/dist/react-datepicker.css */ \"(app-pages-browser)/./node_modules/react-datepicker/dist/react-datepicker.css\");\n/* harmony import */ var _utils_urls__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../utils/urls */ \"(app-pages-browser)/./src/utils/urls.js\");\n/* harmony import */ var _assets_images_add_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/assets/images/add.png */ \"(app-pages-browser)/./src/assets/images/add.png\");\n/* harmony import */ var _feelinglovelynow_slug__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @feelinglovelynow/slug */ \"(app-pages-browser)/./node_modules/@feelinglovelynow/slug/dist/index.js\");\n/* harmony import */ var _FaqSection__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./FaqSection */ \"(app-pages-browser)/./src/features/blog/components/FaqSection.jsx\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormGroup/FormGroup.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormLabel/FormLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Autocomplete/Autocomplete.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var react_tag_input__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! react-tag-input */ \"(app-pages-browser)/./node_modules/react-tag-input/dist/index.js\");\n/* harmony import */ var _features_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/features/opportunity/hooks/opportunity.hooks */ \"(app-pages-browser)/./src/features/opportunity/hooks/opportunity.hooks.js\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _components_ui_CustomTextInput__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/CustomTextInput */ \"(app-pages-browser)/./src/components/ui/CustomTextInput.jsx\");\n/* harmony import */ var _components_ui_CustomSunEditor__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/CustomSunEditor */ \"(app-pages-browser)/./src/components/ui/CustomSunEditor.jsx\");\n/* harmony import */ var _components_ui_CustomDatePicker__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/CustomDatePicker */ \"(app-pages-browser)/./src/components/ui/CustomDatePicker.jsx\");\n/* harmony import */ var _DocumentImporter__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./DocumentImporter */ \"(app-pages-browser)/./src/features/blog/components/DocumentImporter.jsx\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var _components_ui_CustomSelect__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/CustomSelect */ \"(app-pages-browser)/./src/components/ui/CustomSelect.jsx\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction AddArticleEN(param) {\n    let { errors, touched, setFieldValue, values, onImageSelect, filteredCategories, categories, onCategoriesSelect, debounce } = param;\n    _s();\n    const KeyCodes = {\n        comma: 188,\n        enter: 13\n    };\n    const delimiters = [\n        KeyCodes.comma,\n        KeyCodes.enter\n    ];\n    const [tags, setTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [highlights, setHighlights] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const [description, setDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(values.descriptionEN || \"\");\n    const [url, setUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(values.urlEN || \"\");\n    const [publishNow, setPublishNow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [publishDate, setPublishDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const imageInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const language = \"en\";\n    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedTitle = localStorage.getItem(\"title\");\n        return savedTitle ? JSON.parse(savedTitle) : \"\";\n    });\n    const [metatitle, setMetatitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedMetatitle = localStorage.getItem(\"metatitle\");\n        return savedMetatitle ? JSON.parse(savedMetatitle) : \"\";\n    });\n    const [metaDescription, setMetaDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedMetadescription = localStorage.getItem(\"metaDescription\");\n        return savedMetadescription ? JSON.parse(savedMetadescription) : \"\";\n    });\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedContent = localStorage.getItem(\"content\");\n        return savedContent ? JSON.parse(savedContent) : \"\";\n    });\n    const handlePaste = (event, cleanData, maxCharCount)=>{\n        let html = cleanData;\n        html = html.replace(/<strong>(.*?)$/g, \"<strong>$1</strong>\");\n        return html;\n    };\n    const handleEditorChange = (newContent)=>{\n        debounce();\n        setContent(newContent);\n        setFieldValue(\"contentEN\", newContent);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (title) {\n            localStorage.setItem(\"title\", JSON.stringify(title));\n        }\n        if (content) {\n            localStorage.setItem(\"content\", JSON.stringify(content));\n        }\n        if (metatitle) {\n            localStorage.setItem(\"metatitle\", JSON.stringify(metatitle));\n        }\n        if (metaDescription) {\n            localStorage.setItem(\"metaDescription\", JSON.stringify(metaDescription));\n        }\n    }, [\n        title,\n        content,\n        metatitle,\n        metaDescription\n    ]);\n    const handlePhotoChange = async ()=>{\n        const selectedFile = imageInputRef.current.files[0];\n        setSelectedImage(imageInputRef.current.files[0]);\n        if (selectedFile) {\n            onImageSelect(selectedFile, language);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setFieldValue(\"titleEN\", title);\n        setFieldValue(\"descriptionEN\", description);\n        setFieldValue(\"urlEN\", url);\n        setFieldValue(\"keywordsEN\", tags.map((t)=>t.text));\n        setFieldValue(\"highlightsEN\", highlights.map((h)=>h.text));\n        setFieldValue(\"contentEN\", content);\n        setFieldValue(\"metaTitleEN\", metatitle);\n        setFieldValue(\"metaDescriptionEN\", metaDescription);\n    }, [\n        title,\n        description,\n        url,\n        tags,\n        highlights,\n        content,\n        metatitle,\n        metaDescription\n    ]);\n    const useSaveFileHook = (0,_features_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_8__.useSaveFile)();\n    let uuidPhoto;\n    uuidPhoto = (0,uuid__WEBPACK_IMPORTED_MODULE_15__[\"default\"])().replace(/-/g, \"\");\n    const handlePhotoBlogChange = async (file, info, core, uploadHandler)=>{\n        if (file instanceof HTMLImageElement) {\n            const src = file.src;\n            if (src.startsWith(\"data:image\")) {\n                const base64Data = src.split(\",\")[1];\n                const contentType = src.match(/data:(.*?);base64/)[1];\n                const byteCharacters = atob(base64Data);\n                const byteNumbers = new Array(byteCharacters.length).fill(0).map((_, i)=>byteCharacters.charCodeAt(i));\n                const byteArray = new Uint8Array(byteNumbers);\n                const blob = new Blob([\n                    byteArray\n                ], {\n                    type: contentType\n                });\n                const fileName = `image_${Date.now()}.${contentType.split(\"/\")[1]}`;\n                const selectedFile = new File([\n                    blob\n                ], fileName, {\n                    type: contentType\n                });\n                await uploadFile(selectedFile, uploadHandler, core, file);\n            } else {\n                fetch(src).then((response)=>response.blob()).then((blob)=>{\n                    const contentType = blob.type;\n                    const fileName = `image_${Date.now()}.${contentType.split(\"/\")[1]}`;\n                    const selectedFile = new File([\n                        blob\n                    ], fileName, {\n                        type: contentType\n                    });\n                    uploadFile(selectedFile, uploadHandler, core, file);\n                }).catch((error)=>console.error(\"Error converting image URL to Blob:\", error));\n            }\n        } else {\n            console.error(\"File is not an HTMLImageElement.\");\n        }\n    };\n    const uploadFile = (selectedFile, uploadHandler, core, originalImage)=>{\n        let uuidPhoto;\n        uuidPhoto = (0,uuid__WEBPACK_IMPORTED_MODULE_15__[\"default\"])().replace(/-/g, \"\");\n        const formData = new FormData();\n        formData.append(\"file\", selectedFile);\n        const extension = selectedFile.name.split(\".\").pop();\n        const currentYear = new Date().getFullYear();\n        useSaveFileHook.mutate({\n            resource: \"blogs\",\n            folder: currentYear.toString(),\n            filename: uuidPhoto,\n            body: {\n                formData,\n                t\n            }\n        }, {\n            onSuccess: (dataUUID)=>{\n                const uuidPhotoFileName = dataUUID.message === \"uuid exist\" ? dataUUID.uuid : `${uuidPhoto}.${extension}`;\n                const imageUrl = `${\"http://localhost:4000/api/v1\"}/files/${uuidPhotoFileName}`;\n                originalImage.src = imageUrl;\n                uploadHandler({\n                    result: [\n                        {\n                            id: uuidPhotoFileName,\n                            url: imageUrl\n                        }\n                    ]\n                });\n            },\n            onError: (error)=>{\n                console.error(\"Error uploading file:\", error);\n            }\n        });\n    };\n    const handleContentExtracted = (extractedContent)=>{\n        setFieldValue(\"contentEN\", extractedContent);\n        setContent(extractedContent);\n        localStorage.setItem(\"content\", JSON.stringify(extractedContent));\n        debounce();\n    };\n    const handleMetadataExtracted = (metadata)=>{\n        if (metadata.title && !values.titleEN) {\n            setFieldValue(\"titleEN\", metadata.title);\n            const url = (0,_feelinglovelynow_slug__WEBPACK_IMPORTED_MODULE_6__.slug)(metadata.title);\n            setFieldValue(\"urlEN\", url);\n        }\n        if (metadata.description && !values.descriptionEN) {\n            setFieldValue(\"descriptionEN\", metadata.description);\n        }\n        if (metadata.keywords && metadata.keywords.length > 0) {\n            const keywordTags = metadata.keywords.map((keyword, index)=>({\n                    id: `extracted-${index}`,\n                    text: keyword\n                }));\n            const existingKeywords = values.keywordsEN || [];\n            const mergedKeywords = [\n                ...existingKeywords,\n                ...keywordTags\n            ];\n            setFieldValue(\"keywordsEN\", mergedKeywords.map((tag)=>tag.text));\n            const existingTags = tags || [];\n            const mergedTags = [\n                ...existingTags,\n                ...keywordTags\n            ];\n            setTags(mergedTags);\n        }\n        debounce();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"label-pentabell\",\n                children: \"Add article English : \"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 264,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTextInput__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    label: t(\"createArticle:title\"),\n                                    name: \"titleEN\",\n                                    value: title,\n                                    onChange: (e)=>{\n                                        const v = e.target.value;\n                                        setTitle(v);\n                                        setUrl((0,_feelinglovelynow_slug__WEBPACK_IMPORTED_MODULE_6__.slug)(v));\n                                        debounce();\n                                    },\n                                    error: touched.titleEN && errors.titleEN\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 268,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 266,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"label-form\",\n                                    children: [\n                                        t(\"createArticle:categories\"),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                multiple: true,\n                                                className: \"input-pentabell\",\n                                                id: \"tags-standard\",\n                                                options: filteredCategories.length > 0 ? filteredCategories : categories,\n                                                getOptionLabel: (option)=>option.name,\n                                                selected: values.categoryEN.length > 0 ? (filteredCategories.length > 0 ? filteredCategories : categories).filter((category)=>values.categoryEN.includes(category.id)) : [],\n                                                onChange: (event, selectedOptions)=>{\n                                                    const categoryIds = selectedOptions.map((category)=>category.id);\n                                                    setFieldValue(\"categoryEN\", categoryIds);\n                                                    onCategoriesSelect(categoryIds);\n                                                },\n                                                renderInput: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        ...params,\n                                                        className: \"input-pentabell  multiple-select\",\n                                                        variant: \"standard\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 21\n                                                    }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 284,\n                                columnNumber: 11\n                            }, this),\n                            touched.category && errors.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"label-error\",\n                                children: errors.category\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 265,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTextInput__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            label: \"Description\",\n                            name: \"descriptionEN\",\n                            rows: 3,\n                            multiline: true,\n                            value: values.descriptionEN,\n                            onChange: (e)=>{\n                                const descriptionEN = e.target.value;\n                                setFieldValue(\"descriptionEN\", descriptionEN);\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 335,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 334,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                    lineNumber: 333,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 332,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"label-form\",\n                            children: [\n                                \"Highlights\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    id: \"tags\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tag_input__WEBPACK_IMPORTED_MODULE_21__.WithContext, {\n                                        tags: highlights,\n                                        className: \"input-pentabell\" + (errors.highlightsEN && touched.highlightsEN ? \" is-invalid\" : \"\"),\n                                        delimiters: delimiters,\n                                        handleDelete: (i)=>{\n                                            const updatedTags = highlights.filter((tag, index)=>index !== i);\n                                            setHighlights(updatedTags);\n                                            setFieldValue(\"highlightsEN\", updatedTags.map((tag)=>tag.text));\n                                        },\n                                        handleAddition: (tag)=>{\n                                            setHighlights([\n                                                ...highlights,\n                                                tag\n                                            ]);\n                                            setFieldValue(\"highlightsEN\", [\n                                                ...highlights,\n                                                tag\n                                            ].map((item)=>item.text));\n                                        },\n                                        inputFieldPosition: \"bottom\",\n                                        autocomplete: true,\n                                        allowDragDrop: false\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_22__.ErrorMessage, {\n                                    className: \"label-error\",\n                                    name: \"keywordsEN\",\n                                    component: \"div\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 352,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 351,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                    lineNumber: 350,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 349,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DocumentImporter__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        onContentExtracted: handleContentExtracted,\n                        onMetadataExtracted: handleMetadataExtracted,\n                        language: \"EN\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 396,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomSunEditor__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        content: content || values?.contentEN || \"\",\n                        onChange: (newContent)=>{\n                            setContent(newContent);\n                            setFieldValue(\"contentEN\", newContent);\n                            debounce();\n                        },\n                        onPaste: handlePaste,\n                        onImageUpload: handlePhotoBlogChange\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 401,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 395,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 413,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FaqSection__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                values: values,\n                setFieldValue: setFieldValue,\n                errors: errors,\n                touched: touched,\n                language: \"EN\",\n                debounce: debounce\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 414,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 422,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTextInput__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    label: t(\"createArticle:metaTitle\"),\n                                    name: \"metaTitleEN\",\n                                    value: metatitle || values.metaTitleEN,\n                                    onChange: (e)=>{\n                                        const metaTitleEN = e.target.value;\n                                        setFieldValue(\"metaTitleEN\", metaTitleEN);\n                                        setMetatitle(metaTitleEN);\n                                        debounce();\n                                    },\n                                    showLength: true,\n                                    maxLength: 65\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 427,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 426,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 424,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTextInput__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                label: t(\"createArticle:url\"),\n                                name: \"urlEN\",\n                                value: url,\n                                onChange: (e)=>setUrl(e.target.value),\n                                error: touched.urlEN && errors.urlEN\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 444,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 443,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 442,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 423,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTextInput__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            label: t(\"createArticle:metaDescription\"),\n                            name: \"metaDescriptionEN\",\n                            value: metaDescription || values.metaDescriptionEN,\n                            onChange: (e)=>{\n                                const metaDescriptionEN = e.target.value;\n                                setFieldValue(\"metaDescriptionEN\", metaDescriptionEN);\n                                setMetaDescription(metaDescriptionEN);\n                                debounce();\n                            },\n                            showLength: true,\n                            maxLength: 160\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 457,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 456,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                    lineNumber: 455,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 454,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"label-form\",\n                            children: [\n                                t(\"createArticle:featuredImage\"),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"upload-container\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: `image-upload-en`,\n                                        className: \"file-labels\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"file\",\n                                                id: `image-upload-en`,\n                                                name: \"imageEN\",\n                                                accept: \".png, .jpg, .jpeg, .webp\",\n                                                ref: imageInputRef,\n                                                onChange: (e)=>{\n                                                    setFieldValue(\"imageEN\", e.target.files[0]);\n                                                    handlePhotoChange();\n                                                },\n                                                className: \"file-input\" + (errors.imageEN && touched.imageEN ? \" is-invalid\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"upload-area\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"icon-pic\",\n                                                            style: {\n                                                                backgroundImage: `url(\"${selectedImage ? URL.createObjectURL(selectedImage) : values.imageEN ? `${process.env.REACT_APP_API_URL}${_utils_urls__WEBPACK_IMPORTED_MODULE_4__.API_URLS.files}/${values.imageEN}` : _assets_images_add_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"].src}\")`,\n                                                                backgroundSize: \"cover\",\n                                                                backgroundRepeat: \"no-repeat\",\n                                                                backgroundPosition: \"center\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                            lineNumber: 498,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                        lineNumber: 497,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"upload-text\",\n                                                                children: t(\"createArticle:addFeatImg\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                                lineNumber: 516,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"upload-description\",\n                                                                children: t(\"createArticle:clickBox\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                                lineNumber: 519,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                        lineNumber: 515,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_22__.ErrorMessage, {\n                                                name: \"image\",\n                                                component: \"div\",\n                                                className: \"invalid-feedback error\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 479,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 476,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 475,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                    lineNumber: 474,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 473,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTextInput__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                label: t(\"createArticle:alt\"),\n                                name: \"altEN\",\n                                value: values.altEN,\n                                onChange: (e)=>{\n                                    setFieldValue(\"altEN\", e.target.value);\n                                    debounce();\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 539,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 538,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 537,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomSelect__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    label: t(\"createArticle:visibility\"),\n                                    name: \"visibilityEN\",\n                                    value: values.visibilityEN,\n                                    onChange: (e)=>setFieldValue(\"visibilityEN\", e.target.value),\n                                    options: _utils_constants__WEBPACK_IMPORTED_MODULE_13__.Visibility,\n                                    error: touched.visibilityEN && errors.visibilityEN,\n                                    getOptionLabel: (item)=>item,\n                                    getOptionValue: (item)=>item\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 553,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 552,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 550,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"label-form\",\n                                    children: [\n                                        t(\"createArticle:keyword\"),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            id: \"tags\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tag_input__WEBPACK_IMPORTED_MODULE_21__.WithContext, {\n                                                tags: tags,\n                                                className: \"input-pentabell\" + (errors.keywordsEN && touched.keywordsEN ? \" is-invalid\" : \"\") + (tags.length === 0 ? \" no-tags\" : \"\"),\n                                                delimiters: delimiters,\n                                                handleDelete: (i)=>{\n                                                    const updatedTags = tags.filter((tag, index)=>index !== i);\n                                                    setTags(updatedTags);\n                                                    setFieldValue(\"keywordsEN\", updatedTags.map((tag)=>tag.text));\n                                                },\n                                                handleAddition: (tag)=>{\n                                                    setTags([\n                                                        ...tags,\n                                                        tag\n                                                    ]);\n                                                    setFieldValue(\"keywordsEN\", [\n                                                        ...tags,\n                                                        tag\n                                                    ].map((item)=>item.text));\n                                                    debounce();\n                                                },\n                                                inputFieldPosition: \"bottom\",\n                                                autocomplete: true,\n                                                allowDragDrop: false\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                lineNumber: 573,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                            lineNumber: 572,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_22__.ErrorMessage, {\n                                            className: \"label-error\",\n                                            name: \"keywordsEN\",\n                                            component: \"div\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                            lineNumber: 606,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 569,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 568,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 566,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 536,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"label-form\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_22__.Field, {\n                                    type: \"checkbox\",\n                                    name: \"publishNow\",\n                                    checked: publishNow,\n                                    onChange: (e)=>{\n                                        setPublishNow(e.target.checked);\n                                        if (e.target.checked) {\n                                            setFieldValue(\"publishDateEN\", new Date().toISOString());\n                                        }\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 618,\n                                    columnNumber: 13\n                                }, this),\n                                t(\"createArticle:publishNow\")\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 617,\n                            columnNumber: 11\n                        }, this),\n                        !publishNow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomDatePicker__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    label: t(\"createArticle:publishDate\"),\n                                    value: values.publishDateEN || new Date(),\n                                    onChange: (date)=>setFieldValue(\"publishDateEN\", date),\n                                    error: touched.publishDateEN && errors.publishDateEN\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 634,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 633,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 632,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                    lineNumber: 616,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 615,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_22__.Field, {\n                type: \"hidden\",\n                name: \"publishDateEN\",\n                value: publishNow ? new Date().toISOString() : publishDate.toISOString()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 646,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(AddArticleEN, \"llvZVW20fTwrlxkztVeG1u5sgoE=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _features_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_8__.useSaveFile\n    ];\n});\n_c = AddArticleEN;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AddArticleEN);\nvar _c;\n$RefreshReg$(_c, \"AddArticleEN\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/AddArticleEN.jsx\n"));

/***/ })

});