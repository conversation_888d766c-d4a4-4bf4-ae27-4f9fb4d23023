"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const user_model_1 = __importDefault(require("../user/user.model"));
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
class Userservice {
    constructor() {
        this.getAllUsers = async (page, limit, search = '') => {
            const query = search ? { email: { $regex: search, $options: 'i' } } : {};
            const skip = (page - 1) * limit;
            const users = await user_model_1.default.find(query).select('-refreshTokens').skip(skip).limit(limit);
            const total = await user_model_1.default.countDocuments(query);
            return {
                total,
                page,
                totalPages: Math.ceil(total / limit),
                limit,
                data: users,
            };
        };
    }
}
exports.default = Userservice;
//# sourceMappingURL=userservice.js.map