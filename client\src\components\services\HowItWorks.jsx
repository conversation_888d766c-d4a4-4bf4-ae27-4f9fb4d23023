import initTranslations from "@/app/i18n";
import { Container } from "@mui/material";

async function HowItWorks({ steps, locale }) {
  const { t, resources } = await initTranslations(locale, [
    "payrollService",
    "global",
  ]);

  return (
    <div id="how-it-works">
      <Container className="custom-max-width">
        <h2 className="heading-h1 text-white text-center">
          {" "}
          {t("payrollService:howItWorks:title")}
        </h2>
        <ul className="multi-step">
          {steps.map((step, index) => (
            <li key={index}>
              <span className="step"></span>
              <div className="content">
                <div
                  className={index % 2 === 0 ? "content-odd" : "content-even"}
                >
                  <h3 className="title">{step.title}</h3>
                  <p className="paragraph">{step.description}</p>
                </div>
              </div>
            </li>
          ))}
        </ul>
      </Container>
    </div>
  );
}

export default HowItWorks;
