"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AlertService = void 0;
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const messages_1 = require("@/utils/helpers/messages");
const alert_model_1 = __importDefault(require("./alert.model"));
const user_model_1 = __importDefault(require("../user/user.model"));
const alert_model_2 = __importDefault(require("./alert.model"));
const notification_model_1 = __importDefault(require("../notifications/notification.model"));
const settings_model_1 = __importDefault(require("../settings/settings.model"));
class AlertService {
    constructor() {
        this.Alert = alert_model_1.default;
    }
    async create(alertData, currentUser) {
        const existingAlert = await this.Alert.findOne({
            createdBy: currentUser._id,
            isActive: true,
        }).lean();
        if (existingAlert) {
            throw new http_exception_1.default(400, messages_1.MESSAGES.ALERT.ACTIVE_ALERT);
        }
        const createdAlert = await this.Alert.create({
            ...alertData,
            createdBy: currentUser._id,
            isActive: true,
        });
        return createdAlert.toObject();
    }
    async getAlerts(currentUser) {
        const alerts = await this.Alert.find({
            createdBy: currentUser._id,
            isActive: true
        });
        return alerts.map(alert => alert.toObject());
    }
    async get(alertId) {
        const alert = await this.Alert.findById(alertId).lean();
        if (!alert) {
            throw new http_exception_1.default(404, messages_1.MESSAGES.ALERT.NOT_FOUND);
        }
        return alert;
    }
    async toggle(alertId, status) {
        await this.get(alertId);
        await alert_model_2.default.findByIdAndUpdate(alertId, { isActive: status });
    }
    async getById(id) {
        const alert = await alert_model_2.default.findById(id).lean();
        if (!alert)
            throw new http_exception_1.default(404, messages_1.MESSAGES.ALERT.NOT_FOUND);
        return alert;
    }
    async update(currentUser, id, alertData) {
        const alert = await alert_model_2.default.findById(id).lean();
        if (!alert) {
            throw new http_exception_1.default(404, messages_1.MESSAGES.ALERT.NOT_FOUND);
        }
        if (alert.createdBy.toString() !== currentUser._id.toString()) {
            throw new http_exception_1.default(403, messages_1.MESSAGES.ALERT.UNAUTHORIZED_UPDATE);
        }
        const updatedAlert = await this.Alert.findByIdAndUpdate(id, { ...alertData }, { new: true }).lean();
        return updatedAlert;
    }
    async addAlertToAllUsers() {
        try {
            const usersWithoutAlerts = await user_model_1.default.find({
                $or: [{ alerts: { $exists: false } }, { alerts: null }],
            });
            if (!usersWithoutAlerts || usersWithoutAlerts.length === 0) {
                throw new http_exception_1.default(404, messages_1.MESSAGES.ALERT.NOT_FOUND);
            }
            const operations = usersWithoutAlerts.map(async (user) => {
                if (!user.alerts) {
                    const alert = new alert_model_2.default({
                        createdBy: user._id,
                        industry: [],
                        title: '',
                        country: [],
                        isActive: true,
                        frequence: 'weekly',
                    });
                    const savedAlert = await alert.save();
                    await user_model_1.default.updateOne({ _id: user._id }, { $set: { alerts: savedAlert._id } });
                }
            });
            await Promise.all(operations);
        }
        catch (error) {
            throw new http_exception_1.default(500, messages_1.MESSAGES.GENERAL.SERVER_ERROR);
        }
    }
    async addNotificationsTo() {
        try {
            const users = await user_model_1.default.find();
            if (!users || users.length === 0) {
                throw new http_exception_1.default(404, messages_1.MESSAGES.ALERT.NO_USERS_FOR_CREATION);
            }
            const notifications = users.map(user => ({
                receiver: user._id,
                sender: null,
                message: "Welcome to Pentabell, we are happy to see you here!",
                link: process.env.LOGIN_LINK ?? '',
            }));
            const result = await notification_model_1.default.insertMany(notifications);
            if (!result || result.length === 0) {
                throw new http_exception_1.default(500, messages_1.MESSAGES.GENERAL.SERVER_ERROR);
            }
        }
        catch (error) {
            throw new http_exception_1.default(500, messages_1.MESSAGES.GENERAL.SERVER_ERROR);
        }
    }
    async addsettings() {
        try {
            const usersWithoutAlerts = await user_model_1.default.find();
            if (usersWithoutAlerts.length === 0) {
                return;
            }
            for (const user of usersWithoutAlerts) {
                const alert = new settings_model_1.default({
                    user: user._id,
                    notifications: {
                        newJobAlerts: {
                            email: true,
                            website: true,
                        },
                        appliedJobStatusUpdates: {
                            email: true,
                            website: true,
                        },
                        newsLetter: {
                            email: true,
                            website: true,
                        },
                    },
                });
                const savedAlert = await alert.save();
            }
        }
        catch (error) {
            throw new http_exception_1.default(500, messages_1.MESSAGES.GENERAL.SERVER_ERROR);
        }
    }
    async removeAlertFromAllUsers() {
        try {
            const updateResult = await user_model_1.default.updateMany({}, { $set: { alerts: null } });
        }
        catch (error) {
            throw new http_exception_1.default(500, messages_1.MESSAGES.GENERAL.SERVER_ERROR);
        }
    }
    async getAll(queries) {
        const { paginated, industry, createdAt, sortOrder, createdBy, isActive } = queries;
        const pageNumber = Number(queries.pageNumber) || 1;
        const pageSize = Number(queries.pageSize) || 8;
        const queryConditions = {};
        if (createdAt) {
            const date = new Date(createdAt);
            const nextYear = date.getFullYear() + 1;
            const antDate = new Date(nextYear, 0, 0, 24, 59, 59, 999);
            queryConditions['createdAt'] = {
                $gte: date.toISOString(),
                $lte: antDate.toISOString(),
            };
        }
        if (industry) {
            queryConditions['industry'] = industry;
        }
        if (createdBy) {
            queryConditions['createdBy'] = createdBy;
        }
        if (isActive) {
            queryConditions['isActive'] = isActive;
        }
        if (paginated === 'false') {
            const alerts = await this.Alert.find(queryConditions)
                .sort({ createdAt: sortOrder === 'asc' ? 1 : -1 })
                .select('industry country isActive createdAt')
                .lean();
            return { alerts: alerts };
        }
        const totalAlerts = await this.Alert.countDocuments(queryConditions);
        const totalPages = Math.ceil(totalAlerts / pageSize);
        const alerts = await this.Alert.find(queryConditions)
            .sort({ createdAt: sortOrder === 'asc' ? 1 : -1 })
            .select('industry country isActive createdAt')
            .skip((pageNumber - 1) * pageSize)
            .limit(pageSize)
            .lean();
        return {
            pageNumber,
            pageSize,
            totalPages,
            totalAlerts,
            alerts: alerts,
        };
    }
}
exports.AlertService = AlertService;
//# sourceMappingURL=alert.service.js.map