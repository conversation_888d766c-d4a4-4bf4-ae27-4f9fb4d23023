{"version": 3, "file": "logging.middleware.js", "sourceRoot": "", "sources": ["../../src/middlewares/logging.middleware.ts"], "names": [], "mappings": ";;;AAEA,kDAA+C;AAC/C,yDAA6D;AAEtD,MAAM,aAAa,GAAG,KAAK,EAAE,OAAY,EAAE,QAAkB,EAAE,IAAkB,EAAE,EAAE;IACxF,MAAM,MAAM,GAAG,IAAI,eAAM,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,aAAa,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC;IAE/G,MAAM,WAAW,GAAG;QAChB,MAAM,EAAE,OAAO,CAAC,MAAM;QACtB,GAAG,EAAE,OAAO,CAAC,WAAW;QACxB,EAAE,EAAE,OAAO,CAAC,EAAE;QACd,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,IAAI,EAAE,OAAO,CAAC,IAAI;QAClB,KAAK,EAAE,OAAO,CAAC,KAAK;QACpB,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,YAAY;KACrC,CAAC;IAEF,IAAI,YAAY,GAAG,EAAE,CAAC;IACtB,MAAM,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAElD,MAAM,IAAI,GAAG,MAAM,IAAA,4BAAgB,EAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAEtD,QAAQ,CAAC,IAAI,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE;QAC3B,YAAY,GAAG,IAAI,CAAC;QACpB,YAAY,CAAC,IAAI,CAAC,CAAC;IACvB,CAAC,CAAQ,CAAC;IAEV,QAAQ,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;QACvB,MAAM,YAAY,GAAG;YACjB,UAAU,EAAE,QAAQ,CAAC,UAAU;YAC/B,OAAO,EAAE,QAAQ,CAAC,UAAU,EAAE;YAC9B,IAAI,EAAE,YAAY;SACrB,CAAC;QACF,MAAM,UAAU,GAAG,IAAI,IAAI,QAAQ,WAAW,CAAC,EAAE,QAAQ,WAAW,CAAC,MAAM,QAAQ,WAAW,CAAC,GAAG,QAAQ,YAAY,CAAC,UAAU,GAAG,CAAC;QAErI,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC;IAEH,OAAO,IAAI,EAAE,CAAC;AAClB,CAAC,CAAC;AAnCW,QAAA,aAAa,iBAmCxB"}