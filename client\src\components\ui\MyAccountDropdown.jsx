import { useState } from "react";
import {
  Button,
  Menu,
  Menu<PERSON>tem,
  Fade,
  Dialog,
  Tooltip,
  IconButton,
  Avatar,
  Divider,
  ListItemIcon,
} from "@mui/material";
import useCurrentUser from "@/features/auth/hooks/currentUser.hooks";
import { useTranslation } from "react-i18next";
import { generateLocalizedSlug, stringAvatar } from "@/utils/functions";
import {
  authRoutes,
  baseUrlBackoffice,
  baseUrlFrontoffice,
  commonRoutes,
} from "@/helpers/routesList";
import { Role } from "../../utils/constants";
import Settings from "@mui/icons-material/Settings";
import Logout from "@mui/icons-material/Logout";

function MyAccountDropdown({ locale }) {
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);
  const { t } = useTranslation();
  const { user } = useCurrentUser();
  const [dialogOpen, setDialogOpen] = useState(false);
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };


  const myDashUrl = user?.roles?.includes(Role.CANDIDATE)
    ? `/${baseUrlFrontoffice.baseURL.route}`
    : `/${baseUrlBackoffice.baseURL.route}`;

  return (
    <div className="dropdown-menu">
      <IconButton
        onClick={handleClick}
        size="small"
        sx={{ ml: 2 }}
        aria-controls={open ? "account-menu" : undefined}
        aria-haspopup="true"
        aria-expanded={open ? "true" : undefined}
      >
        <Avatar
          sx={{ width: 45, height: 45, backgroundColor: "#dbe8f6" }}
          src={`${process.env.NEXT_PUBLIC_BASE_API_URL}/files/${user?.profilePicture}`}
        >
          {" "}
          <span
            style={{
              color: "#234791",
              fontFamily: "Proxima-Nova-SemiBold",
              textTransform: "uppercase",
            }}
          >
            {" "}
            {user?.firstName?.split(" ")[0][0].toUpperCase()}{" "}

          </span>
        </Avatar>{" "}
      </IconButton>
      <Menu
        anchorEl={anchorEl}
        id="account-menu"
        open={open}
        onClose={handleClose}
        onClick={handleClose}
        slotProps={{
          paper: {
            elevation: 0,
            sx: {
              overflow: "visible",
              filter: "drop-shadow(0px 2px 8px rgba(0,0,0,0.32))",
              mt: 1.5,
              "& .MuiAvatar-root": {
                width: 32,
                height: 32,
                ml: -0.5,
                mr: 1,
              },
              "&::before": {
                content: '""',
                display: "block",
                position: "absolute",
                top: 0,
                right: 14,
                width: 10,
                height: 10,
                bgcolor: "background.paper",
                transform: "translateY(-50%) rotate(45deg)",
                zIndex: 0,
              },
            },
          },
        }}
        transformOrigin={{ horizontal: "right", vertical: "top" }}
        anchorOrigin={{ horizontal: "right", vertical: "bottom" }}
      >
        <MenuItem
          sx={{ color: "#234791", fontFamily: "Proxima-Nova-Regular" }}
          onClick={() => {
            window.location.href = generateLocalizedSlug(
              locale,
              `${myDashUrl}`
            );
          }}
        >
          {t("global:dashboard")}
        </MenuItem>
        <MenuItem
          sx={{ color: "#234791", fontFamily: "Proxima-Nova-Regular" }}
          onClick={() => {
            window.location.href = generateLocalizedSlug(
              locale,
              `${myDashUrl}/${commonRoutes.myProfile.route}`
            );
          }}
        >
          {t("global:myProfile")}
        </MenuItem>
        <Divider />

        <MenuItem
          sx={{ color: "#234791", fontFamily: "Proxima-Nova-Regular" }}
          onClick={() => {
            window.location.href = generateLocalizedSlug(
              locale,
              `${myDashUrl}/${commonRoutes.settings.route}`
            );
          }}
        >
          <ListItemIcon>
            <Settings fontSize="small" />
          </ListItemIcon>
          {t("global:settings")}
        </MenuItem>
        <MenuItem
          sx={{ color: "#234791", fontFamily: "Proxima-Nova-Regular" }}
          onClick={() => {
            window.location.href = generateLocalizedSlug(
              locale,
              `/${authRoutes.logout.route}`
            );
          }}
        >
          <ListItemIcon>
            <Logout fontSize="small" />
          </ListItemIcon>
          {t("global:logout")}
        </MenuItem>
      </Menu>
      {/* <Button
        className={"dropdown-toggle"}
        id="fade-button"
        aria-controls={open ? "fade-menu" : undefined}
        aria-haspopup="true"
        aria-expanded={open ? "true" : undefined}
        onClick={handleClick}
      >
        <Link
          className={"dropdown-toggle-link my-profile-img"}
          locale={locale === "en" ? "en" : "fr"}
          href={generateLocalizedSlug(locale, `${myDashUrl}/${commonRoutes.myProfile.route}`)}
        >
          {user?.profilePicture && user?.profilePicture?.trim() !== "" ? (
            <img
              src={`${process.env.NEXT_PUBLIC_BASE_API_URL}/files/${user?.profilePicture}`}
              width={80}
              height={80}
              alt="profile-picture"
              loading="lazy"
            />
          ) : (
            <SvgUserIcon />
          )}
        </Link>
        {open ? <SvgArrowDown /> : <SvgArrowUp />}
      </Button>
      <Menu
        id="fade-menu"
        MenuListProps={{
          "aria-labelledby": "fade-button",
        }}
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        TransitionComponent={Fade}
        disableScrollLock={true}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
      >
        <MenuItem className={`sub-menu dropdown-item`} key={"index"}>
          <Link
            locale={locale === "en" ? "en" : "fr"}
            href={generateLocalizedSlug(locale, `${myDashUrl}/${commonRoutes.myProfile.route}`)}
            className={"dropdown-item-link"}
          >
            <SvgUserIcon /> profile
          </Link>
        </MenuItem>

        <MenuItem className={`sub-menu dropdown-item`} key={"index"}>
          <Link
            locale={locale === "en" ? "en" : "fr"}
            href={generateLocalizedSlug(locale, `/${authRoutes.logout.route}`)}
            className={"dropdown-item-link"}
          >
            <SvgLogoutIcon />
            {t(authRoutes.logout.i18nName)}
          </Link>
        </MenuItem>
      </Menu>
      <Dialog open={dialogOpen} onClose={handleCloseDialog}>
        <NotificationComponent onClose={handleCloseDialog} />
      </Dialog> */}
    </div>
  );
}

export default MyAccountDropdown;
