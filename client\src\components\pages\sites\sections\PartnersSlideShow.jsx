import { Container } from "@mui/material";
import useInView from "@/features/images/hooks/view.hooks";

const PartnerSlide = ({ item, t }) => {
  const { isInView, elementRef } = useInView();

  return (
    <div className="embla__slide" ref={elementRef}>
      <Container
        className="slide__container custom-max-width"
        sx={{
          backgroundImage: isInView
            ? `url(${item.logo.src})`
            : "linear-gradient(to bottom, rgba(35, 71, 145, 0), rgba(35, 71, 145, 0.6))",
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
          transition: "background-image 0.5s ease-in-out",
        }}
      >
        {item.label && (
          <img
            width={0}
            height={0}
            alt={`${t("aboutUs:partners:strategicPartner")}${item.label}`}
            title={`${t("aboutUs:partners:collaborationAvec")}${item.label}`}
            src={item.logo.src}
            style={{ display: "none" }}
            loading="lazy"
          />
        )}
      </Container>
    </div>
  );
};

export default PartnerSlide;
