import banner from "@/assets/images/Dubai/Pentabell-Dubai.webp";
import BannerComponents from "@/components/pages/sites/sections/BannerComponents";
import OurPartners from "@/components/pages/sites/sections/OurPartners";
import ResponsiveRowTitleText from "@/components/ui/ResponsiveRowTitleText";
import GlobalHRServicesSection from "@/components/pages/sites/sections/GlobalHRServicesSection";
import serviceimgS1 from "@/assets/images/services/service1.png";
import r1 from "@/assets/images/services/recrutement1.png";
import r2 from "@/assets/images/services/recrutement2.png";
import r3 from "@/assets/images/services/recrutement3.png";
import r4 from "@/assets/images/services/recrutement4.png";
import TunisiaOfficePageForm from "@/features/forms/components/TunisiaOfficePageForm";
import OfficeLocationMapDubai from "@/components/pages/sites/offices-sections/dubai/OfficeLocationMapDubai";
import initTranslations from "@/app/i18n";
import OfficeInfoUAE from "@/components/pages/sites/offices-sections/dubai/OfficeInfoUAE";
import BusinessInUAE from "@/components/pages/sites/offices-sections/dubai/BusinessInUAE";
import EORServicesUAE from "@/components/pages/sites/offices-sections/dubai/EORServicesUAE";
import ComplexityControlSectionPanel from "@/components/pages/sites/offices-sections/ComplexityControlSectionPanel";
import UAELaborData from "@/components/pages/sites/offices-sections/labor-laws/labor-data/UAELaborData";
import { websiteRoutesList } from "@/helpers/routesList";
import { axiosGetJsonSSR } from "@/config/axios";
export async function generateMetadata({ params: { locale } }) {
  const canonicalUrl = `https://www.pentabell.com/${locale !== "en" ? `${locale}/` : ""
    }recruitment-staffing-agency-dubai/`;

  const languages = {
    fr: `https://www.pentabell.com/fr/recruitment-staffing-agency-dubai/`,
    en: `https://www.pentabell.com/recruitment-staffing-agency-dubai/`,
    "x-default": `https://www.pentabell.com/recruitment-staffing-agency-dubai/`,
  };
  const { t } = await initTranslations(locale, [
    "Tunisia",
    "servicesByCountry",
  ]);

  try {
    const res = await axiosGetJsonSSR.get(
      `${process.env.NEXT_PUBLIC_BASE_API_URL_SSR}/seoTags/${locale}/recruitment-staffing-agency-dubai`
    );
    const seoTags = res?.data?.status === 200;
    if (seoTags) {
      return {
        title: res?.data?.data?.versions[0]?.metaTitle,
        description: res?.data?.data?.versions[0]?.metaDescription,
        robots: res?.data?.data?.robotMeta,
        alternates: {
          canonical: canonicalUrl,
          languages,
        },
      };
    }
  } catch (error) {
    console.error("Error fetching SEO tags:", error);
  }

  return {
    title: t("servicesByCountry:dubai:metaTitle"),
    description: t("servicesByCountry:dubai:metaDescription"),
    alternates: {
      canonical: canonicalUrl,
      languages,
    },
    robots: "follow, index, max-snippet:-1, max-image-preview:large",
  };
}
async function hiringEmployeesDubaiGuide({ params: { locale } }) {
  const { t } = await initTranslations(locale, [
    "Tunisia",
    "africa",
    "middleeast",
    "dubai",
  ]);
  const SERVICES = [
    {
      id: "s1",
      title: t("Tunisia:services:dataS1:title"),
      description: t("Tunisia:services:dataS1:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.payrollServices.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: serviceimgS1,
      altImg: t("Tunisia:services:dataS1:altImg"),
    },
    {
      id: "s2",
      title: t("Tunisia:services:dataS2:title"),
      description: t("Tunisia:services:dataS2:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.consultingServices.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r4,
      altImg: t("Tunisia:services:dataS2:altImg"),
      altImg: t("Tunisia:services:dataS2:altImg"),
    },
    {
      id: "s3",
      title: t("Tunisia:services:dataS3:title"),
      description: t("Tunisia:services:dataS3:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.technicalAssistance.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r3,
      altImg: t("Tunisia:services:dataS3:altImg"),
      altImg: t("Tunisia:services:dataS3:altImg"),
    },
    {
      id: "s4",
      title: t("Tunisia:services:dataS4:title"),
      description: t("Tunisia:services:dataS4:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.aiSourcing.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r2,
      altImg: t("Tunisia:services:dataS4:altImg"),
      altImg: t("Tunisia:services:dataS4:altImg"),
    },
    {
      id: "s5",
      title: t("Tunisia:services:dataS5:title"),
      description: t("Tunisia:services:dataS5:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.directHiring.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r1,
      altImg: t("Tunisia:services:dataS5:altImg"),
      altImg: t("Tunisia:services:dataS5:altImg"),
    },
  ];
  return (
    <div>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Organization",
            name: t("contactUs:bureux:contacts:uae"),
            address: {
              "@type": "PostalAddress",
              streetAddress: "HDS Business Center Office 306 JLT",
              addressLocality: "Dubai",
              addressCountry: "AE",
            },
            telephone: "+971 4 4876 0672",
            email: "<EMAIL>",
            url:
              locale === "en"
                ? "https://www.pentabell.com/recruitment-staffing-agency-dubai/"
                : `https://www.pentabell.com/${locale}/recruitment-staffing-agency-dubai/`,
          }),
        }}
      />
      <BannerComponents
        title={t("dubai:title")}
        description={t("dubai:description")}
        bannerImg={banner}
        height={"100vh"}
        altImg={t("dubai:altImg")}
      />
      <OurPartners disableTxt={true} />
      <ResponsiveRowTitleText
        title={t("dubai:intro:title")}
        paragraph={t("dubai:intro:description")}
      />
      <OfficeInfoUAE t={t} />
      <BusinessInUAE t={t} />
      <EORServicesUAE t={t} />
      <OfficeLocationMapDubai t={t} />
      <ComplexityControlSectionPanel t={t} />

      <GlobalHRServicesSection
        title={t("dubai:services:title")}
        SERVICES={SERVICES}
        defaultImage={serviceimgS1}
      />
      <UAELaborData />
      <TunisiaOfficePageForm country={"Dubai"} defaultCountryPhone="ae" />
    </div>
  );
}

export default hiringEmployeesDubaiGuide;
