"use client";
import { Container, Grid } from "@mui/material";
import frenchSaudi from "../../assets/images/events/frenchSaudi.png";
import pentabellFrance from "../../assets/images/events/pentabellFrance.png";
import leap from "../../assets/images/events/leap.png";
import ProcessHtml from "./ProcessHtml";
import EventsCard from "./EventsCard";
import image1 from "@/assets/images/Libyaevent/image1.png";
import image2 from "@/assets/images/Libyaevent/image2.png";
import image3 from "@/assets/images/Libyaevent/image3.png";
import image4 from "@/assets/images/Libyaevent/image4.png";
import image5 from "@/assets/images/Libyaevent/image5.png";
import image6 from "@/assets/images/Libyaevent/image6.png";
import image7 from "@/assets/images/Libyaevent/image7.png";
import imagemobile1 from "@/assets/images/Libyaevent/imagemobilelibya1.png";
import imagemobile2 from "@/assets/images/Libyaevent/imagemobilelibya2.png";
import imagemobile3 from "@/assets/images/Libyaevent/imagemobilelibya3.png";
import imagemobile4 from "@/assets/images/Libyaevent/imagemobilelibya4.png";
import imagemobile5 from "@/assets/images/Libyaevent/imagelibya5.png";
import imagemobile6 from "@/assets/images/Libyaevent/imagemobilelibya6.png";
import imagemobile7 from "@/assets/images/Libyaevent/imagemobilelibya7.png";
import EmblaCarousel from "@/components/embla_slider/EmblaCarousel";
import { API_URLS } from "@/utils/urls";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { axiosGetJson } from "@/config/axios";
import InfoSection from "@/components/ui/InfoSection";

export default function EventLibya({ language, event }) {
  const { t } = useTranslation();
  const [events, setEvents] = useState();
  const fetchEvents = async () => {
    try {
      const response = await axiosGetJson.get(API_URLS.events, {
        params: {
          visibility: "Public",
          pageNumber: 1,
          pageSize: 4,
          language: language,
        },
      });
      let { Events } = response.data;

      const eventsFiltered = Events?.filter(
        (currentEvent) => currentEvent._id !== event?._id
      ).slice(0, 3);
      setEvents(eventsFiltered);
    } catch (error) {
      console.error("Failed to fetch events:", error);
    }
  };

  useEffect(() => {
    fetchEvents();
  }, [event, language]);

  const OPTIONS = {};
  const SLIDES = [
    image1,
    image2,
    image3,
    image4,
    image5,
    image6,
    image7,

  ];
  const SlidesMobile = [
    imagemobile1,
    imagemobile2,
    imagemobile3,
    imagemobile4,
    imagemobile5,
    imagemobile6,
    imagemobile7,
  ]

  const decarbonizationSection = {
    sector: t("eventDetailsLibya:sectorValue"),
    country: t("eventDetailsLibya:locationValue"),
    countryConcerned: t("eventDetailsLibya:countryConcernedValue"),
    organiser: t("eventDetailsLibya:organiserValue"),
  };
  let infoSection;
  if (event) {
    infoSection = {
      sector: event?.versions[0]?.sector,
      country: t(`country:${event?.country?.replace(/\s+/g, "")}`),
      countryConcerned: event?.versions[0]?.countryConcerned,
      organiser: event?.versions[0]?.organiser,
    };
  }
  const eventData = [
    {
      title: t("event:events.event1.title"),
      eventDate: t("event:events.event1.eventDate"),
      postingDate: t("event:events.event1.postingDate"),
      exactPlace: t("event:events.event1.exactPlace"),
      link: "franco-saudi-decarbonization-days",
      type: "events",
      image: frenchSaudi,
    },
    {
      title: t("event:events.event11.title"),
      eventDate: t("event:events.event11.eventDate"),
      postingDate: t("event:events.event11.postingDate"),
      exactPlace: t("event:events.event11.exactPlace"),
      type: "events",
      link: `leap-tech-conference-2025-riyadh`,
      image: leap,
    },
    {
      title: t("event:events.event2.title"),
      eventDate: t("event:events.event2.eventDate"),
      postingDate: t("event:events.event2.postingDate"),
      exactPlace: t("event:events.event2.exactPlace"),
      link:
        language === "en"
          ? `pentabell-salon-sme-and-european-microwave-week`
          : "pentabell-salon-sme-and-european-microwave-week",
      type: "blog",
      image: pentabellFrance,
    },
  ];
  return (
    <div id="event-page">
      {event ? (
        <div id="event-detail">
          <div className="custom-max-width">
            <InfoSection t={t} infos={infoSection} />{" "}
            <ProcessHtml htmlString={event?.versions[0]?.content} />
            <Grid
              className="more-events-section"
              container
              rowSpacing={0}
              columnSpacing={3}
            >
              {" "}
              {events?.length > 0 &&
                events?.map((event, index) => (
                  <EventsCard
                    key={index}
                    eventData={event}
                    language={language}
                    isEvent={true}
                  />
                ))}{" "}
            </Grid>
          </div>{" "}
        </div>
      ) : (
        <div id="event-detail">
          <Container className="custom-max-width">
            <InfoSection t={t} infos={decarbonizationSection} />
            <div className="details">
              <p className="heading-h1">
                {t("eventDetailsLibya:eventProgram:eventProgram")}
              </p>
              <p className="text">
                {t("eventDetailsLibya:eventProgram:data11")}
              </p>
              <p className="text">
                {t("eventDetailsLibya:eventProgram:data12")}
              </p>


              <p className="heading-h1">
                {t("eventDetailsLibya:aboutEvent:aboutEvent")}
              </p>

              <p className="text">
                {t("eventDetailsLibya:aboutEvent:description")}
              </p>

              <EmblaCarousel slides={SLIDES} options={OPTIONS} slidesMobile={SlidesMobile} />
              <p className="heading-h1">{t("eventDetailsLibya:moreEvents")}</p>
            </div>
            <Grid
              className="more-events-section"
              container
              rowSpacing={0}
              columnSpacing={3}
            >
              {eventData?.map((event, index) => (
                <EventsCard
                  key={index}
                  eventData={event}
                  language={language}
                  isEvent={true}
                />
              ))}
            </Grid>
          </Container>
        </div>
      )}
    </div>
  );
}
