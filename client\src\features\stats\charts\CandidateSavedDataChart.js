
import React from 'react';
import { useTranslation } from "react-i18next";
import StatusCard from "@/components/ui/charts/StatusCard";
import job from "@/assets/images/applicationsdashboard.png";

const CandidateSavedDataChart = ({ totalOpp, totalArticles }) => {
  const { t } = useTranslation();

  return (
    <div className="stats-container">
      <StatusCard
        iconSrc={job.src}
        iconAlt="saved-jobs"
        title={t('statisticsApp:savedJobs')}
        value={totalOpp}
        cardClassName="stats-job"
      />
      <StatusCard
        iconSrc={job.src}
        iconAlt="saved-articles"
        title={t('statisticsApp:savedArticles')}
        value={totalArticles}
        cardClassName="stats-views"
      />
    </div>
  );
};

export default CandidateSavedDataChart;
