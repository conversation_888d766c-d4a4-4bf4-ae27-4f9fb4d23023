"use client";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Button, Grid, Typography } from "@mui/material";
import { useGetCandidaturesById } from "../hooks/Candidatures.hooks";
import { formatDate, formatResumeName } from "@/utils/functions";
import { API_URLS } from "@/utils/urls";

const Detailscandidatures = ({ candidatureId }) => {
  const { t, i18n } = useTranslation();
  const { data, isLoading, isError, error } =
    useGetCandidaturesById(candidatureId);
  const [IdCandidat, setCandidateId] = useState(null);
  useEffect(() => {
    if (data && data.candidate) {
      setCandidateId(data.candidate._id);
    }
  }, [data]);

  if (isLoading) {
    return <Typography>Loading...</Typography>;
  }

  if (isError) {
    return <Typography color="error">Error: {error.message}</Typography>;
  }

  if (!data) {
    return <Typography>No candidature data found.</Typography>;
  }
  const { candidate, opportunity, resume, note, status, applicationDate } =
    data;

  const handleCandidateRedirect = () => {
    if (IdCandidat) {
      const candidateDetailsUrl = `/backoffice/applications/${IdCandidat}/`;

      window.open(candidateDetailsUrl, "_blank");
    } else {
      console.error("Candidate ID is missing or invalid");
    }
  };

  return (
    <div id="container" className="recent-application-pentabell">
      <div className="main-content">
        <Typography variant="h4" className="heading-h2 semi-bold">
          {t("application:candidatures")} :{" "}
          {opportunity?.versions[i18n.language]?.title}
        </Typography>

        <Grid container spacing={2} sx={{ marginTop: "10px", padding: "20px" }}>
          <Grid item xs={12} sm={6}>
            <Typography variant="subtitle1" className="label-form">
              {t("application:fullName")}
            </Typography>
            <Typography variant="body1" color="textSecondary">
              {candidate?.user?.firstName
                ? `${candidate?.user?.firstName} ${candidate?.user?.lastName}`
                : `N/A`}
            </Typography>
          </Grid>
          <Grid item>
            <Button
              variant="outlined"
              size="small"
              onClick={handleCandidateRedirect}
            >
              {t("application:viewDetailsCandidate")}
            </Button>
          </Grid>
          <Grid item xs={12} sm={6}>
            <Typography variant="subtitle1" className="label-form">
              {t("candidate:email")}
            </Typography>
            <Typography variant="body1" color="textSecondary">
              {candidate?.user?.email || "N/A"}
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6}>
            <Typography variant="subtitle1" className="label-form">
              {t("application:phone")}
            </Typography>
            <Typography variant="body1" color="textSecondary">
              {candidate?.user?.phone || "N/A"}
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6}>
            <Typography variant="subtitle1" className="label-form">
              {t("application:Country")}
            </Typography>
            <Typography variant="body1" color="textSecondary">
              {candidate?.user?.country || "N/A"}
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6}>
            <Typography variant="subtitle1" className="label-form">
              {t("application:jobTitle")}
            </Typography>
            <Typography variant="body1" color="textSecondary">
              {candidate?.user?.jobTitle || "N/A"}
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6}>
            <Typography variant="subtitle1" className="label-form">
              {t("application:Status")}
            </Typography>
            <Typography variant="body1" color="textSecondary">
              {status || "N/A"}
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6}>
            <Typography variant="subtitle1" className="label-form">
              {t("candidate:applicationDate")}
            </Typography>
            <Typography variant="body1" color="textSecondary">
              {applicationDate ? formatDate(applicationDate) : "N/A"}
            </Typography>
          </Grid>{" "}
          <Grid item xs={12} sm={6}>
            <Typography variant="subtitle1" className="label-form">
              {t("application:job")}
            </Typography>
            <Typography variant="body1" color="textSecondary">
              {opportunity?.versions[i18n.language]?.title || "N/A"}
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6}>
            <Typography variant="subtitle1" className="label-form">
              {t("application:Resume")}
            </Typography>
            <Typography variant="body1" color="textSecondary">
              <Button
                onClick={() =>
                  window.open(
                    `${process.env.NEXT_PUBLIC_BASE_API_URL}${API_URLS.files}/${resume}`,
                    "_blank"
                  )
                }
              >
                {formatResumeName(
                  resume,
                  `${candidate?.user?.firstName} ${candidate?.user?.lastName}`
                )}
              </Button>
            </Typography>
          </Grid>
          <Grid item xs={12} sm={12} key={"message"}>
            <Typography variant="subtitle1" className="label-form">
              {t("application:Note")}
            </Typography>
            <Typography variant="body1" color="textSecondary">
              {note || "N/A"}
            </Typography>
          </Grid>
        </Grid>
      </div>
    </div>
  );
};

export default Detailscandidatures;
