import { useEffect, useState } from "react";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Card,
  CardContent,
  Grid,
  TextField,
} from "@mui/material";

import CustomButton from "@/components/ui/CustomButton";
import SvgexpandIcon from "@/assets/images/icons/arrowUp.svg";
import SvgRefreshIcon from "@/assets/images/icons/refreshIcon.svg";
import CustomPieChart from "@/components/ui/charts/CustomPieChart";
import { useGetApplicationsStat } from "../stats.hooks";

export default function ApplicationsByStatus({ t }) {
  const [dateFromApplication, setDateFromApplication] = useState(() => {
    const fourMonthsBefore = new Date();
    fourMonthsBefore.setMonth(fourMonthsBefore.getMonth() - 4);
    fourMonthsBefore.setDate(1);
    return fourMonthsBefore.toISOString().split("T")[0];
  });
  const [dateToApplication, setDateToApplication] = useState(() => {
    const today = new Date();
    return today.toISOString().split("T")[0];
  });
  const [searchApplication, setSearchApplication] = useState(false);

  const getDataPieApplications = useGetApplicationsStat({
    dateFrom: dateFromApplication,
    dateTo: dateToApplication,
    barChart: null,
  });

  const resetSearchApplication = () => {
    setDateToApplication(() => {
      const today = new Date();
      return today.toISOString().split("T")[0];
    });
    setDateFromApplication("2024-09-01");

    setSearchApplication(!searchApplication);
  };

  const pieChart = {
    title: t("statsDash:applicationsByStatus"),
    dataset: getDataPieApplications?.data?.map((app) => ({
      label: app.status,
      value: app.totalApplications,
    })),
    colors: ["#E97611", "#018055", "#D73232"],
  };

  useEffect(() => {
    getDataPieApplications.refetch();
  }, [searchApplication]);

  return (
    <Card className="card">
      <CardContent>
        <p className="heading-h3" gutterBottom>
          {pieChart.title}
        </p>
        <Accordion elevation={0} disableGutters={true}>
          <AccordionSummary
            aria-controls="panel1bh-content"
            id="panel1bh-header"
            className="svg-accordion"
            expandIcon={<SvgexpandIcon />}
          >
            <h3 className="label-pentabell">{t("statsDash:filters")}</h3>
          </AccordionSummary>
          <AccordionDetails elevation={0}>
            <Grid container className="chart-grid" spacing={1}>
              <Grid item xs={12} sm={6}>
                <TextField
                  label={t("statsDash:fromDate")}
                  type="date"
                  value={dateFromApplication}
                  onChange={(e) => setDateFromApplication(e.target.value)}
                  fullWidth
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label={t("statsDash:toDate")}
                  type="date"
                  value={dateToApplication}
                  onChange={(e) => setDateToApplication(e.target.value)}
                  fullWidth
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>

              <Grid item xs={3} sm={1} md={4} className="btns-filter dashboard">
                <CustomButton
                  icon={<SvgRefreshIcon />}
                  className={"btn btn-outlined btn-refresh full-width"}
                  onClick={resetSearchApplication}
                />
              </Grid>
              <Grid item xs={11} sm={11} md={8}>
                {" "}
                <CustomButton
                  text={t("statsDash:filter")}
                  onClick={() => {
                    setSearchApplication(!searchApplication);
                  }}
                  className={"btn btn-outlined btn-filter-stat full-width"}
                />
              </Grid>
            </Grid>
          </AccordionDetails>
        </Accordion>
        <div className="chart-wrapper">
          {" "}
          <CustomPieChart donuts={true} chart={pieChart} />{" "}
          {pieChart.dataset?.some((item) => item["value"] > 0) && (
            <div className="labelstats-wrapper">
              <div className="label-wrapper">
                <span className="accepted-dot" />
                <span className="label-chart">{t("statsDash:accepted")}</span>
              </div>
              <div className="label-wrapper">
                <span className="rejected-dot" />
                <span className="label-chart">{t("statsDash:rejected")}</span>
              </div>
              <div className="label-wrapper">
                <span className="pending-dot" />
                <span className="label-chart">{t("statsDash:pending")}</span>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
