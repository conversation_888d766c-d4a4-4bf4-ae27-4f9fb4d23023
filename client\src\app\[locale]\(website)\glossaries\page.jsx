import banner from "@/assets/images/website/banner/Pentabell-joinUs.webp";
import GlossaryBanner from "@/components/pages/sites/sections/GlossaryBanner.jsx";
import initTranslations from "@/app/i18n";
import { axiosGetJsonSSR } from "@/config/axios";
import GlossaryListWebsite from "@/features/glossary/component/GlossariesListWebsite";

export async function generateMetadata({ params: { locale } }) {
  const canonicalUrl = `https://www.pentabell.com/${
    locale !== "en" ? `${locale}/` : ""
  }glossaries/`;

  const languages = {
    fr: `https://www.pentabell.com/fr/glossaries/`,
    en: `https://www.pentabell.com/glossaries/`,
    "x-default": `https://www.pentabell.com/glossaries/`,
  };

  const { t } = await initTranslations(locale, ["joinUs"]);

  try {
    const res = await axiosGetJsonSSR.get(
      `${process.env.NEXT_PUBLIC_BASE_API_URL_SSR}/seoTags/${locale}/glossaries`
    );
    const seoTags = res?.data?.status === 200;
    if (seoTags) {
      return {
        title: res?.data?.data?.versions[0]?.metaTitle,
        description: res?.data?.data?.versions[0]?.metaDescription,
        robots: res?.data?.data?.robotMeta,
        alternates: {
          canonical: canonicalUrl,
          languages,
        },
      };
    }
  } catch (error) {
    console.error("Error fetching SEO tags:", error);
  }
  return {
    title: t("joinUs:metaTitle"),
    description: t("joinUs:metaDescription"),
    alternates: {
      canonical: canonicalUrl,
      languages,
    },
    robots: "follow, index, max-snippet:-1, max-image-preview:large",
  };
}

async function page({ searchParams, params: { locale } }) {
  const { t } = await initTranslations(locale, ["glossary", "global"]);

  const searchWord = searchParams?.word
    ? String(searchParams.word).trim().slice(0, 100)
    : "";

  let glossaries = {};
  let letters = [];
  let error = null;
  let isSearchResult = false;

  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    const res = await axiosGetJsonSSR.get("/glossaries", {
      params: {
        dashboard: "false",
        language: locale || "en",
        word: searchWord,
      },
      signal: controller.signal,
      timeout: 10000,
    });

    clearTimeout(timeoutId);

    if (res?.data && typeof res.data === "object") {
      glossaries = res.data;
      letters = Object.keys(glossaries).filter(
        (key) => Array.isArray(glossaries[key]) && glossaries[key].length > 0
      );
      isSearchResult = Boolean(searchWord);
    } else {
      throw new Error("Invalid response format");
    }
  } catch (err) {
    console.error("Error fetching glossaries:", err);
    error = err.message || "Failed to load glossaries";

    // Set empty state
    glossaries = {};
    letters = [];
  }

  const hasContent = letters.length > 0;
  const isEmpty = !hasContent && !error;
  const isEmptySearch = !hasContent && isSearchResult;

  const translations = {
    emptyState: {
      title: t("glossary:emptyState:title") || "No Glossary Terms Available",
      description:
        t("glossary:emptyState:description") ||
        "We're currently building our comprehensive glossary. Check back soon for detailed definitions and explanations of industry terms.",
      exploreButton:
        t("glossary:emptyState:exploreButton") || "Explore Our Blogs",
    },
    searchEmpty: {
      title: t("glossary:searchEmpty:title") || "No Results Found",
      description:
        t("glossary:searchEmpty:description") ||
        "No glossary terms found for your search. Try searching with different keywords or browse all terms.",
      clearButton: t("glossary:searchEmpty:clearButton") || "Clear Search",
      browseButton:
        t("glossary:searchEmpty:browseButton") || "Browse All Terms",
    },
    error: {
      title: t("glossary:error:title") || "Unable to Load Glossary",
      description:
        t("glossary:error:description") ||
        "We're experiencing technical difficulties loading the glossary. Please try again later.",
      retryButton: t("glossary:error:retryButton") || "Try Again",
    },
    searchResults: {
      title: t("glossary:searchResults:title") || "Search Results",
      count: t("glossary:searchResults:count") || "terms found",
    },
    showMore: t("glossary:showMore") || "Show more",
    showLess: t("glossary:showLess") || "Show less",
    backToTop: t("glossary:backToTop") || "Back to Top",
  };

  return (
    <>
      <GlossaryBanner
        altImg={"Join Us, Find Careers and Jobs at Pentabell"}
        bannerImg={banner}
        height={"100vh"}
        letters={letters}
        searchWord={searchWord}
        hasContent={hasContent}
      />
      <GlossaryListWebsite
        glossaries={glossaries}
        locale={locale}
        error={error}
        isEmpty={isEmpty}
        isEmptySearch={isEmptySearch}
        searchWord={searchWord}
        translations={translations}
      />
    </>
  );
}

export default page;
