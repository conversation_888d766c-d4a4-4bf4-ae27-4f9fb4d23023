"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createManyCandidatesSchema = exports.createCandidateSchema = void 0;
const joi_1 = __importDefault(require("joi"));
const educationSchema = joi_1.default.object({
    degree: joi_1.default.string().required(),
    endDate: joi_1.default.string(),
    startDate: joi_1.default.string().required(),
    university: joi_1.default.string().required(),
});
const experienceSchema = joi_1.default.object({
    company: joi_1.default.string(),
    contractType: joi_1.default.string(),
    duration: joi_1.default.string(),
    endDate: joi_1.default.string(),
    location: joi_1.default.string(),
    startDate: joi_1.default.string(),
    title: joi_1.default.string().required(),
});
const certificationSchema = joi_1.default.object({
    academy: joi_1.default.string(),
    endDate: joi_1.default.string(),
    startDate: joi_1.default.string(),
    title: joi_1.default.string(),
});
const createCandidateSchema = joi_1.default.object({
    certifications: joi_1.default.array().items(certificationSchema),
    dateCreation: joi_1.default.string(),
    dateUpdate: joi_1.default.string(),
    educations: joi_1.default.array().items(educationSchema).required(),
    emails: joi_1.default.array().items(joi_1.default.string()),
    experiences: joi_1.default.array().items(experienceSchema).required(),
    experiencesCompany: joi_1.default.array().items(joi_1.default.string()),
    fileName: joi_1.default.string(),
    industry: joi_1.default.string(),
    industries: joi_1.default.array().items(joi_1.default.string()),
    industriesBinary: joi_1.default.string(),
    isArchived: joi_1.default.boolean(),
    isPentabellMember: joi_1.default.boolean(),
    isValidXp: joi_1.default.boolean(),
    jobTitleAng: joi_1.default.string(),
    jobTitleFr: joi_1.default.string(),
    profilePicture: joi_1.default.string(),
    linkedinImage: joi_1.default.string(),
    linkedinUrl: joi_1.default.string(),
    location: joi_1.default.string(),
    monthsOfExperiences: joi_1.default.number(),
    name: joi_1.default.string(),
    nationalities: joi_1.default.array().items(joi_1.default.string()),
    numberOfCertifications: joi_1.default.number(),
    numberOfEducations: joi_1.default.number(),
    numberOfExperiences: joi_1.default.number(),
    numberOfSkills: joi_1.default.number(),
    phones: joi_1.default.array().items(joi_1.default.string()),
    skills: joi_1.default.array().items(joi_1.default.string()).required(),
    status: joi_1.default.string(),
    webSiteUrl: joi_1.default.string(),
    summary: joi_1.default.string(),
});
exports.createCandidateSchema = createCandidateSchema;
const createManyCandidatesSchema = joi_1.default.array().items(createCandidateSchema);
exports.createManyCandidatesSchema = createManyCandidatesSchema;
//# sourceMappingURL=candidat.validation.js.map