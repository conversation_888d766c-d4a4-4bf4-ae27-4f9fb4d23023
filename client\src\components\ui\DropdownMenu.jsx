import { useRef, useState } from "react";
import { <PERSON>ton, <PERSON>u, MenuItem, Fade } from "@mui/material";
import { usePathname } from "next/navigation";
import { useTranslation } from "react-i18next";

import SvgArrowDown from "../../assets/images/icons/arrowDown.svg";
import SvgArrowUp from "../../assets/images/icons/arrowUp.svg";
import { generateLocalizedSlug } from "@/utils/functions";

const DropdownMenu = ({
  buttonLabel,
  buttonHref,
  menuItems,
  subMenu,
  locale,
  withFlag,
  selectedFlag,
}) => {
  const pathname = usePathname();
  const { t } = useTranslation();
  const [anchorEl, setAnchorEl] = useState(null);

  const open = Boolean(anchorEl);
  const timeoutRef = useRef(null);

  const isDetailBlogPath = () =>
    pathname.includes("/blog/") &&
    !pathname.includes("/blog/category/") &&
    pathname !== "/blog/" &&
    pathname !== "/fr/blog/";

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleClick2 = (event, item) => {
    event.stopPropagation();
    if (item.onClick) {
      item.onClick();
    } else if (item.subItems == undefined) handleClose();
  };

  const handleMouseEnter = (event) => {
    clearTimeout(timeoutRef.current);
    setAnchorEl(event.currentTarget);
  };

  const handleMouseLeave = () => {
    timeoutRef.current = setTimeout(() => {
      setAnchorEl(null);
    }, 150);
  };

  return (
    <div
      className={subMenu ? `sub-dropdown-menu` : `dropdown-menu`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      style={{ display: "inline-block", position: "relative" }}
    >
      <Button
        className={
          pathname.includes(buttonHref) && buttonHref !== ""
            ? "navbar-link dropdown-toggle active"
            : isDetailBlogPath()
            ? "navbar-link dropdown-toggle whiteBg"
            : "navbar-link dropdown-toggle"
        }
        id="fade-button"
        aria-controls={open ? "fade-menu" : undefined}
        aria-haspopup="true"
        aria-expanded={open ? "true" : undefined}
        onClick={handleClick}
      >
        <Button
          className={
            pathname.includes(buttonHref) && buttonHref !== ""
              ? "dropdown-toggle-link active"
              : isDetailBlogPath()
              ? "dropdown-toggle-link whiteBg"
              : "dropdown-toggle-link"
          }
          href={
            buttonLabel?.toLowerCase() === "resources" ||
            buttonLabel?.toLowerCase() === "ressources"
              ? undefined
              : buttonHref
              ? generateLocalizedSlug(locale, buttonHref).replace("/en/", "/")
              : pathname.replace("/en/", "/")
          }
          locale={locale === "en" ? "en" : "fr"}
          onClick={(e) => {
            if (
              buttonLabel?.toLowerCase() === "resources" ||
              buttonLabel?.toLowerCase() === "ressources"
            ) {
              e.preventDefault();
              e.stopPropagation();
            }
          }}
          sx={{
            ...(buttonLabel?.toLowerCase() === "resources" ||
            buttonLabel?.toLowerCase() === "ressources"
              ? {
                  cursor: "default",
                  textDecoration: "none",
                  "&:hover": {
                    textDecoration: "none",
                  },
                }
              : {}),
          }}
        >
          {withFlag ? (
            <img
              className="flag-lang"
              src={selectedFlag.src}
              width={26}
              height={22}
              disabled
              loading="lazy"
            />
          ) : (
            buttonLabel
          )}
        </Button>
        {open ? <SvgArrowDown /> : <SvgArrowUp />}
      </Button>

      <Menu
        id="fade-menu"
        MenuListProps={{
          "aria-labelledby": "fade-button",
          autoFocusItem: false,
          disableAutoFocusItem: true,
        }}
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        TransitionComponent={Fade}
        disableScrollLock={true}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: subMenu ? "right" : "left",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: subMenu ? "left" : "left",
        }}
        slotProps={{
          paper: {
            onMouseEnter: () => clearTimeout(timeoutRef.current),
            onMouseLeave: handleMouseLeave,
            sx: {
              pointerEvents: "auto",
            },
          },
        }}
      >
        {menuItems.map((item, index) => (
          <MenuItem
            className={
              item.subItems ? `sub-menu dropdown-item` : `dropdown-item`
            }
            key={index}
            onClick={(e) => handleClick2(e, item)}
            onMouseEnter={() => clearTimeout(timeoutRef.current)}
            onMouseLeave={item.subItems ? undefined : handleMouseLeave}
            sx={{
              "&:hover": {
                backgroundColor: item.subItems
                  ? "transparent !important"
                  : "rgba(0, 0, 0, 0.04)",
              },
              "&.Mui-focusVisible": {
                backgroundColor: "transparent !important",
              },
              "&.Mui-selected": {
                backgroundColor: "transparent !important",
              },
              "&:focus": {
                backgroundColor: "transparent !important",
              },
              "&:first-of-type": {
                backgroundColor: "transparent !important",
              },
            }}
          >
            {item.subItems ? (
              <DropdownMenu
                buttonLabel={item?.i18nName ? t(item?.i18nName) : item.name}
                buttonHref={item.route}
                menuItems={item.subItems}
                subMenu={true}
                locale={locale}
              />
            ) : item.route ? (
              <Button
                href={generateLocalizedSlug(locale, item.route)}
                locale={locale === "en" ? "en" : "fr"}
                className={
                  pathname.includes(item.route)
                    ? "dropdown-item-link active"
                    : "dropdown-item-link"
                }
              >
                {item.icon ?? item.icon}{" "}
                {item?.i18nName ? t(item?.i18nName) : item.name}
              </Button>
            ) : (
              <Button className="dropdown-item-link" href="#">
                {withFlag ? (
                  <img
                    className="flag-lang"
                    src={item.flag.src}
                    width={26}
                    height={22}
                    loading="lazy"
                  />
                ) : item?.i18nName ? (
                  t(item?.i18nName)
                ) : (
                  item.name
                )}
              </Button>
            )}
          </MenuItem>
        ))}
      </Menu>
    </div>
  );
};

export default DropdownMenu;
