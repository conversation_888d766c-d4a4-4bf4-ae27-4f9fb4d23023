import { useState, useRef } from "react";
import { <PERSON>ton, <PERSON>u, MenuItem, Fade } from "@mui/material";
import SvgArrowDown from "../../assets/images/icons/arrowDown.svg";
import SvgArrowUp from "../../assets/images/icons/arrowUp.svg";
import { usePathname } from "next/navigation";
import { generateLocalizedSlug } from "@/utils/functions";
import { useTranslation } from "react-i18next";

const DropdownMenu = ({
  buttonLabel,
  buttonHref,
  menuItems,
  subMenu,
  locale,
  withFlag,
  selectedFlag,
}) => {
  const pathname = usePathname();
  const buttonRef = useRef(null);

  const { t } = useTranslation();
  const [anchorEl, setAnchorEl] = useState(null);

  const open = Boolean(anchorEl);

  const isDetailBlogPath = () =>
    pathname.includes("/blog/") &&
    !pathname.includes("/blog/category/") &&
    pathname !== "/blog/" &&
    pathname !== "/fr/blog/";

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleClick2 = (event, item) => {
    event.stopPropagation(); // Prevent event bubbling
    if (item.onClick) {
      item.onClick(); // Call the onClick handler if provided
    } else {
      if (item.subItems == undefined) handleClose();
    }
  };

  return (
    <div className={subMenu ? `sub-dropdown-menu` : `dropdown-menu`}>
      <Button
        ref={buttonRef}
        className={
          pathname.includes(buttonHref) && buttonHref !== ""
            ? "navbar-link dropdown-toggle active"
            : isDetailBlogPath()
            ? "navbar-link dropdown-toggle whiteBg"
            : "navbar-link dropdown-toggle"
        }
        id="fade-button"
        aria-controls={open ? "fade-menu" : undefined}
        aria-haspopup="true"
        aria-expanded={open ? "true" : undefined}
        onClick={handleClick}
      >
        <Button
          className={
            pathname.includes(buttonHref) && buttonHref !== ""
              ? "dropdown-toggle-link active"
              : isDetailBlogPath()
              ? "dropdown-toggle-link whiteBg"
              : "dropdown-toggle-link"
          }
          href={
            // Disable href when buttonLabel is "resources"
            buttonLabel?.toLowerCase() === "resources" ||
            buttonLabel?.toLowerCase() === "ressources"
              ? "#"
              : buttonHref
              ? generateLocalizedSlug(locale, buttonHref).replace("/en/", "/")
              : pathname.replace("/en/", "/")
          }
          locale={locale === "en" ? "en" : "fr"}
          onClick={(e) => {
            // Prevent navigation when buttonLabel is "resources"
            if (
              buttonLabel?.toLowerCase() === "resources" ||
              buttonLabel?.toLowerCase() === "ressources"
            ) {
              e.preventDefault();
              e.stopPropagation();
            }
          }}
          sx={{
            // Ensure resources text has the same styling as other menu items
            ...(buttonLabel?.toLowerCase() === "resources" ||
            buttonLabel?.toLowerCase() === "ressources"
              ? {
                  cursor: "default",
                  textDecoration: "none",
                  "&:hover": {
                    textDecoration: "none",
                  },
                }
              : {}),
          }}
        >
          {withFlag ? (
            <img
              className="flag-lang"
              src={selectedFlag.src}
              width={26}
              height={22}
              disabled
              loading="lazy"
            />
          ) : (
            buttonLabel
          )}
        </Button>
        {open ? <SvgArrowDown /> : <SvgArrowUp />}
      </Button>
      <Menu
        id="fade-menu"
        MenuListProps={{
          "aria-labelledby": "fade-button",
        }}
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        TransitionComponent={Fade}
        disableScrollLock={true}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: subMenu ? "right" : "left",
        }}
      >
        {menuItems.map((item, index) => (
          <MenuItem
            className={
              item.subItems ? `sub-menu dropdown-item` : `dropdown-item`
            }
            key={index}
            onClick={(e) => handleClick2(e, item)}
          >
            {item.subItems ? (
              // If the item has a subMenu, render another DropdownMenu
              <DropdownMenu
                buttonLabel={item?.i18nName ? t(item?.i18nName) : item.name}
                buttonHref={item.route}
                menuItems={item.subItems}
                subMenu={true}
                forceOpen={hoveredSubMenu === index}
              />
            ) : // If no subMenu, render a simple link
            item.route ? (
              <Button
                href={generateLocalizedSlug(locale, item.route)}
                locale={locale === "en" ? "en" : "fr"}
                className={
                  pathname.includes(item.route)
                    ? "dropdown-item-link active"
                    : "dropdown-item-link"
                }
              >
                {item.icon ?? item.icon}{" "}
                {item?.i18nName ? t(item?.i18nName) : item.name}
              </Button>
            ) : (
              <Button className="dropdown-item-link" href="#">
                {withFlag ? (
                  <img
                    className="flag-lang"
                    src={item.flag.src}
                    width={26}
                    height={22}
                    loading="lazy"
                  />
                ) : item?.i18nName ? (
                  t(item?.i18nName)
                ) : (
                  item.name
                )}
              </Button>
            )}
          </MenuItem>
        ))}
      </Menu>
    </div>
  );
};

export default DropdownMenu;
