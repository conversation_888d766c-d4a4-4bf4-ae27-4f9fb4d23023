import { Container } from "@mui/material";
import { highlightMatchingWords } from "@/utils/functions";

function BannerComponents({
  bannerImg,
  height,
  title,
  IconImg,
  description,
  subtitle,
  bottomChildren,
  opportunitysubTitle,
  centerValue,
  altImg,
  topChildren,
  isEvent,
  categories,
  title2,
  titleHighlight,
  bannerImgDynamic,
  t,
  link,
  linkTitle,
  highlights,
}) {
  return (
    <div
      id="banner-component"
      className={centerValue === true ? "center-banner" : ""}
      style={{
        backgroundImage: bannerImg?.src
          ? `url(${bannerImg.src})`
          : bannerImgDynamic
          ? `url(${bannerImgDynamic})`
          : "none",
        height: height || "auto",
      }}
    >
      {altImg && (
        <img
          width={0}
          height={0}
          alt={altImg}
          src=""
          style={{ display: "none" }}
          loading="lazy"
        />
      )}

      <Container className="custom-max-width">
        {topChildren && topChildren}
        {IconImg && <img src={IconImg.src} />}
        {isEvent ? (
          <>
            {highlights ? (
              highlights.length > 0 ? (
                <h1 className="heading-h1 text-white">
                  {" "}
                  {highlightMatchingWords(titleHighlight, highlights)}{" "}
                </h1>
              ) : (
                <>
                  {" "}
                  <h1 className="heading-h1 text-white">
                    <span className="text-yellow">{title} </span>
                    {titleHighlight}
                    {title2}
                  </h1>
                  <p className="sub-heading text-slide text-white  ">
                    {subtitle}
                  </p>
                </>
              )
            ) : (
              <>
                {" "}
                <h1 className="heading-h1 text-white">
                  <span className="text-yellow">{title} </span>
                  {titleHighlight}
                  {title2}
                </h1>
                <p className="sub-heading text-slide text-white  ">
                  {subtitle}
                </p>
              </>
            )}

            {/*             {guide?.categoryguides?.length > 0 && (
            <div className="categories-list">
              {guide.categoryguides.map(
                (category) =>
                  category?.url && (
                    <span className="category light" key={category.url}>
                      <a
                        href={`${
                          language === "en"
                            ? `/blog/category/${category.url}`
                            : `/${language}/blog/category/${category.url}`
                        }/`}
                      >
                        {category.name}
                      </a>
                    </span>
                  )
              )} */}
          </>
        ) : (
          <>
            {" "}
            <h1 className="heading-h1 text-white">
              {title ? title : "Services We Offer"}
            </h1>
            <p className="sub-heading text-slide text-white  ">{subtitle}</p>
            <p className="sub-heading text-slide text-white  ">
              {opportunitysubTitle}
            </p>
            <p className="sub-heading text-slide text-white  ">
              {description ? description : null}
            </p>
            {link && (
              <a
                href={link}
                style={{ textDecoration: "none", width: "fit-content" }}
                className={"btn btn-filled "}
              >
                {linkTitle}
              </a>
            )}
          </>
        )}
        {bottomChildren && bottomChildren}
      </Container>
    </div>
  );
}

export default BannerComponents;
