"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/layout",{

/***/ "(app-pages-browser)/./src/components/ui/DropdownMenu.jsx":
/*!********************************************!*\
  !*** ./src/components/ui/DropdownMenu.jsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Menu/Menu.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Fade/Fade.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _assets_images_icons_arrowDown_svg__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../assets/images/icons/arrowDown.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowDown.svg\");\n/* harmony import */ var _assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../assets/images/icons/arrowUp.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowUp.svg\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _utils_functions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/functions */ \"(app-pages-browser)/./src/utils/functions.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst DropdownMenu = (param)=>{\n    let { buttonLabel, buttonHref, menuItems, subMenu, locale, withFlag, selectedFlag } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_6__.useTranslation)();\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const open = Boolean(anchorEl);\n    const isDetailBlogPath = ()=>pathname.includes(\"/blog/\") && !pathname.includes(\"/blog/category/\") && pathname !== \"/blog/\" && pathname !== \"/fr/blog/\";\n    const handleClick = (event)=>{\n        setAnchorEl(event.currentTarget);\n    };\n    const handleClose = ()=>{\n        setAnchorEl(null);\n    };\n    const handleClick2 = (event, item)=>{\n        event.stopPropagation();\n        if (item.onClick) {\n            item.onClick();\n        } else if (item.subItems == undefined) handleClose();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: subMenu ? `sub-dropdown-menu` : `dropdown-menu`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: pathname.includes(buttonHref) && buttonHref !== \"\" ? \"navbar-link dropdown-toggle active\" : isDetailBlogPath() ? \"navbar-link dropdown-toggle whiteBg\" : \"navbar-link dropdown-toggle\",\n                id: \"fade-button\",\n                \"aria-controls\": open ? \"fade-menu\" : undefined,\n                \"aria-haspopup\": \"true\",\n                \"aria-expanded\": open ? \"true\" : undefined,\n                onClick: handleClick,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: pathname.includes(buttonHref) && buttonHref !== \"\" ? \"dropdown-toggle-link active\" : isDetailBlogPath() ? \"dropdown-toggle-link whiteBg\" : \"dropdown-toggle-link\",\n                        href: buttonLabel?.toLowerCase() === \"resources\" || buttonLabel?.toLowerCase() === \"ressources\" ? \"#\" : buttonHref ? (0,_utils_functions__WEBPACK_IMPORTED_MODULE_5__.generateLocalizedSlug)(locale, buttonHref).replace(\"/en/\", \"/\") : pathname.replace(\"/en/\", \"/\"),\n                        locale: locale === \"en\" ? \"en\" : \"fr\",\n                        onClick: (e)=>{\n                            if (buttonLabel?.toLowerCase() === \"resources\" || buttonLabel?.toLowerCase() === \"ressources\") {\n                                e.preventDefault();\n                                e.stopPropagation();\n                            }\n                        },\n                        sx: {\n                            ...buttonLabel?.toLowerCase() === \"resources\" || buttonLabel?.toLowerCase() === \"ressources\" ? {\n                                cursor: \"default\",\n                                textDecoration: \"none\",\n                                \"&:hover\": {\n                                    textDecoration: \"none\"\n                                }\n                            } : {}\n                        },\n                        children: withFlag ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            className: \"flag-lang\",\n                            src: selectedFlag.src,\n                            width: 26,\n                            height: 22,\n                            disabled: true,\n                            loading: \"lazy\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 102,\n                            columnNumber: 13\n                        }, undefined) : buttonLabel\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, undefined),\n                    open ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowDown_svg__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 114,\n                        columnNumber: 17\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 114,\n                        columnNumber: 36\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                id: \"fade-menu\",\n                MenuListProps: {\n                    \"aria-labelledby\": \"fade-button\"\n                },\n                anchorEl: anchorEl,\n                open: open,\n                onClose: handleClose,\n                TransitionComponent: _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                disableScrollLock: true,\n                anchorOrigin: {\n                    vertical: \"bottom\",\n                    horizontal: subMenu ? \"right\" : \"left\"\n                },\n                children: menuItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: item.subItems ? `sub-menu dropdown-item` : `dropdown-item`,\n                        onClick: (e)=>handleClick2(e, item),\n                        children: item.subItems ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DropdownMenu, {\n                            buttonLabel: item?.i18nName ? t(item?.i18nName) : item.name,\n                            buttonHref: item.route,\n                            menuItems: item.subItems,\n                            subMenu: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 140,\n                            columnNumber: 15\n                        }, undefined) : item.route ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            href: (0,_utils_functions__WEBPACK_IMPORTED_MODULE_5__.generateLocalizedSlug)(locale, item.route),\n                            locale: locale === \"en\" ? \"en\" : \"fr\",\n                            className: pathname.includes(item.route) ? \"dropdown-item-link active\" : \"dropdown-item-link\",\n                            children: [\n                                item.icon ?? item.icon,\n                                \" \",\n                                item?.i18nName ? t(item?.i18nName) : item.name\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 147,\n                            columnNumber: 15\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"dropdown-item-link\",\n                            href: \"#\",\n                            children: withFlag ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                className: \"flag-lang\",\n                                src: item.flag.src,\n                                width: 26,\n                                height: 22,\n                                loading: \"lazy\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                                lineNumber: 162,\n                                columnNumber: 19\n                            }, undefined) : item?.i18nName ? t(item?.i18nName) : item.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 160,\n                            columnNumber: 15\n                        }, undefined)\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 132,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DropdownMenu, \"kmMXD6S/WNK2NQEz2iCMSS4bykM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname,\n        react_i18next__WEBPACK_IMPORTED_MODULE_6__.useTranslation\n    ];\n});\n_c = DropdownMenu;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DropdownMenu);\nvar _c;\n$RefreshReg$(_c, \"DropdownMenu\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/DropdownMenu.jsx\n"));

/***/ })

});