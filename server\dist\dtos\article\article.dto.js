"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CategoryVersionArticleDTO = exports.CustomCategoryVersion = exports.CustomArticleVersionDTO = exports.ArticleDTO = exports.ArticleVersionDTO = void 0;
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const article_category_dto_1 = require("./article.category.dto");
class ArticleVersionDTO {
    constructor(articleVersion = {}) {
        Object.assign(this, articleVersion);
    }
}
exports.ArticleVersionDTO = ArticleVersionDTO;
__decorate([
    (0, class_transformer_1.Expose)({ name: '_id' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ArticleVersionDTO.prototype, "id", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'language' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ArticleVersionDTO.prototype, "language", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'title' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ArticleVersionDTO.prototype, "title", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'keywords' }),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], ArticleVersionDTO.prototype, "keywords", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'metaTitle' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ArticleVersionDTO.prototype, "metaTitle", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'metaDescription' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ArticleVersionDTO.prototype, "metaDescription", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'url' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ArticleVersionDTO.prototype, "slug", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'alt' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ArticleVersionDTO.prototype, "alt", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'image' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ArticleVersionDTO.prototype, "picture", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    (0, class_transformer_1.Type)(() => article_category_dto_1.CategoryDTO),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], ArticleVersionDTO.prototype, "category", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'shareOnSocialMedia' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ArticleVersionDTO.prototype, "share", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'content' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ArticleVersionDTO.prototype, "content", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'canonical' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ArticleVersionDTO.prototype, "canonical", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'visibility' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ArticleVersionDTO.prototype, "visibility", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'publishDate' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], ArticleVersionDTO.prototype, "publishDate", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'createdBy' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ArticleVersionDTO.prototype, "createdBy", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'createdAt' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], ArticleVersionDTO.prototype, "creationDate", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'updatedAt' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], ArticleVersionDTO.prototype, "updateDate", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'isArchived' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ArticleVersionDTO.prototype, "archived", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Object)
], ArticleVersionDTO.prototype, "buffer", void 0);
class ArticleDTO {
    constructor(article) {
        Object.assign(this, article);
    }
}
exports.ArticleDTO = ArticleDTO;
__decorate([
    (0, class_transformer_1.Expose)({ name: '_id' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ArticleDTO.prototype, "id", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'versions' }),
    (0, class_transformer_1.Type)(() => ArticleVersionDTO),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], ArticleDTO.prototype, "versions", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'tags' }),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], ArticleDTO.prototype, "tags", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'totalCommentaires' }),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], ArticleDTO.prototype, "totalComments", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'robotsMeta' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ArticleDTO.prototype, "robotsMeta", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'createdAt' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], ArticleDTO.prototype, "creationDate", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Date)
], ArticleDTO.prototype, "updatedAt", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Number)
], ArticleDTO.prototype, "__v", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Object)
], ArticleDTO.prototype, "buffer", void 0);
class CustomArticleVersionDTO {
    constructor(articleVersion = {}) {
        Object.assign(this, articleVersion);
    }
}
exports.CustomArticleVersionDTO = CustomArticleVersionDTO;
__decorate([
    (0, class_transformer_1.Expose)({ name: 'id' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CustomArticleVersionDTO.prototype, "_id", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'language' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CustomArticleVersionDTO.prototype, "language", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'title' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CustomArticleVersionDTO.prototype, "title", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'keywords' }),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], CustomArticleVersionDTO.prototype, "keywords", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'metaTitle' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CustomArticleVersionDTO.prototype, "metaTitle", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'metaDescription' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CustomArticleVersionDTO.prototype, "metaDescription", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'url' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CustomArticleVersionDTO.prototype, "slug", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'alt' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CustomArticleVersionDTO.prototype, "alt", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'image' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CustomArticleVersionDTO.prototype, "picture", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'categories' }),
    (0, class_transformer_1.Type)(() => CustomCategoryVersion),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], CustomArticleVersionDTO.prototype, "categories", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'shareOnSocialMedia' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CustomArticleVersionDTO.prototype, "share", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'content' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CustomArticleVersionDTO.prototype, "content", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'canonical' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CustomArticleVersionDTO.prototype, "canonical", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'visibility' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CustomArticleVersionDTO.prototype, "visibility", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'publishDate' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], CustomArticleVersionDTO.prototype, "publishDate", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'createdBy' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CustomArticleVersionDTO.prototype, "createdBy", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'createdAt' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], CustomArticleVersionDTO.prototype, "creationDate", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'updatedAt' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], CustomArticleVersionDTO.prototype, "updateDate", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'isArchived' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CustomArticleVersionDTO.prototype, "archived", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Object)
], CustomArticleVersionDTO.prototype, "buffer", void 0);
class CustomCategoryVersion {
    constructor(customCategoryVersion = {}) {
        Object.assign(this, customCategoryVersion);
    }
}
exports.CustomCategoryVersion = CustomCategoryVersion;
__decorate([
    (0, class_transformer_1.Expose)({ name: 'id' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CustomCategoryVersion.prototype, "id", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'name' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CustomCategoryVersion.prototype, "name", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'url' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CustomCategoryVersion.prototype, "slug", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Object)
], CustomCategoryVersion.prototype, "buffer", void 0);
class CategoryVersionArticleDTO {
    constructor(article = {}) {
        Object.assign(this, article);
    }
}
exports.CategoryVersionArticleDTO = CategoryVersionArticleDTO;
__decorate([
    (0, class_transformer_1.Expose)({ name: '_id' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CategoryVersionArticleDTO.prototype, "id", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'versions' }),
    (0, class_transformer_1.Type)(() => CustomArticleVersionDTO),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", CustomArticleVersionDTO)
], CategoryVersionArticleDTO.prototype, "versions", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'tags' }),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], CategoryVersionArticleDTO.prototype, "tags", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'robotsMeta' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CategoryVersionArticleDTO.prototype, "robotsMeta", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'createdAt' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], CategoryVersionArticleDTO.prototype, "creationDate", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Date)
], CategoryVersionArticleDTO.prototype, "updatedAt", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Number)
], CategoryVersionArticleDTO.prototype, "__v", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Object)
], CategoryVersionArticleDTO.prototype, "buffer", void 0);
//# sourceMappingURL=article.dto.js.map