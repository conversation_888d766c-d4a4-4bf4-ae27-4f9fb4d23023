"use client";
import teamPic from "@/assets/images/website/teamPic.webp";
import { useTranslation } from "react-i18next";
import Image from "next/image";

function MeetTheTeam() {
  const { t } = useTranslation();
  return (
    <div id="team">
      <h2 className="heading-h1 text-white text-center">
        {t("joinUs:dreamTeam")}
      </h2>
      <div className="team-img">
        <img
          width={1429}
          height={666}
          src={teamPic.src}
          alt="pentabell teamPic"
          loading="lazy"
        />
      </div>
    </div>
  );
}

export default MeetTheTeam;
