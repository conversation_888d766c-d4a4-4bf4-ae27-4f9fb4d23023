"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/edit/[id]/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/EditArticle.jsx":
/*!******************************************************!*\
  !*** ./src/features/blog/components/EditArticle.jsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,Grid,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Accordion/Accordion.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,Grid,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionSummary/AccordionSummary.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,Grid,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionDetails/AccordionDetails.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,Grid,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,Grid,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormGroup/FormGroup.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,Grid,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormLabel/FormLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,Grid,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,Grid,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,Grid,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mui/icons-material/ExpandMore */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/ExpandMore.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! yup */ \"(app-pages-browser)/./node_modules/yup/index.esm.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var _user_component_updateProfile_experience_DialogModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../user/component/updateProfile/experience/DialogModal */ \"(app-pages-browser)/./src/features/user/component/updateProfile/experience/DialogModal.jsx\");\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! formik */ \"(app-pages-browser)/./node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var _components_loading_Loading__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../components/loading/Loading */ \"(app-pages-browser)/./src/components/loading/Loading.jsx\");\n/* harmony import */ var _utils_urls__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../utils/urls */ \"(app-pages-browser)/./src/utils/urls.js\");\n/* harmony import */ var _opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../opportunity/hooks/opportunity.hooks */ \"(app-pages-browser)/./src/features/opportunity/hooks/opportunity.hooks.js\");\n/* harmony import */ var _config_axios__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/config/axios */ \"(app-pages-browser)/./src/config/axios.js\");\n/* harmony import */ var _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../hooks/blog.hook */ \"(app-pages-browser)/./src/features/blog/hooks/blog.hook.js\");\n/* harmony import */ var _AddArticle__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./AddArticle */ \"(app-pages-browser)/./src/features/blog/components/AddArticle.jsx\");\n/* harmony import */ var _assets_images_icons_archiveicon_popup_svg__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/assets/images/icons/archiveicon-popup.svg */ \"(app-pages-browser)/./src/assets/images/icons/archiveicon-popup.svg\");\n/* harmony import */ var react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! react-datepicker/dist/react-datepicker.css */ \"(app-pages-browser)/./node_modules/react-datepicker/dist/react-datepicker.css\");\n/* harmony import */ var _features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/features/auth/hooks/currentUser.hooks */ \"(app-pages-browser)/./src/features/auth/hooks/currentUser.hooks.js\");\n/* harmony import */ var _components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst EditArticle = (param)=>{\n    let { articleId } = param;\n    _s();\n    const { data } = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__.useGetArticleByIdAll)(articleId);\n    const usedisarchivedArticleHook = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__.usearchivedarticle)();\n    const { data: dataEN, isLoading: isLoadingEN, isError, error } = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__.useGetArticleById)(articleId, \"en\");\n    const { data: dataFR, isLoading: isLoadingFR } = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__.useGetArticleById)(articleId, \"fr\");\n    const [isArchived, setIsArchived] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isArchivedFr, setIsArchivedFr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { user } = (0,_features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_15__[\"default\"])();\n    const [englishVersion, setEnglishVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [frenchVersion, setFrenchVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const useUpdateArticleHook = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__.useUpdateArticle)();\n    const useSaveFileHook = (0,_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_9__.useSaveFile)();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const currentYear = new Date().getFullYear();\n    const formikRefEn = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const formikRefFr = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const formikRefAll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [expanded, setExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(`panel`);\n    const [formdataEN, setFormDataEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [formdataFR, setFormDataFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const formdata = new FormData();\n    const formdatafr = new FormData();\n    const [uuidPhotoFileNameEN, setUuidPhotoFileNameEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedAction, setSelectedAction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedEnCategories, setSelectedEnCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(dataEN?.versions?.categories.map((category)=>category.id) || []);\n    const [filteredFrCategories, setFilteredFrCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [openDialog, setOpenDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openDialogfr, setOpenDialogfr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleOpenDialog = (action1)=>{\n        setSelectedAction(action1);\n        setOpenDialog(true);\n    };\n    const handleOpenDialogfr = (action1)=>{\n        setSelectedAction(action1);\n        setOpenDialogfr(true);\n    };\n    const handleCloseDialogfr = ()=>{\n        setOpenDialogfr(false);\n    };\n    const handleCloseDialog = ()=>{\n        setOpenDialog(false);\n    };\n    const useUpdateArticleAllHook = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__.useUpdateArticleAll)();\n    const [uuidPhotoFileNameFR, setUuidPhotoFileNameFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [uuidPhotoEN, setUuidPhotoEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uuidPhotoFR, setUuidPhotoFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // CTS Banner state variables\n    const [uuidCTSBannerPhotoFileNameEN, setUuidCTSBannerPhotoFileNameEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [uuidCTSBannerPhotoFileNameFR, setUuidCTSBannerPhotoFileNameFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [uuidCTSBannerPhotoEN, setUuidCTSBannerPhotoEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uuidCTSBannerPhotoFR, setUuidCTSBannerPhotoFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [formDataCTSBannerEN, setFormDataCTSBannerEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [formDataCTSBannerFR, setFormDataCTSBannerFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setEnglishVersion(dataEN ? dataEN?.versions : \"\");\n        setFrenchVersion(dataFR ? dataFR?.versions : \"\");\n        if (dataEN?.versions?.categories.length > 0 && dataEN?.versions?.categories[0] != {}) {\n            setSelectedEnCategories(dataEN?.versions?.categories.map((category)=>category.id));\n        }\n        setIsArchived(dataEN?.versions?.isArchived || false);\n        setIsArchivedFr(dataFR?.versions?.isArchived || false);\n    }, [\n        dataEN,\n        dataFR\n    ]);\n    const handleConfirmAction = ()=>{\n        if (selectedAction === \"archive\") {\n            handledisarchivearticleen(true);\n        } else if (selectedAction === \"desarchive\") {\n            handledisarchivearticleen(false);\n        }\n        setOpenDialog(false);\n    };\n    const handleConfirmActionFr = ()=>{\n        if (selectedAction === \"archive\") {\n            handlearchiveanddisarchivearticleFr(true);\n        } else if (selectedAction === \"desarchive\") {\n            handlearchiveanddisarchivearticleFr(false);\n        }\n        setOpenDialogfr(false);\n    };\n    const handleImageSelect = async function(selectedFile, language) {\n        let imageType = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"main\";\n        if (language === \"en\") {\n            let uuidPhotos = (0,uuid__WEBPACK_IMPORTED_MODULE_17__[\"default\"])().replace(/-/g, \"\");\n            if (imageType === \"ctsBanner\") {\n                setUuidCTSBannerPhotoEN(uuidPhotos);\n                const ctsBannerFormData = new FormData();\n                ctsBannerFormData.append(\"file\", selectedFile);\n                setFormDataCTSBannerEN(ctsBannerFormData);\n                const extension = selectedFile.name.split(\".\").pop();\n                setUuidCTSBannerPhotoFileNameEN(`${uuidPhotos}.${extension}`);\n            } else {\n                setUuidPhotoEN(uuidPhotos);\n                formdata.append(\"file\", selectedFile);\n                setFormDataEN(formdata);\n                const extension = selectedFile.name.split(\".\").pop();\n                setUuidPhotoFileNameEN(`${uuidPhotos}.${extension}`);\n            }\n        } else if (language === \"fr\") {\n            let uuidPhotos = (0,uuid__WEBPACK_IMPORTED_MODULE_17__[\"default\"])().replace(/-/g, \"\");\n            if (imageType === \"ctsBanner\") {\n                setUuidCTSBannerPhotoFR(uuidPhotos);\n                const ctsBannerFormData = new FormData();\n                ctsBannerFormData.append(\"file\", selectedFile);\n                setFormDataCTSBannerFR(ctsBannerFormData);\n                const extension = selectedFile.name.split(\".\").pop();\n                setUuidCTSBannerPhotoFileNameFR(`${uuidPhotos}.${extension}`);\n            } else {\n                setUuidPhotoFR(uuidPhotos);\n                formdatafr.append(\"file\", selectedFile);\n                setFormDataFR(formdatafr);\n                const extension = selectedFile.name.split(\".\").pop();\n                setUuidPhotoFileNameFR(`${uuidPhotos}.${extension}`);\n            }\n        }\n    };\n    const handleRemoveCTSBanner = (language)=>{\n        if (language === \"en\") {\n            setUuidCTSBannerPhotoFileNameEN(\"\");\n            setUuidCTSBannerPhotoEN(null);\n            setFormDataCTSBannerEN(null);\n            setEnglishVersion((prev)=>({\n                    ...prev,\n                    ctsBanner: null\n                }));\n            if (formikRefEn.current) {\n                formikRefEn.current.setFieldValue(\"ctsBannerImage\", null);\n                formikRefEn.current.setFieldValue(\"ctsBannerLink\", \"\");\n            }\n        } else if (language === \"fr\") {\n            setUuidCTSBannerPhotoFileNameFR(\"\");\n            setUuidCTSBannerPhotoFR(null);\n            setFormDataCTSBannerFR(null);\n            if (formikRefFr.current) {\n                formikRefFr.current.setFieldValue(\"ctsBannerImage\", null);\n                formikRefFr.current.setFieldValue(\"ctsBannerLink\", \"\");\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setEnglishVersion(dataEN ? dataEN?.versions : \"\");\n        setFrenchVersion(dataFR ? dataFR?.versions : \"\");\n        setSelectedEnCategories(dataEN?.versions?.categories.map((category)=>category.id));\n    }, [\n        dataEN,\n        dataFR\n    ]);\n    const initialValuesEn = {\n        metaTitle: englishVersion?.metaTitle,\n        metaDescription: englishVersion?.metaDescription,\n        description: englishVersion?.description,\n        visibility: englishVersion ? englishVersion.visibility : \"\",\n        category: englishVersion?.categories?.length > 0 ? englishVersion.categories?.map((category)=>category.id) : [\n            {\n                name: \"\"\n            }\n        ],\n        image: englishVersion?.image,\n        keywords: englishVersion?.keywords?.map((keyword, index)=>({\n                id: keyword.trim() || `id-${index}`,\n                text: keyword.trim()\n            })),\n        title: englishVersion?.title,\n        url: englishVersion?.url,\n        alt: englishVersion?.alt,\n        content: englishVersion?.content,\n        language: \"en\",\n        publishDate: englishVersion?.publishDate,\n        createdBy: user?.firstName + \" \" + user?.lastName,\n        highlights: englishVersion?.highlights?.map((keyword, index)=>({\n                id: keyword.trim() || `id-${index}`,\n                text: keyword.trim()\n            })),\n        faqTitle: englishVersion?.faqTitle || \"\",\n        faq: englishVersion?.faq || [],\n        ctsBannerImage: englishVersion?.ctsBanner?.image || \"\",\n        ctsBannerLink: englishVersion?.ctsBanner?.link || \"\"\n    };\n    const initialValuesAll = {\n        robotsMeta: data?.robotsMeta\n    };\n    const initialValuesFr = {\n        metaTitle: frenchVersion ? frenchVersion?.metaTitle : \"\",\n        metaDescription: frenchVersion ? frenchVersion?.metaDescription : \"\",\n        description: frenchVersion ? frenchVersion?.description : \"\",\n        visibility: frenchVersion ? frenchVersion?.visibility : \"\",\n        category: frenchVersion?.categories?.length > 0 ? frenchVersion.categories?.map((category)=>category.id) : [\n            {\n                name: \"\"\n            }\n        ],\n        image: frenchVersion ? frenchVersion?.image : \"\",\n        keywords: frenchVersion?.keywords?.map((keyword, index)=>({\n                id: keyword?.trim() || `id-${index}`,\n                text: keyword?.trim()\n            })),\n        title: frenchVersion ? frenchVersion?.title : \"\",\n        url: frenchVersion ? frenchVersion?.url : \"\",\n        alt: frenchVersion ? frenchVersion?.alt : \"\",\n        content: frenchVersion ? frenchVersion?.content : \"\",\n        language: \"fr\",\n        publishDate: frenchVersion ? frenchVersion?.publishDate : \"\",\n        createdBy: user?.firstName + \" \" + user?.lastName,\n        highlights: frenchVersion?.highlights?.map((keyword, index)=>({\n                id: keyword.trim() || `id-${index}`,\n                text: keyword.trim()\n            })),\n        faqTitle: frenchVersion?.faqTitle || \"\",\n        faq: frenchVersion?.faq || [],\n        ctsBannerImage: frenchVersion?.ctsBanner?.image || \"\",\n        ctsBannerLink: frenchVersion?.ctsBanner?.link || \"\"\n    };\n    const validationSchemaEN = yup__WEBPACK_IMPORTED_MODULE_3__.object().shape({\n        metaTitle: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n        metaDescription: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n        title: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n        image: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n        visibility: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\"))\n    });\n    const handleSaveSetting = (values)=>{\n        const data = {\n            robotsMeta: values?.robotsMeta\n        };\n        useUpdateArticleAllHook.mutate({\n            data: data,\n            id: articleId\n        }, {\n            onSuccess: ()=>{\n            // setTimeout(\n            //\n            //   (window.location.href = `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.opportunities.route}/`),\n            //   \"3000\"\n            // );\n            }\n        });\n    };\n    const handledisarchivearticleen = async ()=>{\n        const action1 = isArchived ? false : true;\n        try {\n            await usedisarchivedArticleHook.mutateAsync({\n                language: \"en\",\n                id: articleId,\n                archive: action1\n            });\n            setIsArchived(action1);\n        } catch (error) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(t(action1 ? \"article:archivedError\" : \"article:desarchivedError\"));\n            console.error(\"Erreur lors de l'archivage/d\\xe9sarchivage de l'article:\", error);\n        }\n    };\n    const handlearchiveanddisarchivearticleFr = async ()=>{\n        try {\n            await usedisarchivedArticleHook.mutateAsync({\n                language: \"fr\",\n                id: articleId,\n                archive: action\n            });\n        } catch (error) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(t(action ? \"article:archivedError\" : \"article:desarchivedError\"));\n        }\n    };\n    const handleSaveEN = async ()=>{\n        const enValues = formikRefEn.current.values;\n        const hasId = enValues.keywords.every((item)=>item.hasOwnProperty(\"id\"));\n        const highlightshasId = enValues?.highlights?.every((item)=>item.hasOwnProperty(\"id\"));\n        if (hasId) {\n            enValues.keywords = enValues.keywords.filter((item)=>item.hasOwnProperty(\"id\") && item.text !== \"\").map((item)=>item.text);\n        }\n        if (highlightshasId) {\n            enValues.highlights = enValues.keywords.filter((item)=>item.hasOwnProperty(\"id\") && item.text !== \"\").map((item)=>item.text);\n        }\n        if (enValues.category.length > 0 && typeof enValues.category[0] === \"object\") {\n            let categoryEn = [];\n            enValues.category.map((category)=>categoryEn.push(category.id));\n            enValues.category = categoryEn;\n        }\n        formikRefEn.current.submitForm();\n        const isEnFormValid = formikRefEn.current.isValid && Object.keys(formikRefEn.current.errors).length === 0;\n        if (isEnFormValid) {\n            if (uuidCTSBannerPhotoFileNameEN) {\n                enValues.ctsBanner = {\n                    ...enValues.ctsBanner,\n                    image: uuidCTSBannerPhotoFileNameEN\n                };\n            }\n            const uploadPromises = [];\n            if (uuidPhotoFileNameEN) {\n                enValues.image = uuidPhotoFileNameEN;\n                uploadPromises.push(useSaveFileHook.mutateAsync({\n                    resource: \"blogs\",\n                    folder: currentYear,\n                    filename: uuidPhotoEN,\n                    body: {\n                        formData: formdataEN,\n                        t\n                    }\n                }).then((data)=>{\n                    enValues.image = data.uuid;\n                }));\n            }\n            if (uuidCTSBannerPhotoFileNameEN) {\n                uploadPromises.push(useSaveFileHook.mutateAsync({\n                    resource: \"blogs\",\n                    folder: currentYear,\n                    filename: uuidCTSBannerPhotoEN,\n                    body: {\n                        formData: formDataCTSBannerEN,\n                        t\n                    }\n                }).then((data)=>{\n                    enValues.ctsBanner = {\n                        image: data.uuid,\n                        link: enValues.ctsBannerLink\n                    };\n                }));\n            }\n            if (uploadPromises.length > 0) {\n                Promise.all(uploadPromises).then(()=>{\n                    useUpdateArticleHook.mutate({\n                        data: enValues,\n                        language: \"en\",\n                        id: articleId\n                    });\n                });\n            } else {\n                useUpdateArticleHook.mutate({\n                    data: enValues,\n                    language: \"en\",\n                    id: articleId\n                });\n            }\n        }\n    };\n    const handleSaveFR = async ()=>{\n        const frValues = formikRefFr.current.values;\n        const hasId = frValues.keywords?.every((item)=>item.hasOwnProperty(\"id\"));\n        const highlightshasId = frValues?.highlights?.every((item)=>item.hasOwnProperty(\"id\"));\n        if (hasId) {\n            frValues.keywords = frValues.keywords.filter((item)=>item.hasOwnProperty(\"id\") && item.text !== \"\").map((item)=>item.text);\n        }\n        if (highlightshasId) {\n            frValues.highlights = frValues.keywords.filter((item)=>item?.hasOwnProperty(\"id\") && item.text !== \"\").map((item)=>item.text);\n        }\n        if (frValues.category.length > 0 && typeof frValues.category[0] === \"object\") {\n            let categoryFr = [];\n            frValues.category.map((category)=>categoryFr.push(category.id));\n            frValues.category = categoryFr;\n        }\n        formikRefFr.current.submitForm();\n        const isFrFormValid = formikRefFr.current.isValid && Object.keys(formikRefFr.current.errors).length === 0;\n        if (isFrFormValid) {\n            if (uuidCTSBannerPhotoFileNameFR) {\n                frValues.ctsBanner = {\n                    link: frValues.ctsBannerLink,\n                    image: uuidCTSBannerPhotoFileNameFR\n                };\n            }\n            const uploadPromises = [];\n            if (uuidPhotoFileNameFR) {\n                frValues.image = uuidPhotoFileNameFR;\n                uploadPromises.push(useSaveFileHook.mutateAsync({\n                    resource: \"blogs\",\n                    folder: currentYear,\n                    filename: uuidPhotoFR,\n                    body: {\n                        formData: formdataFR,\n                        t\n                    }\n                }).then((data)=>{\n                    if (data.message === \"uuid exist\") {\n                        frValues.image = data.uuid;\n                    }\n                }));\n            }\n            if (uuidCTSBannerPhotoFileNameFR) {\n                uploadPromises.push(useSaveFileHook.mutateAsync({\n                    resource: \"blogs\",\n                    folder: currentYear,\n                    filename: uuidCTSBannerPhotoFR,\n                    body: {\n                        formData: formDataCTSBannerFR,\n                        t\n                    }\n                }).then((data)=>{\n                    frValues.ctsBanner = {\n                        ...frValues.ctsBanner,\n                        image: data.uuid\n                    };\n                }));\n            }\n            if (uploadPromises.length > 0) {\n                Promise.all(uploadPromises).then(()=>{\n                    useUpdateArticleHook.mutate({\n                        data: frValues,\n                        language: \"fr\",\n                        id: articleId\n                    });\n                });\n            } else {\n                useUpdateArticleHook.mutate({\n                    data: frValues,\n                    language: \"fr\",\n                    id: articleId\n                });\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchFrenchCategories = async ()=>{\n            try {\n                const response = await _config_axios__WEBPACK_IMPORTED_MODULE_10__.axiosGetJson.get(`${_utils_urls__WEBPACK_IMPORTED_MODULE_8__.API_URLS.categories}/en/${selectedEnCategories}`);\n                const transformedCategories = response.data.map((category)=>({\n                        id: category._id,\n                        name: category.name\n                    }));\n                setFilteredFrCategories(transformedCategories);\n            } catch (error) {}\n        };\n        if (selectedEnCategories?.length > 0) {\n            fetchFrenchCategories();\n        } else {\n            setFilteredFrCategories([]);\n        }\n    }, [\n        selectedEnCategories\n    ]);\n    const handleChangeAccordion = (panel)=>(event, newExpanded)=>{\n            setExpanded(newExpanded ? panel : false);\n        };\n    if (isLoadingFR || isLoadingEN) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"content\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_loading_Loading__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                lineNumber: 559,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n            lineNumber: 558,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"heading-h2 semi-bold\",\n                children: t(\"createArticle:editArticle\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                lineNumber: 566,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"container\",\n                className: \"recent-application-pentabell\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"main-content\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"commun\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            id: \"experiences\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_18__.Formik, {\n                                    initialValues: initialValuesAll,\n                                    innerRef: formikRefAll,\n                                    children: (param)=>{\n                                        let { errors, touched, setFieldValue, values } = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_18__.Form, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    id: \"accordion\",\n                                                    expanded: expanded === `panel`,\n                                                    onChange: handleChangeAccordion(`panel`),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                                lineNumber: 581,\n                                                                columnNumber: 37\n                                                            }, void 0),\n                                                            \"aria-controls\": `panel-content`,\n                                                            id: `panel-header`,\n                                                            children: t(\"createArticle:settings\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                            lineNumber: 580,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            className: \"accordion-detail\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                container: true,\n                                                                spacing: 4,\n                                                                sx: {\n                                                                    alignItems: \"flex-end\"\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        item: true,\n                                                                        xs: 12,\n                                                                        md: 10,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                className: \"label-form\",\n                                                                                children: [\n                                                                                    t(\"createArticle:Robotsmeta\"),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                        className: \"select-pentabell\",\n                                                                                        variant: \"standard\",\n                                                                                        sx: {\n                                                                                            m: 1,\n                                                                                            minWidth: 120\n                                                                                        },\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                            value: values.robotsMeta,\n                                                                                            onChange: (event)=>{\n                                                                                                setFieldValue(\"robotsMeta\", event.target.value);\n                                                                                            },\n                                                                                            children: _utils_constants__WEBPACK_IMPORTED_MODULE_6__.RobotsMeta.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                                                    value: item,\n                                                                                                    children: item\n                                                                                                }, index, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                                                                    lineNumber: 612,\n                                                                                                    columnNumber: 39\n                                                                                                }, undefined))\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                                                            lineNumber: 602,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                                                        lineNumber: 597,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_18__.ErrorMessage, {\n                                                                                        className: \"label-error\",\n                                                                                        name: \"robotsMeta\",\n                                                                                        component: \"div\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                                                        lineNumber: 618,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                                                lineNumber: 595,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                                            lineNumber: 594,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                                        lineNumber: 593,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    \" \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        item: true,\n                                                                        xs: 12,\n                                                                        md: 2,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            text: \"Save\",\n                                                                            className: \"btn btn-filled\",\n                                                                            onClick: ()=>handleSaveSetting(values)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                                            lineNumber: 627,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                                        lineNumber: 626,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                                lineNumber: 588,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                            lineNumber: 587,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, `panel}`, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                    lineNumber: 574,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \" \"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                            lineNumber: 573,\n                                            columnNumber: 19\n                                        }, undefined);\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                    lineNumber: 571,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddArticle__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    image: englishVersion?.image,\n                                    ctsBannerImage: englishVersion?.ctsBanner?.image,\n                                    language: \"en\",\n                                    initialValues: initialValuesEn,\n                                    formRef: formikRefEn,\n                                    onImageSelect: handleImageSelect,\n                                    validationSchema: validationSchemaEN,\n                                    isEdit: true,\n                                    onCategoriesSelect: (categories)=>{\n                                        setSelectedEnCategories(categories);\n                                    },\n                                    onRemoveCTSBanner: handleRemoveCTSBanner\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                    lineNumber: 639,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"btn-container\",\n                                    children: [\n                                        \"\\xa0\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            text: t(\"global:save\"),\n                                            className: \"btn btn-filled\",\n                                            onClick: handleSaveEN\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                            lineNumber: 655,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            text: isArchived ? t(\"global:unarchive\") : t(\"global:archive\"),\n                                            className: \"btn btn-filled\",\n                                            onClick: ()=>handleOpenDialog(isArchived ? \"desarchive\" : \"archive\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                            lineNumber: 660,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                    lineNumber: 653,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_user_component_updateProfile_experience_DialogModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    open: openDialog,\n                                    message: selectedAction === \"archive\" ? t(\"messages:archiveConfirmation\") : t(\"messages:desarchiveConfirmation\"),\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_archiveicon_popup_svg__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                        lineNumber: 677,\n                                        columnNumber: 23\n                                    }, void 0),\n                                    onClose: handleCloseDialog,\n                                    onConfirm: handleConfirmAction\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                    lineNumber: 670,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                    lineNumber: 681,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    id: \"accordion\",\n                                    expanded: expanded === `panel-1`,\n                                    onChange: handleChangeAccordion(`panel-1`),\n                                    isEdit: true,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                lineNumber: 690,\n                                                columnNumber: 31\n                                            }, void 0),\n                                            \"aria-controls\": `panel-content`,\n                                            id: `panel-header`,\n                                            children: t(\"createArticle:editArticleFr\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                            lineNumber: 689,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"accordion-detail\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddArticle__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    image: frenchVersion?.image,\n                                                    ctsBannerImage: frenchVersion?.ctsBanner?.image,\n                                                    language: \"fr\",\n                                                    initialValues: initialValuesFr,\n                                                    formRef: formikRefFr,\n                                                    onImageSelect: handleImageSelect,\n                                                    validationSchema: validationSchemaEN,\n                                                    isEdit: true,\n                                                    filteredCategories: filteredFrCategories,\n                                                    onRemoveCTSBanner: handleRemoveCTSBanner\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                    lineNumber: 697,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"btn-container\",\n                                                    children: [\n                                                        \"\\xa0\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            text: t(\"global:save\"),\n                                                            className: \"btn btn-filled\",\n                                                            onClick: handleSaveFR\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                            lineNumber: 711,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            text: isArchivedFr ? t(\"global:unarchive\") : t(\"global:archive\"),\n                                                            className: \"btn btn-filled\",\n                                                            onClick: ()=>handleOpenDialogfr(isArchivedFr ? \"desarchive\" : \"archive\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                            lineNumber: 716,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                    lineNumber: 709,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_user_component_updateProfile_experience_DialogModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    open: openDialogfr,\n                                                    message: selectedAction === \"archive\" ? t(\"messages:archiveConfirmationfr\") : t(\"messages:desarchiveConfirmationfr\"),\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_archiveicon_popup_svg__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                        lineNumber: 738,\n                                                        columnNumber: 27\n                                                    }, void 0),\n                                                    onClose: handleCloseDialogfr,\n                                                    onConfirm: handleConfirmActionFr\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                    lineNumber: 731,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                            lineNumber: 696,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, `panel-1`, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                    lineNumber: 682,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                            lineNumber: 570,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                        lineNumber: 569,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                    lineNumber: 568,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                lineNumber: 567,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(EditArticle, \"dkIejSfP202WnpYQC3id0f6HnKM=\", false, function() {\n    return [\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__.useGetArticleByIdAll,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__.useGetArticleById,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__.useGetArticleById,\n        _features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__.useUpdateArticle,\n        _opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_9__.useSaveFile,\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__.useUpdateArticleAll\n    ];\n});\n_c = EditArticle;\n/* harmony default export */ __webpack_exports__[\"default\"] = (EditArticle);\nvar _c;\n$RefreshReg$(_c, \"EditArticle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/EditArticle.jsx\n"));

/***/ })

});