import banner from "@/assets/images/egypt/Pentabell-Egypt.webp";;
import BannerComponents from "@/components/pages/sites/sections/BannerComponents";
import OurPartners from "@/components/pages/sites/sections/OurPartners";
import ResponsiveRowTitleText from "@/components/ui/ResponsiveRowTitleText";
import GlobalHRServicesSection from "@/components/pages/sites/sections/GlobalHRServicesSection";
import r1 from "@/assets/images/services/recrutement1.png";
import r2 from "@/assets/images/services/recrutement2.png";
import r3 from "@/assets/images/services/recrutement3.png";
import r4 from "@/assets/images/services/recrutement4.png";
import TunisiaOfficePageForm from "@/features/forms/components/TunisiaOfficePageForm";
import ComplexityControlSectionPanel from "@/components/pages/sites/offices-sections/ComplexityControlSectionPanel";
import OfficeEgypt from "@/components/pages/sites/offices-sections/egypt/OfficeEgypt";
import BussinessinEgypt from "@/components/pages/sites/offices-sections/egypt/BussinessinEgypt";
import OfficeLocationMapEgypt from "@/components/pages/sites/offices-sections/egypt/OfficeLocationMapEgypt";
import EORServiceSEgypt from "@/components/pages/sites/offices-sections/egypt/EORServiceSEgypt";
import initTranslations from "@/app/i18n";
import EgyptLaborData from "@/components/pages/sites/offices-sections/labor-laws/labor-data/EgyptLaborData";
import { websiteRoutesList } from "@/helpers/routesList";
import { axiosGetJsonSSR } from "@/config/axios";
import serviceimgS1 from "@/assets/images/services/service1.png";

export async function generateMetadata({ params: { locale } }) {
  const canonicalUrl = `https://www.pentabell.com/${locale !== "en" ? `${locale}/` : ""
    }guide-to-hiring-employees-in-egypt/`;

  const languages = {
    fr: `https://www.pentabell.com/fr/guide-to-hiring-employees-in-egypt/`,
    en: `https://www.pentabell.com/guide-to-hiring-employees-in-egypt/`,
    "x-default": `https://www.pentabell.com/guide-to-hiring-employees-in-egypt/`,
  };

  const { t } = await initTranslations(locale, ["servicesByCountry"]);

  try {
    const res = await axiosGetJsonSSR.get(
      `${process.env.NEXT_PUBLIC_BASE_API_URL_SSR}/seoTags/${locale}/guide-to-hiring-employees-in-egypt`
    );
    const seoTags = res?.data?.status === 200;
    if (seoTags) {
      return {
        title: res?.data?.data?.versions[0]?.metaTitle,
        description: res?.data?.data?.versions[0]?.metaDescription,
        robots: res?.data?.data?.robotMeta,
        alternates: {
          canonical: canonicalUrl,
          languages,
        },
      };
    }
  } catch (error) {
    console.error("Error fetching SEO tags:", error);
  }
  return {
    title: t("servicesByCountry:egypt:metaTitle"),
    description: t("servicesByCountry:egypt:metaDescription"),
    alternates: {
      canonical: canonicalUrl,
      languages,
    },
    robots: "follow, index, max-snippet:-1, max-image-preview:large",
  };
}

async function hiringEmployeesEgypteGuide({ params: { locale } }) {
  const { t } = await initTranslations(locale, ["Tunisia", "Egypte"]);

  const SERVICES = [
    {
      id: "s1",
      title: t("Tunisia:services:dataS1:title"),
      description: t("Tunisia:services:dataS1:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.payrollServices.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: serviceimgS1,
      altImg: t("Tunisia:services:dataS1:altImg"),
    },
    {
      id: "s2",
      title: t("Tunisia:services:dataS2:title"),
      description: t("Tunisia:services:dataS2:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.consultingServices.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r4,
      altImg: t("Tunisia:services:dataS2:altImg"),
    },
    {
      id: "s3",
      title: t("Tunisia:services:dataS3:title"),
      description: t("Tunisia:services:dataS3:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.technicalAssistance.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r3,
      altImg: t("Tunisia:services:dataS3:altImg"),
    },
    {
      id: "s4",
      title: t("Tunisia:services:dataS4:title"),
      description: t("Tunisia:services:dataS4:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.aiSourcing.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r2,
      altImg: t("Tunisia:services:dataS4:altImg"),
    },
    {
      id: "s5",
      title: t("Tunisia:services:dataS5:title"),
      description: t("Tunisia:services:dataS5:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.directHiring.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r1,
      altImg: t("Tunisia:services:dataS5:altImg"),
    },
  ];
  return (
    <div>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Organization",
            name: t("contactUs:bureux:contacts:egypte"),
            address: {
              "@type": "PostalAddress",
              streetAddress: "8 El Birgas street, Garden City",
              addressLocality: "Cairo",
              addressCountry: "EG",
            },
            telephone: "+33 1 73 07 42 54",
            email: "<EMAIL>",
            url:
              locale === "en"
                ? "https://www.pentabell.com/guide-to-hiring-employees-in-egypt/"
                : `https://www.pentabell.com/${locale}/guide-to-hiring-employees-in-egypt/`,
          }),
        }}
      />
      <BannerComponents
        title={t("Egypte:title")}
        description={t("Egypte:description")}
        bannerImg={banner}
        altImg={t("Egypte:altImg")}
        height={"100vh"}
      />
      <OurPartners disableTxt={true} />
      <ResponsiveRowTitleText
        title={t("Egypte:intro:title")}
        paragraph={t("Egypte:intro:description")}
      />
      <OfficeEgypt t={t} />
      <BussinessinEgypt t={t} />
      <EORServiceSEgypt t={t} />
      <OfficeLocationMapEgypt t={t} />
      <ComplexityControlSectionPanel t={t} />
      <GlobalHRServicesSection
        title={t("Egypte:services:title")}
        SERVICES={SERVICES}
        defaultImage={serviceimgS1}
      />
      <EgyptLaborData />
      <TunisiaOfficePageForm country={"Egypt"} defaultCountryPhone="eg" />
    </div>
  );
}

export default hiringEmployeesEgypteGuide;
