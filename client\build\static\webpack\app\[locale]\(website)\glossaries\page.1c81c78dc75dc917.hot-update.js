"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/glossaries/page",{

/***/ "(app-pages-browser)/./src/components/sections/GlossaryBanner.jsx":
/*!****************************************************!*\
  !*** ./src/components/sections/GlossaryBanner.jsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Container_IconButton_Input_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Container,IconButton,Input!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Container_IconButton_Input_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Container,IconButton,Input!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Input/Input.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Container_IconButton_Input_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Container,IconButton,Input!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Container_IconButton_Input_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Container,IconButton,Input!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _assets_images_icons_BookIcon_svg__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/assets/images/icons/BookIcon.svg */ \"(app-pages-browser)/./src/assets/images/icons/BookIcon.svg\");\n/* harmony import */ var _assets_images_icons_searchIcon_svg__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/assets/images/icons/searchIcon.svg */ \"(app-pages-browser)/./src/assets/images/icons/searchIcon.svg\");\n/* harmony import */ var _assets_images_icons_refreshIcon_svg__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/assets/images/icons/refreshIcon.svg */ \"(app-pages-browser)/./src/assets/images/icons/refreshIcon.svg\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction GlossaryBanner(param) {\n    let { bannerImg, height, altImg, letters = [], searchWord = \"\", hasContent = true } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [keyword, setKeyword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(searchWord || searchParams?.get(\"word\") || \"\");\n    const handleSearchChange = (e)=>{\n        setKeyword(e.target.value);\n    };\n    const handleSearchClick = ()=>{\n        const params = new URLSearchParams();\n        if (keyword.trim()) {\n            params.set(\"word\", keyword.trim());\n        }\n        const queryString = params.toString();\n        router.push(`${pathname}${queryString ? `?${queryString}` : \"\"}`);\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\") {\n            handleSearchClick();\n        }\n    };\n    const handleClearSearch = ()=>{\n        setKeyword(\"\");\n        router.push(pathname);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"glossary-banner\",\n        className: \"center-banner\",\n        style: {\n            backgroundImage: `url(${bannerImg.src})`,\n            height: height\n        },\n        children: [\n            altImg && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                width: 0,\n                height: 0,\n                alt: altImg,\n                src: \"\",\n                style: {\n                    display: \"none\"\n                },\n                loading: \"lazy\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                lineNumber: 61,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_IconButton_Input_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"top-section custom-max-width\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_BookIcon_svg__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"heading-h1 text-white page-title\",\n                        children: t(\"glossary:banner:title\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"glossary-search\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_IconButton_Input_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"glossary-search-input\",\n                                type: \"text\",\n                                placeholder: \"What are you looking for ?\",\n                                onChange: handleSearchChange,\n                                onKeyDown: handleKeyDown,\n                                value: keyword,\n                                endAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_IconButton_Input_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    onClick: handleClearSearch,\n                                    edge: \"end\",\n                                    sx: {\n                                        color: \"#1976d2\",\n                                        \"&:hover\": {\n                                            backgroundColor: \"rgba(25, 118, 210, 0.04)\"\n                                        }\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_refreshIcon_svg__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 17\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 15\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_IconButton_Input_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"btn-search\",\n                                onClick: handleSearchClick,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_searchIcon_svg__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this),\n                    hasContent && letters?.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"letters\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"letter selected\",\n                                children: \"#\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                                lineNumber: 105,\n                                columnNumber: 13\n                            }, this),\n                            letters.map((letter, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: `#${letter}`,\n                                    className: \"letter\",\n                                    children: letter\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 15\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, this),\n                    !hasContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"letters-empty\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                color: \"white\",\n                                opacity: 0.8,\n                                fontSize: \"14px\",\n                                marginTop: \"20px\"\n                            },\n                            children: searchWord ? `No results found for \"${searchWord}\"` : \"No glossary terms available\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                            lineNumber: 116,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n_s(GlossaryBanner, \"XcJDQxtYUQazr3yog0GQznqGJAQ=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = GlossaryBanner;\n/* harmony default export */ __webpack_exports__[\"default\"] = (GlossaryBanner);\nvar _c;\n$RefreshReg$(_c, \"GlossaryBanner\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/GlossaryBanner.jsx\n"));

/***/ })

});