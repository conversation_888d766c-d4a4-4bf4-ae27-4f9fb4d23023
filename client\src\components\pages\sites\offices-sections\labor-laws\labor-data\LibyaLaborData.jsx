"use client";
import LaborLaws from "../LaborLaws";
import { createList } from '@/utils/functions';
export const LABOR_KEYS = {
    workingHours: {
        title: "libya:libyaLabor:workingHours:title",
    },
    employmentContracts: {
        title: "libya:libyaLabor:employmentContracts:title",
    },

};
export const libyaLaborData = {
    title: "libya:libyaLabor:title",
    sections: [
        {
            id: 1,
            title: LABOR_KEYS.workingHours.title,
            subsections: [
                {
                    title: "libya:libyaLabor:workingHours:subTitle1",
                    description: "libya:libyaLabor:workingHours:description",
                },
                {
                    title: "libya:libyaLabor:workingHours:subTitle2",
                    description: "libya:libyaLabor:workingHours:description2",
                },
            ],
        },
        {
            id: 2,
            title: LABOR_KEYS.employmentContracts.title,
            subsections: [
                {
                    title: "libya:libyaLabor:employmentContracts:title1",
                },
                {
                    list: createList(["libya:libyaLabor:employmentContracts:data1", "libya:libyaLabor:employmentContracts:data2"]),
                },

            ],
        },
        {
            id: 3,
            title: "libya:libyaLabor:payroll:title",
            type: "payroll",
            titleKey: "libya:libyaLabor:payroll:title1",
            descriptionKey: "libya:libyaLabor:payroll:description",
            items: [
                {
                    titleKey: "libya:libyaLabor:payroll:fiscalYear:title",
                    dateKeys: ["libya:libyaLabor:payroll:fiscalYear:date1", "libya:libyaLabor:payroll:fiscalYear:date2"],
                    descriptionKey: "libya:libyaLabor:payroll:fiscalYear:description",
                },
                {
                    titleKey: "libya:libyaLabor:payroll:payrollCycle:title",
                    dateKeys: ["libya:libyaLabor:payroll:payrollCycle:date"],
                    descriptionKey: "libya:libyaLabor:payroll:payrollCycle:description",
                },
                {
                    titleKey: "libya:libyaLabor:payroll:minimumWage:title",
                    dateKeys: ["libya:libyaLabor:payroll:minimumWage:wage", "libya:libyaLabor:payroll:minimumWage:date"],
                    descriptionKey: "libya:libyaLabor:payroll:minimumWage:description",
                },
                {
                    titleKey: "libya:libyaLabor:payroll:payrollManagement:title",
                    dateKeys: ["libya:libyaLabor:payroll:payrollManagement:date1", "libya:libyaLabor:payroll:payrollManagement:date2"],
                    descriptionKey: "libya:libyaLabor:payroll:payrollManagement:description",
                },


            ],
        },
        {
            id: 4,
            title: "libya:libyaLabor:termination:title",
            subsections: [
                {
                    title: "libya:libyaLabor:termination:title1",
                    description: "libya:libyaLabor:termination:description1",
                },
                {
                    title: "libya:libyaLabor:termination:title2",
                    description: "libya:libyaLabor:termination:description2",

                },
                {
                    list: createList(["libya:libyaLabor:termination:data1", "libya:libyaLabor:termination:data2", "libya:libyaLabor:termination:data3", "libya:libyaLabor:termination:data4"]),
                },

                {
                    title: "libya:libyaLabor:termination:title3",
                    description: "libya:libyaLabor:termination:description3",
                },

                {
                    title: "libya:libyaLabor:termination:title4",
                    description: "libya:libyaLabor:termination:description4",

                },


            ],
        },
        {
            id: 5,
            title: "libya:libyaLabor:leaveEntitlements:title",
            type: "leaveEntitlements",
            data: {
                description: "libya:libyaLabor:leaveEntitlements:description",
                subTitle: "libya:libyaLabor:leaveEntitlements:subTitle",
                subDescription: "libya:libyaLabor:leaveEntitlements:subDescription",




                leaves: [
                    { date: "libya:libyaLabor:leaveEntitlements:leaves:dataS1:date", title: "libya:libyaLabor:leaveEntitlements:leaves:dataS1:title" },
                    { date: "libya:libyaLabor:leaveEntitlements:leaves:dataS2:date", title: "libya:libyaLabor:leaveEntitlements:leaves:dataS2:title" },
                    { date: "libya:libyaLabor:leaveEntitlements:leaves:dataS3:date", title: "libya:libyaLabor:leaveEntitlements:leaves:dataS3:title" },
                    { date: "libya:libyaLabor:leaveEntitlements:leaves:dataS4:date", title: "libya:libyaLabor:leaveEntitlements:leaves:dataS4:title" },
                    { date: "libya:libyaLabor:leaveEntitlements:leaves:dataS5:date", title: "libya:libyaLabor:leaveEntitlements:leaves:dataS5:title" },
                    { date: "libya:libyaLabor:leaveEntitlements:leaves:dataS6:date", title: "libya:libyaLabor:leaveEntitlements:leaves:dataS6:title" },
                    { date: "libya:libyaLabor:leaveEntitlements:leaves:dataS7:date", title: "libya:libyaLabor:leaveEntitlements:leaves:dataS7:title" },
                    { date: "libya:libyaLabor:leaveEntitlements:leaves:dataS8:date", title: "libya:libyaLabor:leaveEntitlements:leaves:dataS8:title" },
                    { date: "libya:libyaLabor:leaveEntitlements:leaves:dataS9:date", title: "libya:libyaLabor:leaveEntitlements:leaves:dataS9:title" },
                    { date: "libya:libyaLabor:leaveEntitlements:leaves:dataS10:date", title: "libya:libyaLabor:leaveEntitlements:leaves:dataS10:title" },
                    { date: "libya:libyaLabor:leaveEntitlements:leaves:dataS11:date", title: "libya:libyaLabor:leaveEntitlements:leaves:dataS11:title" },

                ],
                paidTimeOff: {
                    title: "libya:libyaLabor:leaveEntitlements:leaves:paidTimeOff:title",
                    description: "libya:libyaLabor:leaveEntitlements:leaves:paidTimeOff:description1",
                },

                maternityLeave: {
                    title: "libya:libyaLabor:leaveEntitlements:leaves:maternityLeave:title",
                    description: ["libya:libyaLabor:leaveEntitlements:leaves:maternityLeave:description1"],
                },
                sickLeave: {
                    title: "libya:libyaLabor:leaveEntitlements:leaves:sickLeave:title",
                    description: "libya:libyaLabor:leaveEntitlements:leaves:sickLeave:description",
                },
            },
        },
        {
            id: 6,
            title: "libya:libyaLabor:tax:title",
            subsections: [
                { description: "libya:libyaLabor:tax:description" },
                {
                    title: "libya:libyaLabor:tax:title1",
                    description: "libya:libyaLabor:tax:description1"

                },
                {
                    list: createList(["libya:libyaLabor:tax:data1"]),
                },
                {
                    title: "libya:libyaLabor:tax:title2",
                    description: "libya:libyaLabor:tax:description2",
                    list: createList(["libya:libyaLabor:tax:dataS1", "libya:libyaLabor:tax:dataS2", "libya:libyaLabor:tax:dataS3"]),
                },
            ],
        },
        {
            id: 7,
            title: "libya:libyaLabor:visa:title",
            subsections: [
                { description: "libya:libyaLabor:visa:description1" },
                { description: "libya:libyaLabor:visa:description2" },
                { title: "libya:libyaLabor:visa:title1" },
                {
                    title: "libya:libyaLabor:visa:title2",
                    description: "libya:libyaLabor:visa:description3"
                },
                { title: "libya:libyaLabor:visa:title3", description: "libya:libyaLabor:visa:description4" },
                {
                    list: createList(["libya:libyaLabor:visa:data1", "libya:libyaLabor:visa:data2", "libya:libyaLabor:visa:data3", "libya:libyaLabor:visa:data4", "libya:libyaLabor:visa:data5", "libya:libyaLabor:visa:data6", "libya:libyaLabor:visa:data7", "libya:libyaLabor:visa:data8"]),
                },

            ],
        },
    ],
};


export default function LibyaLaborData() {
    return <LaborLaws data={libyaLaborData} />;
}
