import OfficeLocationMap from "@/components/ui/OfficeLocationMap";

function OfficeLocationMapEgypt({ t }) {
  return (
    <OfficeLocationMap
      title={t("Egypte:officeLocation:label")}
      subtitle={t("Egypte:officeLocation:title")}
      address={t("Egypte:officeLocation:address")}
      tel={t("Egypte:officeLocation:tel1")}
      email={t("Egypte:officeLocation:mail")}
      linkText={t("Egypte:officeLocation:talk")}
      mapSrc="https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d6908.10051409472!2d31.232283!3d30.035415999999998!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x145840ccc19a9c45%3A0x9eaeba178c16fd08!2s8%20Al%20Bergas%2C%20Qasr%20El%20Nil%2C%20Cairo%20Governorate%204272015%2C%20%C3%89gypte!5e0!3m2!1sfr!2sus!4v1728580479664!5m2!1sfr!2sus"
    />
  );
}
export default OfficeLocationMapEgypt;


