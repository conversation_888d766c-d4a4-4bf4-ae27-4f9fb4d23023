/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/layout",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cshared%5C%5Clib%5C%5Clazy-dynamic%5C%5Cdynamic-bailout-to-csr.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cshared%5C%5Clib%5C%5Clazy-dynamic%5C%5Cpreload-css.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Cnode_modules%5C%5Creact-toastify%5C%5Cdist%5C%5Creact-toastify.esm.mjs%22%2C%22ids%22%3A%5B%22ToastContainer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Cnode_modules%5C%5Creact-toastify%5C%5Cdist%5C%5CReactToastify.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Clayouts%5C%5CFooter.jsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CTranslationProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Csrc%5C%5Clib%5C%5Creact-query-client.js%22%2C%22ids%22%3A%5B%22ReactQueryProvider%22%5D%7D&server=false!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cshared%5C%5Clib%5C%5Clazy-dynamic%5C%5Cdynamic-bailout-to-csr.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cshared%5C%5Clib%5C%5Clazy-dynamic%5C%5Cpreload-css.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Cnode_modules%5C%5Creact-toastify%5C%5Cdist%5C%5Creact-toastify.esm.mjs%22%2C%22ids%22%3A%5B%22ToastContainer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Cnode_modules%5C%5Creact-toastify%5C%5Cdist%5C%5CReactToastify.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Clayouts%5C%5CFooter.jsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CTranslationProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Csrc%5C%5Clib%5C%5Creact-query-client.js%22%2C%22ids%22%3A%5B%22ReactQueryProvider%22%5D%7D&server=false! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/script.js */ \"(app-pages-browser)/./node_modules/next/dist/client/script.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-toastify/dist/react-toastify.esm.mjs */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-toastify/dist/ReactToastify.css */ \"(app-pages-browser)/./node_modules/react-toastify/dist/ReactToastify.css\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layouts/Footer.jsx */ \"(app-pages-browser)/./src/components/layouts/Footer.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/TranslationProvider.js */ \"(app-pages-browser)/./src/components/TranslationProvider.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/lib/react-query-client.js */ \"(app-pages-browser)/./src/lib/react-query-client.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cshared%5C%5Clib%5C%5Clazy-dynamic%5C%5Cdynamic-bailout-to-csr.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cshared%5C%5Clib%5C%5Clazy-dynamic%5C%5Cpreload-css.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Cnode_modules%5C%5Creact-toastify%5C%5Cdist%5C%5Creact-toastify.esm.mjs%22%2C%22ids%22%3A%5B%22ToastContainer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Cnode_modules%5C%5Creact-toastify%5C%5Cdist%5C%5CReactToastify.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5Clayouts%5C%5CFooter.jsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Csrc%5C%5Ccomponents%5C%5CTranslationProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Csrc%5C%5Clib%5C%5Creact-query-client.js%22%2C%22ids%22%3A%5B%22ReactQueryProvider%22%5D%7D&server=false!\n"));

/***/ })

});