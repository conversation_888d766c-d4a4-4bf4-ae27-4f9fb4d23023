"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const authentication_middleware_1 = __importDefault(require("@/middlewares/authentication.middleware"));
const account_service_1 = __importDefault(require("../services/account.service"));
const findUser_middleware_1 = __importDefault(require("@/middlewares/findUser.middleware"));
const upload_middleware_1 = require("../../../middlewares/upload.middleware");
const validateApiKey_middleware_1 = __importDefault(require("@/middlewares/validateApiKey.middleware"));
const authorization_middleware_1 = require("@/middlewares/authorization.middleware");
const constants_1 = require("@/utils/helpers/constants");
const messages_1 = require("@/utils/helpers/messages");
class AccountController {
    constructor() {
        this.path = '/account';
        this.router = (0, express_1.Router)();
        this.accountService = new account_service_1.default();
        this.getAccount = async (request, response, next) => {
            try {
                const id = request.user._id;
                const result = await this.accountService.getCurrentUser({ _id: id });
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.updateAccount = async (request, response, next) => {
            try {
                const id = request.user._id;
                const updateAccountData = request.body;
                const isAvatarFieldExists = !!request.file;
                let file = null;
                if (isAvatarFieldExists)
                    file = request.file;
                await this.accountService.update(updateAccountData, id, file);
                response.send({
                    message: messages_1.MESSAGES.USER.USER_UPDATED,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.updatePassword = async (request, response, next) => {
            try {
                const id = request.user._id;
                const updatePasswordData = request.body;
                const result = await this.accountService.updatePassword(updatePasswordData, id);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.getAllUsers = async (request, response, next) => {
            try {
                const users = await this.accountService.getAllUsers();
                response.status(200).send(users);
            }
            catch (error) {
                next(error);
            }
        };
        this.getAllCandidats = async (request, response, next) => {
            try {
                const candidats = await this.accountService.getAllCandidats();
                response.status(200).send(candidats);
            }
            catch (error) {
                next(error);
            }
        };
        this.initialiseRoutes();
    }
    initialiseRoutes() {
        this.router.get(`${this.path}`, validateApiKey_middleware_1.default, authentication_middleware_1.default, findUser_middleware_1.default, validateApiKey_middleware_1.default, this.getAccount);
        this.router.put(`${this.path}`, authentication_middleware_1.default, upload_middleware_1.uploadMiddleware, this.updateAccount);
        this.router.put(`${this.path}/password`, authentication_middleware_1.default, this.updatePassword);
        this.router.get(`${this.path}/candidats`, validateApiKey_middleware_1.default, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN]), this.getAllCandidats);
        this.router.get(`${this.path}/users`, validateApiKey_middleware_1.default, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN]), this.getAllUsers);
    }
}
exports.default = AccountController;
//# sourceMappingURL=account.controller.js.map