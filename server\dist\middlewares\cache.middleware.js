"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCachedData = getCachedData;
exports.validateCache = validateCache;
exports.invalidateCache = invalidateCache;
const node_cache_1 = __importDefault(require("node-cache"));
const apiCache = new node_cache_1.default({ stdTTL: 86400 });
function getCachedData() {
    const allKeys = apiCache.keys();
    return apiCache.mget(allKeys);
}
function validateCache(request, response, next) {
    const key = request.originalUrl || request.url;
    const cachedResponse = apiCache.get(key);
    if (cachedResponse) {
        return response.send(cachedResponse);
    }
    else {
        response.originalSend = response.send;
        response.send = (body) => {
            apiCache.set(key, body);
            response.originalSend(body);
        };
        next();
    }
}
function invalidateCache(request, response, next) {
    try {
        const key = request.originalUrl || request.url;
        const baseKey = key.split('/').slice(3, 6).join('/');
        const keysToInvalidate = apiCache.keys().filter(k => k.includes(baseKey.split('/')[0]) || k.includes(baseKey.split('/')[1]));
        if (keysToInvalidate.length > 0) {
            keysToInvalidate.forEach(k => apiCache.del(k));
            console.log(`Cache invalidated for keys: ${keysToInvalidate.join(', ')}`);
        }
        else {
            console.log(`No cache keys to invalidate for base key: ${baseKey}`);
        }
    }
    catch (error) {
        console.error(`Error invalidating cache for URL: ${request.url}`, error);
    }
    finally {
        next();
    }
}
//# sourceMappingURL=cache.middleware.js.map