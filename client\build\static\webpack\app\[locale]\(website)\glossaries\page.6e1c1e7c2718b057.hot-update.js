"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/glossaries/page",{

/***/ "(app-pages-browser)/./src/components/sections/GlossaryBanner.jsx":
/*!****************************************************!*\
  !*** ./src/components/sections/GlossaryBanner.jsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Container_Input_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Container,Input!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Container_Input_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Container,Input!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Input/Input.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Container_Input_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Container,Input!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _assets_images_icons_BookIcon_svg__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/assets/images/icons/BookIcon.svg */ \"(app-pages-browser)/./src/assets/images/icons/BookIcon.svg\");\n/* harmony import */ var _assets_images_icons_searchIcon_svg__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/assets/images/icons/searchIcon.svg */ \"(app-pages-browser)/./src/assets/images/icons/searchIcon.svg\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction GlossaryBanner(param) {\n    let { bannerImg, height, altImg, letters = [], searchWord = \"\", hasContent = true } = param;\n    _s();\n    const searchQueryParams = new URLSearchParams();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [keyword, setKeyword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(searchWord || searchParams?.get(\"word\") || \"\");\n    const handleSearchChange = (e)=>{\n        setKeyword(e.target.value);\n    };\n    const handleSearchClick = ()=>{\n        const params = new URLSearchParams();\n        if (keyword.trim()) {\n            params.set(\"word\", keyword.trim());\n        }\n        const queryString = params.toString();\n        router.push(`${pathname}${queryString ? `?${queryString}` : \"\"}`);\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\") {\n            handleSearchClick();\n        }\n    };\n    const handleClearSearch = ()=>{\n        setKeyword(\"\");\n        router.push(pathname);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"glossary-banner\",\n        className: \"center-banner\",\n        style: {\n            backgroundImage: `url(${bannerImg.src})`,\n            height: height\n        },\n        children: [\n            altImg && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                width: 0,\n                height: 0,\n                alt: altImg,\n                src: \"\",\n                style: {\n                    display: \"none\"\n                },\n                loading: \"lazy\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                lineNumber: 58,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_Input_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"top-section custom-max-width\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_BookIcon_svg__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"heading-h1 text-white page-title\",\n                        children: t(\"glossar\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"glossary-search\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_Input_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"glossary-search-input\",\n                                type: \"text\",\n                                placeholder: \"What are you looking for ?\",\n                                onChange: handleSearchChange,\n                                onKeyDown: handleKeyDown,\n                                value: keyword\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_Input_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"btn-search\",\n                                onClick: handleSearchClick,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_searchIcon_svg__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    hasContent && letters?.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"letters\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"letter selected\",\n                                children: \"#\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this),\n                            letters.map((letter, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: `#${letter}`,\n                                    className: \"letter\",\n                                    children: letter\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 15\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, this),\n                    !hasContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"letters-empty\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                color: \"white\",\n                                opacity: 0.8,\n                                fontSize: \"14px\",\n                                marginTop: \"20px\"\n                            },\n                            children: searchWord ? `No results found for \"${searchWord}\"` : \"No glossary terms available\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                            lineNumber: 99,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                        lineNumber: 98,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n_s(GlossaryBanner, \"5cK5uZLgiTLM+7PkB8Pjb+OyIXg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = GlossaryBanner;\n/* harmony default export */ __webpack_exports__[\"default\"] = (GlossaryBanner);\nvar _c;\n$RefreshReg$(_c, \"GlossaryBanner\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/GlossaryBanner.jsx\n"));

/***/ })

});