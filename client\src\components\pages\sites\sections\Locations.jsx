"use client"
import { Container } from "@mui/material";

import SvglocationPin from "@/assets/images/icons/locationPin.svg";
import SvgcallUs from "@/assets/images/icons/callUs.svg";
import Svgemail from "@/assets/images/icons/email.svg";
import { useTranslation } from "react-i18next";

function Locations() {
  const { t } = useTranslation();

  return (
    <Container id="locations-section"  className="custom-max-width">
      <h2 className="heading-h1">
       {t('contactUs:locations:title')}
      </h2>
      <p className="sub-heading">
      {t('contactUs:locations:description')}
      </p>
      <div className="locations">
        <div className="location-item">
          <p className="label">
            {" "}
            <SvglocationPin /> {t('contactUs:locations:address')}
          </p>
          <p className="value paragraph">
           {t('contactUs:locations:addressDescription')}
          </p>
        </div>
        <div className="location-item">
          <p className="label">
            {" "}
            <SvgcallUs /> {t('contactUs:locations:call')}
          </p>
          <p className="value paragraph">+33 1 73 07 42 54</p>
        </div>
        <div className="location-item">
          <p className="label">
            {" "}
            <Svgemail /> Email
          </p>
          <p className="value paragraph"><EMAIL></p>
        </div>
      </div>
    </Container>
  );
}

export default Locations;
