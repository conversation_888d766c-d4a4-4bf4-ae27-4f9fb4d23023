"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateOpeningResponseDTO = exports.OpportunityDTO = exports.OpportunityVersionDTO = void 0;
const constants_1 = require("@/utils/helpers/constants");
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
class User {
    constructor(user = {}) {
        Object.assign(this, user);
    }
}
__decorate([
    (0, class_transformer_1.Expose)({ name: '_id' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], User.prototype, "id", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'firstName' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], User.prototype, "firstName", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'lastName' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], User.prototype, "lastName", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Object)
], User.prototype, "buffer", void 0);
class OpportunityVersionDTO {
    constructor(opportunityDTO = {}) {
        Object.assign(this, opportunityDTO);
    }
}
exports.OpportunityVersionDTO = OpportunityVersionDTO;
__decorate([
    (0, class_transformer_1.Expose)({ name: '_id' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], OpportunityVersionDTO.prototype, "id", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'language' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], OpportunityVersionDTO.prototype, "language", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'title' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], OpportunityVersionDTO.prototype, "title", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'jobDescription' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], OpportunityVersionDTO.prototype, "description", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'gender' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], OpportunityVersionDTO.prototype, "gender", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'shareOnSocialMedia' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], OpportunityVersionDTO.prototype, "share", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'createdBy' }),
    (0, class_transformer_1.Type)(() => User),
    __metadata("design:type", User)
], OpportunityVersionDTO.prototype, "userId", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'url' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], OpportunityVersionDTO.prototype, "url", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'isPublished' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], OpportunityVersionDTO.prototype, "published", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'visibility' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], OpportunityVersionDTO.prototype, "visibility", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'createdAt' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], OpportunityVersionDTO.prototype, "creationDate", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'updatedAt' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], OpportunityVersionDTO.prototype, "updateDate", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'publishDate' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], OpportunityVersionDTO.prototype, "publishedAt", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Object)
], OpportunityVersionDTO.prototype, "buffer", void 0);
class OpportunityDTO {
    constructor(opportunityDTO = {}) {
        Object.assign(this, opportunityDTO);
    }
}
exports.OpportunityDTO = OpportunityDTO;
__decorate([
    (0, class_transformer_1.Expose)({ name: '_id' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], OpportunityDTO.prototype, "id", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'OpportunityVersions' }),
    (0, class_validator_1.IsArray)(),
    (0, class_transformer_1.Type)(() => OpportunityVersionDTO),
    __metadata("design:type", Array)
], OpportunityDTO.prototype, "versions", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'applicants' }),
    (0, class_validator_1.IsArray)(),
    (0, class_transformer_1.Type)(() => User),
    __metadata("design:type", Array)
], OpportunityDTO.prototype, "candidates", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'status' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], OpportunityDTO.prototype, "applicationStatus", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'dateOfExpiration' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], OpportunityDTO.prototype, "expirationDate", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'dateOfRequisition' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], OpportunityDTO.prototype, "requisitionDate", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'industry' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], OpportunityDTO.prototype, "sector", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'country' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", Object)
], OpportunityDTO.prototype, "location", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'reference' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], OpportunityDTO.prototype, "ref", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'contractType' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], OpportunityDTO.prototype, "contract", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'minExperience' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], OpportunityDTO.prototype, "minExperience", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'maxExperience' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], OpportunityDTO.prototype, "maxExperience", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'createdAt' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], OpportunityDTO.prototype, "creationDate", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'updatedAt' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], OpportunityDTO.prototype, "updatedDate", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Number)
], OpportunityDTO.prototype, "__v", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Object)
], OpportunityDTO.prototype, "buffer", void 0);
class UpdateOpeningResponseDTO {
    constructor(updateOpeningResponseDTO = {}) {
        Object.assign(this, updateOpeningResponseDTO);
    }
}
exports.UpdateOpeningResponseDTO = UpdateOpeningResponseDTO;
__decorate([
    (0, class_transformer_1.Expose)({ name: '_id' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateOpeningResponseDTO.prototype, "id", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Array)
], UpdateOpeningResponseDTO.prototype, "OpportunityVersions", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Array)
], UpdateOpeningResponseDTO.prototype, "applicants", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'status' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateOpeningResponseDTO.prototype, "applicationStatus", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'dateOfExpiration' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], UpdateOpeningResponseDTO.prototype, "expirationDate", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'dateOfRequisition' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], UpdateOpeningResponseDTO.prototype, "requisitionDate", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'industry' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateOpeningResponseDTO.prototype, "sector", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'country' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", Object)
], UpdateOpeningResponseDTO.prototype, "location", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'reference' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateOpeningResponseDTO.prototype, "ref", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'contractType' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateOpeningResponseDTO.prototype, "contract", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'minExperience' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UpdateOpeningResponseDTO.prototype, "minExperience", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'maxExperience' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UpdateOpeningResponseDTO.prototype, "maxExperience", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'createdAt' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], UpdateOpeningResponseDTO.prototype, "creationDate", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'updatedAt' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], UpdateOpeningResponseDTO.prototype, "updatedDate", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Number)
], UpdateOpeningResponseDTO.prototype, "__v", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Object)
], UpdateOpeningResponseDTO.prototype, "buffer", void 0);
//# sourceMappingURL=opportunity.dto.js.map