"use client";
import { useEffect, useState, useRef } from "react";
import { useRouter } from "next/navigation";
import moment from "moment";
import useCurrentUser from "@/features/auth/hooks/currentUser.hooks";
import { axiosGetJson } from "@/config/axios";
import { io } from "socket.io-client";
import { findnotificationColoredIcon } from "@/utils/functions";
import {
  baseUrlBackoffice,
  baseUrlFrontoffice,
  commonRoutes,
} from "../../helpers/routesList";
import { Role } from "../../utils/constants";
import { useTheme, useMediaQuery, Tooltip } from "@mui/material";
import { Menu } from "@mui/material";
import CustomButton from "@/components/ui/CustomButton";
import SvgNotifications from "@/assets/images/icons/notifications.svg";

const NotificationComponent = ({
  locale,
  websiteHeaderIcon,
  pathname,
  isDetailBlogPath,
}) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const { user } = useCurrentUser();
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const socket = useRef(null);
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const handleCloseMenu = () => {
    setAnchorEl(null);
  };

  useEffect(() => {
    isMobile && handleCloseMenu();
  }, [isMobile]);
  const formatDuration = (receivedTime) => {
    const duration = moment.duration(moment().diff(moment(receivedTime)));
  
    if (duration.asMonths() >= 1) {
      return `${Math.floor(duration.asMonths())}mois`;
    } else if (duration.asDays() >= 1) {
      return `${Math.floor(duration.asDays())}j`;
    } else if (duration.asHours() >= 1) {
      return `${Math.floor(duration.asHours())}h`;
    } else {
      return `${Math.floor(duration.minutes())}min`;
    }
  };
  const openNotif = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleNotificationClick = async (notification) => {
    try {
      notification.isRead = true;

      await axiosGetJson.put(
        `${process.env.NEXT_PUBLIC_BASE_API_URL}/notifications/${notification._id}`,
        notification
      );

      setUnreadCount(unreadCount - 1);
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
    }
    if (notification?.link && notification?.link !== "http://localhost:3000") {
      window.location.href = notification.link;
    }
  };

  const handleSeeAllClick = () => {
    handleCloseMenu();
    if (
      user?.roles?.includes(Role.ADMIN) ||
      user?.roles?.includes(Role.EDITOR)
    ) {
      router.push(
        `/${baseUrlBackoffice.baseURL.route}/${commonRoutes.notifications.route}/`
      );
    } else if (user?.roles?.includes(Role.CANDIDATE)) {
      router.push(
        `/${baseUrlFrontoffice.baseURL.route}/${commonRoutes.notifications.route}/`
      );
    }
  };

  useEffect(() => {
    socket.current = io(process.env.NEXT_PUBLIC_BASE_URL, {
      path: "/api/v1/socket.io",
      withCredentials: true,
    });

    socket.current.on("initial-notifications", (initialNotifications) => {
      const formattedNotifications = initialNotifications.map(
        (notification) => ({
          ...notification,
          receivedTime: formatDuration(notification.receivedTime),
        })
      );
      setNotifications(formattedNotifications);
      setUnreadCount(
        formattedNotifications.filter((notification) => !notification.isRead)
          .length
      );
    });

    socket.current.on("notification", (notification) => {
      const formattedNotification = {
        ...notification[0],
        receivedTime: formatDuration(notification[0].receivedTime),
      };
      setNotifications((prevNotifications) => {
        const updatedNotifications = [
          formattedNotification,
          ...prevNotifications,
        ];
        return updatedNotifications.slice(0, 4);
      });
      setUnreadCount((prevCount) => prevCount + 1);
    });

    return () => {
      socket.current.disconnect();
    };
  }, []);

  const handleMarkAllAsRead = async () => {
    try {
      await axiosGetJson.put(
        `${process.env.NEXT_PUBLIC_BASE_API_URL}/notifications`,
        { isRead: true }
      );
      setNotifications((prevNotifications) =>
        prevNotifications.map((notification) => ({
          ...notification,
          isRead: (notification.isRead = true),
        }))
      );
      setUnreadCount(0);
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
    }
  };

  const handleMarkOneAsRead = async (id, notification) => {
    try {
      notification.isRead = true;

      // await axiosGetJson.put(
      //   `${process.env.NEXT_PUBLIC_BASE_API_URL}/notifications/${id}`,
      //   { isRead: true }
      // );
      // setNotifications((prevNotifications) =>
      //   prevNotifications.map((notification) => ({
      //     ...notification,
      //     isRead: (notification.isRead = true),
      //   }))
      // );
      // setUnreadCount(0);
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
    }
  };
  return (
    <>
      <div className="notif-section">
        {unreadCount && unreadCount > 0 ? (
          <span
            className={websiteHeaderIcon ? "notif-nbr-header" : "notif-nbr"}
          >
            {unreadCount > 99 ? "+99" : unreadCount}
          </span>
        ) : null}

        {websiteHeaderIcon ? (
          <Tooltip title="Notifications">
            <CustomButton
              id="Notifications"
              onClick={openNotif}
              icon={<SvgNotifications />}
              locale={locale}
              className={
                pathname.includes("/notifications")
                  ? "btn btn-notif navbar-link active"
                  : isDetailBlogPath()
                  ? "btn btn-notif navbar-link whiteBg"
                  : "btn btn-notif navbar-link"
              }
            />
          </Tooltip>
        ) : (
          <Tooltip title="Notifications">
            <CustomButton
              onClick={openNotif}
              id="Notifications"
              icon={<SvgNotifications />}
              locale={locale}
              className={" btn btn-link"}
            />
          </Tooltip>
        )}
      </div>
      <Menu
        id="account-menu"
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleCloseMenu}
        slotProps={{
          paper: {
            elevation: 0,
            sx: {
              backgroundColor: "#e4effc",
              overflow: "visible",
              filter: "drop-shadow(0px 2px 8px rgba(0,0,0,0.32))",
              mt: 1.5,
              "& .MuiAvatar-root": {
                width: 32,
                height: 32,
                ml: -0.5,
                mr: 1,
              },
              "&::before": {
                content: '""',
                display: "block",
                position: "absolute",
                top: 0,
                right: 14,
                width: 10,
                height: 10,
                bgcolor: "#e4effc",
                transform: "translateY(-50%) rotate(45deg)",
                zIndex: 0,
              },
            },
          },
        }}
        transformOrigin={{ horizontal: "right", vertical: "top" }}
        anchorOrigin={{ horizontal: "right", vertical: "bottom" }}
      >
        <div id="notifications">
          <div className="container-alerts">
            <div className="notifications-alerts">
              <div className="header-alert">
                <h2>
                  <span className="title">Notifications</span>
                  {unreadCount > 0 && (
                    <span className="unread-notification-number">
                      {unreadCount}
                    </span>
                  )}
                </h2>
                <p onClick={handleMarkAllAsRead}>Mark all as read</p>
              </div>
              <div className="body-alert">
                {notifications.map((notification) => (
                  <div
                    key={notification._id}
                    className={`notification ${
                      !notification.isRead ? "unreaded" : "readed"
                    }`}
                    onClick={() => handleNotificationClick(notification)}
                  >
                    <div className="avatar">
                      {findnotificationColoredIcon(notification.type)}
                    </div>
                    <div className="text">
                      <div className="text-top">
                        <p>
                          <span className="profil-name">
                            {notification.senderName}
                          </span>
                          <span className="message-notif">
                            {notification.message}
                          </span>
                          <span className="notification-description-message">
                            {notification.description}
                          </span>
                        </p>
                        <div className="notif-details">
                          <span className="notification-timestamp">
                            {notification.receivedTime}
                          </span>
                          {!notification.isRead && (
                            <span className="unread-dot"></span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              <button
                onClick={handleSeeAllClick}
                className="see-all"
                type="button"
              >
                See All
              </button>
            </div>
          </div>
        </div>
      </Menu>
    </>
  );
};

export default NotificationComponent;
