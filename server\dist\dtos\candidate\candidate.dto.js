"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CandidateDTO = exports.ExperienceDTO = exports.EducationDTO = exports.CertificationDTO = void 0;
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const user_dto_1 = require("./../user/user.dto");
class CertificationDTO {
    constructor(certification = {}) {
        Object.assign(this, certification);
    }
}
exports.CertificationDTO = CertificationDTO;
__decorate([
    (0, class_transformer_1.Expose)({ name: '_id' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CertificationDTO.prototype, "id", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'academy' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CertificationDTO.prototype, "academy", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'endDate' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CertificationDTO.prototype, "endDate", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'startDate' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CertificationDTO.prototype, "startDate", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'title' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CertificationDTO.prototype, "title", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'createdAt' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], CertificationDTO.prototype, "creationDate", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'updatedAt' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], CertificationDTO.prototype, "updateDate", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Number)
], CertificationDTO.prototype, "__v", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Object)
], CertificationDTO.prototype, "buffer", void 0);
class EducationDTO {
    constructor(certification = {}) {
        Object.assign(this, certification);
    }
}
exports.EducationDTO = EducationDTO;
__decorate([
    (0, class_transformer_1.Expose)({ name: '_id' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], EducationDTO.prototype, "id", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'degree' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], EducationDTO.prototype, "degree", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'endDate' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], EducationDTO.prototype, "endDate", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'startDate' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], EducationDTO.prototype, "startDate", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'university' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], EducationDTO.prototype, "university", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'fieldOfStudy' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], EducationDTO.prototype, "fieldOfStudy", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'createdAt' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], EducationDTO.prototype, "creationDate", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'updatedAt' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], EducationDTO.prototype, "updateDate", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Number)
], EducationDTO.prototype, "__v", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Object)
], EducationDTO.prototype, "buffer", void 0);
class ExperienceDTO {
    constructor(certification = {}) {
        Object.assign(this, certification);
    }
}
exports.ExperienceDTO = ExperienceDTO;
__decorate([
    (0, class_transformer_1.Expose)({ name: '_id' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ExperienceDTO.prototype, "id", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'company' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ExperienceDTO.prototype, "company", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'contractType' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ExperienceDTO.prototype, "contractType", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'duration' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ExperienceDTO.prototype, "duration", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'endDate' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ExperienceDTO.prototype, "endDate", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'location' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ExperienceDTO.prototype, "location", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'startDate' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ExperienceDTO.prototype, "startDate", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'title' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ExperienceDTO.prototype, "title", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'jobDescription' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ExperienceDTO.prototype, "jobDescription", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'currentJob' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ExperienceDTO.prototype, "currentJob", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'createdAt' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], ExperienceDTO.prototype, "creationDate", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'updatedAt' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], ExperienceDTO.prototype, "updateDate", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Number)
], ExperienceDTO.prototype, "__v", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Object)
], ExperienceDTO.prototype, "buffer", void 0);
class CandidateDTO {
    constructor(candidate = {}) {
        Object.assign(this, candidate);
    }
}
exports.CandidateDTO = CandidateDTO;
__decorate([
    (0, class_transformer_1.Expose)({ name: '_id' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CandidateDTO.prototype, "id", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'cv' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CandidateDTO.prototype, "resume", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'summary' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CandidateDTO.prototype, "summary", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'user' }),
    (0, class_transformer_1.Type)(() => user_dto_1.UserInformationDTO),
    __metadata("design:type", user_dto_1.UserInformationDTO)
], CandidateDTO.prototype, "userInformation", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'certifications' }),
    (0, class_transformer_1.Type)(() => CertificationDTO),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], CandidateDTO.prototype, "certifications", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'educations' }),
    (0, class_transformer_1.Type)(() => EducationDTO),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], CandidateDTO.prototype, "educations", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'experiences' }),
    (0, class_transformer_1.Type)(() => ExperienceDTO),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], CandidateDTO.prototype, "experiences", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'emails' }),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], CandidateDTO.prototype, "emails", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'flatText' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CandidateDTO.prototype, "flatText", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'experiencesCompany' }),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], CandidateDTO.prototype, "companies", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'industry' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CandidateDTO.prototype, "sector", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'industries' }),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], CandidateDTO.prototype, "sectors", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'industriesBinary' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CandidateDTO.prototype, "sectorsBinary", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'jobTitleAng' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CandidateDTO.prototype, "jobTitle", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'jobTitleFr' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CandidateDTO.prototype, "frenchJobTitle", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'country' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CandidateDTO.prototype, "location", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'linkedinUrl' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CandidateDTO.prototype, "linkedInLink", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'monthsOfExperiences' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CandidateDTO.prototype, "experiencesMonthsCount", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'numberOfCertifications' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CandidateDTO.prototype, "certificationsCount", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'numberOfEducations' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CandidateDTO.prototype, "educationsCount", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'numberOfExperiences' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CandidateDTO.prototype, "experiencesCount", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'numberOfSkills' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CandidateDTO.prototype, "skillsCount", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'webSiteUrl' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CandidateDTO.prototype, "webSiteLink", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'skills' }),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], CandidateDTO.prototype, "skills", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'shortList' }),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], CandidateDTO.prototype, "favouriteOpportunities", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'shortListArticle' }),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], CandidateDTO.prototype, "favouriteArticles", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'createdAt' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], CandidateDTO.prototype, "creationDate", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'updatedAt' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], CandidateDTO.prototype, "updateDate", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Number)
], CandidateDTO.prototype, "__v", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Object)
], CandidateDTO.prototype, "buffer", void 0);
//# sourceMappingURL=candidate.dto.js.map