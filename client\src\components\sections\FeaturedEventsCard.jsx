"use client ";
import { Card, CardContent, CardMedia, Grid } from "@mui/material";
import { websiteRoutesList } from "@/helpers/routesList";
import YellowLocation from "@/assets/images/icons/YellowLocation.svg";

import CustomButton from "@/components/ui/CustomButton";
import SvgArrow from "@/assets/images/icons/arrow.svg";

import Link from "next/link";
import { useTranslation } from "react-i18next";

function FeaturedEventsCard({ eventData, language }) {
  const { t } = useTranslation();
  const handleClick = (event, href) => {
    event.preventDefault();
    window.location.href = href;
  };
  const colors = [
    "oil-gaz",
    "energy",
    "banking-insurance",
    "transport",
    "it-telecom",
    "pharma",
  ];

  let lastColor = "";

  function getRandomColor() {
    let randomColor;
    do {
      randomColor = colors[Math.floor(Math.random() * colors.length)];
    } while (randomColor === lastColor);
    lastColor = randomColor;
    return randomColor;
  }

  return (
    <Grid
      className="event-item"
      item
      xs={12}
      sm={6}
      md={4}
      key={eventData?.title}
      sx={{ marginBottom: "10px" }}
    >
      <Card className="card featured">
        <Link
          locale={language === "en" ? "en" : "fr"}
          href={`${
            eventData.type === "events"
              ? language === "en"
                ? `/${websiteRoutesList.events.route}/${eventData?.link}`
                : `/${language}/${websiteRoutesList.events.route}/${eventData?.link}`
              : language === "en"
              ? `/${websiteRoutesList.blog.route}/${eventData?.link}`
              : `/${language}/${websiteRoutesList.blog.route}/${eventData?.link}`
          }/`}
        >
          <CardMedia
            className="card-image"
            component="img"
            image={eventData?.image.src}
            alt={eventData?.title}
          />
        </Link>
        <CardContent className="card-content">
          <Link
            locale={language === "en" ? "en" : "fr"}
            href={`${
              eventData.type === "events"
                ? language === "en"
                  ? `/${websiteRoutesList.events.route}/${eventData?.link}`
                  : `/${language}/${websiteRoutesList.events.route}/${eventData?.link}`
                : language === "en"
                ? `/${websiteRoutesList.blog.route}/${eventData?.link}`
                : `/${language}/${websiteRoutesList.blog.route}/${eventData?.link}`
            }/`}
          >
            {/* <Grid item className="date-container">
              {" "}
              <p className="event-date">{eventData?.eventDate}</p>
            </Grid> */}
            <p className="blog-title">{eventData?.title}</p>{" "}
          </Link>
          <Grid item className="date-container">
            {" "}
            <div className="location-container">
              {" "}
              <YellowLocation />{" "}
              <p className={`blog-description ksa`}>
                {eventData?.exactPlace}{" "}
              </p>{" "}
            </div>{" "}
            <p className="blog-description ksa"> {eventData?.eventDate} </p>
          </Grid>
        </CardContent>
        <div className="card-bottom">
          <CustomButton
            text={t("global:discoverBtn")}
            className={"btn btn-ghost right-icon"}
            icon={<SvgArrow />}
            link={`${
              eventData.type === "events"
                ? `/${websiteRoutesList.events.route}/${eventData?.link}`
                : `/${websiteRoutesList.blog.route}/${eventData?.link}`
            }`}
          />
        </div>
        {/*  {eventData?.publishDate && (
            <p className="blog-time">
              <SvgTime />{" "}
              {formatDateArticle(eventData?.publishDate)}
            </p>
          )}
         */}
      </Card>
    </Grid>
  );
}

export default FeaturedEventsCard;
