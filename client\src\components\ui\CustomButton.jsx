"use client";

import Link from "next/link";
import { useTranslation } from "react-i18next";

import { generateLocalizedSlug } from "@/utils/functions";

function CustomButton({
  className,
  onClick,
  text,
  icon,
  link,
  leftIcon,
  type,
  disabled,
  externalLink,
  id,
  aHref,
  samePage,
  ariaLabel,
}) {
  const { i18n } = useTranslation();
  const locale = i18n.language;

  const linkOrABalise = aHref ? (
    <a
      key={id}
      className={
        leftIcon || icon
          ? leftIcon == true
            ? `${className} left-icon`
            : `${className} right-icon`
          : `${className}`
      }
      target={externalLink && !samePage ? "_blank" : "_self"}
      locale={locale === "en" ? "en" : "fr"}
      href={externalLink ? link : generateLocalizedSlug(locale, link)}
      aria-label={ariaLabel ? ariaLabel : text ?? text}
      onClick={onClick ?? onClick}
    >
      {(leftIcon == true && icon) ?? icon}
      {text ?? text}
      {((!leftIcon || leftIcon != true) && icon) ?? icon}
    </a>
  ) : (
    <Link
      key={id}
      className={
        leftIcon || icon
          ? leftIcon == true
            ? `${className} left-icon`
            : `${className} right-icon`
          : `${className}`
      }
      target={externalLink == true && !samePage ? "_blank" : "_self"}
      locale={locale === "en" ? "en" : "fr"}
      href={externalLink == true ? link : generateLocalizedSlug(locale, link)}
      aria-label={ariaLabel ? ariaLabel : text ?? text}
    >
      {(leftIcon == true && icon) ?? icon}
      {text ?? text}
      {((!leftIcon || leftIcon != true) && icon) ?? icon}
    </Link>
  );

  return (
    <>
      {link ? (
        linkOrABalise
      ) : (
        <button
          key={id}
          type={type ? type : "button"}
          disabled={disabled}
          onClick={onClick}
          className={
            leftIcon || icon
              ? leftIcon == true
                ? `${className} left-icon`
                : `${className} right-icon`
              : `${className}`
          }
        >
          {(leftIcon == true && icon) ?? icon}
          {text ?? text}
          {((!leftIcon || leftIcon != true) && icon) ?? icon}
        </button>
      )}
    </>
  );
}

export default CustomButton;
