"use client";
import { Container } from "@mui/material";
import CustomButton from "@/components/ui/CustomButton";
import ctaBg from "@/assets/images/website/coporate-profile/cta_corporate_profile.png"
import { useTranslation } from "react-i18next";
import { websiteRoutesList } from "@/helpers/routesList";

function ExploreMore() {
  const {t} = useTranslation();
  return (
    <Container id="explore-more-section" className="custom-max-width"
    style={{
             backgroundImage: `url("${ctaBg.src}")`,
            backgroundSize: "contain",
            backgroundRepeat: "no-repeat",
            backgroundPosition: "center",
          }} >
      <div className="text-section blue-color text-center"
     >
            <h2 className="heading-h1 text-white mb-0">
            {t("aboutUs:exploreMore:title")}</h2>
            <p className="sub-heading text-white text-center">
            {t("aboutUs:exploreMore:description")}
            </p>
            <div className="btns">
            <CustomButton
              text={t("aboutUs:exploreMore:item")}
              className={"btn btn-filled first-child"}
              link={`/${websiteRoutesList.corporatProfile.route}`}
              aHref
            />
             
            </div>
        
          </div>
    </Container>
  );
}

export default ExploreMore;
