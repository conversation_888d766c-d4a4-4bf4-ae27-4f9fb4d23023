const StatusCard = ({
    iconSrc,
    iconAlt,
    iconClassName = "stats-icon",
    title,
    value,
    cardClassName = "",
    onClick
}) => {
    return (
        <div className={`stats-card ${cardClassName}`} onClick={onClick}>
            <div className="stats-content">
                <div className="stats-icon-wrapper">
                    <img src={iconSrc} alt={iconAlt} className={iconClassName} />
                </div>
                <div className="stats-info">
                    <span className="stats-title">{title}</span>
                    <span className="stats-value">{value}</span>
                </div>
            </div>
        </div>
    );
};

export default StatusCard;
