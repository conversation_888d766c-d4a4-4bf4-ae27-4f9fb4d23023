
import SvgUsers from "@/assets/images/icons/yellow/Users.svg";
import Svglanguage from "@/assets/images/icons/yellow/language.svg";
import Svggdp from "@/assets/images/icons/yellow/gdp.svg";
import Svgcurrency from "@/assets/images/icons/yellow/currency.svg";
import SvgcapitalCity from "@/assets/images/icons/yellow/capitalCity.svg";
import Svggross from "@/assets/images/icons/yellow/gross.svg";
import OfficeInfo from "@/components/ui/OfficeInfo";

function OfficeEgypt({ t }) {
  const data = [
    {
      icon: <SvgUsers />,
      titleKey: "Egypte:officeInfoTN:title1",
      descriptionKey: "Egypte:officeInfoTN:description1"
    },
    {
      icon: <Svglanguage />,
      titleKey: "Egypte:officeInfoTN:title2",
      descriptionKey: "Egypte:officeInfoTN:description2"
    },
    {
      icon: <Svggdp />,
      titleKey: "Egypte:officeInfoTN:title3",
      descriptionKey: "Egypte:officeInfoTN:description3"
    },
    {
      icon: <Svgcurrency />,
      titleKey: "Egypte:officeInfoTN:title4",
      descriptionKey: "Egypte:officeInfoTN:description4"
    },
    {
      icon: <SvgcapitalCity />,
      titleKey: "Egypte:officeInfoTN:title5",
      descriptionKey: "Egypte:officeInfoTN:description5"
    },
    {
      icon: <Svggross />,
      titleKey: "Egypte:officeInfoTN:title6",
      descriptionKey: "Egypte:officeInfoTN:description6"
    }
  ];

  return <OfficeInfo data={data} t={t} />;
}


export default OfficeEgypt;
