"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/add/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/AddArticleFroala.jsx":
/*!***********************************************************!*\
  !*** ./src/features/blog/components/AddArticleFroala.jsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Accordion/Accordion.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionSummary/AccordionSummary.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionDetails/AccordionDetails.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormGroup/FormGroup.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormLabel/FormLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mui/icons-material/ExpandMore */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/ExpandMore.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! yup */ \"(app-pages-browser)/./node_modules/yup/index.esm.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! formik */ \"(app-pages-browser)/./node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var _utils_urls__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../utils/urls */ \"(app-pages-browser)/./src/utils/urls.js\");\n/* harmony import */ var _AddArticleEN__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./AddArticleEN */ \"(app-pages-browser)/./src/features/blog/components/AddArticleEN.jsx\");\n/* harmony import */ var _AddArticleFR__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./AddArticleFR */ \"(app-pages-browser)/./src/features/blog/components/AddArticleFR.jsx\");\n/* harmony import */ var _opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../opportunity/hooks/opportunity.hooks */ \"(app-pages-browser)/./src/features/opportunity/hooks/opportunity.hooks.js\");\n/* harmony import */ var _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../hooks/blog.hook */ \"(app-pages-browser)/./src/features/blog/hooks/blog.hook.js\");\n/* harmony import */ var react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-datepicker/dist/react-datepicker.css */ \"(app-pages-browser)/./node_modules/react-datepicker/dist/react-datepicker.css\");\n/* harmony import */ var _config_axios__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/config/axios */ \"(app-pages-browser)/./src/config/axios.js\");\n/* harmony import */ var _features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/features/auth/hooks/currentUser.hooks */ \"(app-pages-browser)/./src/features/auth/hooks/currentUser.hooks.js\");\n/* harmony import */ var _components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! lodash/debounce */ \"(app-pages-browser)/./node_modules/lodash/debounce.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_15__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AddArticle = ()=>{\n    _s();\n    const { user } = (0,_features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_13__[\"default\"])();\n    const savedArticle = localStorage.getItem(\"savedArticle\");\n    const useUpdateAutoSaveHook = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useUpdateAutoSave)();\n    const useCreateArticleHook = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useCreateArticle)();\n    const useCreateAutoSaveHook = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useCreateAutoSave)();\n    const useSaveFileHook = (0,_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_9__.useSaveFile)();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const currentYear = new Date().getFullYear();\n    const formikRefAll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [expanded, setExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(`panel`);\n    const [formdataEN, setFormDataEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new FormData());\n    const [formdataFR, setFormDataFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new FormData());\n    const [uuidPhotoFileNameEN, setUuidPhotoFileNameEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [uuidPhotoFileNameFR, setUuidPhotoFileNameFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedLanguages, setSelectedLanguages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        en: true,\n        fr: false\n    });\n    const [categoriesEN, setCategoriesEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categoriesFR, setCategoriesFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredCategoriesEN, setFilteredCategoriesEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredCategoriesFR, setFilteredCategoriesFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCategoriesEN, setSelectedCategoriesEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCategoriesFR, setSelectedCategoriesFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const getCategoriesEN = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories)(\"en\");\n    const getCategoriesFR = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories)(\"fr\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (getCategoriesEN.data?.categories) {\n            const transformedCategories = getCategoriesEN.data.categories.map((category)=>({\n                    id: category.versionscategory[0]?.id,\n                    name: category.versionscategory[0]?.name\n                }));\n            setCategoriesEN(transformedCategories);\n        }\n    }, [\n        getCategoriesEN.data\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (getCategoriesFR.data?.categories) {\n            const transformedCategories = getCategoriesFR.data.categories.map((category)=>({\n                    id: category?.versionscategory[0]?.id,\n                    name: category.versionscategory[0]?.name\n                }));\n            setCategoriesFR(transformedCategories);\n        }\n    }, [\n        getCategoriesFR.data\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedCategoriesEN.length > 0) {\n            fetchTranslatedCategories(selectedCategoriesEN, \"en\");\n        } else if (selectedCategoriesFR.length > 0) {\n            fetchTranslatedCategories(selectedCategoriesFR, \"fr\");\n        }\n    }, [\n        selectedCategoriesEN,\n        selectedCategoriesFR\n    ]);\n    const fetchTranslatedCategories = async (selectedCategories, language)=>{\n        try {\n            const response = await _config_axios__WEBPACK_IMPORTED_MODULE_12__.axiosGetJson.get(`${_utils_urls__WEBPACK_IMPORTED_MODULE_6__.API_URLS.categories}/${language}/${selectedCategories}`);\n            if (language === \"en\") {\n                const transformedCategories = response.data.map((category)=>({\n                        id: category._id,\n                        name: category.name\n                    }));\n                setFilteredCategoriesFR(transformedCategories);\n            } else {\n                const transformedCategories = response.data.map((category)=>({\n                        id: category._id,\n                        name: category.name\n                    }));\n                setFilteredCategoriesEN(transformedCategories);\n            }\n        } catch (error) {\n            console.error(\"Error fetching translated categories:\", error);\n        }\n    };\n    const handleImageSelect = async (selectedFile, language)=>{\n        if (language === \"en\") {\n            const uuidPhotos = (0,uuid__WEBPACK_IMPORTED_MODULE_16__[\"default\"])().replace(/-/g, \"\");\n            const newFormData = new FormData();\n            newFormData.append(\"file\", selectedFile);\n            setFormDataEN(newFormData);\n            const extension = selectedFile.name.split(\".\").pop();\n            setUuidPhotoFileNameEN(`${uuidPhotos}.${extension}`);\n        } else if (language === \"fr\") {\n            const uuidPhotos = (0,uuid__WEBPACK_IMPORTED_MODULE_16__[\"default\"])().replace(/-/g, \"\");\n            const newFormData = new FormData();\n            newFormData.append(\"file\", selectedFile);\n            setFormDataFR(newFormData);\n            const extension = selectedFile.name.split(\".\").pop();\n            setUuidPhotoFileNameFR(`${uuidPhotos}.${extension}`);\n        }\n    };\n    // Helper function to safely parse localStorage items\n    const getLocalStorageItem = (key)=>{\n        try {\n            const item = localStorage.getItem(key);\n            return item ? JSON.parse(item) : \"\";\n        } catch (error) {\n            console.warn(`Error parsing localStorage item \"${key}\":`, error);\n            return \"\";\n        }\n    };\n    const titleEN = getLocalStorageItem(\"title\");\n    const metaTitleEN = getLocalStorageItem(\"metatitle\");\n    const metaDescriptionEN = getLocalStorageItem(\"metaDescription\");\n    const contentEN = getLocalStorageItem(\"content\");\n    const contentFR = getLocalStorageItem(\"contentfr\");\n    const titleFR = getLocalStorageItem(\"titlefr\");\n    const metaDescriptionFR = getLocalStorageItem(\"metaDescriptionfr\");\n    const metaTitleFR = getLocalStorageItem(\"metatitlefr\");\n    const initialValues = {\n        robotsMeta: \"index\",\n        metaTitleEN: metaTitleEN,\n        metaDescriptionEN: metaDescriptionEN,\n        descriptionEN: \"\",\n        visibilityEN: \"\",\n        categoryEN: [],\n        imageEN: null,\n        keywordsEN: \"\",\n        titleEN: titleEN,\n        urlEN: \"\",\n        altEN: \"\",\n        contentEN: contentEN,\n        highlightsEN: [],\n        publishDateEN: \"\",\n        faqTitleEN: \"\",\n        faqEN: [],\n        titleFR: titleFR,\n        metaTitleFR: metaTitleFR,\n        metaDescriptionFR: metaDescriptionFR,\n        descriptionFR: \"\",\n        visibilityFR: \"\",\n        categoryFR: [],\n        imageFR: null,\n        keywordsFR: \"\",\n        urlFR: \"\",\n        altFR: \"\",\n        contentFR: contentFR,\n        publishDateFR: \"\",\n        highlightsFR: [],\n        faqTitleFR: \"\",\n        faqFR: []\n    };\n    const validationSchema = yup__WEBPACK_IMPORTED_MODULE_3__.object().shape({\n        ...selectedLanguages.en && {\n            metaTitleEN: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            metaDescriptionEN: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            titleEN: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            category: yup__WEBPACK_IMPORTED_MODULE_3__.array().min(1, t(\"validations:minCategory\")),\n            visibilityEN: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\"))\n        },\n        ...selectedLanguages.fr && {\n            metaTitleFR: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            metaDescriptionFR: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            titleFR: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            visibilityFR: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\"))\n        }\n    });\n    const uploadFile = (filename, formData, lang)=>{\n        return new Promise((resolve)=>{\n            useSaveFileHook.mutate({\n                resource: \"blogs\",\n                folder: currentYear,\n                filename,\n                body: {\n                    formData,\n                    t\n                }\n            }, {\n                onSuccess: (data)=>{\n                    if (data.message === \"uuid exist\") {\n                        resolve({\n                            lang,\n                            uuid: data.uuid\n                        });\n                    } else {\n                        resolve({\n                            lang,\n                            uuid: data.uuid\n                        });\n                    }\n                },\n                onError: (error)=>{\n                    console.error(`Error uploading ${lang} image:`, error);\n                    resolve({\n                        lang,\n                        uuid: null\n                    });\n                }\n            });\n        });\n    };\n    const clearLocalStorage = ()=>{\n        const keysToRemove = [\n            \"title\",\n            \"content\",\n            \"titlefr\",\n            \"contentfr\",\n            \"metaDescription\",\n            \"metaDescriptionfr\",\n            \"metatitle\",\n            \"metatitlefr\",\n            \"savedArticle\"\n        ];\n        keysToRemove.forEach((key)=>{\n            try {\n                localStorage.removeItem(key);\n            } catch (error) {\n                console.warn(`Error removing localStorage item \"${key}\":`, error);\n            }\n        });\n    };\n    const handleSubmit = async (values)=>{\n        const data = {\n            robotsMeta: values.robotsMeta,\n            versions: []\n        };\n        if (!selectedLanguages.en && !selectedLanguages.fr) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please select at least one version!\");\n            return;\n        }\n        try {\n            let resultEN, resultFR;\n            // Validate required files\n            if (selectedLanguages.en && (!uuidPhotoFileNameEN || !formdataEN)) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please select an image for the English version!\");\n                return;\n            }\n            if (selectedLanguages.fr && (!uuidPhotoFileNameFR || !formdataFR)) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please select an image for the French version!\");\n                return;\n            }\n            if (selectedLanguages.en) {\n                resultEN = await uploadFile(uuidPhotoFileNameEN, formdataEN, \"English\");\n            }\n            if (selectedLanguages.fr) {\n                resultFR = await uploadFile(uuidPhotoFileNameFR, formdataFR, \"French\");\n            }\n            if (selectedLanguages.en && resultEN.uuid) {\n                data.versions.push({\n                    language: \"en\",\n                    metaTitle: values.metaTitleEN,\n                    title: values.titleEN,\n                    metaDescription: values.metaDescriptionEN,\n                    description: values.descriptionEN,\n                    url: values.urlEN,\n                    visibility: values.visibilityEN,\n                    publishDate: values.publishDateEN,\n                    content: values.contentEN,\n                    alt: values.altEN,\n                    keywords: values.keywordsEN,\n                    highlights: values.highlightsEN,\n                    image: resultEN.uuid,\n                    category: values.categoryEN === \"\" ? [] : values.categoryEN,\n                    createdBy: user?.firstName + \" \" + user?.lastName,\n                    faqTitle: values.faqTitleEN || \"\",\n                    faq: values.faqEN || []\n                });\n            }\n            if (selectedLanguages.fr && resultFR.uuid) {\n                data.versions.push({\n                    language: \"fr\",\n                    metaTitle: values.metaTitleFR,\n                    title: values.titleFR,\n                    metaDescription: values.metaDescriptionFR,\n                    url: values.urlFR,\n                    visibility: values.visibilityFR,\n                    description: values.descriptionFR,\n                    publishDate: values.publishDateFR,\n                    content: values.contentFR,\n                    alt: values.altFR,\n                    keywords: values.keywordsFR,\n                    highlights: values.highlightsFR,\n                    image: resultFR.uuid,\n                    category: values.categoryFR === \"\" ? [] : values.categoryFR,\n                    createdBy: user?.firstName + \" \" + user?.lastName,\n                    faqTitle: values.faqTitleFR || \"\",\n                    faq: values.faqFR || []\n                });\n            }\n            if (data.versions.length > 0) {\n                if (isSavedArticle !== \"\") {\n                    useUpdateAutoSaveHook.mutate({\n                        data: data,\n                        id: isSavedArticle\n                    }, {\n                        onSuccess: ()=>{\n                            clearLocalStorage();\n                            window.location.href = `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.blogs.route}/`;\n                        }\n                    });\n                } else {\n                    useCreateArticleHook.mutate({\n                        data\n                    }, {\n                        onSuccess: ()=>{\n                            clearLocalStorage();\n                            window.location.href = `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.blogs.route}/`;\n                        }\n                    });\n                }\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"No valid image uploads found. Article not created.\");\n            }\n        } catch (error) {\n            console.error(\"Error processing article submission:\", error);\n            if (error.message?.includes(\"upload\")) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to upload images. Please try again.\");\n            } else if (error.message?.includes(\"network\")) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Network error. Please check your connection and try again.\");\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(`An error occurred while processing uploads: ${error.message || \"Unknown error\"}`);\n            }\n            throw error;\n        }\n    };\n    const handleChangeAccordion = (panel)=>(_, newExpanded)=>{\n            setExpanded(newExpanded ? panel : false);\n        };\n    const handleCategoriesENSelect = (selectedCategories)=>{\n        setSelectedCategoriesEN(selectedCategories);\n    };\n    const handleCategoriesFRSelect = (selectedCategories)=>{\n        setSelectedCategoriesFR(selectedCategories);\n    };\n    const [isSavedArticle, setIsSavedArticle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(savedArticle || \"\");\n    const isSavedArticleRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(isSavedArticle);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        isSavedArticleRef.current = isSavedArticle;\n    }, [\n        isSavedArticle\n    ]);\n    const autosave = async ()=>{\n        try {\n            const values = formikRefAll.current?.values;\n            if (!values) return;\n            const data = {\n                robotsMeta: values?.robotsMeta,\n                versions: []\n            };\n            if (selectedLanguages.en) {\n                data.versions.push({\n                    language: \"en\",\n                    metaTitle: values.metaTitleEN,\n                    title: values.titleEN,\n                    metaDescription: values.metaDescriptionEN,\n                    url: values.urlEN,\n                    visibility: \"Draft\",\n                    publishDate: values.publishDateEN,\n                    content: values.contentEN,\n                    alt: values.altEN,\n                    keywords: values.keywordsEN,\n                    category: values.categoryEN,\n                    highlights: values.highlightsEN,\n                    faqTitle: values.faqTitleEN || \"\",\n                    faq: values.faqEN || []\n                });\n            }\n            if (selectedLanguages.fr) {\n                data.versions.push({\n                    language: \"fr\",\n                    metaTitle: values.metaTitleFR,\n                    title: values.titleFR,\n                    metaDescription: values.metaDescriptionFR,\n                    url: values.urlFR,\n                    visibility: \"Draft\",\n                    publishDate: values.publishDateFR,\n                    content: values.contentFR,\n                    alt: values.altFR,\n                    keywords: values.keywordsFR,\n                    category: values.categoryFR,\n                    highlights: values.highlightsFR,\n                    faqTitle: values.faqTitleFR || \"\",\n                    faq: values.faqFR || []\n                });\n            }\n            if (isSavedArticleRef.current != \"\") {\n                useUpdateAutoSaveHook.mutate({\n                    data: data,\n                    id: isSavedArticleRef.current\n                });\n            } else {\n                if (data.versions.length > 0) {\n                    useCreateAutoSaveHook.mutate({\n                        data\n                    }, {\n                        onSuccess: (data)=>{\n                            setIsSavedArticle(data.articleId);\n                            localStorage.setItem(\"savedArticle\", data.articleId);\n                        }\n                    });\n                }\n            }\n        } catch (error) {\n            console.warn(\"Auto-save failed:\", error);\n        }\n    };\n    // Optimized debounce timing: 2 minutes instead of 2 minutes (120000ms)\n    const handleChange = lodash_debounce__WEBPACK_IMPORTED_MODULE_15___default()(autosave, 30000); // 30 seconds for better UX\n    const handleClear = ()=>{\n        formikRefAll.current?.resetForm();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"heading-h2 semi-bold\",\n                children: t(\"createArticle:addArticle\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                lineNumber: 488,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"container\",\n                className: \"recent-application-pentabell\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `main-content`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"commun\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"label-form\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: selectedLanguages.en,\n                                                onChange: ()=>setSelectedLanguages((prev)=>({\n                                                            ...prev,\n                                                            en: !prev.en\n                                                        }))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"English\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                        lineNumber: 494,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"label-form\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: selectedLanguages.fr,\n                                                onChange: ()=>setSelectedLanguages((prev)=>({\n                                                            ...prev,\n                                                            fr: !prev.fr\n                                                        }))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"French\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                lineNumber: 493,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                id: \"experiences\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    id: \"form\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.Formik, {\n                                        initialValues: initialValues,\n                                        validationSchema: validationSchema,\n                                        innerRef: formikRefAll,\n                                        onSubmit: handleSubmit,\n                                        className: \"formik-form\",\n                                        children: (param)=>{\n                                            let { errors, touched, setFieldValue, values } = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.Form, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        id: \"accordion\",\n                                                        disableGutters: true,\n                                                        expanded: expanded === `panel`,\n                                                        onChange: handleChangeAccordion(`panel`),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                    lineNumber: 540,\n                                                                    columnNumber: 39\n                                                                }, void 0),\n                                                                \"aria-controls\": `panel-content`,\n                                                                id: `panel-header`,\n                                                                children: t(\"createArticle:settings\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                lineNumber: 539,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"accordion-detail\",\n                                                                elevation: 0,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"inline-group\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                    className: \"label-form\",\n                                                                                    children: [\n                                                                                        \"Robots meta\",\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                            className: \"select-pentabell\",\n                                                                                            variant: \"standard\",\n                                                                                            sx: {\n                                                                                                m: 1,\n                                                                                                minWidth: 120\n                                                                                            },\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                                value: _utils_constants__WEBPACK_IMPORTED_MODULE_5__.RobotsMeta.filter((option)=>values.robotsMeta === option),\n                                                                                                selected: values.robotsMeta,\n                                                                                                onChange: (event)=>{\n                                                                                                    setFieldValue(\"robotsMeta\", event.target.value);\n                                                                                                },\n                                                                                                children: _utils_constants__WEBPACK_IMPORTED_MODULE_5__.RobotsMeta.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                                        value: item,\n                                                                                                        children: item\n                                                                                                    }, item, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                                        lineNumber: 573,\n                                                                                                        columnNumber: 41\n                                                                                                    }, undefined))\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                                lineNumber: 560,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                            lineNumber: 555,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.ErrorMessage, {\n                                                                                            className: \"label-error\",\n                                                                                            name: \"robotsMeta\",\n                                                                                            component: \"div\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                            lineNumber: 579,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                    lineNumber: 553,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                lineNumber: 552,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.ErrorMessage, {\n                                                                                name: \"robotsMeta\",\n                                                                                component: \"div\",\n                                                                                className: \"label-error\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                lineNumber: 587,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                        lineNumber: 551,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                    lineNumber: 550,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                lineNumber: 546,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, `panel`, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                        lineNumber: 532,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    selectedLanguages.en && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddArticleEN__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        errors: errors,\n                                                        touched: touched,\n                                                        setFieldValue: setFieldValue,\n                                                        values: values,\n                                                        onImageSelect: handleImageSelect,\n                                                        debounce: handleChange,\n                                                        categories: categoriesEN,\n                                                        filteredCategories: filteredCategoriesEN,\n                                                        onCategoriesSelect: handleCategoriesENSelect\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                        lineNumber: 597,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    selectedLanguages.fr && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddArticleFR__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        errors: errors,\n                                                        touched: touched,\n                                                        setFieldValue: setFieldValue,\n                                                        values: values,\n                                                        onImageSelect: handleImageSelect,\n                                                        categories: categoriesFR,\n                                                        filteredCategories: filteredCategoriesFR,\n                                                        onCategoriesSelect: handleCategoriesFRSelect,\n                                                        debounce: handleChange\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                        lineNumber: 610,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"btn-container\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                type: \"button\",\n                                                                text: \"Clear\",\n                                                                className: \"btn btn-filled\",\n                                                                onClick: handleClear\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                lineNumber: 623,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                type: \"submit\",\n                                                                text: \"Save\",\n                                                                className: \"btn btn-filled\",\n                                                                onClick: ()=>{}\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                lineNumber: 629,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                        lineNumber: 622,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                lineNumber: 531,\n                                                columnNumber: 21\n                                            }, undefined);\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                    lineNumber: 522,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                lineNumber: 521,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                        lineNumber: 492,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                    lineNumber: 491,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                lineNumber: 490,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(AddArticle, \"rnB4oaCXGsNbzt/ZpNSvh2lS5Yo=\", false, function() {\n    return [\n        _features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useUpdateAutoSave,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useCreateArticle,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useCreateAutoSave,\n        _opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_9__.useSaveFile,\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories\n    ];\n});\n_c = AddArticle;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AddArticle);\nvar _c;\n$RefreshReg$(_c, \"AddArticle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/AddArticleFroala.jsx\n"));

/***/ })

});