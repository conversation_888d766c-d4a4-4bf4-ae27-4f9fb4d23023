"use client";
import { Container, Grid, useMediaQuery, useTheme } from "@mui/material";
import Svg9001 from "@/assets/images/icons/9001.svg";
import Svg45001 from "@/assets/images/icons/45001.svg";
import Svg14001 from "@/assets/images/icons/14001.svg";

import business from "@/assets/images/Saudi/ksa-business.png";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
function Overview() {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const { t } = useTranslation();
  return (
    <Container id="ksa" className="custom-max-width">
      <Grid className="iso-wrapper" container>
        <Grid
          item
          md={6}
          xs={12}
          sx={{
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
          }}
        >
          <h2 className={`heading-h1 ${isMobile ? "text-center" : ""}`}>
            {t("ksa:overview:title")}
          </h2>
        </Grid>
        <Grid
          item
          md={6}
          xs={12}
          sx={{
            display: "flex",
            justifyContent: "center",
            flexDirection: "column",
            padding: "20px 0",
          }}
        >
          <span className="icons-wrapper">
            {" "}
            <Svg9001 className="icon" />
            <Svg45001 className="icon" /> <Svg14001 className="icon" />
          </span>
          <p className={`icons-wrapper label ${isMobile ? "text-center" : ""}`}>
            {" "}
            {t("ksa:overview:certif")}
          </p>
        </Grid>
        <Grid item md={12} sx={{ paddingLeft: "0px !important" }}>
          <hr className="divider" />
        </Grid>
        <Grid
          item
          columnGap={isMobile ? 2 : 20}
          sx={{
            display: "flex",
            flexDirection: "row",
            justifyContent: "center",
            flexWrap: "wrap",
          }}
        >
          <div className="overview">
            <p className="heading-h1">250K+</p>
            <span className="grey"> {t("ksa:overview:resume")}</span>
          </div>
          <div className="overview">
            {" "}
            <p className="heading-h1">20+</p>
            <span className="grey">{t("ksa:overview:experience")}</span>
          </div>
          <div className="overview">
            {" "}
            <p className="heading-h1">15K+</p>
            <span className="grey">{t("ksa:overview:experts")}</span>
          </div>
        </Grid>
      </Grid>
    </Container>
  );
}

export default Overview;
