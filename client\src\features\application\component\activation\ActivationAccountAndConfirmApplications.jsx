"use client";


import Svgcircle from "@/assets/images/checkcfonfirm.svg"
import ReusableConfirmation from "../ReusableConfirmation";


function ActivationAccountAndConfirmApplications({ t }) {
    const Buttons = [


        {
            text: "Discover Opportunities",
            onClick: () => (
                window.location.href = "/opportunities"
            ),
         
        },
        {
            text: "See Insights",
            onClick: () => (
                window.location.href = "/blog"
            ),
           
        }
    ];
    return (

        <ReusableConfirmation
            icon={Svgcircle}
            message="Thank You for Confirming Your Application"
            text="Your application has been successfully submitted and your account is now active."
            buttons={Buttons}
        />
    );
}

export default ActivationAccountAndConfirmApplications;
