import OurPartners from "@/components/pages/sites/sections/OurPartners";
import banner from "@/assets/images/website/banner/Middleeast.webp";
import GlobalHRServicesSection from "@/components/pages/sites/sections/GlobalHRServicesSection";
import r1 from "@/assets/images/services/recrutement1.png";
import r2 from "@/assets/images/services/recrutement2.png";
import r3 from "@/assets/images/services/recrutement3.png";
import r4 from "@/assets/images/services/recrutement4.png";
import serviceImg from "@/assets/images/test/service.png";
import CTAPayroll from "@/components/pages/sites/services/CTAPayroll";
import AfricaForm from "@/features/forms/components/AfricaForm";
import ctaAfricaBg from "@/assets/images/services/ctaAfricaPage.png";
import initTranslations from "@/app/i18n";
import MiddleEastBanner from "@/components/pages/sites/offices-sections/middleEast/MiddleEastBanner";
import NavigateMiddleEastMarket from "@/components/pages/sites/offices-sections/middleEast/NavigateMiddleEastMarket";
import HrSolutionsInMiddleEast from "@/components/pages/sites/offices-sections/middleEast/HrSolutionsInMiddleEast";
import LocationsInMiddleEast from "@/components/pages/sites/offices-sections/middleEast/LocationsInMiddleEast";
import CTMContact from "@/components/pages/sites/offices-sections/middleEast/CTMContact";
import { websiteRoutesList } from "@/helpers/routesList";
import { axiosGetJsonSSR } from "@/config/axios";
import serviceimgS1 from "@/assets/images/services/service1.png";

export async function generateMetadata({ params: { locale } }) {
  const canonicalUrl = `https://www.pentabell.com/${
    locale !== "en" ? `${locale}/` : ""
  }international-recruitment-staffing-company-in-middle-east/`;

  const languages = {
    fr: `https://www.pentabell.com/fr/international-recruitment-staffing-company-in-middle-east/`,
    en: `https://www.pentabell.com/international-recruitment-staffing-company-in-middle-east/`,
    "x-default": `https://www.pentabell.com/international-recruitment-staffing-company-in-middle-east/`,
  };
  const { t } = await initTranslations(locale, ["servicesByCountry"]);
  try {
    const res = await axiosGetJsonSSR.get(
      `${process.env.NEXT_PUBLIC_BASE_API_URL_SSR}/seoTags/${locale}/international-recruitment-staffing-company-in-middle-east`
    );
    const seoTags = res?.data?.status === 200;
    if (seoTags) {
      return {
        title: res?.data?.data?.versions[0]?.metaTitle,
        description: res?.data?.data?.versions[0]?.metaDescription,
        robots: res?.data?.data?.robotMeta,
        alternates: {
          canonical: canonicalUrl,
          languages,
        },
      };
    }
  } catch (error) {
    console.error("Error fetching SEO tags:", error);
  }
  return {
    title: t("servicesByCountry:middleEast:metaTitle"),
    description: t("servicesByCountry:middleEast:metaDescription"),
    alternates: {
      canonical: canonicalUrl,
      languages,
    },
    robots: "follow, index, max-snippet:-1, max-image-preview:large",
  };
}

async function companyInMiddleeast({ params: { locale } }) {
  const { t } = await initTranslations(locale, [
    "Tunisia",
    "europe",
    "africa",
    "middleeast",
    "ksa"
  ]);
  const SERVICES = [
    {
      id: "s1",
      title: t("Tunisia:services:dataS1:title"),
      description: t("Tunisia:services:dataS1:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.payrollServices.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: serviceImg,
      altImg: t("Tunisia:services:dataS1:altImg"),
    },
    {
      id: "s2",
      title: t("Tunisia:services:dataS2:title"),
      description: t("Tunisia:services:dataS2:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.consultingServices.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r4,
      altImg: t("Tunisia:services:dataS2:altImg"),
    },
    {
      id: "s3",
      title: t("Tunisia:services:dataS3:title"),
      description: t("Tunisia:services:dataS3:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.technicalAssistance.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r3,
      altImg: t("Tunisia:services:dataS3:altImg"),
    },
    {
      id: "s4",
      title: t("Tunisia:services:dataS4:title"),
      description: t("Tunisia:services:dataS4:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.aiSourcing.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r2,
      altImg: t("Tunisia:services:dataS4:altImg"),
    },
    {
      id: "s5",
      title: t("Tunisia:services:dataS5:title"),
      description: t("Tunisia:services:dataS5:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.directHiring.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r1,
      altImg: t("Tunisia:services:dataS5:altImg"),
    },
  ];
  return (
    <div>
      <MiddleEastBanner
        altImg={t("middleeast:banner:altImg")}
        bannerImg={banner}
        height={"100vh"}
        t={t}
      />
      <OurPartners disableTxt={true} />
      <NavigateMiddleEastMarket t={t} />
      <GlobalHRServicesSection
        title={t("middleeast:services:title")}
        subTitle={t("middleeast:services:subTitle")}
        SERVICES={SERVICES}
        defaultImage={serviceimgS1}
      />

      <CTAPayroll
        light={true}
        title={t("middleeast:hire:title")}
        description={t("middleeast:hire:description")}
        btnLink={"#middle east-form"}
        btnText={"Get started"}
        img={ctaAfricaBg.src}
        imgAlt={t("europe:contact:altImage")}
      />
      <HrSolutionsInMiddleEast t={t} />
      <LocationsInMiddleEast t={t} />
      <CTMContact t={t} />
      <AfricaForm country={"Middle East"} />
    </div>
  );
}

export default companyInMiddleeast;
