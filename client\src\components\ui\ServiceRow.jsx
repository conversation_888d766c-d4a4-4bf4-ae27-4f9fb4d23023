import { Container, Grid } from "@mui/material";
import SvgCheckCircle from "../../assets/images/icons/checkCircle.svg";

import CustomButton from "./CustomButton";

function ServiceRow({ reverse, darkBg, data }) {
  return (
    <div className={darkBg == true ? "dark-bg" : null}>
      <Container className={"service-row custom-max-width"}>
        <Grid
          container
          justifyContent="space-between"
          className={reverse == true ? "reverse" : null}
        >
          <Grid item xs={12} sm={12} md={6} p={0}>
            <div className="content">
              {data.label ? <p className="label yellow">{data.label}</p> : null}
              {data.title ? (
                <h3 className="heading-h3 text-white">{data.title}</h3>
              ) : null}

              {data.subTitle ? (
                <h3 className="sub-heading text-white">{data.subTitle}</h3>
              ) : null}
              {data.paragraph ? (
                <p className="paragraph text-white">{data.paragraph}</p>
              ) : null}

              <div className="data">
                {data.items
                  ? data.items.map((item, index) => {
                      return (
                        <p className="data-item text-white" key={index}>
                          <SvgCheckCircle />
                          {item}
                        </p>
                      );
                    })
                  : null}

                {data.itemsWithParagraph
                  ? data.itemsWithParagraph.map((item, index) => {
                      return (
                        <div className="data-item-paragraph" key={index}>
                          <h3 className="sub-heading text-white">
                            {" "}
                            <SvgCheckCircle />
                            {item.title}
                          </h3>
                          <p className="paragraph text-white">
                            {item.paragraph}
                          </p>
                        </div>
                      );
                    })
                  : null}
              </div>
              {data.btnLabel && (
                <CustomButton
                  text={data.btnLabel}
                  link={data.btnLink}
                  className={"btn btn-filled"}
                  aHref
                />
              )}
            </div>
          </Grid>
          <Grid item xs={12} sm={12} md={6} p={0}>
            <div className="feauture-img">
              <div>
                <img
                  src={data.featureImg.src}
                  alt={data.altImg}
                  loading="lazy"
                />
              </div>
            </div>
          </Grid>
        </Grid>
      </Container>
    </div>
  );
}

export default ServiceRow;
