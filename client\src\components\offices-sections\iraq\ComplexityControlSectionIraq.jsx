import SvgComprehensiveEmploymentIcon from "@/assets/images/services/icons/ComprehensiveEmploymentIcon.svg";
import SvgGlobalPayrollIcon from "@/assets/images/services/icons/GlobalPayrollIcon.svg";
import SvgtickIcon from "@/assets/images/services/icons/tickIcon.svg";
import SvgMultiCurrencyIcon from "@/assets/images/services/icons/MultiCurrencyIcon.svg";
import SvglegalIcon from "@/assets/images/services/icons/legalIcon.svg";
import SvgtaxManagementIcon from "@/assets/images/services/icons/taxManagementIcon.svg";
import ComplexityControlSection from "@/components/ui/ComplexityControlSection";


function ComplexityControlSectionIraq({ t }) {
  const items = [
    {
      icon: SvgComprehensiveEmploymentIcon,
      title: t("iraq:complexity:dataS1:title"),
      description: t("iraq:complexity:dataS1:description"),
    },
    {
      icon: SvgGlobalPayrollIcon,
      title: t("iraq:complexity:dataS2:title"),
      description: t("iraq:complexity:dataS2:description"),
    },
    {
      icon: SvgtickIcon,
      title: t("iraq:complexity:dataS3:title"),
      description: t("iraq:complexity:dataS3:description"),
    },
    {
      icon: SvgtaxManagementIcon,
      title: t("iraq:complexity:dataS4:title"),
      description: t("iraq:complexity:dataS4:description"),
    },
    {
      icon: SvglegalIcon,
      title: t("iraq:complexity:dataS5:title"),
      description: t("iraq:complexity:dataS5:description"),
    },
    {
      icon: SvgMultiCurrencyIcon,
      title: t("iraq:complexity:dataS6:title"),
      description: t("iraq:complexity:dataS6:description"),
    },
  ];

  return (
    <ComplexityControlSection
      title1={t("iraq:complexity:title1")}
      title2={t("iraq:complexity:title2")}
      description=""
      items={items}
    />
  );
}

export default ComplexityControlSectionIraq;
