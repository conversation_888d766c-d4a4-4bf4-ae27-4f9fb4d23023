"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const envalid_1 = require("envalid");
function validateEnv() {
    (0, envalid_1.cleanEnv)(process.env, {
        NODE_ENV: (0, envalid_1.str)({
            choices: ['dev', 'prod', 'test'],
        }),
        APP_PORT: (0, envalid_1.port)(),
        DB_USER: (0, envalid_1.str)(),
        DB_PASSWORD: (0, envalid_1.str)(),
        DB_NAME: (0, envalid_1.str)(),
        DB_HOST: (0, envalid_1.str)(),
        DB_PORT: (0, envalid_1.num)(),
        ACCESS_TOKEN_PRIVATE_KEY: (0, envalid_1.str)(),
        ACCESS_TOKEN_TIME: (0, envalid_1.str)(),
        REFRESH_TOKEN_PRIVATE_KEY: (0, envalid_1.str)(),
        REFRESH_TOKEN_TIME: (0, envalid_1.str)(),
        RESET_PASSWORD_TOKEN_PRIVATE_KEY: (0, envalid_1.str)(),
        RESET_PASSWORD_TIME: (0, envalid_1.str)(),
        RESET_PASSWORD_URL_FRONT: (0, envalid_1.str)(),
        CONFIRM_ACCOUNT_URL_FRONT: (0, envalid_1.str)(),
        COOKIE_MAX_AGE: (0, envalid_1.num)(),
    });
}
exports.default = validateEnv;
//# sourceMappingURL=validateEnv.js.map