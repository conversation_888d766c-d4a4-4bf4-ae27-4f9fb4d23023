"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/layout",{

/***/ "(app-pages-browser)/./src/components/ui/DropdownMenu.jsx":
/*!********************************************!*\
  !*** ./src/components/ui/DropdownMenu.jsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DropdownMenu; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Menu/Menu.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n\nvar _s = $RefreshSig$();\n\n\nfunction DropdownMenu(param) {\n    let { buttonLabel, menuItems = [], locale, buttonHref } = param;\n    _s();\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const open = Boolean(anchorEl);\n    const handleOpen = (event)=>{\n        setAnchorEl(event.currentTarget);\n    };\n    const handleClose = ()=>{\n        setAnchorEl(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        onMouseEnter: handleOpen,\n        onMouseLeave: handleClose,\n        style: {\n            display: \"inline-block\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                \"aria-controls\": open ? \"dropdown-menu\" : undefined,\n                \"aria-haspopup\": \"true\",\n                \"aria-expanded\": open ? \"true\" : undefined,\n                onClick: handleOpen,\n                className: \"navbar-link dropdown-toggle\",\n                children: buttonLabel\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                id: \"dropdown-menu\",\n                anchorEl: anchorEl,\n                open: open,\n                onClose: handleClose,\n                MenuListProps: {\n                    onMouseLeave: handleClose\n                },\n                anchorOrigin: {\n                    vertical: \"bottom\",\n                    horizontal: \"left\"\n                },\n                transformOrigin: {\n                    vertical: \"top\",\n                    horizontal: \"left\"\n                },\n                children: menuItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        component: \"a\",\n                        href: item.route,\n                        onClick: handleClose,\n                        children: item.name\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 55,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n_s(DropdownMenu, \"+aMDa7FPcESUyQDF1vq0RSMn4/k=\");\n_c = DropdownMenu;\nvar _c;\n$RefreshReg$(_c, \"DropdownMenu\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/DropdownMenu.jsx\n"));

/***/ })

});