"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/layout",{

/***/ "(app-pages-browser)/./src/components/ui/DropdownMenu.jsx":
/*!********************************************!*\
  !*** ./src/components/ui/DropdownMenu.jsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Menu/Menu.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Fade/Fade.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _assets_images_icons_arrowDown_svg__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../assets/images/icons/arrowDown.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowDown.svg\");\n/* harmony import */ var _assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../assets/images/icons/arrowUp.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowUp.svg\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _utils_functions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/functions */ \"(app-pages-browser)/./src/utils/functions.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst DropdownMenu = (param)=>{\n    let { buttonLabel, buttonHref, menuItems, subMenu, locale, withFlag, selectedFlag, forceOpen = false } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    const buttonRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_6__.useTranslation)();\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const open = Boolean(anchorEl);\n    const isDetailBlogPath = ()=>pathname.includes(\"/blog/\") && !pathname.includes(\"/blog/category/\") && pathname !== \"/blog/\" && pathname !== \"/fr/blog/\";\n    const handleClick = (event)=>{\n        setAnchorEl(event.currentTarget);\n    };\n    const handleClose = ()=>{\n        setAnchorEl(null);\n    };\n    const handleClick2 = (event, item)=>{\n        event.stopPropagation(); // Prevent event bubbling\n        if (item.onClick) {\n            item.onClick(); // Call the onClick handler if provided\n        } else {\n            if (item.subItems == undefined) handleClose();\n        }\n    };\n    const handleMouseEnter = (item, index)=>{\n        if (item.subItems) {\n            setHoveredSubMenu(index);\n        }\n    };\n    const handleMouseLeave = ()=>{\n        // Use a small delay to prevent flickering when moving between parent and child\n        setTimeout(()=>{\n            setHoveredSubMenu(null);\n        }, 100);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: subMenu ? `sub-dropdown-menu` : `dropdown-menu`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                ref: buttonRef,\n                className: pathname.includes(buttonHref) && buttonHref !== \"\" ? \"navbar-link dropdown-toggle active\" : isDetailBlogPath() ? \"navbar-link dropdown-toggle whiteBg\" : \"navbar-link dropdown-toggle\",\n                id: \"fade-button\",\n                \"aria-controls\": open ? \"fade-menu\" : undefined,\n                \"aria-haspopup\": \"true\",\n                \"aria-expanded\": open ? \"true\" : undefined,\n                onClick: handleClick,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: pathname.includes(buttonHref) && buttonHref !== \"\" ? \"dropdown-toggle-link active\" : isDetailBlogPath() ? \"dropdown-toggle-link whiteBg\" : \"dropdown-toggle-link\",\n                        href: // Disable href when buttonLabel is \"resources\"\n                        buttonLabel?.toLowerCase() === \"resources\" || buttonLabel?.toLowerCase() === \"ressources\" ? \"#\" : buttonHref ? (0,_utils_functions__WEBPACK_IMPORTED_MODULE_5__.generateLocalizedSlug)(locale, buttonHref).replace(\"/en/\", \"/\") : pathname.replace(\"/en/\", \"/\"),\n                        locale: locale === \"en\" ? \"en\" : \"fr\",\n                        onClick: (e)=>{\n                            // Prevent navigation when buttonLabel is \"resources\"\n                            if (buttonLabel?.toLowerCase() === \"resources\" || buttonLabel?.toLowerCase() === \"ressources\") {\n                                e.preventDefault();\n                                e.stopPropagation();\n                            }\n                        },\n                        sx: {\n                            // Ensure resources text has the same styling as other menu items\n                            ...buttonLabel?.toLowerCase() === \"resources\" || buttonLabel?.toLowerCase() === \"ressources\" ? {\n                                cursor: \"default\",\n                                textDecoration: \"none\",\n                                \"&:hover\": {\n                                    textDecoration: \"none\"\n                                }\n                            } : {}\n                        },\n                        children: withFlag ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            className: \"flag-lang\",\n                            src: selectedFlag.src,\n                            width: 26,\n                            height: 22,\n                            disabled: true,\n                            loading: \"lazy\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 123,\n                            columnNumber: 13\n                        }, undefined) : buttonLabel\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, undefined),\n                    open ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowDown_svg__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 135,\n                        columnNumber: 17\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 135,\n                        columnNumber: 36\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                id: \"fade-menu\",\n                MenuListProps: {\n                    \"aria-labelledby\": \"fade-button\"\n                },\n                anchorEl: anchorEl,\n                open: open,\n                onClose: handleClose,\n                TransitionComponent: _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                disableScrollLock: true,\n                anchorOrigin: {\n                    vertical: \"bottom\",\n                    horizontal: subMenu ? \"right\" : \"left\"\n                },\n                children: menuItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        onMouseEnter: ()=>handleMouseEnter(item, index),\n                        onMouseLeave: handleMouseLeave,\n                        style: {\n                            position: \"relative\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: item.subItems ? `sub-menu dropdown-item` : `dropdown-item`,\n                            onClick: (e)=>handleClick2(e, item),\n                            children: item.subItems ? // If the item has a subMenu, render another DropdownMenu\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DropdownMenu, {\n                                buttonLabel: item?.i18nName ? t(item?.i18nName) : item.name,\n                                buttonHref: item.route,\n                                menuItems: item.subItems,\n                                subMenu: true,\n                                forceOpen: hoveredSubMenu === index\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                                lineNumber: 167,\n                                columnNumber: 17\n                            }, undefined) : item.route ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                href: (0,_utils_functions__WEBPACK_IMPORTED_MODULE_5__.generateLocalizedSlug)(locale, item.route),\n                                locale: locale === \"en\" ? \"en\" : \"fr\",\n                                className: pathname.includes(item.route) ? \"dropdown-item-link active\" : \"dropdown-item-link\",\n                                children: [\n                                    item.icon ?? item.icon,\n                                    \" \",\n                                    item?.i18nName ? t(item?.i18nName) : item.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                                lineNumber: 176,\n                                columnNumber: 17\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"dropdown-item-link\",\n                                href: \"#\",\n                                children: withFlag ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    className: \"flag-lang\",\n                                    src: item.flag.src,\n                                    width: 26,\n                                    height: 22,\n                                    loading: \"lazy\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 21\n                                }, undefined) : item?.i18nName ? t(item?.i18nName) : item.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                                lineNumber: 189,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 159,\n                            columnNumber: 13\n                        }, undefined)\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 153,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DropdownMenu, \"4V3zRxhqtQbEi//2DJUd7fdbnz8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname,\n        react_i18next__WEBPACK_IMPORTED_MODULE_6__.useTranslation\n    ];\n});\n_c = DropdownMenu;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DropdownMenu);\nvar _c;\n$RefreshReg$(_c, \"DropdownMenu\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/DropdownMenu.jsx\n"));

/***/ })

});