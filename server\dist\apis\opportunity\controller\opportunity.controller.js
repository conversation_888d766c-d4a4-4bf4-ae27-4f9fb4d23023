"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const authentication_middleware_1 = __importDefault(require("@/middlewares/authentication.middleware"));
const mongoId_validation_middleware_1 = __importStar(require("@/middlewares/mongoId-validation.middleware"));
const opportunity_service_1 = __importDefault(require("../service/opportunity.service"));
const authorization_middleware_1 = require("@/middlewares/authorization.middleware");
const constants_1 = require("@/utils/helpers/constants");
const messages_1 = require("@/utils/helpers/messages");
const queries_validation_middleware_1 = __importDefault(require("@/middlewares/queries-validation.middleware"));
const opportunity_application_service_1 = __importDefault(require("../service/opportunity.application.service"));
const validateApiKey_middleware_1 = __importDefault(require("@/middlewares/validateApiKey.middleware"));
class OpportunityController {
    constructor() {
        this.path = '/opportunities';
        this.router = (0, express_1.Router)();
        this.opportunityService = new opportunity_service_1.default();
        this.applicationService = new opportunity_application_service_1.default();
        this.getOpportunityByUrlANDlanguages = async (req, res, next) => {
            try {
                const { url, language } = req.params;
                res.send(await this.opportunityService.getOpportunityByUrl(language, url));
            }
            catch (error) {
                next(error);
            }
        };
        this.getDistinctCountries = async (req, res, next) => {
            try {
                res.send(await this.opportunityService.getDistinctCountries());
            }
            catch (error) {
                next(error);
            }
        };
        this.archivedOpportunite = async (request, response, next) => {
            try {
                const opportunityId = request.params.id;
                await this.opportunityService.archiveOpportunity(opportunityId);
                response.status(204).send();
            }
            catch (error) {
                next(error);
            }
        };
        this.updateMainFields = async (req, res, next) => {
            try {
                const { opportunityId } = req.params;
                const updateData = req.body;
                res.send(await this.opportunityService.updateMainOpportunityFields(opportunityId, updateData));
            }
            catch (error) {
                next(error);
            }
        };
        this.getSlugBySlugOpportunity = async (req, res, next) => {
            try {
                const { url, language } = req.params;
                res.send(await this.opportunityService.getSlugBySlug(language, url));
            }
            catch (error) {
                next(error);
            }
        };
        this.toggleArchiveStatusByLanguageAndId = async (req, res, next) => {
            try {
                const { language, opportunityId } = req.params;
                const { archive } = req.body;
                if (!language || !opportunityId) {
                    res.status(400).json({ message: messages_1.MESSAGES.OPPORTUNITY.MISSING_LANGUAGE_OR_ID });
                    return;
                }
                if (archive === undefined) {
                    res.status(400).json({ message: messages_1.MESSAGES.OPPORTUNITY.MISSING_ARCHIVE_FLAG });
                    return;
                }
                const updateOpportunity = await this.opportunityService.toggleArchivedOppportunity(language, opportunityId, archive);
                res.status(200).json({
                    updateOpportunity,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.updateOpportunityVersion = async (req, res, next) => {
            try {
                const { opportunityId, language } = req.params;
                const versionData = req.body;
                if (!opportunityId || !language)
                    throw new http_exception_1.default(400, messages_1.MESSAGES.OPPORTUNITY.MISSING_LANGUAGE_OR_ID);
                res.status(200).send(await this.opportunityService.updateOpportunityVersion(opportunityId, language, versionData));
            }
            catch (error) {
                next(error);
            }
        };
        this.getAll = async (request, response, next) => {
            try {
                const queries = request.query;
                const result = await this.opportunityService.getAll(queries);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.getAllUrgentandLatest = async (request, response, next) => {
            try {
                const queries = request.query;
                const result = await this.opportunityService.getAllUrgentLatest(queries);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.get = async (request, response, next) => {
            try {
                response.send(await this.opportunityService.get(request.params.opportunityId));
            }
            catch (error) {
                next(error);
            }
        };
        this.delete = async (request, response, next) => {
            try {
                const opportunityId = request.params.id;
                await this.opportunityService.delete(opportunityId);
                response.send({
                    message: messages_1.MESSAGES.OPPORTUNITY.DELETED,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.importOpportunitiesFromHunter = async (request, response, next) => {
            try {
                response.send(await this.opportunityService.importOpportunitiesFromHunter());
            }
            catch (error) {
                next(error);
            }
        };
        this.applyForJob = async (req, res, next) => {
            try {
                const application = await this.applicationService.applyForJob(req.body, req.params.opportunityId, req.user);
                res.status(201).send(application);
            }
            catch (error) {
                next(error);
            }
        };
        this.getApplication = async (request, response, next) => {
            try {
                const applicationId = request.params.id;
                const result = await this.applicationService.get(applicationId);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.getAllApplications = async (request, response, next) => {
            try {
                const queries = request.query;
                const currentUser = request.user;
                const result = await this.applicationService.getAll(queries, currentUser);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.getstats = async (request, response, next) => {
            try {
                const queries = request.query;
                const currentUser = request.user;
                const result = await this.applicationService.getApplicationsStats(queries, currentUser);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.getAllApplicationsByCandidat = async (request, response, next) => {
            try {
                const queries = request.query;
                const candidateId = request.params.id;
                if (!candidateId) {
                    throw new http_exception_1.default(400, messages_1.MESSAGES.APPLICATION.ID_CANDIDATE_REQUIRED);
                }
                const result = await this.applicationService.getAllApplicationsByCandidat(queries, candidateId);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.getAllCandidatures = async (request, response, next) => {
            try {
                const queries = request.query;
                const result = await this.applicationService.getAllApplications(queries);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.updateapplicationStatus = async (request, response, next) => {
            try {
                const applicationId = request.params.id;
                const applicationData = request.body;
                const result = await this.applicationService.updateApplicationStatus(applicationId, applicationData);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.cancelApplication = async (request, response, next) => {
            try {
                const id = request.params.id;
                const currentUser = request.user;
                await this.applicationService.cancelApplication(id, currentUser);
                response.send({
                    message: messages_1.MESSAGES.APPLICATION.DELETED,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.updateOpportunitySitemap = async (request, response, next) => {
            try {
                await this.opportunityService.updateOpportunitySitemap();
                response.send({
                    message: messages_1.MESSAGES.SITEMAP.CREATED,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.initialiseRoutes();
    }
    initialiseRoutes() {
        this.router.get(`${this.path}`, validateApiKey_middleware_1.default, this.getAll);
        this.router.get(`${this.path}/urgent`, validateApiKey_middleware_1.default, this.getAllUrgentandLatest);
        this.router.get(`${this.path}/applications`, validateApiKey_middleware_1.default, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.CANDIDATE]), queries_validation_middleware_1.default, this.getAllApplications);
        this.router.get(`${this.path}/stats`, validateApiKey_middleware_1.default, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.CANDIDATE]), queries_validation_middleware_1.default, this.getstats);
        this.router.get('/candidate/applications/:id', this.getAllApplicationsByCandidat);
        this.router.get('/applications', validateApiKey_middleware_1.default, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), queries_validation_middleware_1.default, this.getAllCandidatures);
        this.router.get(`${this.path}/:opportunityId`, validateApiKey_middleware_1.default, authentication_middleware_1.default, mongoId_validation_middleware_1.validateMongoIds, this.get);
        this.router.get(`${this.path}/import/hunter`, this.importOpportunitiesFromHunter);
        this.router.get('/applications/:id', validateApiKey_middleware_1.default, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.CANDIDATE, constants_1.Role.ADMIN]), mongoId_validation_middleware_1.default, this.getApplication);
        this.router.get(`${this.path}/:language/:url`, validateApiKey_middleware_1.default, this.getOpportunityByUrlANDlanguages);
        this.router.get(`${this.path}/opposite/:language/:url`, validateApiKey_middleware_1.default, this.getSlugBySlugOpportunity);
        this.router.put(`${this.path}/:language/:opportunityId/desarchiver`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), this.toggleArchiveStatusByLanguageAndId);
        this.router.post(`${this.path}/applications/:opportunityId`, this.applyForJob);
        this.router.post(`${this.path}/:language/:opportunityId`, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), this.updateOpportunityVersion);
        this.router.get('/countries', this.getDistinctCountries);
        this.router.put(`${this.path}/:opportunityId`, this.updateMainFields);
        this.router.put(`/UpdateJobdescription`, async (req, res, next) => {
            try {
                await this.opportunityService.updateJobDescriptions();
            }
            catch (error) {
                next(error);
            }
        });
        this.router.delete(`${this.path}/:id`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.RECRUITER]), mongoId_validation_middleware_1.default, this.delete);
        this.router.delete(`${this.path}/applications/:id`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.CANDIDATE]), mongoId_validation_middleware_1.default, this.cancelApplication);
        this.router.delete(`${this.path}/archive/:id`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), this.archivedOpportunite);
        this.router.put('/applications/:id', this.updateapplicationStatus);
        this.router.put(`${this.path}`, this.updateOpportunitySitemap);
    }
}
exports.default = OpportunityController;
//# sourceMappingURL=opportunity.controller.js.map