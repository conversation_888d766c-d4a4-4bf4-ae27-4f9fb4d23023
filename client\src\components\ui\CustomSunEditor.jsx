
import SunEditor from "suneditor-react";
import plugins from "suneditor/src/plugins";
import "suneditor/dist/css/suneditor.min.css";

export default function CustomSunEditor({
  content = "",
  onChange,
  onPaste,
  onImageUpload,
  height = "300px",
  maxHeight = "400px",
  showCharCount = true,
  buttonList = null,
}) {
  return (
    <SunEditor
      setContents={content}
      onChange={onChange}
      onPaste={onPaste}
      onImageUpload={onImageUpload}
      setOptions={{
        plugins,
        cleanHTML: false,
        disableHtmlSanitizer: true,
        minHeight: height,
        maxHeight: maxHeight,
        resizingBar: false,
        defaultTag: "div",
        showPathLabel: false,
        charCounter: showCharCount,
        charCounterType: "byte",
        font: [
          "Proxima-Nova-Regular",
          "Proxima-Nova-Medium",
          "Proxima-Nova-Semibold",
          "Proxima-Nova-Bold",
          "Proxima-Nova-Extrabold",
          "Proxima-Nova-Black",
          "Proxima-Nova-Light",
          "Proxima-Nova-Thin",
          "Arial",
          "Times New Roman",
          "Sans-Serif",
        ],

        colorList: [
          [
            "#234791",
            "#d69b19",
            "#cc3233",
            "#009966",
            "#0b3051",
            "#2BBFAD",
            "#0b305100",
            "#0a305214",
            "#743794",
            "#ff0000",
            "#ff5e00",
            "#ffe400",
            "#abf200",
            "#00d8ff",
            "#0055ff",
            "#6600ff",
            "#ff00dd",
            "#000000",
            "#ffd8d8",
            "#fae0d4",
            "#faf4c0",
            "#e4f7ba",
            "#d4f4fa",
            "#d9e5ff",
            "#e8d9ff",
            "#ffd9fa",
            "#f1f1f1",
            "#ffa7a7",
            "#ffc19e",
            "#faed7d",
            "#cef279",
            "#b2ebf4",
            "#b2ccff",
            "#d1b2ff",
            "#ffb2f5",
            "#bdbdbd",
            "#f15f5f",
            "#f29661",
            "#e5d85c",
            "#bce55c",
            "#5cd1e5",
            "#6699ff",
            "#a366ff",
            "#f261df",
            "#8c8c8c",
            "#980000",
            "#993800",
            "#998a00",
            "#6b9900",
            "#008299",
            "#003399",
            "#3d0099",
            "#990085",
            "#353535",
            "#670000",
            "#662500",
            "#665c00",
            "#476600",
            "#005766",
            "#002266",
            "#290066",
            "#660058",
            "#222222",
          ],

        ],
        addTagsWhitelist: "h1|h2|h3|h4|h5|h6|p|div|span|strong|em|ul|li|ol|a|img|table|thead|tbody|tr|td|br|hr|button",
        buttonList: buttonList || [
          ["undo", "redo"],
          ["font", "fontSize", "formatBlock"],
          ["bold", "underline", "italic", "strike"],
          ["fontColor", "hiliteColor"],
          ["align", "list", "lineHeight"],
          ["outdent", "indent"],
          ["table", "horizontalRule", "link", "image", "video"],
          ["fullScreen", "showBlocks", "codeView", "preview"],
          ["removeFormat"],
        ],
      }}
    />
  );
}
