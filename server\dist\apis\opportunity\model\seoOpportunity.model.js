"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const seoOpportunitySchema = new mongoose_1.Schema({
    metaTitle: {
        type: String,
        required: true,
        minlength: 3,
    },
    metaDescription: {
        type: String,
        required: true,
    },
    robotMeta: {
        type: String,
        required: true
    }
}, {
    timestamps: true,
});
exports.default = (0, mongoose_1.model)('SeoOpportunity', seoOpportunitySchema);
//# sourceMappingURL=seoOpportunity.model.js.map