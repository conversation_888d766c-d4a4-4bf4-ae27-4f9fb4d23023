import Benefits from "../../../../components/expert-care/Benefits";
import ECBanner from "../../../../components/expert-care/ECBanner";
import dynamic from "next/dynamic";
import EcFeatures from "../../../../components/expert-care/EcFeatures";
const VideoEC = dynamic(() => import("@/components/expert-care/VideoEC"), {
  ssr: false,
});
export async function generateMetadata() {
  return {
    title: "International Recruitment, Staffing & Payroll Agency | Pentabell",
    description:
      "Seeking the best recruitment agency ? Pentabell is an award-winning provider of international recruitment, staffing, consulting and HR services.",
    robots: "nofollow, noindex",
  };
}

async function page({ params: { locale } }) {
  return (
    <div>
      <ECBanner />
      <Benefits />
      <VideoEC />
      <EcFeatures />
    </div>
  );
}

export default page;
