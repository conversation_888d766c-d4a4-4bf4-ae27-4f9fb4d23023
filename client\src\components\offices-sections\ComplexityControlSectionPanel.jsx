import SvgComprehensiveEmploymentIcon from "@/assets/images/services/icons/ComprehensiveEmploymentIcon.svg";
import SvgGlobalPayrollIcon from "@/assets/images/services/icons/GlobalPayrollIcon.svg";
import SvgtickIcon from "@/assets/images/services/icons/tickIcon.svg";
import SvgMultiCurrencyIcon from "@/assets/images/services/icons/MultiCurrencyIcon.svg";
import SvglegalIcon from "@/assets/images/services/icons/legalIcon.svg";
import SvgtaxManagementIcon from "@/assets/images/services/icons/taxManagementIcon.svg";
import ComplexityControlSection from "@/components/ui/ComplexityControlSection";



function ComplexityControlSectionPanel({ t }) {
  const items = [
    {
      icon: SvgComprehensiveEmploymentIcon,
      title: t("Tunisia:complexity:dataS1:title"),
      description: t("Tunisia:complexity:dataS1:description"),
    },
    {
      icon: SvgGlobalPayrollIcon,
      title: t("Tunisia:complexity:dataS2:title"),
      description: t("Tunisia:complexity:dataS2:description"),
    },
    {
      icon: SvgtickIcon,
      title: t("Tunisia:complexity:dataS3:title"),
      description: t("Tunisia:complexity:dataS3:description"),
    },
    {
      icon: SvgtaxManagementIcon,
      title: t("Tunisia:complexity:dataS4:title "),
      description: t("Tunisia:complexity:dataS4:description"),
    },
    {
      icon: SvglegalIcon,
      title: t("Tunisia:complexity:dataS5:title"),
      description: t("Tunisia:complexity:dataS5:description"),
    },
    {
      icon: SvgMultiCurrencyIcon,
      title: t("Tunisia:complexity:dataS6:title"),
      description: t("Tunisia:complexity:dataS6:description"),
    },
  ];

  return (
    <ComplexityControlSection
      title1={t("Tunisia:complexity:title1")}
      title2={t("Tunisia:complexity:title2")}
      description=""
      items={items}
    />
  );
}

export default ComplexityControlSectionPanel;
