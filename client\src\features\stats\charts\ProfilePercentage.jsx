"use client";

import React, { useState, useEffect, useRef } from "react";
import PropTypes from "prop-types";
import { CircularProgressbar, buildStyles } from "react-circular-progressbar";
import "react-circular-progressbar/dist/styles.css";
import userIcon from "@/assets/images/userdashicon.png";
import CustomButton from "../../../components/ui/CustomButton";
import { useTranslation } from "react-i18next";
import { baseUrlFrontoffice } from "../../../helpers/routesList";

function ProfilePercentage({ percentage }) {
  const [progress, setProgress] = useState(0);
  const { t } = useTranslation();
  const progressRef = useRef(progress);
  const animationFrameRef = useRef(null);

  useEffect(() => {

    setProgress(0);

    let start = 0;
    const duration = 500;
    const increment = (percentage - start) / (duration / 16);


    const updateProgress = () => {
      start += increment;

      if (start >= percentage) {
        setProgress(percentage);
        return;
      }

      progressRef.current = start;
      setProgress(start);
      animationFrameRef.current = requestAnimationFrame(updateProgress);
    };

    animationFrameRef.current = requestAnimationFrame(updateProgress);

    return () => cancelAnimationFrame(animationFrameRef.current);
  }, [percentage]);


  return (
    <div className="progress-container">
      <p className="profile-title">{t("HomeDashboard:titleProgress")}</p>

      <div className="gauge-container" style={{ position: "relative", width: "150px", height: "150px" }}>
        <CircularProgressbar
          value={percentage}
          maxValue={100}
          strokeWidth={6}
          styles={buildStyles({
            pathColor: "#234791",
            trailColor: "#f8fafd",
            strokeLinecap: "round",
            strokeMiterlimit: "2",
            textSize: "24px",
            pathTransitionDuration: 2,
          })}
        />


        <div
          className="view-info"
          style={{
            position: "absolute",
            top: "35%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            textAlign: "center",
          }}
        >
          <img
            src={userIcon.src}
            className="user-icon-progress"
            alt="User Icon"
            loading="lazy"
            style={{ width: "32px", height: "32px", borderRadius: "50%" }}
          />
        </div>  <div
          style={{
            position: "absolute",
            top: "50%",
            left: "50%",

            transform: "translateX(-50%)",
            color: "#234791",
            fontSize: "30px",
            fontWeight: "600",
            textAlign: "center",
          }}
        >
          {Math.round(percentage)}%
        </div>
      </div>

      <div className="upgrade-button">
        <CustomButton
          className="btn btn-homepage btn-filled"
          text={t("HomeDashboard:Upgrade")}
          link={`/${baseUrlFrontoffice.baseURL.route}/my-profile`}
        />
      </div>
    </div>
  );
}

ProfilePercentage.propTypes = {
  percentage: PropTypes.number.isRequired,
};

export default ProfilePercentage;
