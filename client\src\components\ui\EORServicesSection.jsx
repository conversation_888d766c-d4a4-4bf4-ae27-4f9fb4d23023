import { Container, Grid } from "@mui/material";
import EmblaCarouselThumbsTeamPic from "./emblaCarousel/EmblaCarouselThumbsTeamPic"

function EORServicesSection({ t, imgSlides = [], translationKeyPrefix = "", Description }) {
  const OPTIONS = {};

  return (
    <Container id="eor-services-tn" className="custom-max-width">
      <Grid className="container" justifyContent="space-between" container spacing={2}>
        <Grid item xs={12} sm={6}>
          <div className="team-thumbs">
            <EmblaCarouselThumbsTeamPic slides={imgSlides} options={OPTIONS} />
          </div>
        </Grid>
        <Grid item xs={12} sm={6}>
          <div className="right-section">
            <p className="paragraph text-yellow">
              {t(`${translationKeyPrefix}:subTitle`)}
            </p>
            <p className="heading-h2 text-white">
              {t(`${translationKeyPrefix}:title`)}
            </p>
            <p className="paragraph text-white">
              {t(`${translationKeyPrefix}:description`)}
            </p>
            <p className="paragraph text-white">
              {t(`${Description}`)}
            </p>
          </div>
        </Grid>
      </Grid>
    </Container>
  );
}

export default EORServicesSection;
