"use client";
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { styled } from '@mui/material/styles';
import Paper from '@mui/material/Paper';
import { Box, Card, Grid, Grid2 } from "@mui/material";
import ProfilePercentage from "@/features/stats/charts/ProfilePercentage";     
import CandidateApplicationsChart from "../../application/component/CandidateApplicationsChart";

import { useGetUserData } from "@/features/user/hooks/updateProfile.hooks";
import { useGetApplications } from "../../application/hooks/application.hooks";
import ApplicationsStatusChart from "@/features/stats/charts/ApplicationsStatusChart";
import ProfileViews from "../../application/component/ProfileViews";
import FavoriteArticleHomePage from "@/features/favorite/components/FavoriteArticleHomePage";
import FavoriteOpportunitiesHomePage from "@/features/favorite/components/FavoriteOpportunitiesHomePage";

const HomePageDashboardCandidat = () => {
    const { t } = useTranslation();
    const { data } = useGetUserData(t);

    const [searchQuery, setSearchQuery] = useState("");
    const [applicationsData, setApplicationsData] = useState(null);
    const [paginationModel, setPaginationModel] = useState({ pageSize: 5, page: 0 });

    const getApplications = useGetApplications({
        pageSize: paginationModel.pageSize,
        pageNumber: paginationModel.page + 1,
        searchQuery,
    });

    useEffect(() => {
        getApplications.refetch() && setApplicationsData(getApplications.data);
    }, [getApplications.data, paginationModel, searchQuery]);

    const Item = styled(Paper)(({ theme }) => ({
        backgroundColor: '#fff',
        ...theme.typography.body2,
        padding: theme.spacing(1),
        textAlign: 'center',
        color: theme.palette.text.secondary,
    }));

    return (
        <>
            <ApplicationsStatusChart
                accepted={applicationsData?.totalAccepted}
                rejected={applicationsData?.totalRejected}
                pending={applicationsData?.totalPending}
                totalApplications={applicationsData?.candidateNumberOfApp}
            />

            <Grid container spacing={2}>
                <Grid item xs={12} sm={4} md={3}>
                    <Item className="list-Home-page" sx={{ height: "100% ", padding: "1px" }}>
                        <ProfilePercentage
                            percentage={Math.round(data?.candidate.profilePercentage || 0)}
                            maxPercentage={91}
                        />
                    </Item>
                </Grid>
                <Grid item xs={12} sm={4} md={6}>
                    <Item className="list-Home-page stat" sx={{ height: "100% ", padding: "1px" }}>
                        <CandidateApplicationsChart />
                    </Item>
                </Grid>
                <Grid item xs={12} sm={4} md={3} >
                    <Item className="list-Home-page" sx={{ height: "100%", padding: "1px" }}>
                        <ProfileViews   applicationData={applicationsData?.candidateNumberOfApp +1 }
                        />
                    </Item>
                </Grid>
                <Grid item xs={12} sm={6} md={6}>
                    <Card className="list-Home-page list-saved-article" sx={{ height: "100%", padding: "16px" }}>
                        <FavoriteOpportunitiesHomePage />
                    </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={6}>
                    <Card className="list-Home-page" sx={{ height: "100%", padding: "16px" }}>
                        <FavoriteArticleHomePage  />
                    </Card>
                </Grid>
            </Grid>
        </>
    );
};

export default HomePageDashboardCandidat;
