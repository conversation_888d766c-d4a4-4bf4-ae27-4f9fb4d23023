import SvgUsers from "@/assets/images/icons/yellow/Users.svg";
import Svglanguage from "@/assets/images/icons/yellow/language.svg";
import Svggdp from "@/assets/images/icons/yellow/gdp.svg";
import Svgcurrency from "@/assets/images/icons/yellow/currency.svg";
import SvgcapitalCity from "@/assets/images/icons/yellow/capitalCity.svg";
import Svggross from "@/assets/images/icons/yellow/gross.svg";
import OfficeInfo from "@/components/ui/OfficeInfo";

function OfficeInfoFrance({ t }) {
  const data = [
    {
      icon: <SvgUsers />,
      titleKey: "france:officeInfoTN:title1",
      descriptionKey: "france:officeInfoTN:description1"
    },
    {
      icon: <Svglanguage />,
      titleKey: "france:officeInfoTN:title2",
      descriptionKey: "france:officeInfoTN:description2"
    },
    {
      icon: <Svggdp />,
      titleKey: "france:officeInfoTN:title3",
      descriptionKey: "france:officeInfoTN:description3"
    },
    {
      icon: <Svgcurrency />,
      titleKey: "france:officeInfoTN:title4",
      descriptionKey: "france:officeInfoTN:description4"
    },
    {
      icon: <SvgcapitalCity />,
      titleKey: "france:officeInfoTN:title5",
      descriptionKey: "france:officeInfoTN:description5"
    },
    {
      icon: <Svggross />,
      titleKey: "france:officeInfoTN:title6",
      descriptionKey: "france:officeInfoTN:description6"
    }
  ];

  return <OfficeInfo data={data} t={t} />;
}


export default OfficeInfoFrance;
