"use client";;
import { Container, Grid, useMediaQuery, useTheme } from "@mui/material";
import Link from "next/link";

import bannerImg from "@/assets/images/website/coporate-profile/digital-waves3.png";
import bannerImgMobile from "@/assets/images/website/coporate-profile/digital-waves-mobile.png";
import earthGraph from "@/assets/images/website/coporate-profile/earth-graph.png";
import earthGraphMobile from "@/assets/images/website/coporate-profile/earth-graph-mobile.png";
import teamPic from "@/assets/images/website/coporate-profile/slides/badira-event.jpg";
import ramadaniftar from "@/assets/images/website/coporate-profile/slides/ramadaniftar.jpg";
import rhteam from "@/assets/images/website/coporate-profile/slides/rhteam.jpg";
import maTeam from "@/assets/images/website/coporate-profile/slides/maTeam.jpg";


import yellowShape from "@/assets/images/website/coporate-profile/yellow-shape.png";
import greenShape from "@/assets/images/website/coporate-profile/green-shape.png";
import Partners from "./Partners";
import img2 from "@/assets/images/website/coporate-profile/slides/Image_20250512_160458_968.jpg";
import img22 from "@/assets/images/website/coporate-profile/slides/devteam.jpg";
import img3 from "@/assets/images/website/coporate-profile/slides/amjad.jpg";
import img55 from "@/assets/images/website/coporate-profile/slides/ksaa.jpg";

import s2 from "@/assets/images/website/coporate-profile/slides/team.jpg";
import s5 from "@/assets/images/website/coporate-profile/slides/Image_20241223_101621_130.jpg";
import { useEffect, useState } from "react";
import { websiteRoutesList } from "../../../../helpers/routesList";
const images = [
  { src: teamPic, alt: "Pentabell’s Annual Sales Team Gathering" },
  { src: s2, alt: "Pentabell Team Morocco Team " },
  { src: s5, alt: "Pentabell 𝐈𝐒𝐎 𝟗𝟎𝟎𝟏-𝟐𝟎𝟏𝟓 Certification" },
  { src: img2, alt: "Pentabell at QHSE Expo 2025" },
  { src: img3, alt: "Pentabell UAE Team" },
  { src: ramadaniftar, alt: "Pentabell Tunisia Team Gathering 2025" },
  { src: img55, alt: "Pentabell at French Decarbonization Days 2025 - KSA" },
  { src: img22, alt: "Pentabell Dev Team" },
  { src: rhteam, alt: "Pentabell HR Team" },
  { src: maTeam, alt: "Pentabell at GITEX 2025" },

];

export default function WhoWeAre() {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const [currentIndex, setCurrentIndex] = useState(0);
  const [fade, setFade] = useState(false);

  const mainImage = images[currentIndex];
  const nextImage = images[(currentIndex + 1) % images.length];

  useEffect(() => {
    const interval = setInterval(() => {
      setFade(true);
      setTimeout(() => {
        setCurrentIndex((prev) => (prev + 1) % images.length);
        setFade(false);
      }, 100);
    }, 2500);
    return () => clearInterval(interval);
  }, []);

  return (
    <div
      id="who-we-are-section"
      style={{
        backgroundImage: `url(${isMobile ? bannerImgMobile.src : bannerImg.src
          })`,
        backgroundSize: "cover",
        backgroundPosition: "bottom",
        backgroundRepeat: "no-repeat",
        backgroundBlendMode: "hard-light",
      }}
    >
      <Container id="team-images" className="custom-max-width">
        <img
          className="fixed-element green"
          height={"100%"}
          width={"auto"}
          alt={"altImg"}
          src={greenShape.src}
          loading="lazy"
        />
        <Grid container columnSpacing={3} className="team-img">

          <Grid item xs={12} sm={8}>
            <img
              className={`image ${fade ? "fade-out" : "fade-in"}`}
              width={"100%"}
              height={"auto"}
              alt={mainImage.alt}
              title={mainImage.alt}
              src={mainImage.src.src}
              loading="lazy"
            />
          </Grid>
          <Grid item xs={12} sm={4}>
            <img
              className={`image ${fade ? "fade-out" : "fade-in"}`}
              width={"100%"}
              height={"auto"}
              alt={nextImage.alt}
              src={nextImage.src.src}
              loading="lazy"
            />
          </Grid>
        </Grid>
        <img
          className="fixed-element yellow"
          height={"100%"}
          width={"auto"}
          alt={"altImg"}
          src={yellowShape.src}
          loading="lazy"
        />
      </Container>
      <Container className="custom-max-width z-index">
        <Grid container>
          <Grid item xs={12} sm={12} md={10}>
            <p className="paragraph text-yellow mt-2 ">Who We Are ?</p>
            <h1 className="heading-h2 text-white bold">
              A trusted global leader in
              delivering solutions that bridge strategy, technology, and human
              potential.
            </h1>
            <p className="sub-heading text-white custom-sub-heading">
              Since our founding in 2005, Pentabell has evolved from a regional payroll specialist into a diversified <Link className="internal-link" href={`/${websiteRoutesList.services.route}`}>HR solutions</Link> partner. With a focus on talent strategy, HR digital transformation, and leadership development, we help businesses build resilient, future-ready workforces. Our growth is driven by a dynamic team of experts, advanced HR technologies, and strategic collaborations with world-class organizations across industries.

            </p>
          </Grid>

          <Grid item xs={0} sm={0} md={2}></Grid>
        </Grid>
      </Container>

      <div id="graph-map">
        <Container className="custom-max-width">
          <Grid container>
            <Grid item xs={12} sm={12} md={12}>
              <h2 className="heading-h2 text-yellow text-center bold">
                Expanding EMEA Strategic Footprint
              </h2>
              {
                !isMobile &&
                <h2 className="heading-h3 text-white text-center bold">
                  Cross-Border Solutions for Scalable Operations in Key Markets and Industries.
                </h2>
              }
            </Grid>

            <Grid item xs={12} sm={12} sx={{ marginTop: "-6%" }}>
              <Grid container columnSpacing={2} justifyContent={"space-evenly"}>
                <Grid item xs={12} sm={12}>
                  <img
                    width={"100%"}
                    height={"auto"}
                    alt={"Pentabell"}
                    src={isMobile ? earthGraphMobile.src : earthGraph.src}
                    loading="lazy"
                    className="gloabl-sites"
                  />
                </Grid>
                <Grid item xs={12} sm={3} className="country-data p-0">
                  <h3 className="sub-heading text-yellow semi-bold">
                    
                      <Link className="internal-link yellow" href={`/${websiteRoutesList.middleEastPage.route}`}>
                      Middle East</Link>
                    
                  </h3>
                  <p className="paragraph text-white">
                    Through strategic, long-term partnerships, we've delivered
                    multiple high-impact projects across the Middle East—fueling
                    sustained market growth and positioning Pentabell as a
                    trusted provider of cross-border solutions.
                  </p>
                </Grid>
                <Grid item xs={12} sm={3} className="country-data p-0">
                  <h3 className="sub-heading text-yellow semi-bold">
                    
                      <Link className="internal-link yellow" href={`/${websiteRoutesList.africaPage.route}`}>
                      Africa</Link></h3>
                  <p className="paragraph text-white">
                    With a strong presence and history of collaboration across
                    the continent, we've led numerous large-scale initiatives,
                    expanding our footprint in telecom and transportation
                    through scalable operations and reliable partnerships.
                  </p>
                </Grid>
                <Grid item xs={12} sm={3} className="country-data p-0">
                  <h3 className="sub-heading text-yellow semi-bold">
                    
                      <Link className="internal-link yellow" href={`/${websiteRoutesList.europePage.route}`}>
                      Europe</Link>
                    </h3>
                  <p className="paragraph text-white">
                    Europe continues to be a key growth engine, where our
                    tech-enabled workforce solutions have significantly
                    increased operational scalability—driven by impactful
                    strategies and strong local alliances
                  </p>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Container>
      </div>
      <Partners />
    </div>
  );
}
