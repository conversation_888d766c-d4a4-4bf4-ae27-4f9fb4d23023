"use client";

import { Container, Grid, useMediaQuery, useTheme } from "@mui/material";

import bannerImg from "@/assets/images/website/coporate-profile/expertcare-bg.png";
import bannerImgMobile from "@/assets/images/website/coporate-profile/expertcare-bg-mobile.png";
import bannerEC from "@/assets/images/website/banner/banner-ec.png";

function ExpertCareSection() {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  return (
    <div
      id="expert-care-section"
      style={{
        backgroundImage: `url(${isMobile ? bannerImgMobile.src : bannerImg.src
          })`,
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat",
      }}
    >
      <Container className="custom-max-width">
        <Grid container columnSpacing={3} alignItems={"center"}>
          <Grid item xs={12} sm={5}>
            <h3 >
              <span className="heading-h1 text-yellow semi-bold">ExpertCare</span>
              <br />
              <span className="heading-h3 text-white  semi-bold">
                End-to-End Consultant Management Platform
              </span>
            </h3>
            <p className="paragraph text-white">
              ExpertCare streamlines the entire consultant lifecycle—onboarding,
              contract management, document workflows, benefits administration,
              and offboarding—while enabling real-time salary processing,
              compliance tracking, and secure access to contracts,
              certifications, and payroll details.
            </p>
          </Grid>
          <Grid item xs={12} sm={7}>
            <img
              width={"100%"}
              height={"auto"}
              alt={`End-to-End Consultant Management Platform`}
              title={`ExpertCare`}
              src={bannerEC.src}
              loading="lazy"
            />
          </Grid>

          <Grid item xs={12} sm={4}>
            <h4 className="sub-heading text-yellow semi-bold mb-1">
              Comprehensive Self-Service Portal
            </h4>
            <p className="paragraph text-white">
              Secure access to contracts, payroll, benefits, and real-time
              updates anytime, anywhere, with streamlined communication to
              reduce back-and-forth.
            </p>
          </Grid>
          <Grid item xs={12} sm={4}>
            <h4 className="sub-heading text-yellow  semi-bold mb-1">
              Document & Benefits Management
            </h4>
            <p className="paragraph text-white">
              Digital workflows for contracts, compliance, onboarding, and
              efficient handling of allowances, reimbursements, and bonuses.
            </p>
          </Grid>
          <Grid item xs={12} sm={4}>
            <h4 className="sub-heading text-yellow semi-bold mb-1">
              Enhanced Security & Transparency
            </h4>
            <p className="paragraph text-white">
              All sensitive documents are protected with advanced encryption,
              ensuring consultants have instant, secure access to up-to-date
              information.
            </p>
          </Grid>
        </Grid>
      </Container>
    </div>
  );
}

export default ExpertCareSection;
