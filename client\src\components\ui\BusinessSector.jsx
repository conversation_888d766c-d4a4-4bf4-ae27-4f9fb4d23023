import { Container } from "@mui/material";

function BusinessSector({ titleKey, descriptionKey, sectors, t }) {
  return (
    <Container id="business-tunisia" className="custom-max-width">
      <h2 className="heading-h1 text-center">{t(titleKey)}</h2>
      <p className="sub-heading text-center">{t(descriptionKey)}</p>
      <div className="locations">
        {sectors.map((sector, index) => (
          <div key={index} className="location-item">
            <p className="label">
              <span>{sector.icon}</span>
            </p>
            <p className="value paragraph">{t(sector.titleKey)}</p>
            <p className="description paragraph">{sector.description}</p>
          </div>
        ))}
      </div>
    </Container>
  );
}

export default BusinessSector;
