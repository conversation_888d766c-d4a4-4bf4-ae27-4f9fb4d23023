"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/blog/[url]/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/new-blog/BlogPageDetails.jsx":
/*!*******************************************************************!*\
  !*** ./src/features/blog/components/new-blog/BlogPageDetails.jsx ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! moment */ \"(app-pages-browser)/./node_modules/moment/moment.js\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var moment_locale_fr__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! moment/locale/fr */ \"(app-pages-browser)/./node_modules/moment/locale/fr.js\");\n/* harmony import */ var moment_locale_fr__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(moment_locale_fr__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _features_user_component_updateProfile_experience_DialogModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/features/user/component/updateProfile/experience/DialogModal */ \"(app-pages-browser)/./src/features/user/component/updateProfile/experience/DialogModal.jsx\");\n/* harmony import */ var _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../hooks/blog.hook */ \"(app-pages-browser)/./src/features/blog/hooks/blog.hook.js\");\n/* harmony import */ var _ArticleContent__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../ArticleContent */ \"(app-pages-browser)/./src/features/blog/components/ArticleContent.jsx\");\n/* harmony import */ var _utils_functions__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/functions */ \"(app-pages-browser)/./src/utils/functions.js\");\n/* harmony import */ var _assets_images_icons_time_svg__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/assets/images/icons/time.svg */ \"(app-pages-browser)/./src/assets/images/icons/time.svg\");\n/* harmony import */ var _components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* harmony import */ var _CommentsListByBlog__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../CommentsListByBlog */ \"(app-pages-browser)/./src/features/blog/components/CommentsListByBlog.jsx\");\n/* harmony import */ var _CreateBlogComment__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../CreateBlogComment */ \"(app-pages-browser)/./src/features/blog/components/CreateBlogComment.jsx\");\n/* harmony import */ var _assets_images_icons_arrowRight_svg__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/assets/images/icons/arrowRight.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowRight.svg\");\n/* harmony import */ var _features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/features/auth/hooks/currentUser.hooks */ \"(app-pages-browser)/./src/features/auth/hooks/currentUser.hooks.js\");\n/* harmony import */ var _utils_urls__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/utils/urls */ \"(app-pages-browser)/./src/utils/urls.js\");\n/* harmony import */ var _config_axios__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/config/axios */ \"(app-pages-browser)/./src/config/axios.js\");\n/* harmony import */ var _components_pages_sites_sections_NewsletterSubscription__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/pages/sites/sections/NewsletterSubscription */ \"(app-pages-browser)/./src/components/pages/sites/sections/NewsletterSubscription.jsx\");\n/* harmony import */ var _opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ../../../opportunity/hooks/opportunity.hooks */ \"(app-pages-browser)/./src/features/opportunity/hooks/opportunity.hooks.js\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var _assets_images_icons_bookmark_svg__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/assets/images/icons/bookmark.svg */ \"(app-pages-browser)/./src/assets/images/icons/bookmark.svg\");\n/* harmony import */ var _ContentTable__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./ContentTable */ \"(app-pages-browser)/./src/features/blog/components/new-blog/ContentTable.jsx\");\n/* harmony import */ var _ShareOnSocialMedia__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./ShareOnSocialMedia */ \"(app-pages-browser)/./src/features/blog/components/new-blog/ShareOnSocialMedia.jsx\");\n/* harmony import */ var _RelatedBlog__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ./RelatedBlog */ \"(app-pages-browser)/./src/features/blog/components/new-blog/RelatedBlog.jsx\");\n/* harmony import */ var _FaqAccordion__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ./FaqAccordion */ \"(app-pages-browser)/./src/features/blog/components/new-blog/FaqAccordion.jsx\");\n/* harmony import */ var _CTSBanner__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./CTSBanner */ \"(app-pages-browser)/./src/features/blog/components/new-blog/CTSBanner.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction BlogPageDetails(param) {\n    let { id, article, language, url } = param;\n    _s();\n    const [showDeleteConfirmation, setShowDeleteConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [jobsTodelete, setJobsTodelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)();\n    const handeleDeleteconfirmation = ()=>{\n        setJobsTodelete(id);\n        setShowDeleteConfirmation(true);\n    };\n    const handlecanceldelete = ()=>{\n        setShowDeleteConfirmation(false);\n    };\n    const deleteOpportunityFromShortlist = async (articleId)=>{\n        try {\n            await _config_axios__WEBPACK_IMPORTED_MODULE_17__.axiosGetJson.delete(`/favourite/${articleId}`, {\n                data: {\n                    type: \"article\"\n                }\n            });\n            setIsSaved(false);\n        } catch (error) {}\n    };\n    const handleToggleOpportunity = async ()=>{\n        try {\n            await deleteOpportunityFromShortlist(jobsTodelete);\n        } catch (error) {}\n        setShowDeleteConfirmation(false);\n    };\n    const theme = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"])();\n    const { user } = (0,_features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_15__[\"default\"])();\n    const { i18n } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)();\n    const isMobile = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"])(theme.breakpoints.down(\"sm\"));\n    const useAddTofavouriteHook = (0,_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_19__.useAddToFavourite)();\n    moment__WEBPACK_IMPORTED_MODULE_2___default().locale(i18n.language || \"en\");\n    const [headings, setHeadings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [modifiedHtmlContent, setModifiedHtmlContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(article?.content);\n    const [relatedArticles, setRelatedArticles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isSaved, setIsSaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const getArticle = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_7__.useGetArticleByUrlANDlanguage)({\n        language,\n        urlArticle: url\n    }, {\n        enabled: !!url\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const parser = new DOMParser();\n        const doc = parser.parseFromString(article?.content, \"text/html\");\n        const extractedHeadings = [];\n        Array.from(doc.querySelectorAll(\"h2, h3\")).forEach((heading)=>{\n            const id = heading.innerText.toLowerCase().replace(/\\s+/g, \"-\").replace(/[^a-z0-9\\-]/g, \"\");\n            heading.id = id;\n            extractedHeadings.push({\n                tagName: heading.tagName.toLowerCase(),\n                content: heading.innerText,\n                id\n            });\n        });\n        setHeadings(extractedHeadings);\n        setModifiedHtmlContent(doc.body.innerHTML);\n    }, [\n        article?.content\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkIfSaved = async ()=>{\n            if (id) {\n                try {\n                    const response = await _config_axios__WEBPACK_IMPORTED_MODULE_17__.axiosGetJson.get(`/favourite/is-saved/${id}`);\n                    setIsSaved(response.data);\n                } catch (error) {\n                    if (false) {}\n                }\n            }\n        };\n        checkIfSaved();\n    }, [\n        id\n    ]);\n    const articleId = getArticle?.data?._id;\n    const handleSaveClick = ()=>{\n        if (!user) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.warning(\"Login or create account to save article.\");\n        } else {\n            useAddTofavouriteHook.mutate({\n                id: id,\n                title: article?.title,\n                typeOfFavourite: \"article\"\n            }, {\n                onSuccess: ()=>{\n                    setIsSaved(true);\n                }\n            });\n        }\n    };\n    const { data: commentsData, isLoading: commentsLoading, refetch: commentsRefetch } = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_7__.useGetComments)({\n        articleId,\n        pageNumber: 1,\n        pageSize: 6,\n        paginated: true\n    }, {\n        enabled: !!articleId\n    });\n    // const categorySlugs = article.categories.map((cat) => cat.url).join(\"/\");\n    const sahredUrl = `${\"http://localhost:3000\"}/blog/${url}`;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchRelatedArticles = async ()=>{\n            if (article?.categories?.length > 0) {\n                const categorySlugs = article?.categories.map((cat)=>cat.url).join(\"/\");\n                if (!categorySlugs) return;\n                try {\n                    const response = await _config_axios__WEBPACK_IMPORTED_MODULE_17__.axiosGetJsonSSR.get(`${_utils_urls__WEBPACK_IMPORTED_MODULE_16__.API_URLS.articles}/${language}/blog/category/${categorySlugs}`, {\n                    });\n                    if (response.status === 200) {\n                        const data = await response.data;\n                        const fetchedArticles = data?.articles?.map((item)=>({\n                                id: item._id,\n                                title: item.versions[0]?.title || \"Titre non disponible\",\n                                picture: item.versions[0]?.image || \"\",\n                                description: item.versions[0]?.metaDescription || \"Description non disponible\",\n                                slug: item.versions[0]?.url || \"\"\n                            }));\n                        setRelatedArticles(fetchedArticles);\n                    } else {\n                        if (false) {}\n                    }\n                } catch  {\n                    if (false) {}\n                }\n            }\n        };\n        fetchRelatedArticles();\n    }, [\n        article?.categories,\n        language\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"blog-page-details\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        id: \"blog-header\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                            className: \"custom-max-width\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                container: true,\n                                spacing: 3,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        sm: 6,\n                                        children: [\n                                            article?.categories?.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"categories-list\",\n                                                children: article.categories.map((category)=>category?.url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"category light\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: `${language === \"en\" ? `/blog/category/${category.url}` : `/${language}/blog/category/${category.url}`}/`,\n                                                            children: category.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, category.url, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 27\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"heading-h1\",\n                                                children: article?.highlights ? article?.highlights.length > 0 ? (0,_utils_functions__WEBPACK_IMPORTED_MODULE_9__.highlightMatchingWords)(article?.title, article?.highlights) : (0,_utils_functions__WEBPACK_IMPORTED_MODULE_9__.splitLastWord)(article?.title) : (0,_utils_functions__WEBPACK_IMPORTED_MODULE_9__.splitLastWord)(article?.title)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"sub-heading date \",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_time_svg__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \" \",\n                                                    article?.publishDate ? (0,_utils_functions__WEBPACK_IMPORTED_MODULE_9__.capitalizeFirstLetter)(moment__WEBPACK_IMPORTED_MODULE_2___default()(article?.publishDate).format(\"LL\")) : \"\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_time_svg__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \" \",\n                                                    article?.publishDate ? (0,_utils_functions__WEBPACK_IMPORTED_MODULE_9__.capitalizeFirstLetter)(moment__WEBPACK_IMPORTED_MODULE_2___default()(article?.publishDate).format(\"LT\")) : \"\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 17\n                                            }, this),\n                                            (!user || user?.roles?.includes(_utils_constants__WEBPACK_IMPORTED_MODULE_20__.Role.CANDIDATE)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                leftIcon: true,\n                                                text: isSaved ? \"Saved\" : \"Save\",\n                                                onClick: isSaved ? handeleDeleteconfirmation : handleSaveClick,\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_bookmark_svg__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: `${isSaved ? \"btn-filled-yellow\" : \"\"}`\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 23\n                                                }, void 0),\n                                                className: `btn btn-ghost p-save ${isSaved ? \"btn-filled-yellow \" : \"\"}`\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        sm: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"blog-img\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: `${\"http://localhost:4000/api/v1\"}/files/${article?.image}`,\n                                                width: 700,\n                                                height: 700,\n                                                alt: article?.alt,\n                                                loading: \"lazy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                                lineNumber: 208,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                            lineNumber: 207,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        type: \"application/ld+json\",\n                        dangerouslySetInnerHTML: {\n                            __html: JSON.stringify({\n                                \"@context\": \"https://schema.org\",\n                                \"@type\": \"BreadcrumbList\",\n                                itemListElement: [\n                                    {\n                                        \"@type\": \"ListItem\",\n                                        position: 1,\n                                        item: {\n                                            \"@id\": language === \"en\" ? `https://www.pentabell.com/blog/` : `https://www.pentabell.com/${language}/blog/`,\n                                            name: \"Blog\"\n                                        }\n                                    },\n                                    article?.categories[0].url && {\n                                        \"@type\": \"ListItem\",\n                                        position: 2,\n                                        item: {\n                                            \"@id\": language === \"en\" ? `https://www.pentabell.com/blog/category/${article?.categories[0].url}/` : `https://www.pentabell.com/${language}/blog/category/${article?.categories[0].url}/`,\n                                            name: article?.categories[0].name\n                                        }\n                                    }\n                                ]\n                            })\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                        lineNumber: 291,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                        className: \"custom-max-width\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"categories-path\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        locale: language === \"en\" ? \"en\" : \"fr\",\n                                        href: `${language === \"en\" ? `/blog` : `/${language}/blog`}/`,\n                                        className: \"link\",\n                                        children: \"Blog\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 13\n                                    }, this),\n                                    article?.categories?.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowRight_svg__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 17\n                                            }, this),\n                                            article?.categories[0].url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                className: \"link\",\n                                                href: `${language === \"en\" ? `/blog/category/${article?.categories[0].url}` : `/${language}/blog/category/${article?.categories[0].url}`}/`,\n                                                children: article?.categories[0].name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                                lineNumber: 326,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                className: \"container\",\n                                container: true,\n                                columnSpacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        sm: 8,\n                                        children: [\n                                            isMobile && headings?.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ContentTable__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                headings: headings\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 17\n                                            }, this) : null,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"blog-content\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ArticleContent__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    htmlContent: modifiedHtmlContent\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CTSBanner__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                ctsBanner: article?.ctsBanner\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 15\n                                            }, this),\n                                            article?.faq?.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FaqAccordion__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                title: article.faqTitle,\n                                                items: article.faq\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        sm: 4,\n                                        id: \"sticky-sidebar\",\n                                        children: !isMobile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                headings?.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ContentTable__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    headings: headings\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 21\n                                                }, this) : null,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ShareOnSocialMedia__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    sahredUrl: sahredUrl\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"section\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pages_sites_sections_NewsletterSubscription__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : null\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                                lineNumber: 353,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                className: \"container\",\n                                container: true,\n                                columnSpacing: 2,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 8,\n                                    children: [\n                                        isMobile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ShareOnSocialMedia__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    sahredUrl: sahredUrl\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"section\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pages_sites_sections_NewsletterSubscription__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : null,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RelatedBlog__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            relatedArticles: relatedArticles,\n                                            language: language,\n                                            articleId: articleId\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateBlogComment__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            refetch: commentsRefetch,\n                                            articleId: articleId,\n                                            user: user\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CommentsListByBlog__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            articleId: articleId,\n                                            data: commentsData,\n                                            isLoading: commentsLoading,\n                                            refetch: commentsRefetch,\n                                            user: user\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                                lineNumber: 382,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                        lineNumber: 325,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                lineNumber: 205,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_user_component_updateProfile_experience_DialogModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                open: showDeleteConfirmation,\n                message: t(\"messages:supprimerarticlefavoris\"),\n                onClose: handlecanceldelete,\n                onConfirm: handleToggleOpportunity\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\new-blog\\\\BlogPageDetails.jsx\",\n                lineNumber: 414,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(BlogPageDetails, \"/v+Y0kfYQ3w5JdtTNTAZGIlNz1A=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation,\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n        _features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        react_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation,\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n        _opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_19__.useAddToFavourite,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_7__.useGetArticleByUrlANDlanguage,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_7__.useGetComments\n    ];\n});\n_c = BlogPageDetails;\n/* harmony default export */ __webpack_exports__[\"default\"] = (BlogPageDetails);\nvar _c;\n$RefreshReg$(_c, \"BlogPageDetails\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9mZWF0dXJlcy9ibG9nL2NvbXBvbmVudHMvbmV3LWJsb2cvQmxvZ1BhZ2VEZXRhaWxzLmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQzRDO0FBQ2hCO0FBQ29CO0FBQ1E7QUFDakI7QUFDYjtBQUNxQjtBQUMwQztBQUsxRDtBQUNnQjtBQUlwQjtBQUMwQjtBQUNHO0FBQ0Q7QUFDRjtBQUNZO0FBQ0k7QUFDN0I7QUFDdUI7QUFDK0I7QUFDYjtBQUN4QztBQUNvQjtBQUNYO0FBQ1I7QUFDWTtBQUNkO0FBQ0U7QUFDTjtBQUVwQyxTQUFTa0MsZ0JBQWdCLEtBQThCO1FBQTlCLEVBQUVDLEVBQUUsRUFBRUMsT0FBTyxFQUFFQyxRQUFRLEVBQUVDLEdBQUcsRUFBRSxHQUE5Qjs7SUFDdkIsTUFBTSxDQUFDQyx3QkFBd0JDLDBCQUEwQixHQUFHdkMsK0NBQVFBLENBQUM7SUFDckUsTUFBTSxDQUFDd0MsY0FBY0MsZ0JBQWdCLEdBQUd6QywrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLEVBQUUwQyxDQUFDLEVBQUUsR0FBR25DLDZEQUFjQTtJQUM1QixNQUFNb0MsNEJBQTRCO1FBQ2hDRixnQkFBZ0JQO1FBQ2hCSywwQkFBMEI7SUFDNUI7SUFDQSxNQUFNSyxxQkFBcUI7UUFDekJMLDBCQUEwQjtJQUM1QjtJQUNBLE1BQU1NLGlDQUFpQyxPQUFPQztRQUM1QyxJQUFJO1lBQ0YsTUFBTXpCLHdEQUFZQSxDQUFDMEIsTUFBTSxDQUFDLENBQUMsV0FBVyxFQUFFRCxVQUFVLENBQUMsRUFBRTtnQkFDbkRFLE1BQU07b0JBQUVDLE1BQU07Z0JBQVU7WUFDMUI7WUFDQUMsV0FBVztRQUNiLEVBQUUsT0FBT0MsT0FBTyxDQUFDO0lBQ25CO0lBQ0EsTUFBTUMsMEJBQTBCO1FBQzlCLElBQUk7WUFDRixNQUFNUCwrQkFBK0JMO1FBQ3ZDLEVBQUUsT0FBT1csT0FBTyxDQUFDO1FBQ2pCWiwwQkFBMEI7SUFDNUI7SUFFQSxNQUFNYyxRQUFRakQsdUdBQVFBO0lBQ3RCLE1BQU0sRUFBRWtELElBQUksRUFBRSxHQUFHbkMsbUZBQWNBO0lBQy9CLE1BQU0sRUFBRW9DLElBQUksRUFBRSxHQUFHaEQsNkRBQWNBO0lBQy9CLE1BQU1pRCxXQUFXbkQsdUdBQWFBLENBQUNnRCxNQUFNSSxXQUFXLENBQUNDLElBQUksQ0FBQztJQUN0RCxNQUFNQyx3QkFBd0JuQyx3RkFBaUJBO0lBRS9DdkIsb0RBQWEsQ0FBQ3NELEtBQUtuQixRQUFRLElBQUk7SUFFL0IsTUFBTSxDQUFDeUIsVUFBVUMsWUFBWSxHQUFHOUQsK0NBQVFBLENBQUMsRUFBRTtJQUMzQyxNQUFNLENBQUMrRCxxQkFBcUJDLHVCQUF1QixHQUFHaEUsK0NBQVFBLENBQzVEbUMsU0FBUzhCO0lBRVgsTUFBTSxDQUFDQyxpQkFBaUJDLG1CQUFtQixHQUFHbkUsK0NBQVFBLENBQUMsRUFBRTtJQUN6RCxNQUFNLENBQUNvRSxTQUFTbEIsV0FBVyxHQUFHbEQsK0NBQVFBLENBQUM7SUFDdkMsTUFBTXFFLGFBQWE1RCwrRUFBNkJBLENBQzlDO1FBQUUyQjtRQUFVa0MsWUFBWWpDO0lBQUksR0FDNUI7UUFBRWtDLFNBQVMsQ0FBQyxDQUFDbEM7SUFBSTtJQUduQnRDLGdEQUFTQSxDQUFDO1FBQ1IsTUFBTXlFLFNBQVMsSUFBSUM7UUFDbkIsTUFBTUMsTUFBTUYsT0FBT0csZUFBZSxDQUFDeEMsU0FBUzhCLFNBQVM7UUFDckQsTUFBTVcsb0JBQW9CLEVBQUU7UUFFNUJDLE1BQU1DLElBQUksQ0FBQ0osSUFBSUssZ0JBQWdCLENBQUMsV0FBV0MsT0FBTyxDQUFDLENBQUNDO1lBQ2xELE1BQU0vQyxLQUFLK0MsUUFBUUMsU0FBUyxDQUN6QkMsV0FBVyxHQUNYQyxPQUFPLENBQUMsUUFBUSxLQUNoQkEsT0FBTyxDQUFDLGdCQUFnQjtZQUMzQkgsUUFBUS9DLEVBQUUsR0FBR0E7WUFDYjBDLGtCQUFrQlMsSUFBSSxDQUFDO2dCQUNyQkMsU0FBU0wsUUFBUUssT0FBTyxDQUFDSCxXQUFXO2dCQUNwQ2xCLFNBQVNnQixRQUFRQyxTQUFTO2dCQUMxQmhEO1lBQ0Y7UUFDRjtRQUVBNEIsWUFBWWM7UUFDWlosdUJBQXVCVSxJQUFJYSxJQUFJLENBQUNDLFNBQVM7SUFDM0MsR0FBRztRQUFDckQsU0FBUzhCO0tBQVE7SUFFckJsRSxnREFBU0EsQ0FBQztRQUNSLE1BQU0wRixlQUFlO1lBQ25CLElBQUl2RCxJQUFJO2dCQUNOLElBQUk7b0JBQ0YsTUFBTXdELFdBQVcsTUFBTXJFLHdEQUFZQSxDQUFDc0UsR0FBRyxDQUFDLENBQUMsb0JBQW9CLEVBQUV6RCxHQUFHLENBQUM7b0JBRW5FZ0IsV0FBV3dDLFNBQVMxQyxJQUFJO2dCQUMxQixFQUFFLE9BQU9HLE9BQU87b0JBQ2QsSUFBSXlDLEtBQThCLEVBQ2hDQyxFQUE2RDFDO2dCQUNqRTtZQUNGO1FBQ0Y7UUFFQXNDO0lBQ0YsR0FBRztRQUFDdkQ7S0FBRztJQUVQLE1BQU1ZLFlBQVl1QixZQUFZckIsTUFBTThDO0lBRXBDLE1BQU1DLGtCQUFrQjtRQUN0QixJQUFJLENBQUN6QyxNQUFNO1lBQ1RoRCxpREFBS0EsQ0FBQzBGLE9BQU8sQ0FBQztRQUNoQixPQUFPO1lBQ0xyQyxzQkFBc0JzQyxNQUFNLENBQzFCO2dCQUNFL0QsSUFBSUE7Z0JBQ0pnRSxPQUFPL0QsU0FBUytEO2dCQUNoQkMsaUJBQWlCO1lBQ25CLEdBQ0E7Z0JBQ0VDLFdBQVc7b0JBQ1RsRCxXQUFXO2dCQUNiO1lBQ0Y7UUFFSjtJQUNGO0lBQ0EsTUFBTSxFQUNKRixNQUFNcUQsWUFBWSxFQUNsQkMsV0FBV0MsZUFBZSxFQUMxQkMsU0FBU0MsZUFBZSxFQUN6QixHQUFHL0YsZ0VBQWNBLENBQ2hCO1FBQ0VvQztRQUNBNEQsWUFBWTtRQUNaQyxVQUFVO1FBQ1ZDLFdBQVc7SUFDYixHQUNBO1FBQUVyQyxTQUFTLENBQUMsQ0FBQ3pCO0lBQVU7SUFHekIsNEVBQTRFO0lBQzVFLE1BQU0rRCxZQUFZLENBQUMsRUFBRWpCLHVCQUFvQyxDQUFDLE1BQU0sRUFBRXZELElBQUksQ0FBQztJQUV2RXRDLGdEQUFTQSxDQUFDO1FBQ1IsTUFBTWlILHVCQUF1QjtZQUMzQixJQUFJN0UsU0FBUzhFLFlBQVlDLFNBQVMsR0FBRztnQkFDbkMsTUFBTUMsZ0JBQWdCaEYsU0FBUzhFLFdBQzVCRyxJQUFJLENBQUNDLE1BQVFBLElBQUloRixHQUFHLEVBQ3BCaUYsS0FBSztnQkFFUixJQUFJLENBQUNILGVBQWU7Z0JBRXBCLElBQUk7b0JBQ0YsTUFBTXpCLFdBQVcsTUFBTXBFLDJEQUFlQSxDQUFDcUUsR0FBRyxDQUN4QyxDQUFDLEVBQUV2RSxrREFBUUEsQ0FBQ21HLFFBQVEsQ0FBQyxDQUFDLEVBQUVuRixTQUFTLGVBQWUsRUFBRStFLGNBQWMsQ0FBQyxFQUNqRTtvQkFFQTtvQkFHRixJQUFJekIsU0FBUzhCLE1BQU0sS0FBSyxLQUFLO3dCQUMzQixNQUFNeEUsT0FBTyxNQUFNMEMsU0FBUzFDLElBQUk7d0JBQ2hDLE1BQU15RSxrQkFBa0J6RSxNQUFNdUUsVUFBVUgsSUFBSSxDQUFDTSxPQUFVO2dDQUNyRHhGLElBQUl3RixLQUFLNUIsR0FBRztnQ0FDWkksT0FBT3dCLEtBQUtDLFFBQVEsQ0FBQyxFQUFFLEVBQUV6QixTQUFTO2dDQUNsQzBCLFNBQVNGLEtBQUtDLFFBQVEsQ0FBQyxFQUFFLEVBQUVFLFNBQVM7Z0NBQ3BDQyxhQUNFSixLQUFLQyxRQUFRLENBQUMsRUFBRSxFQUFFSSxtQkFDbEI7Z0NBQ0ZDLE1BQU1OLEtBQUtDLFFBQVEsQ0FBQyxFQUFFLEVBQUV0RixPQUFPOzRCQUNqQzt3QkFFQThCLG1CQUFtQnNEO29CQUNyQixPQUFPO3dCQUNMLElBQUk3QixLQUE4QixFQUNoQ0MsRUFBa0Q7b0JBQ3REO2dCQUNGLEVBQUUsT0FBTTtvQkFDTixJQUFJRCxLQUE4QixFQUNoQ0MsRUFBa0Q7Z0JBQ3REO1lBQ0Y7UUFDRjtRQUVBbUI7SUFDRixHQUFHO1FBQUM3RSxTQUFTOEU7UUFBWTdFO0tBQVM7SUFDbEMscUJBQ0U7OzBCQUNFLDhEQUFDNkY7Z0JBQUkvRixJQUFHOztrQ0FDTiw4REFBQytGO3dCQUFJL0YsSUFBRztrQ0FDTiw0RUFBQ2hDLDJGQUFTQTs0QkFBQ2dJLFdBQVU7c0NBQ25CLDRFQUFDL0gsMkZBQUlBO2dDQUFDZ0ksU0FBUztnQ0FBQ0MsU0FBUzs7a0RBQ3ZCLDhEQUFDakksMkZBQUlBO3dDQUFDdUgsSUFBSTt3Q0FBQ1csSUFBSTt3Q0FBSUMsSUFBSTs7NENBQ3BCbkcsU0FBUzhFLFlBQVlDLFNBQVMsbUJBQzdCLDhEQUFDZTtnREFBSUMsV0FBVTswREFDWi9GLFFBQVE4RSxVQUFVLENBQUNHLEdBQUcsQ0FDckIsQ0FBQ21CLFdBQ0NBLFVBQVVsRyxxQkFDUiw4REFBQ21HO3dEQUFLTixXQUFVO2tFQUNkLDRFQUFDTzs0REFDQ0MsTUFBTSxDQUFDLEVBQ0x0RyxhQUFhLE9BQ1QsQ0FBQyxlQUFlLEVBQUVtRyxTQUFTbEcsR0FBRyxDQUFDLENBQUMsR0FDaEMsQ0FBQyxDQUFDLEVBQUVELFNBQVMsZUFBZSxFQUFFbUcsU0FBU2xHLEdBQUcsQ0FBQyxDQUFDLENBQ2pELENBQUMsQ0FBQztzRUFFRmtHLFNBQVNJLElBQUk7Ozs7Ozt1REFSb0JKLFNBQVNsRyxHQUFHOzs7Ozs7Ozs7OzBEQWdCNUQsOERBQUN1RztnREFBR1YsV0FBVTswREFDWC9GLFNBQVMwRyxhQUNOMUcsU0FBUzBHLFdBQVczQixTQUFTLElBQzNCckcsd0VBQXNCQSxDQUNwQnNCLFNBQVMrRCxPQUNUL0QsU0FBUzBHLGNBRVhsSCwrREFBYUEsQ0FBQ1EsU0FBUytELFNBQ3pCdkUsK0RBQWFBLENBQUNRLFNBQVMrRDs7Ozs7OzBEQUU3Qiw4REFBQzRDO2dEQUFFWixXQUFVOztrRUFDWCw4REFBQ3BILHNFQUFPQTs7Ozs7b0RBQUk7b0RBQ1hxQixTQUFTNEcsY0FDTm5JLHVFQUFxQkEsQ0FDbkJYLDZDQUFNQSxDQUFDa0MsU0FBUzRHLGFBQWFDLE1BQU0sQ0FBQyxTQUV0QztrRUFDSiw4REFBQ2xJLHNFQUFPQTs7Ozs7b0RBQUk7b0RBQ1hxQixTQUFTNEcsY0FDTm5JLHVFQUFxQkEsQ0FDbkJYLDZDQUFNQSxDQUFDa0MsU0FBUzRHLGFBQWFDLE1BQU0sQ0FBQyxTQUV0Qzs7Ozs7Ozs0Q0FFSixFQUFDMUYsUUFBUUEsTUFBTTJGLE9BQU9DLFNBQVN6SCxtREFBSUEsQ0FBQzBILFNBQVMsb0JBQzdDLDhEQUFDcEksb0VBQVlBO2dEQUNYcUksVUFBVTtnREFDVkMsTUFBTWpGLFVBQVUsVUFBVTtnREFDMUJrRixTQUNFbEYsVUFBVXpCLDRCQUE0Qm9EO2dEQUV4Q3dELG9CQUNFLDhEQUFDN0gsMEVBQVdBO29EQUNWd0csV0FBVyxDQUFDLEVBQUU5RCxVQUFVLHNCQUFzQixHQUFHLENBQUM7Ozs7OztnREFHdEQ4RCxXQUFXLENBQUMscUJBQXFCLEVBQy9COUQsVUFBVSx1QkFBdUIsR0FDbEMsQ0FBQzs7Ozs7Ozs7Ozs7O2tEQUtSLDhEQUFDakUsMkZBQUlBO3dDQUFDdUgsSUFBSTt3Q0FBQ1csSUFBSTt3Q0FBSUMsSUFBSTtrREFDckIsNEVBQUNMOzRDQUFJQyxXQUFVO3NEQUNiLDRFQUFDc0I7Z0RBQ0NDLEtBQUssQ0FBQyxFQUFFN0QsOEJBQW9DLENBQUMsT0FBTyxFQUFFekQsU0FBUzBGLE1BQU0sQ0FBQztnREFDdEU4QixPQUFPO2dEQUNQQyxRQUFRO2dEQUNSQyxLQUFLMUgsU0FBUzBIO2dEQUNkQyxTQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FVcEIsOERBQUNDO3dCQUNDOUcsTUFBSzt3QkFDTCtHLHlCQUF5Qjs0QkFDdkJDLFFBQVFDLEtBQUtDLFNBQVMsQ0FBQztnQ0FDckIsWUFBWTtnQ0FDWixTQUFTO2dDQUNUQyxpQkFBaUI7b0NBQ2Y7d0NBQ0UsU0FBUzt3Q0FDVEMsVUFBVTt3Q0FDVjNDLE1BQU07NENBQ0osT0FDRXRGLGFBQWEsT0FDVCxDQUFDLCtCQUErQixDQUFDLEdBQ2pDLENBQUMsMEJBQTBCLEVBQUVBLFNBQVMsTUFBTSxDQUFDOzRDQUNuRHVHLE1BQU07d0NBQ1I7b0NBQ0Y7b0NBQ0F4RyxTQUFTOEUsVUFBVSxDQUFDLEVBQUUsQ0FBQzVFLE9BQU87d0NBQzVCLFNBQVM7d0NBQ1RnSSxVQUFVO3dDQUNWM0MsTUFBTTs0Q0FDSixPQUNFdEYsYUFBYSxPQUNULENBQUMsd0NBQXdDLEVBQUVELFNBQVM4RSxVQUFVLENBQUMsRUFBRSxDQUFDNUUsSUFBSSxDQUFDLENBQUMsR0FDeEUsQ0FBQywwQkFBMEIsRUFBRUQsU0FBUyxlQUFlLEVBQUVELFNBQVM4RSxVQUFVLENBQUMsRUFBRSxDQUFDNUUsSUFBSSxDQUFDLENBQUM7NENBQzFGc0csTUFBTXhHLFNBQVM4RSxVQUFVLENBQUMsRUFBRSxDQUFDMEI7d0NBQy9CO29DQUNGO2lDQUNEOzRCQUNIO3dCQUNGOzs7Ozs7a0NBR0YsOERBQUN6SSwyRkFBU0E7d0JBQUNnSSxXQUFVOzswQ0FDbkIsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ087d0NBQ0M3RSxRQUFReEIsYUFBYSxPQUFPLE9BQU87d0NBQ25Dc0csTUFBTSxDQUFDLEVBQUV0RyxhQUFhLE9BQU8sQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLENBQUMsRUFBRUEsU0FBUyxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUM7d0NBQzdEOEYsV0FBVTtrREFDWDs7Ozs7O29DQUlBL0YsU0FBUzhFLFlBQVlDLFNBQVMsbUJBQzdCOzswREFDRSw4REFBQ2hHLDRFQUFhQTs7Ozs7NENBQ2JpQixTQUFTOEUsVUFBVSxDQUFDLEVBQUUsQ0FBQzVFLHFCQUN0Qiw4REFBQ29HO2dEQUNDUCxXQUFVO2dEQUNWUSxNQUFNLENBQUMsRUFDTHRHLGFBQWEsT0FDVCxDQUFDLGVBQWUsRUFBRUQsU0FBUzhFLFVBQVUsQ0FBQyxFQUFFLENBQUM1RSxJQUFJLENBQUMsR0FDOUMsQ0FBQyxDQUFDLEVBQUVELFNBQVMsZUFBZSxFQUFFRCxTQUFTOEUsVUFBVSxDQUFDLEVBQUUsQ0FBQzVFLElBQUksQ0FBQyxDQUMvRCxDQUFDLENBQUM7MERBRUZGLFNBQVM4RSxVQUFVLENBQUMsRUFBRSxDQUFDMEI7Ozs7Ozs7Ozs7Ozs7OzBDQU1sQyw4REFBQ3hJLDJGQUFJQTtnQ0FBQytILFdBQVU7Z0NBQVlDLFNBQVM7Z0NBQUNtQyxlQUFlOztrREFDbkQsOERBQUNuSywyRkFBSUE7d0NBQUN1SCxJQUFJO3dDQUFDVyxJQUFJO3dDQUFJQyxJQUFJOzs0Q0FDcEI5RSxZQUFZSyxVQUFVcUQsU0FBUyxrQkFDOUIsOERBQUN0RixzREFBWUE7Z0RBQUNpQyxVQUFVQTs7Ozs7dURBQ3RCOzBEQUNKLDhEQUFDb0U7Z0RBQUlDLFdBQVU7MERBQ2IsNEVBQUN2SCx1REFBY0E7b0RBQUM0SixhQUFheEc7Ozs7Ozs7Ozs7OzBEQUUvQiw4REFBQy9CLG1EQUFTQTtnREFBQ3dJLFdBQVdySSxTQUFTcUk7Ozs7Ozs0Q0FDOUJySSxTQUFTc0ksS0FBS3ZELFNBQVMsbUJBQ3RCLDhEQUFDbkYsc0RBQVlBO2dEQUFDbUUsT0FBTy9ELFFBQVF1SSxRQUFRO2dEQUFFQyxPQUFPeEksUUFBUXNJLEdBQUc7Ozs7Ozs7Ozs7OztrREFHN0QsOERBQUN0SywyRkFBSUE7d0NBQUN1SCxJQUFJO3dDQUFDVyxJQUFJO3dDQUFJQyxJQUFJO3dDQUFHcEcsSUFBRztrREFDMUIsQ0FBQ3NCLHlCQUNBOztnREFDR0ssVUFBVXFELFNBQVMsa0JBQ2xCLDhEQUFDdEYsc0RBQVlBO29EQUFDaUMsVUFBVUE7Ozs7OzJEQUN0Qjs4REFFSiw4REFBQ2hDLDREQUFrQkE7b0RBQUNnRixXQUFXQTs7Ozs7OzhEQUMvQiw4REFBQ29CO29EQUFJQyxXQUFVOzhEQUNiLDRFQUFDM0csZ0dBQXNCQTs7Ozs7Ozs7Ozs7MkRBR3pCOzs7Ozs7Ozs7Ozs7MENBSVIsOERBQUNwQiwyRkFBSUE7Z0NBQUMrSCxXQUFVO2dDQUFZQyxTQUFTO2dDQUFDbUMsZUFBZTswQ0FDbkQsNEVBQUNuSywyRkFBSUE7b0NBQUN1SCxJQUFJO29DQUFDVyxJQUFJO29DQUFJQyxJQUFJOzt3Q0FDcEI5RSx5QkFDQzs7OERBQ0UsOERBQUMzQiw0REFBa0JBO29EQUFDZ0YsV0FBV0E7Ozs7Ozs4REFDL0IsOERBQUNvQjtvREFBSUMsV0FBVTs4REFDYiw0RUFBQzNHLGdHQUFzQkE7Ozs7Ozs7Ozs7OzJEQUd6QjtzREFDSiw4REFBQ08scURBQVdBOzRDQUNWb0MsaUJBQWlCQTs0Q0FDakI5QixVQUFVQTs0Q0FDVlUsV0FBV0E7Ozs7OztzREFFYiw4REFBQzdCLDJEQUFpQkE7NENBQ2hCdUYsU0FBU0M7NENBQ1QzRCxXQUFXQTs0Q0FDWFEsTUFBTUE7Ozs7OztzREFFUiw4REFBQ3RDLDREQUFrQkE7NENBQ2pCOEIsV0FBV0E7NENBQ1hFLE1BQU1xRDs0Q0FDTkMsV0FBV0M7NENBQ1hDLFNBQVNDOzRDQUNUbkQsTUFBTUE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU9oQiw4REFBQzlDLHFHQUFXQTtnQkFDVm9LLE1BQU10STtnQkFDTnVJLFNBQVNuSSxFQUFFO2dCQUNYb0ksU0FBU2xJO2dCQUNUbUksV0FBVzNIOzs7Ozs7OztBQUluQjtHQS9YU25COztRQUdPMUIseURBQWNBO1FBdUJkSCxtR0FBUUE7UUFDTGUsK0VBQWNBO1FBQ2RaLHlEQUFjQTtRQUNkRixtR0FBYUE7UUFDQW1CLG9GQUFpQkE7UUFVNUJmLDJFQUE2QkE7UUFvRTVDQyw0REFBY0E7OztLQTVHWHVCO0FBaVlULCtEQUFlQSxlQUFlQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9mZWF0dXJlcy9ibG9nL2NvbXBvbmVudHMvbmV3LWJsb2cvQmxvZ1BhZ2VEZXRhaWxzLmpzeD9jNmY2Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xyXG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCBtb21lbnQgZnJvbSBcIm1vbWVudFwiO1xyXG5pbXBvcnQgeyBDb250YWluZXIsIEdyaWQgfSBmcm9tIFwiQG11aS9tYXRlcmlhbFwiO1xyXG5pbXBvcnQgeyB1c2VUaGVtZSwgdXNlTWVkaWFRdWVyeSB9IGZyb20gXCJAbXVpL21hdGVyaWFsXCI7XHJcbmltcG9ydCB7IHRvYXN0IH0gZnJvbSBcInJlYWN0LXRvYXN0aWZ5XCI7XHJcbmltcG9ydCBcIm1vbWVudC9sb2NhbGUvZnJcIjtcclxuaW1wb3J0IHsgdXNlVHJhbnNsYXRpb24gfSBmcm9tIFwicmVhY3QtaTE4bmV4dFwiO1xyXG5pbXBvcnQgRGlhbG9nTW9kYWwgZnJvbSBcIkAvZmVhdHVyZXMvdXNlci9jb21wb25lbnQvdXBkYXRlUHJvZmlsZS9leHBlcmllbmNlL0RpYWxvZ01vZGFsXCI7XHJcblxyXG5pbXBvcnQge1xyXG4gIHVzZUdldEFydGljbGVCeVVybEFORGxhbmd1YWdlLFxyXG4gIHVzZUdldENvbW1lbnRzLFxyXG59IGZyb20gXCIuLi8uLi9ob29rcy9ibG9nLmhvb2tcIjtcclxuaW1wb3J0IEFydGljbGVDb250ZW50IGZyb20gXCIuLi9BcnRpY2xlQ29udGVudFwiO1xyXG5pbXBvcnQge1xyXG4gIGNhcGl0YWxpemVGaXJzdExldHRlcixcclxuICBoaWdobGlnaHRNYXRjaGluZ1dvcmRzLFxyXG59IGZyb20gXCJAL3V0aWxzL2Z1bmN0aW9uc1wiO1xyXG5pbXBvcnQgU3ZnVGltZSBmcm9tIFwiQC9hc3NldHMvaW1hZ2VzL2ljb25zL3RpbWUuc3ZnXCI7XHJcbmltcG9ydCBDdXN0b21CdXR0b24gZnJvbSBcIkAvY29tcG9uZW50cy91aS9DdXN0b21CdXR0b25cIjtcclxuaW1wb3J0IENvbW1lbnRzTGlzdEJ5QmxvZyBmcm9tIFwiLi4vQ29tbWVudHNMaXN0QnlCbG9nXCI7XHJcbmltcG9ydCBDcmVhdGVCbG9nQ29tbWVudCBmcm9tIFwiLi4vQ3JlYXRlQmxvZ0NvbW1lbnRcIjtcclxuaW1wb3J0IFN2Z0Fycm93UmlnaHQgZnJvbSBcIkAvYXNzZXRzL2ltYWdlcy9pY29ucy9hcnJvd1JpZ2h0LnN2Z1wiO1xyXG5pbXBvcnQgdXNlQ3VycmVudFVzZXIgZnJvbSBcIkAvZmVhdHVyZXMvYXV0aC9ob29rcy9jdXJyZW50VXNlci5ob29rc1wiO1xyXG5pbXBvcnQgeyBBUElfVVJMUyB9IGZyb20gXCJAL3V0aWxzL3VybHNcIjtcclxuaW1wb3J0IHsgYXhpb3NHZXRKc29uLCBheGlvc0dldEpzb25TU1IgfSBmcm9tIFwiQC9jb25maWcvYXhpb3NcIjtcclxuaW1wb3J0IE5ld3NsZXR0ZXJTdWJzY3JpcHRpb24gZnJvbSBcIkAvY29tcG9uZW50cy9wYWdlcy9zaXRlcy9zZWN0aW9ucy9OZXdzbGV0dGVyU3Vic2NyaXB0aW9uXCI7XHJcbmltcG9ydCB7IHVzZUFkZFRvRmF2b3VyaXRlIH0gZnJvbSBcIi4uLy4uLy4uL29wcG9ydHVuaXR5L2hvb2tzL29wcG9ydHVuaXR5Lmhvb2tzXCI7XHJcbmltcG9ydCB7IFJvbGUgfSBmcm9tIFwiQC91dGlscy9jb25zdGFudHNcIjtcclxuaW1wb3J0IFN2Z0Jvb2ttYXJrIGZyb20gXCJAL2Fzc2V0cy9pbWFnZXMvaWNvbnMvYm9va21hcmsuc3ZnXCI7XHJcbmltcG9ydCB7IHNwbGl0TGFzdFdvcmQgfSBmcm9tIFwiQC91dGlscy9mdW5jdGlvbnNcIjtcclxuaW1wb3J0IENvbnRlbnRUYWJsZSBmcm9tIFwiLi9Db250ZW50VGFibGVcIjtcclxuaW1wb3J0IFNoYXJlT25Tb2NpYWxNZWRpYSBmcm9tIFwiLi9TaGFyZU9uU29jaWFsTWVkaWFcIjtcclxuaW1wb3J0IFJlbGF0ZWRCbG9nIGZyb20gXCIuL1JlbGF0ZWRCbG9nXCI7XHJcbmltcG9ydCBGYXFBY2NvcmRpb24gZnJvbSBcIi4vRmFxQWNjb3JkaW9uXCI7XHJcbmltcG9ydCBDVFNCYW5uZXIgZnJvbSBcIi4vQ1RTQmFubmVyXCI7XHJcblxyXG5mdW5jdGlvbiBCbG9nUGFnZURldGFpbHMoeyBpZCwgYXJ0aWNsZSwgbGFuZ3VhZ2UsIHVybCB9KSB7XHJcbiAgY29uc3QgW3Nob3dEZWxldGVDb25maXJtYXRpb24sIHNldFNob3dEZWxldGVDb25maXJtYXRpb25dID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtqb2JzVG9kZWxldGUsIHNldEpvYnNUb2RlbGV0ZV0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgeyB0IH0gPSB1c2VUcmFuc2xhdGlvbigpO1xyXG4gIGNvbnN0IGhhbmRlbGVEZWxldGVjb25maXJtYXRpb24gPSAoKSA9PiB7XHJcbiAgICBzZXRKb2JzVG9kZWxldGUoaWQpO1xyXG4gICAgc2V0U2hvd0RlbGV0ZUNvbmZpcm1hdGlvbih0cnVlKTtcclxuICB9O1xyXG4gIGNvbnN0IGhhbmRsZWNhbmNlbGRlbGV0ZSA9ICgpID0+IHtcclxuICAgIHNldFNob3dEZWxldGVDb25maXJtYXRpb24oZmFsc2UpO1xyXG4gIH07XHJcbiAgY29uc3QgZGVsZXRlT3Bwb3J0dW5pdHlGcm9tU2hvcnRsaXN0ID0gYXN5bmMgKGFydGljbGVJZCkgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgYXdhaXQgYXhpb3NHZXRKc29uLmRlbGV0ZShgL2Zhdm91cml0ZS8ke2FydGljbGVJZH1gLCB7XHJcbiAgICAgICAgZGF0YTogeyB0eXBlOiBcImFydGljbGVcIiB9LFxyXG4gICAgICB9KTtcclxuICAgICAgc2V0SXNTYXZlZChmYWxzZSk7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge31cclxuICB9O1xyXG4gIGNvbnN0IGhhbmRsZVRvZ2dsZU9wcG9ydHVuaXR5ID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgYXdhaXQgZGVsZXRlT3Bwb3J0dW5pdHlGcm9tU2hvcnRsaXN0KGpvYnNUb2RlbGV0ZSk7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge31cclxuICAgIHNldFNob3dEZWxldGVDb25maXJtYXRpb24oZmFsc2UpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IHRoZW1lID0gdXNlVGhlbWUoKTtcclxuICBjb25zdCB7IHVzZXIgfSA9IHVzZUN1cnJlbnRVc2VyKCk7XHJcbiAgY29uc3QgeyBpMThuIH0gPSB1c2VUcmFuc2xhdGlvbigpO1xyXG4gIGNvbnN0IGlzTW9iaWxlID0gdXNlTWVkaWFRdWVyeSh0aGVtZS5icmVha3BvaW50cy5kb3duKFwic21cIikpO1xyXG4gIGNvbnN0IHVzZUFkZFRvZmF2b3VyaXRlSG9vayA9IHVzZUFkZFRvRmF2b3VyaXRlKCk7XHJcblxyXG4gIG1vbWVudC5sb2NhbGUoaTE4bi5sYW5ndWFnZSB8fCBcImVuXCIpO1xyXG5cclxuICBjb25zdCBbaGVhZGluZ3MsIHNldEhlYWRpbmdzXSA9IHVzZVN0YXRlKFtdKTtcclxuICBjb25zdCBbbW9kaWZpZWRIdG1sQ29udGVudCwgc2V0TW9kaWZpZWRIdG1sQ29udGVudF0gPSB1c2VTdGF0ZShcclxuICAgIGFydGljbGU/LmNvbnRlbnRcclxuICApO1xyXG4gIGNvbnN0IFtyZWxhdGVkQXJ0aWNsZXMsIHNldFJlbGF0ZWRBcnRpY2xlc10gPSB1c2VTdGF0ZShbXSk7XHJcbiAgY29uc3QgW2lzU2F2ZWQsIHNldElzU2F2ZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IGdldEFydGljbGUgPSB1c2VHZXRBcnRpY2xlQnlVcmxBTkRsYW5ndWFnZShcclxuICAgIHsgbGFuZ3VhZ2UsIHVybEFydGljbGU6IHVybCB9LFxyXG4gICAgeyBlbmFibGVkOiAhIXVybCB9XHJcbiAgKTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnN0IHBhcnNlciA9IG5ldyBET01QYXJzZXIoKTtcclxuICAgIGNvbnN0IGRvYyA9IHBhcnNlci5wYXJzZUZyb21TdHJpbmcoYXJ0aWNsZT8uY29udGVudCwgXCJ0ZXh0L2h0bWxcIik7XHJcbiAgICBjb25zdCBleHRyYWN0ZWRIZWFkaW5ncyA9IFtdO1xyXG5cclxuICAgIEFycmF5LmZyb20oZG9jLnF1ZXJ5U2VsZWN0b3JBbGwoXCJoMiwgaDNcIikpLmZvckVhY2goKGhlYWRpbmcpID0+IHtcclxuICAgICAgY29uc3QgaWQgPSBoZWFkaW5nLmlubmVyVGV4dFxyXG4gICAgICAgIC50b0xvd2VyQ2FzZSgpXHJcbiAgICAgICAgLnJlcGxhY2UoL1xccysvZywgXCItXCIpXHJcbiAgICAgICAgLnJlcGxhY2UoL1teYS16MC05XFwtXS9nLCBcIlwiKTtcclxuICAgICAgaGVhZGluZy5pZCA9IGlkO1xyXG4gICAgICBleHRyYWN0ZWRIZWFkaW5ncy5wdXNoKHtcclxuICAgICAgICB0YWdOYW1lOiBoZWFkaW5nLnRhZ05hbWUudG9Mb3dlckNhc2UoKSxcclxuICAgICAgICBjb250ZW50OiBoZWFkaW5nLmlubmVyVGV4dCxcclxuICAgICAgICBpZCxcclxuICAgICAgfSk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBzZXRIZWFkaW5ncyhleHRyYWN0ZWRIZWFkaW5ncyk7XHJcbiAgICBzZXRNb2RpZmllZEh0bWxDb250ZW50KGRvYy5ib2R5LmlubmVySFRNTCk7XHJcbiAgfSwgW2FydGljbGU/LmNvbnRlbnRdKTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnN0IGNoZWNrSWZTYXZlZCA9IGFzeW5jICgpID0+IHtcclxuICAgICAgaWYgKGlkKSB7XHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXhpb3NHZXRKc29uLmdldChgL2Zhdm91cml0ZS9pcy1zYXZlZC8ke2lkfWApO1xyXG5cclxuICAgICAgICAgIHNldElzU2F2ZWQocmVzcG9uc2UuZGF0YSk7XHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gXCJkZXZcIilcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcihcIkZhaWxlZCB0byBjaGVjayBpZiBhcnRpY2xlIGlzIHNhdmVkOlwiLCBlcnJvcik7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9O1xyXG5cclxuICAgIGNoZWNrSWZTYXZlZCgpO1xyXG4gIH0sIFtpZF0pO1xyXG5cclxuICBjb25zdCBhcnRpY2xlSWQgPSBnZXRBcnRpY2xlPy5kYXRhPy5faWQ7XHJcblxyXG4gIGNvbnN0IGhhbmRsZVNhdmVDbGljayA9ICgpID0+IHtcclxuICAgIGlmICghdXNlcikge1xyXG4gICAgICB0b2FzdC53YXJuaW5nKFwiTG9naW4gb3IgY3JlYXRlIGFjY291bnQgdG8gc2F2ZSBhcnRpY2xlLlwiKTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIHVzZUFkZFRvZmF2b3VyaXRlSG9vay5tdXRhdGUoXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgaWQ6IGlkLFxyXG4gICAgICAgICAgdGl0bGU6IGFydGljbGU/LnRpdGxlLFxyXG4gICAgICAgICAgdHlwZU9mRmF2b3VyaXRlOiBcImFydGljbGVcIixcclxuICAgICAgICB9LFxyXG4gICAgICAgIHtcclxuICAgICAgICAgIG9uU3VjY2VzczogKCkgPT4ge1xyXG4gICAgICAgICAgICBzZXRJc1NhdmVkKHRydWUpO1xyXG4gICAgICAgICAgfSxcclxuICAgICAgICB9XHJcbiAgICAgICk7XHJcbiAgICB9XHJcbiAgfTtcclxuICBjb25zdCB7XHJcbiAgICBkYXRhOiBjb21tZW50c0RhdGEsXHJcbiAgICBpc0xvYWRpbmc6IGNvbW1lbnRzTG9hZGluZyxcclxuICAgIHJlZmV0Y2g6IGNvbW1lbnRzUmVmZXRjaCxcclxuICB9ID0gdXNlR2V0Q29tbWVudHMoXHJcbiAgICB7XHJcbiAgICAgIGFydGljbGVJZCxcclxuICAgICAgcGFnZU51bWJlcjogMSxcclxuICAgICAgcGFnZVNpemU6IDYsXHJcbiAgICAgIHBhZ2luYXRlZDogdHJ1ZSxcclxuICAgIH0sXHJcbiAgICB7IGVuYWJsZWQ6ICEhYXJ0aWNsZUlkIH1cclxuICApO1xyXG5cclxuICAvLyBjb25zdCBjYXRlZ29yeVNsdWdzID0gYXJ0aWNsZS5jYXRlZ29yaWVzLm1hcCgoY2F0KSA9PiBjYXQudXJsKS5qb2luKFwiL1wiKTtcclxuICBjb25zdCBzYWhyZWRVcmwgPSBgJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19GUk9OVEVORF9VUkx9L2Jsb2cvJHt1cmx9YDtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnN0IGZldGNoUmVsYXRlZEFydGljbGVzID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgICBpZiAoYXJ0aWNsZT8uY2F0ZWdvcmllcz8ubGVuZ3RoID4gMCkge1xyXG4gICAgICAgIGNvbnN0IGNhdGVnb3J5U2x1Z3MgPSBhcnRpY2xlPy5jYXRlZ29yaWVzXHJcbiAgICAgICAgICAubWFwKChjYXQpID0+IGNhdC51cmwpXHJcbiAgICAgICAgICAuam9pbihcIi9cIik7XHJcblxyXG4gICAgICAgIGlmICghY2F0ZWdvcnlTbHVncykgcmV0dXJuO1xyXG5cclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBheGlvc0dldEpzb25TU1IuZ2V0KFxyXG4gICAgICAgICAgICBgJHtBUElfVVJMUy5hcnRpY2xlc30vJHtsYW5ndWFnZX0vYmxvZy9jYXRlZ29yeS8ke2NhdGVnb3J5U2x1Z3N9YCxcclxuICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgIC8vIHBhcmFtczogeyB2aXNpYmlsaXR5OiBcIlB1YmxpY1wiIH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgKTtcclxuXHJcbiAgICAgICAgICBpZiAocmVzcG9uc2Uuc3RhdHVzID09PSAyMDApIHtcclxuICAgICAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmRhdGE7XHJcbiAgICAgICAgICAgIGNvbnN0IGZldGNoZWRBcnRpY2xlcyA9IGRhdGE/LmFydGljbGVzPy5tYXAoKGl0ZW0pID0+ICh7XHJcbiAgICAgICAgICAgICAgaWQ6IGl0ZW0uX2lkLFxyXG4gICAgICAgICAgICAgIHRpdGxlOiBpdGVtLnZlcnNpb25zWzBdPy50aXRsZSB8fCBcIlRpdHJlIG5vbiBkaXNwb25pYmxlXCIsXHJcbiAgICAgICAgICAgICAgcGljdHVyZTogaXRlbS52ZXJzaW9uc1swXT8uaW1hZ2UgfHwgXCJcIixcclxuICAgICAgICAgICAgICBkZXNjcmlwdGlvbjpcclxuICAgICAgICAgICAgICAgIGl0ZW0udmVyc2lvbnNbMF0/Lm1ldGFEZXNjcmlwdGlvbiB8fFxyXG4gICAgICAgICAgICAgICAgXCJEZXNjcmlwdGlvbiBub24gZGlzcG9uaWJsZVwiLFxyXG4gICAgICAgICAgICAgIHNsdWc6IGl0ZW0udmVyc2lvbnNbMF0/LnVybCB8fCBcIlwiLFxyXG4gICAgICAgICAgICB9KSk7XHJcblxyXG4gICAgICAgICAgICBzZXRSZWxhdGVkQXJ0aWNsZXMoZmV0Y2hlZEFydGljbGVzKTtcclxuICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gXCJkZXZcIilcclxuICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKFwiRmFpbGVkIHRvIGZldGNoIHJlbGF0ZWQgYXJ0aWNsZXNcIik7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfSBjYXRjaCB7XHJcbiAgICAgICAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09IFwiZGV2XCIpXHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJGYWlsZWQgdG8gZmV0Y2ggcmVsYXRlZCBhcnRpY2xlc1wiKTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH07XHJcblxyXG4gICAgZmV0Y2hSZWxhdGVkQXJ0aWNsZXMoKTtcclxuICB9LCBbYXJ0aWNsZT8uY2F0ZWdvcmllcywgbGFuZ3VhZ2VdKTtcclxuICByZXR1cm4gKFxyXG4gICAgPD5cclxuICAgICAgPGRpdiBpZD1cImJsb2ctcGFnZS1kZXRhaWxzXCI+XHJcbiAgICAgICAgPGRpdiBpZD1cImJsb2ctaGVhZGVyXCI+XHJcbiAgICAgICAgICA8Q29udGFpbmVyIGNsYXNzTmFtZT1cImN1c3RvbS1tYXgtd2lkdGhcIj5cclxuICAgICAgICAgICAgPEdyaWQgY29udGFpbmVyIHNwYWNpbmc9ezN9PlxyXG4gICAgICAgICAgICAgIDxHcmlkIGl0ZW0geHM9ezEyfSBzbT17Nn0+XHJcbiAgICAgICAgICAgICAgICB7YXJ0aWNsZT8uY2F0ZWdvcmllcz8ubGVuZ3RoID4gMCAmJiAoXHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2F0ZWdvcmllcy1saXN0XCI+XHJcbiAgICAgICAgICAgICAgICAgICAge2FydGljbGUuY2F0ZWdvcmllcy5tYXAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAoY2F0ZWdvcnkpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNhdGVnb3J5Py51cmwgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImNhdGVnb3J5IGxpZ2h0XCIga2V5PXtjYXRlZ29yeS51cmx9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGFcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaHJlZj17YCR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGFuZ3VhZ2UgPT09IFwiZW5cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBgL2Jsb2cvY2F0ZWdvcnkvJHtjYXRlZ29yeS51cmx9YFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBgLyR7bGFuZ3VhZ2V9L2Jsb2cvY2F0ZWdvcnkvJHtjYXRlZ29yeS51cmx9YFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9L2B9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjYXRlZ29yeS5uYW1lfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9hPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwiaGVhZGluZy1oMVwiPlxyXG4gICAgICAgICAgICAgICAgICB7YXJ0aWNsZT8uaGlnaGxpZ2h0c1xyXG4gICAgICAgICAgICAgICAgICAgID8gYXJ0aWNsZT8uaGlnaGxpZ2h0cy5sZW5ndGggPiAwXHJcbiAgICAgICAgICAgICAgICAgICAgICA/IGhpZ2hsaWdodE1hdGNoaW5nV29yZHMoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYXJ0aWNsZT8udGl0bGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYXJ0aWNsZT8uaGlnaGxpZ2h0c1xyXG4gICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgICA6IHNwbGl0TGFzdFdvcmQoYXJ0aWNsZT8udGl0bGUpXHJcbiAgICAgICAgICAgICAgICAgICAgOiBzcGxpdExhc3RXb3JkKGFydGljbGU/LnRpdGxlKX1cclxuICAgICAgICAgICAgICAgIDwvaDE+XHJcbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJzdWItaGVhZGluZyBkYXRlIFwiPlxyXG4gICAgICAgICAgICAgICAgICA8U3ZnVGltZSAvPntcIiBcIn1cclxuICAgICAgICAgICAgICAgICAge2FydGljbGU/LnB1Ymxpc2hEYXRlXHJcbiAgICAgICAgICAgICAgICAgICAgPyBjYXBpdGFsaXplRmlyc3RMZXR0ZXIoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG1vbWVudChhcnRpY2xlPy5wdWJsaXNoRGF0ZSkuZm9ybWF0KFwiTExcIilcclxuICAgICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICA6IFwiXCJ9XHJcbiAgICAgICAgICAgICAgICAgIDxTdmdUaW1lIC8+e1wiIFwifVxyXG4gICAgICAgICAgICAgICAgICB7YXJ0aWNsZT8ucHVibGlzaERhdGVcclxuICAgICAgICAgICAgICAgICAgICA/IGNhcGl0YWxpemVGaXJzdExldHRlcihcclxuICAgICAgICAgICAgICAgICAgICAgICAgbW9tZW50KGFydGljbGU/LnB1Ymxpc2hEYXRlKS5mb3JtYXQoXCJMVFwiKVxyXG4gICAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICAgIDogXCJcIn1cclxuICAgICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgICAgIHsoIXVzZXIgfHwgdXNlcj8ucm9sZXM/LmluY2x1ZGVzKFJvbGUuQ0FORElEQVRFKSkgJiYgKFxyXG4gICAgICAgICAgICAgICAgICA8Q3VzdG9tQnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgbGVmdEljb249e3RydWV9XHJcbiAgICAgICAgICAgICAgICAgICAgdGV4dD17aXNTYXZlZCA/IFwiU2F2ZWRcIiA6IFwiU2F2ZVwifVxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgaXNTYXZlZCA/IGhhbmRlbGVEZWxldGVjb25maXJtYXRpb24gOiBoYW5kbGVTYXZlQ2xpY2tcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgaWNvbj17XHJcbiAgICAgICAgICAgICAgICAgICAgICA8U3ZnQm9va21hcmtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgJHtpc1NhdmVkID8gXCJidG4tZmlsbGVkLXllbGxvd1wiIDogXCJcIn1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgYnRuIGJ0bi1naG9zdCBwLXNhdmUgJHtcclxuICAgICAgICAgICAgICAgICAgICAgIGlzU2F2ZWQgPyBcImJ0bi1maWxsZWQteWVsbG93IFwiIDogXCJcIlxyXG4gICAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICA8L0dyaWQ+XHJcblxyXG4gICAgICAgICAgICAgIDxHcmlkIGl0ZW0geHM9ezEyfSBzbT17Nn0+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJsb2ctaW1nXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxpbWdcclxuICAgICAgICAgICAgICAgICAgICBzcmM9e2Ake3Byb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0JBU0VfQVBJX1VSTH0vZmlsZXMvJHthcnRpY2xlPy5pbWFnZX1gfVxyXG4gICAgICAgICAgICAgICAgICAgIHdpZHRoPXs3MDB9XHJcbiAgICAgICAgICAgICAgICAgICAgaGVpZ2h0PXs3MDB9XHJcbiAgICAgICAgICAgICAgICAgICAgYWx0PXthcnRpY2xlPy5hbHR9XHJcbiAgICAgICAgICAgICAgICAgICAgbG9hZGluZz1cImxhenlcIlxyXG4gICAgICAgICAgICAgICAgICAgIC8vIHByaW9yaXR5XHJcbiAgICAgICAgICAgICAgICAgICAgLy8gcXVhbGl0eT17OTB9XHJcbiAgICAgICAgICAgICAgICAgICAgLy8gbGF5b3V0PVwiaW50cmluc2ljXCJcclxuICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvR3JpZD5cclxuICAgICAgICAgICAgPC9HcmlkPlxyXG4gICAgICAgICAgPC9Db250YWluZXI+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPHNjcmlwdFxyXG4gICAgICAgICAgdHlwZT1cImFwcGxpY2F0aW9uL2xkK2pzb25cIlxyXG4gICAgICAgICAgZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUw9e3tcclxuICAgICAgICAgICAgX19odG1sOiBKU09OLnN0cmluZ2lmeSh7XHJcbiAgICAgICAgICAgICAgXCJAY29udGV4dFwiOiBcImh0dHBzOi8vc2NoZW1hLm9yZ1wiLFxyXG4gICAgICAgICAgICAgIFwiQHR5cGVcIjogXCJCcmVhZGNydW1iTGlzdFwiLFxyXG4gICAgICAgICAgICAgIGl0ZW1MaXN0RWxlbWVudDogW1xyXG4gICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICBcIkB0eXBlXCI6IFwiTGlzdEl0ZW1cIixcclxuICAgICAgICAgICAgICAgICAgcG9zaXRpb246IDEsXHJcbiAgICAgICAgICAgICAgICAgIGl0ZW06IHtcclxuICAgICAgICAgICAgICAgICAgICBcIkBpZFwiOlxyXG4gICAgICAgICAgICAgICAgICAgICAgbGFuZ3VhZ2UgPT09IFwiZW5cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA/IGBodHRwczovL3d3dy5wZW50YWJlbGwuY29tL2Jsb2cvYFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA6IGBodHRwczovL3d3dy5wZW50YWJlbGwuY29tLyR7bGFuZ3VhZ2V9L2Jsb2cvYCxcclxuICAgICAgICAgICAgICAgICAgICBuYW1lOiBcIkJsb2dcIixcclxuICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICBhcnRpY2xlPy5jYXRlZ29yaWVzWzBdLnVybCAmJiB7XHJcbiAgICAgICAgICAgICAgICAgIFwiQHR5cGVcIjogXCJMaXN0SXRlbVwiLFxyXG4gICAgICAgICAgICAgICAgICBwb3NpdGlvbjogMixcclxuICAgICAgICAgICAgICAgICAgaXRlbToge1xyXG4gICAgICAgICAgICAgICAgICAgIFwiQGlkXCI6XHJcbiAgICAgICAgICAgICAgICAgICAgICBsYW5ndWFnZSA9PT0gXCJlblwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgID8gYGh0dHBzOi8vd3d3LnBlbnRhYmVsbC5jb20vYmxvZy9jYXRlZ29yeS8ke2FydGljbGU/LmNhdGVnb3JpZXNbMF0udXJsfS9gXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDogYGh0dHBzOi8vd3d3LnBlbnRhYmVsbC5jb20vJHtsYW5ndWFnZX0vYmxvZy9jYXRlZ29yeS8ke2FydGljbGU/LmNhdGVnb3JpZXNbMF0udXJsfS9gLFxyXG4gICAgICAgICAgICAgICAgICAgIG5hbWU6IGFydGljbGU/LmNhdGVnb3JpZXNbMF0ubmFtZSxcclxuICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgXSxcclxuICAgICAgICAgICAgfSksXHJcbiAgICAgICAgICB9fVxyXG4gICAgICAgIC8+XHJcblxyXG4gICAgICAgIDxDb250YWluZXIgY2xhc3NOYW1lPVwiY3VzdG9tLW1heC13aWR0aFwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjYXRlZ29yaWVzLXBhdGhcIj5cclxuICAgICAgICAgICAgPGFcclxuICAgICAgICAgICAgICBsb2NhbGU9e2xhbmd1YWdlID09PSBcImVuXCIgPyBcImVuXCIgOiBcImZyXCJ9XHJcbiAgICAgICAgICAgICAgaHJlZj17YCR7bGFuZ3VhZ2UgPT09IFwiZW5cIiA/IGAvYmxvZ2AgOiBgLyR7bGFuZ3VhZ2V9L2Jsb2dgfS9gfVxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImxpbmtcIlxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgQmxvZ1xyXG4gICAgICAgICAgICA8L2E+XHJcblxyXG4gICAgICAgICAgICB7YXJ0aWNsZT8uY2F0ZWdvcmllcz8ubGVuZ3RoID4gMCAmJiAoXHJcbiAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgIDxTdmdBcnJvd1JpZ2h0IC8+XHJcbiAgICAgICAgICAgICAgICB7YXJ0aWNsZT8uY2F0ZWdvcmllc1swXS51cmwgJiYgKFxyXG4gICAgICAgICAgICAgICAgICA8YVxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImxpbmtcIlxyXG4gICAgICAgICAgICAgICAgICAgIGhyZWY9e2Ake1xyXG4gICAgICAgICAgICAgICAgICAgICAgbGFuZ3VhZ2UgPT09IFwiZW5cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA/IGAvYmxvZy9jYXRlZ29yeS8ke2FydGljbGU/LmNhdGVnb3JpZXNbMF0udXJsfWBcclxuICAgICAgICAgICAgICAgICAgICAgICAgOiBgLyR7bGFuZ3VhZ2V9L2Jsb2cvY2F0ZWdvcnkvJHthcnRpY2xlPy5jYXRlZ29yaWVzWzBdLnVybH1gXHJcbiAgICAgICAgICAgICAgICAgICAgfS9gfVxyXG4gICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAge2FydGljbGU/LmNhdGVnb3JpZXNbMF0ubmFtZX1cclxuICAgICAgICAgICAgICAgICAgPC9hPlxyXG4gICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICA8Lz5cclxuICAgICAgICAgICAgKX1cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPEdyaWQgY2xhc3NOYW1lPVwiY29udGFpbmVyXCIgY29udGFpbmVyIGNvbHVtblNwYWNpbmc9ezJ9PlxyXG4gICAgICAgICAgICA8R3JpZCBpdGVtIHhzPXsxMn0gc209ezh9PlxyXG4gICAgICAgICAgICAgIHtpc01vYmlsZSAmJiBoZWFkaW5ncz8ubGVuZ3RoID4gMCA/IChcclxuICAgICAgICAgICAgICAgIDxDb250ZW50VGFibGUgaGVhZGluZ3M9e2hlYWRpbmdzfSAvPlxyXG4gICAgICAgICAgICAgICkgOiBudWxsfVxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmxvZy1jb250ZW50XCI+XHJcbiAgICAgICAgICAgICAgICA8QXJ0aWNsZUNvbnRlbnQgaHRtbENvbnRlbnQ9e21vZGlmaWVkSHRtbENvbnRlbnR9IC8+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPENUU0Jhbm5lciBjdHNCYW5uZXI9e2FydGljbGU/LmN0c0Jhbm5lcn0gLz5cclxuICAgICAgICAgICAgICB7YXJ0aWNsZT8uZmFxPy5sZW5ndGggPiAwICYmIChcclxuICAgICAgICAgICAgICAgIDxGYXFBY2NvcmRpb24gdGl0bGU9e2FydGljbGUuZmFxVGl0bGV9IGl0ZW1zPXthcnRpY2xlLmZhcX0gLz5cclxuICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICA8L0dyaWQ+XHJcbiAgICAgICAgICAgIDxHcmlkIGl0ZW0geHM9ezEyfSBzbT17NH0gaWQ9XCJzdGlja3ktc2lkZWJhclwiPlxyXG4gICAgICAgICAgICAgIHshaXNNb2JpbGUgPyAoXHJcbiAgICAgICAgICAgICAgICA8PlxyXG4gICAgICAgICAgICAgICAgICB7aGVhZGluZ3M/Lmxlbmd0aCA+IDAgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgPENvbnRlbnRUYWJsZSBoZWFkaW5ncz17aGVhZGluZ3N9IC8+XHJcbiAgICAgICAgICAgICAgICAgICkgOiBudWxsfVxyXG5cclxuICAgICAgICAgICAgICAgICAgPFNoYXJlT25Tb2NpYWxNZWRpYSBzYWhyZWRVcmw9e3NhaHJlZFVybH0gLz5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzZWN0aW9uXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPE5ld3NsZXR0ZXJTdWJzY3JpcHRpb24gLz5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8Lz5cclxuICAgICAgICAgICAgICApIDogbnVsbH1cclxuICAgICAgICAgICAgPC9HcmlkPlxyXG4gICAgICAgICAgPC9HcmlkPlxyXG5cclxuICAgICAgICAgIDxHcmlkIGNsYXNzTmFtZT1cImNvbnRhaW5lclwiIGNvbnRhaW5lciBjb2x1bW5TcGFjaW5nPXsyfT5cclxuICAgICAgICAgICAgPEdyaWQgaXRlbSB4cz17MTJ9IHNtPXs4fT5cclxuICAgICAgICAgICAgICB7aXNNb2JpbGUgPyAoXHJcbiAgICAgICAgICAgICAgICA8PlxyXG4gICAgICAgICAgICAgICAgICA8U2hhcmVPblNvY2lhbE1lZGlhIHNhaHJlZFVybD17c2FocmVkVXJsfSAvPlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNlY3Rpb25cIj5cclxuICAgICAgICAgICAgICAgICAgICA8TmV3c2xldHRlclN1YnNjcmlwdGlvbiAvPlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICAgICkgOiBudWxsfVxyXG4gICAgICAgICAgICAgIDxSZWxhdGVkQmxvZ1xyXG4gICAgICAgICAgICAgICAgcmVsYXRlZEFydGljbGVzPXtyZWxhdGVkQXJ0aWNsZXN9XHJcbiAgICAgICAgICAgICAgICBsYW5ndWFnZT17bGFuZ3VhZ2V9XHJcbiAgICAgICAgICAgICAgICBhcnRpY2xlSWQ9e2FydGljbGVJZH1cclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIDxDcmVhdGVCbG9nQ29tbWVudFxyXG4gICAgICAgICAgICAgICAgcmVmZXRjaD17Y29tbWVudHNSZWZldGNofVxyXG4gICAgICAgICAgICAgICAgYXJ0aWNsZUlkPXthcnRpY2xlSWR9XHJcbiAgICAgICAgICAgICAgICB1c2VyPXt1c2VyfVxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgPENvbW1lbnRzTGlzdEJ5QmxvZ1xyXG4gICAgICAgICAgICAgICAgYXJ0aWNsZUlkPXthcnRpY2xlSWR9XHJcbiAgICAgICAgICAgICAgICBkYXRhPXtjb21tZW50c0RhdGF9XHJcbiAgICAgICAgICAgICAgICBpc0xvYWRpbmc9e2NvbW1lbnRzTG9hZGluZ31cclxuICAgICAgICAgICAgICAgIHJlZmV0Y2g9e2NvbW1lbnRzUmVmZXRjaH1cclxuICAgICAgICAgICAgICAgIHVzZXI9e3VzZXJ9XHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPC9HcmlkPlxyXG4gICAgICAgICAgPC9HcmlkPlxyXG4gICAgICAgIDwvQ29udGFpbmVyPlxyXG4gICAgICA8L2Rpdj5cclxuXHJcbiAgICAgIDxEaWFsb2dNb2RhbFxyXG4gICAgICAgIG9wZW49e3Nob3dEZWxldGVDb25maXJtYXRpb259XHJcbiAgICAgICAgbWVzc2FnZT17dChcIm1lc3NhZ2VzOnN1cHByaW1lcmFydGljbGVmYXZvcmlzXCIpfVxyXG4gICAgICAgIG9uQ2xvc2U9e2hhbmRsZWNhbmNlbGRlbGV0ZX1cclxuICAgICAgICBvbkNvbmZpcm09e2hhbmRsZVRvZ2dsZU9wcG9ydHVuaXR5fVxyXG4gICAgICAvPlxyXG4gICAgPC8+XHJcbiAgKTtcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgQmxvZ1BhZ2VEZXRhaWxzO1xyXG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJtb21lbnQiLCJDb250YWluZXIiLCJHcmlkIiwidXNlVGhlbWUiLCJ1c2VNZWRpYVF1ZXJ5IiwidG9hc3QiLCJ1c2VUcmFuc2xhdGlvbiIsIkRpYWxvZ01vZGFsIiwidXNlR2V0QXJ0aWNsZUJ5VXJsQU5EbGFuZ3VhZ2UiLCJ1c2VHZXRDb21tZW50cyIsIkFydGljbGVDb250ZW50IiwiY2FwaXRhbGl6ZUZpcnN0TGV0dGVyIiwiaGlnaGxpZ2h0TWF0Y2hpbmdXb3JkcyIsIlN2Z1RpbWUiLCJDdXN0b21CdXR0b24iLCJDb21tZW50c0xpc3RCeUJsb2ciLCJDcmVhdGVCbG9nQ29tbWVudCIsIlN2Z0Fycm93UmlnaHQiLCJ1c2VDdXJyZW50VXNlciIsIkFQSV9VUkxTIiwiYXhpb3NHZXRKc29uIiwiYXhpb3NHZXRKc29uU1NSIiwiTmV3c2xldHRlclN1YnNjcmlwdGlvbiIsInVzZUFkZFRvRmF2b3VyaXRlIiwiUm9sZSIsIlN2Z0Jvb2ttYXJrIiwic3BsaXRMYXN0V29yZCIsIkNvbnRlbnRUYWJsZSIsIlNoYXJlT25Tb2NpYWxNZWRpYSIsIlJlbGF0ZWRCbG9nIiwiRmFxQWNjb3JkaW9uIiwiQ1RTQmFubmVyIiwiQmxvZ1BhZ2VEZXRhaWxzIiwiaWQiLCJhcnRpY2xlIiwibGFuZ3VhZ2UiLCJ1cmwiLCJzaG93RGVsZXRlQ29uZmlybWF0aW9uIiwic2V0U2hvd0RlbGV0ZUNvbmZpcm1hdGlvbiIsImpvYnNUb2RlbGV0ZSIsInNldEpvYnNUb2RlbGV0ZSIsInQiLCJoYW5kZWxlRGVsZXRlY29uZmlybWF0aW9uIiwiaGFuZGxlY2FuY2VsZGVsZXRlIiwiZGVsZXRlT3Bwb3J0dW5pdHlGcm9tU2hvcnRsaXN0IiwiYXJ0aWNsZUlkIiwiZGVsZXRlIiwiZGF0YSIsInR5cGUiLCJzZXRJc1NhdmVkIiwiZXJyb3IiLCJoYW5kbGVUb2dnbGVPcHBvcnR1bml0eSIsInRoZW1lIiwidXNlciIsImkxOG4iLCJpc01vYmlsZSIsImJyZWFrcG9pbnRzIiwiZG93biIsInVzZUFkZFRvZmF2b3VyaXRlSG9vayIsImxvY2FsZSIsImhlYWRpbmdzIiwic2V0SGVhZGluZ3MiLCJtb2RpZmllZEh0bWxDb250ZW50Iiwic2V0TW9kaWZpZWRIdG1sQ29udGVudCIsImNvbnRlbnQiLCJyZWxhdGVkQXJ0aWNsZXMiLCJzZXRSZWxhdGVkQXJ0aWNsZXMiLCJpc1NhdmVkIiwiZ2V0QXJ0aWNsZSIsInVybEFydGljbGUiLCJlbmFibGVkIiwicGFyc2VyIiwiRE9NUGFyc2VyIiwiZG9jIiwicGFyc2VGcm9tU3RyaW5nIiwiZXh0cmFjdGVkSGVhZGluZ3MiLCJBcnJheSIsImZyb20iLCJxdWVyeVNlbGVjdG9yQWxsIiwiZm9yRWFjaCIsImhlYWRpbmciLCJpbm5lclRleHQiLCJ0b0xvd2VyQ2FzZSIsInJlcGxhY2UiLCJwdXNoIiwidGFnTmFtZSIsImJvZHkiLCJpbm5lckhUTUwiLCJjaGVja0lmU2F2ZWQiLCJyZXNwb25zZSIsImdldCIsInByb2Nlc3MiLCJjb25zb2xlIiwiX2lkIiwiaGFuZGxlU2F2ZUNsaWNrIiwid2FybmluZyIsIm11dGF0ZSIsInRpdGxlIiwidHlwZU9mRmF2b3VyaXRlIiwib25TdWNjZXNzIiwiY29tbWVudHNEYXRhIiwiaXNMb2FkaW5nIiwiY29tbWVudHNMb2FkaW5nIiwicmVmZXRjaCIsImNvbW1lbnRzUmVmZXRjaCIsInBhZ2VOdW1iZXIiLCJwYWdlU2l6ZSIsInBhZ2luYXRlZCIsInNhaHJlZFVybCIsImVudiIsIk5FWFRfUFVCTElDX0ZST05URU5EX1VSTCIsImZldGNoUmVsYXRlZEFydGljbGVzIiwiY2F0ZWdvcmllcyIsImxlbmd0aCIsImNhdGVnb3J5U2x1Z3MiLCJtYXAiLCJjYXQiLCJqb2luIiwiYXJ0aWNsZXMiLCJzdGF0dXMiLCJmZXRjaGVkQXJ0aWNsZXMiLCJpdGVtIiwidmVyc2lvbnMiLCJwaWN0dXJlIiwiaW1hZ2UiLCJkZXNjcmlwdGlvbiIsIm1ldGFEZXNjcmlwdGlvbiIsInNsdWciLCJkaXYiLCJjbGFzc05hbWUiLCJjb250YWluZXIiLCJzcGFjaW5nIiwieHMiLCJzbSIsImNhdGVnb3J5Iiwic3BhbiIsImEiLCJocmVmIiwibmFtZSIsImgxIiwiaGlnaGxpZ2h0cyIsInAiLCJwdWJsaXNoRGF0ZSIsImZvcm1hdCIsInJvbGVzIiwiaW5jbHVkZXMiLCJDQU5ESURBVEUiLCJsZWZ0SWNvbiIsInRleHQiLCJvbkNsaWNrIiwiaWNvbiIsImltZyIsInNyYyIsIk5FWFRfUFVCTElDX0JBU0VfQVBJX1VSTCIsIndpZHRoIiwiaGVpZ2h0IiwiYWx0IiwibG9hZGluZyIsInNjcmlwdCIsImRhbmdlcm91c2x5U2V0SW5uZXJIVE1MIiwiX19odG1sIiwiSlNPTiIsInN0cmluZ2lmeSIsIml0ZW1MaXN0RWxlbWVudCIsInBvc2l0aW9uIiwiY29sdW1uU3BhY2luZyIsImh0bWxDb250ZW50IiwiY3RzQmFubmVyIiwiZmFxIiwiZmFxVGl0bGUiLCJpdGVtcyIsIm9wZW4iLCJtZXNzYWdlIiwib25DbG9zZSIsIm9uQ29uZmlybSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/new-blog/BlogPageDetails.jsx\n"));

/***/ })

});