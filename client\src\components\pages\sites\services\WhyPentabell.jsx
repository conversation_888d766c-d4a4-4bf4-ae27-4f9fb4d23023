import { Container, Grid } from "@mui/material";
import whyPentabellBg from "@/assets/images/services/whyPentabellBg.png";
import initTranslations from "@/app/i18n";

async function WhyPentabell({ title1, title2, reasons, locale }) {
  const midIndex = Math.ceil(reasons.length / 2); 
  const { t, resources } = await initTranslations(locale, [
    "payrollService",
    "global",
  ]);

  return (
    <div id="why-pentabell-section">
      <Container className="custom-max-width">
        <Grid container className="container">
 
          <Grid item xs={12} sm={3} className="reasons">
            {reasons.slice(0, midIndex).map((reason, index) => (
              <div className="reason" key={index}>
                <h3 className="heading-h3 text-white text-center">
                  {reason.heading}
                </h3>
                <p className="paragraph text-white">{reason.text}</p>
              </div>
            ))}
          </Grid>
          <Grid item xs={12} sm={5} className="center-img">
            <div className="img">
              <img
                width={301}
                height={304}
                src={whyPentabellBg.src}
                alt="Why Pentabell"
                loading="lazy"
              />
            </div>
            <div className="txt">
              {title2 ? (
                <h2 className="heading-h1 text-white text-center">
                  {" "}
                  <span className="text-yellow">{title1} </span>
                  {title2}{" "}
                </h2>
              ) : (
                <h2 className="heading-h1 text-white text-center">
                  <span className="text-yellow">
                    {" "}
                    {t("payrollService:whyPentabell:title1")}
                  </span>
                  <br />
                  {t("payrollService:whyPentabell:title2")}
                </h2>
              )}
            </div>
          </Grid>

          <Grid item xs={12} sm={3} className="reasons">
            {reasons.slice(midIndex).map((reason, index) => (
              <div className="reason" key={index}>
                <h3 className="heading-h3 text-white text-center">
                  {reason.heading}
                </h3>
                <p className="paragraph text-white">{reason.text}</p>
              </div>
            ))}
          </Grid>
        </Grid>
      </Container>
    </div>
  );
}

export default WhyPentabell;
