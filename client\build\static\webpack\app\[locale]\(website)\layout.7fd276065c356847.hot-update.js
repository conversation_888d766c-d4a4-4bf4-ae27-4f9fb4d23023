"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/layout",{

/***/ "(app-pages-browser)/./src/components/ui/DropdownMenu.jsx":
/*!********************************************!*\
  !*** ./src/components/ui/DropdownMenu.jsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Menu/Menu.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Fade/Fade.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _assets_images_icons_arrowDown_svg__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../assets/images/icons/arrowDown.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowDown.svg\");\n/* harmony import */ var _assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../assets/images/icons/arrowUp.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowUp.svg\");\n/* harmony import */ var _utils_functions__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/functions */ \"(app-pages-browser)/./src/utils/functions.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst DropdownMenu = (param)=>{\n    let { buttonLabel, buttonHref, menuItems, subMenu, locale, withFlag, selectedFlag } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const open = Boolean(anchorEl);\n    const timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isDetailBlogPath = ()=>pathname.includes(\"/blog/\") && !pathname.includes(\"/blog/category/\") && pathname !== \"/blog/\" && pathname !== \"/fr/blog/\";\n    const handleClick = (event)=>{\n        setAnchorEl(event.currentTarget);\n    };\n    const handleClose = ()=>{\n        setAnchorEl(null);\n    };\n    const handleClick2 = (event, item)=>{\n        event.stopPropagation();\n        if (item.onClick) {\n            item.onClick();\n        } else if (item.subItems == undefined) handleClose();\n    };\n    const handleMouseEnter = (event)=>{\n        clearTimeout(timeoutRef.current);\n        setAnchorEl(event.currentTarget);\n    };\n    const handleMouseLeave = ()=>{\n        timeoutRef.current = setTimeout(()=>{\n            setAnchorEl(null);\n        }, 150);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: subMenu ? `sub-dropdown-menu` : `dropdown-menu`,\n        onMouseEnter: (e)=>setAnchorEl(e.currentTarget),\n        onMouseLeave: handleClose,\n        style: {\n            display: \"inline-block\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: pathname.includes(buttonHref) && buttonHref !== \"\" ? \"navbar-link dropdown-toggle active\" : isDetailBlogPath() ? \"navbar-link dropdown-toggle whiteBg\" : \"navbar-link dropdown-toggle\",\n                id: \"fade-button\",\n                \"aria-controls\": open ? \"fade-menu\" : undefined,\n                \"aria-haspopup\": \"true\",\n                \"aria-expanded\": open ? \"true\" : undefined,\n                onClick: handleClick,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: pathname.includes(buttonHref) && buttonHref !== \"\" ? \"dropdown-toggle-link active\" : isDetailBlogPath() ? \"dropdown-toggle-link whiteBg\" : \"dropdown-toggle-link\",\n                        href: buttonLabel?.toLowerCase() === \"resources\" || buttonLabel?.toLowerCase() === \"ressources\" ? \"#\" : buttonHref ? (0,_utils_functions__WEBPACK_IMPORTED_MODULE_6__.generateLocalizedSlug)(locale, buttonHref).replace(\"/en/\", \"/\") : pathname.replace(\"/en/\", \"/\"),\n                        locale: locale === \"en\" ? \"en\" : \"fr\",\n                        onClick: (e)=>{\n                            if (buttonLabel?.toLowerCase() === \"resources\" || buttonLabel?.toLowerCase() === \"ressources\") {\n                                e.preventDefault();\n                                e.stopPropagation();\n                            }\n                        },\n                        sx: {\n                            ...buttonLabel?.toLowerCase() === \"resources\" || buttonLabel?.toLowerCase() === \"ressources\" ? {\n                                cursor: \"default\",\n                                textDecoration: \"none\",\n                                \"&:hover\": {\n                                    textDecoration: \"none\"\n                                }\n                            } : {}\n                        },\n                        children: withFlag ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            className: \"flag-lang\",\n                            src: selectedFlag.src,\n                            width: 26,\n                            height: 22,\n                            disabled: true,\n                            loading: \"lazy\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 119,\n                            columnNumber: 13\n                        }, undefined) : buttonLabel\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, undefined),\n                    open ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowDown_svg__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 131,\n                        columnNumber: 17\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 131,\n                        columnNumber: 36\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                id: \"fade-menu\",\n                MenuListProps: {\n                    \"aria-labelledby\": \"fade-button\"\n                },\n                anchorEl: anchorEl,\n                open: open,\n                onClose: handleClose,\n                TransitionComponent: _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                disableScrollLock: true,\n                anchorOrigin: {\n                    vertical: \"bottom\",\n                    horizontal: subMenu ? \"right\" : \"left\"\n                },\n                transformOrigin: {\n                    vertical: \"top\",\n                    horizontal: subMenu ? \"left\" : \"left\"\n                },\n                children: menuItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: item.subItems ? `sub-menu dropdown-item` : `dropdown-item`,\n                        onClick: (e)=>handleClick2(e, item),\n                        sx: {\n                            // Remove default hover background for items with submenus\n                            ...item.subItems && {\n                                \"&:hover\": {\n                                    backgroundColor: \"transparent !important\"\n                                },\n                                \"&.Mui-focusVisible\": {\n                                    backgroundColor: \"transparent !important\"\n                                },\n                                \"&.Mui-selected\": {\n                                    backgroundColor: \"transparent !important\"\n                                }\n                            }\n                        },\n                        children: item.subItems ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DropdownMenu, {\n                            buttonLabel: item?.i18nName ? t(item?.i18nName) : item.name,\n                            buttonHref: item.route,\n                            menuItems: item.subItems,\n                            subMenu: true,\n                            locale: locale\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 176,\n                            columnNumber: 15\n                        }, undefined) : item.route ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            href: (0,_utils_functions__WEBPACK_IMPORTED_MODULE_6__.generateLocalizedSlug)(locale, item.route),\n                            locale: locale === \"en\" ? \"en\" : \"fr\",\n                            className: pathname.includes(item.route) ? \"dropdown-item-link active\" : \"dropdown-item-link\",\n                            children: [\n                                item.icon ?? item.icon,\n                                \" \",\n                                item?.i18nName ? t(item?.i18nName) : item.name\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 184,\n                            columnNumber: 15\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"dropdown-item-link\",\n                            href: \"#\",\n                            children: withFlag ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                className: \"flag-lang\",\n                                src: item.flag.src,\n                                width: 26,\n                                height: 22,\n                                loading: \"lazy\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                                lineNumber: 199,\n                                columnNumber: 19\n                            }, undefined) : item?.i18nName ? t(item?.i18nName) : item.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 197,\n                            columnNumber: 15\n                        }, undefined)\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 154,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DropdownMenu, \"dkvKbdhBU6hKujyfH1UxLVv1rcc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation\n    ];\n});\n_c = DropdownMenu;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DropdownMenu);\nvar _c;\n$RefreshReg$(_c, \"DropdownMenu\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/DropdownMenu.jsx\n"));

/***/ })

});