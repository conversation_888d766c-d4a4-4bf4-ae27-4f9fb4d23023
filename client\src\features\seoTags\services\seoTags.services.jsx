import { axiosGetJson } from "@/config/axios";
import { API_URLS } from "@/utils/urls";
import { toast } from "react-toastify";

export const createSeo = (body) => {
  let t = body.t;
  return new Promise(async (resolve, reject) => {
    axiosGetJson
      .post(API_URLS.seo, body.data)
      .then((valid) => {
        toast.success("Seo tags added successfully");
        if (valid?.data) {
          resolve(valid.data);
        }
      })
      .catch((err) => {
        if (err && err.response && err.response.data) {
          if (err.response && err.response.status === 409) {
            toast.warning(`Seo Tags with this slug already exists`);
          }
        }
        if (err) {
          reject(err);
        }
      });
  });
};

export const updateSeo = ({ data, language, id }) => {
  return new Promise(async (resolve, reject) => {
    axiosGetJson
      .put(`${API_URLS.seo}/${id}`, data)
      .then((valid) => {
        toast.success(`Seo english updated successfully`);
        if (valid?.data) {
          resolve(valid.data);
        }
      })
      .catch((err) => {
        if (err && err.response && err.response.data) {
            if (err.response && err.response.status === 409) {
                toast.warning(`Seo Tags with this slug already exists`);
              }
        }
        if (err) {
          reject(err);
        }
      });
  });
};

export const getSeo = (body) => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await axiosGetJson.get(`${API_URLS.seo}`, {
        params: {
          paginated: body.paginated,
          language: body.language,
          pageSize: body.pageSize,
          pageNumber: body.pageNumber,
          sortOrder: body.sortOrder,
          slug: body.slug,
        },
      });
      resolve(response.data);
    } catch (err) {
      reject(err);
    }
  });
};

export const getSeoBySlug = (body) => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await axiosGetJson.get(`${API_URLS.seo}/${body.language}/${body.slug}`);
      resolve(response.data);
    } catch (err) {
      reject(err);
    }
  });
};

export const getSeoById = (id) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await axiosGetJson.get(`${API_URLS.seo}/${id}`);
        resolve(response.data);
      } catch (err) {
        reject(err);
      }
    });
  };
  