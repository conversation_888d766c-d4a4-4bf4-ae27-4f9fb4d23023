"use client";

import { useState } from "react";
import { FormGroup, TextField } from "@mui/material";
import { ErrorMessage, Form, Formik } from "formik";
import { useTranslation } from "react-i18next";

import CustomButton from "@/components/ui/CustomButton";
import { useSubscribeToNewsletter } from "@/features/newsletter/hooks/Newsletter.hooks";
import AlertMessage from "@/components/ui/AlertMessage";
import MailWithBg from "../../assets/images/icons/mailWithBg.svg";
import { subscriptionValidationSchema } from "@/utils/validations";

export default function NewsletterSubscription() {
  const [errMsg, setErrMsg] = useState("");
  const [success, setSuccess] = useState(false);

  const { t } = useTranslation();

  const initialValues = {
    email: "",
  };

  const useSubscribeToNewsletterHook = useSubscribeToNewsletter(
    setSuccess,
    setErrMsg
  );

  const handleSubmit = async (values, { resetForm }) => {
    const { email } = values;
    await useSubscribeToNewsletterHook.mutateAsync({
      email,
    });
    resetForm();
    setTimeout(() => {
      setSuccess(false);
    }, 3000);
  };

  return (
    <>
      <div className="icon">
        <MailWithBg />
      </div>
      <p className="title">Subscribe for updates</p>
      <p className="description">
        Turn on this job alert so you don t miss out on openings that could fit
        your needs
      </p>
      <Formik
        initialValues={initialValues}
        validationSchema={() => subscriptionValidationSchema(t)}
        onSubmit={handleSubmit}
      >
        {({ values, handleChange, errors, touched }) => (
          <Form>
            <FormGroup className="form-group">
              <TextField
                className="input-pentabell"
                placeholder="<EMAIL>"
                variant="standard"
                name="email"
                value={values.email}
                onChange={(e) => {
                  handleChange(e);
                  setErrMsg("");
                }}
                error={!!(errors.email && touched.email)}
              />
            </FormGroup>
            <ErrorMessage name="email">
              {(msg) => <span className="error-span">{msg}</span>}
            </ErrorMessage>
            <CustomButton
              text={"Submit"}
              className={"btn btn-filled blue"}
              type="submit"
            />
          </Form>
        )}
      </Formik>
      <AlertMessage errMsg={errMsg} success={success} />
    </>
  );
}
