"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/add/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/AddArticleFroala.jsx":
/*!***********************************************************!*\
  !*** ./src/features/blog/components/AddArticleFroala.jsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Accordion/Accordion.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionSummary/AccordionSummary.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionDetails/AccordionDetails.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormGroup/FormGroup.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormLabel/FormLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mui/icons-material/ExpandMore */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/ExpandMore.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! yup */ \"(app-pages-browser)/./node_modules/yup/index.esm.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! formik */ \"(app-pages-browser)/./node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var _utils_urls__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../utils/urls */ \"(app-pages-browser)/./src/utils/urls.js\");\n/* harmony import */ var _AddArticleEN__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./AddArticleEN */ \"(app-pages-browser)/./src/features/blog/components/AddArticleEN.jsx\");\n/* harmony import */ var _AddArticleFR__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./AddArticleFR */ \"(app-pages-browser)/./src/features/blog/components/AddArticleFR.jsx\");\n/* harmony import */ var _opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../opportunity/hooks/opportunity.hooks */ \"(app-pages-browser)/./src/features/opportunity/hooks/opportunity.hooks.js\");\n/* harmony import */ var _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../hooks/blog.hook */ \"(app-pages-browser)/./src/features/blog/hooks/blog.hook.js\");\n/* harmony import */ var react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-datepicker/dist/react-datepicker.css */ \"(app-pages-browser)/./node_modules/react-datepicker/dist/react-datepicker.css\");\n/* harmony import */ var _config_axios__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/config/axios */ \"(app-pages-browser)/./src/config/axios.js\");\n/* harmony import */ var _features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/features/auth/hooks/currentUser.hooks */ \"(app-pages-browser)/./src/features/auth/hooks/currentUser.hooks.js\");\n/* harmony import */ var _components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! lodash/debounce */ \"(app-pages-browser)/./node_modules/lodash/debounce.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_15__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AddArticle = ()=>{\n    _s();\n    const { user } = (0,_features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_13__[\"default\"])();\n    const savedArticle = localStorage.getItem(\"savedArticle\");\n    const useUpdateAutoSaveHook = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useUpdateAutoSave)();\n    const useCreateArticleHook = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useCreateArticle)();\n    const useCreateAutoSaveHook = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useCreateAutoSave)();\n    const useSaveFileHook = (0,_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_9__.useSaveFile)();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const currentYear = new Date().getFullYear();\n    const formikRefAll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [expanded, setExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(`panel`);\n    const [formdataEN, setFormDataEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new FormData());\n    const [formdataFR, setFormDataFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new FormData());\n    const [uuidPhotoFileNameEN, setUuidPhotoFileNameEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [uuidPhotoFileNameFR, setUuidPhotoFileNameFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedLanguages, setSelectedLanguages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        en: true,\n        fr: false\n    });\n    const [categoriesEN, setCategoriesEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categoriesFR, setCategoriesFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredCategoriesEN, setFilteredCategoriesEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredCategoriesFR, setFilteredCategoriesFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCategoriesEN, setSelectedCategoriesEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCategoriesFR, setSelectedCategoriesFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const getCategoriesEN = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories)(\"en\");\n    const getCategoriesFR = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories)(\"fr\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (getCategoriesEN.data?.categories) {\n            const transformedCategories = getCategoriesEN.data.categories.map((category)=>({\n                    id: category.versionscategory[0]?.id,\n                    name: category.versionscategory[0]?.name\n                }));\n            setCategoriesEN(transformedCategories);\n        }\n    }, [\n        getCategoriesEN.data\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (getCategoriesFR.data?.categories) {\n            const transformedCategories = getCategoriesFR.data.categories.map((category)=>({\n                    id: category?.versionscategory[0]?.id,\n                    name: category.versionscategory[0]?.name\n                }));\n            setCategoriesFR(transformedCategories);\n        }\n    }, [\n        getCategoriesFR.data\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedCategoriesEN.length > 0) {\n            fetchTranslatedCategories(selectedCategoriesEN, \"en\");\n        } else if (selectedCategoriesFR.length > 0) {\n            fetchTranslatedCategories(selectedCategoriesFR, \"fr\");\n        }\n    }, [\n        selectedCategoriesEN,\n        selectedCategoriesFR\n    ]);\n    const fetchTranslatedCategories = async (selectedCategories, language)=>{\n        try {\n            const response = await _config_axios__WEBPACK_IMPORTED_MODULE_12__.axiosGetJson.get(`${_utils_urls__WEBPACK_IMPORTED_MODULE_6__.API_URLS.categories}/${language}/${selectedCategories}`);\n            if (language === \"en\") {\n                const transformedCategories = response.data.map((category)=>({\n                        id: category._id,\n                        name: category.name\n                    }));\n                setFilteredCategoriesFR(transformedCategories);\n            } else {\n                const transformedCategories = response.data.map((category)=>({\n                        id: category._id,\n                        name: category.name\n                    }));\n                setFilteredCategoriesEN(transformedCategories);\n            }\n        } catch (error) {\n            console.error(\"Error fetching translated categories:\", error);\n        }\n    };\n    const handleImageSelect = async (selectedFile, language)=>{\n        if (language === \"en\") {\n            const uuidPhotos = (0,uuid__WEBPACK_IMPORTED_MODULE_16__[\"default\"])().replace(/-/g, \"\");\n            const newFormData = new FormData();\n            newFormData.append(\"file\", selectedFile);\n            setFormDataEN(newFormData);\n            const extension = selectedFile.name.split(\".\").pop();\n            setUuidPhotoFileNameEN(`${uuidPhotos}.${extension}`);\n        } else if (language === \"fr\") {\n            const uuidPhotos = (0,uuid__WEBPACK_IMPORTED_MODULE_16__[\"default\"])().replace(/-/g, \"\");\n            const newFormData = new FormData();\n            newFormData.append(\"file\", selectedFile);\n            setFormDataFR(newFormData);\n            const extension = selectedFile.name.split(\".\").pop();\n            setUuidPhotoFileNameFR(`${uuidPhotos}.${extension}`);\n        }\n    };\n    const getLocalStorageItem = (key)=>{\n        try {\n            const item = localStorage.getItem(key);\n            return item ? JSON.parse(item) : \"\";\n        } catch (error) {\n            console.warn(`Error parsing localStorage item \"${key}\":`, error);\n            return \"\";\n        }\n    };\n    const titleEN = getLocalStorageItem(\"title\");\n    const metaTitleEN = getLocalStorageItem(\"metatitle\");\n    const metaDescriptionEN = getLocalStorageItem(\"metaDescription\");\n    const contentEN = getLocalStorageItem(\"content\");\n    const contentFR = getLocalStorageItem(\"contentfr\");\n    const titleFR = getLocalStorageItem(\"titlefr\");\n    const metaDescriptionFR = getLocalStorageItem(\"metaDescriptionfr\");\n    const metaTitleFR = getLocalStorageItem(\"metatitlefr\");\n    const initialValues = {\n        robotsMeta: \"index\",\n        metaTitleEN: metaTitleEN,\n        metaDescriptionEN: metaDescriptionEN,\n        descriptionEN: \"\",\n        visibilityEN: \"\",\n        categoryEN: [],\n        imageEN: null,\n        keywordsEN: \"\",\n        titleEN: titleEN,\n        urlEN: \"\",\n        altEN: \"\",\n        contentEN: contentEN,\n        highlightsEN: [],\n        publishDateEN: \"\",\n        faqTitleEN: \"\",\n        faqEN: [],\n        titleFR: titleFR,\n        metaTitleFR: metaTitleFR,\n        metaDescriptionFR: metaDescriptionFR,\n        descriptionFR: \"\",\n        visibilityFR: \"\",\n        categoryFR: [],\n        imageFR: null,\n        keywordsFR: \"\",\n        urlFR: \"\",\n        altFR: \"\",\n        contentFR: contentFR,\n        publishDateFR: \"\",\n        highlightsFR: [],\n        faqTitleFR: \"\",\n        faqFR: []\n    };\n    const validationSchema = yup__WEBPACK_IMPORTED_MODULE_3__.object().shape({\n        ...selectedLanguages.en && {\n            metaTitleEN: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            metaDescriptionEN: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            titleEN: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            category: yup__WEBPACK_IMPORTED_MODULE_3__.array().min(1, t(\"validations:minCategory\")),\n            visibilityEN: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\"))\n        },\n        ...selectedLanguages.fr && {\n            metaTitleFR: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            metaDescriptionFR: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            titleFR: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            visibilityFR: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\"))\n        }\n    });\n    const uploadFile = (filename, formData, lang)=>{\n        return new Promise((resolve)=>{\n            useSaveFileHook.mutate({\n                resource: \"blogs\",\n                folder: currentYear,\n                filename,\n                body: {\n                    formData,\n                    t\n                }\n            }, {\n                onSuccess: (data)=>{\n                    resolve({\n                        lang,\n                        uuid: data.uuid\n                    });\n                },\n                onError: (error)=>{\n                    console.error(`Error uploading ${lang} image:`, error);\n                    resolve({\n                        lang,\n                        uuid: null\n                    });\n                }\n            });\n        });\n    };\n    const clearLocalStorage = ()=>{\n        const keysToRemove = [\n            \"title\",\n            \"content\",\n            \"titlefr\",\n            \"contentfr\",\n            \"metaDescription\",\n            \"metaDescriptionfr\",\n            \"metatitle\",\n            \"metatitlefr\",\n            \"savedArticle\"\n        ];\n        keysToRemove.forEach((key)=>{\n            try {\n                localStorage.removeItem(key);\n            } catch (error) {\n                console.warn(`Error removing localStorage item \"${key}\":`, error);\n            }\n        });\n    };\n    const handleSubmit = async (values)=>{\n        const data = {\n            robotsMeta: values.robotsMeta,\n            versions: []\n        };\n        if (!selectedLanguages.en && !selectedLanguages.fr) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please select at least one version!\");\n            return;\n        }\n        try {\n            let resultEN, resultFR;\n            // Validate required files\n            if (selectedLanguages.en && (!uuidPhotoFileNameEN || !formdataEN)) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please select an image for the English version!\");\n                return;\n            }\n            if (selectedLanguages.fr && (!uuidPhotoFileNameFR || !formdataFR)) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please select an image for the French version!\");\n                return;\n            }\n            if (selectedLanguages.en) {\n                resultEN = await uploadFile(uuidPhotoFileNameEN, formdataEN, \"English\");\n            }\n            if (selectedLanguages.fr) {\n                resultFR = await uploadFile(uuidPhotoFileNameFR, formdataFR, \"French\");\n            }\n            if (selectedLanguages.en && resultEN.uuid) {\n                data.versions.push({\n                    language: \"en\",\n                    metaTitle: values.metaTitleEN,\n                    title: values.titleEN,\n                    metaDescription: values.metaDescriptionEN,\n                    description: values.descriptionEN,\n                    url: values.urlEN,\n                    visibility: values.visibilityEN,\n                    publishDate: values.publishDateEN,\n                    content: values.contentEN,\n                    alt: values.altEN,\n                    keywords: values.keywordsEN,\n                    highlights: values.highlightsEN,\n                    image: resultEN.uuid,\n                    category: values.categoryEN === \"\" ? [] : values.categoryEN,\n                    createdBy: user?.firstName + \" \" + user?.lastName,\n                    faqTitle: values.faqTitleEN || \"\",\n                    faq: values.faqEN || []\n                });\n            }\n            if (selectedLanguages.fr && resultFR.uuid) {\n                data.versions.push({\n                    language: \"fr\",\n                    metaTitle: values.metaTitleFR,\n                    title: values.titleFR,\n                    metaDescription: values.metaDescriptionFR,\n                    url: values.urlFR,\n                    visibility: values.visibilityFR,\n                    description: values.descriptionFR,\n                    publishDate: values.publishDateFR,\n                    content: values.contentFR,\n                    alt: values.altFR,\n                    keywords: values.keywordsFR,\n                    highlights: values.highlightsFR,\n                    image: resultFR.uuid,\n                    category: values.categoryFR === \"\" ? [] : values.categoryFR,\n                    createdBy: user?.firstName + \" \" + user?.lastName,\n                    faqTitle: values.faqTitleFR || \"\",\n                    faq: values.faqFR || []\n                });\n            }\n            if (data.versions.length > 0) {\n                if (isSavedArticle !== \"\") {\n                    useUpdateAutoSaveHook.mutate({\n                        data: data,\n                        id: isSavedArticle\n                    }, {\n                        onSuccess: ()=>{\n                            clearLocalStorage();\n                            window.location.href = `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.blogs.route}/`;\n                        }\n                    });\n                } else {\n                    useCreateArticleHook.mutate({\n                        data\n                    }, {\n                        onSuccess: ()=>{\n                            clearLocalStorage();\n                            window.location.href = `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.blogs.route}/`;\n                        }\n                    });\n                }\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"No valid image uploads found. Article not created.\");\n            }\n        } catch (error) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"An error occurred while processing uploads.\");\n        }\n    };\n    const handleChangeAccordion = (panel)=>(_, newExpanded)=>{\n            setExpanded(newExpanded ? panel : false);\n        };\n    const handleCategoriesENSelect = (selectedCategories)=>{\n        setSelectedCategoriesEN(selectedCategories);\n    };\n    const handleCategoriesFRSelect = (selectedCategories)=>{\n        setSelectedCategoriesFR(selectedCategories);\n    };\n    const [isSavedArticle, setIsSavedArticle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(savedArticle || \"\");\n    const isSavedArticleRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(isSavedArticle);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        isSavedArticleRef.current = isSavedArticle;\n    }, [\n        isSavedArticle\n    ]);\n    const autosave = async ()=>{\n        try {\n            const values = formikRefAll.current?.values;\n            if (!values) return;\n            const data = {\n                robotsMeta: values?.robotsMeta,\n                versions: []\n            };\n            if (selectedLanguages.en) {\n                data.versions.push({\n                    language: \"en\",\n                    metaTitle: values.metaTitleEN,\n                    title: values.titleEN,\n                    metaDescription: values.metaDescriptionEN,\n                    url: values.urlEN,\n                    visibility: \"Draft\",\n                    publishDate: values.publishDateEN,\n                    content: values.contentEN,\n                    alt: values.altEN,\n                    keywords: values.keywordsEN,\n                    category: values.categoryEN,\n                    highlights: values.highlightsEN,\n                    faqTitle: values.faqTitleEN || \"\",\n                    faq: values.faqEN || []\n                });\n            }\n            if (selectedLanguages.fr) {\n                data.versions.push({\n                    language: \"fr\",\n                    metaTitle: values.metaTitleFR,\n                    title: values.titleFR,\n                    metaDescription: values.metaDescriptionFR,\n                    url: values.urlFR,\n                    visibility: \"Draft\",\n                    publishDate: values.publishDateFR,\n                    content: values.contentFR,\n                    alt: values.altFR,\n                    keywords: values.keywordsFR,\n                    category: values.categoryFR,\n                    highlights: values.highlightsFR,\n                    faqTitle: values.faqTitleFR || \"\",\n                    faq: values.faqFR || []\n                });\n            }\n            if (isSavedArticleRef.current != \"\") {\n                useUpdateAutoSaveHook.mutate({\n                    data: data,\n                    id: isSavedArticleRef.current\n                });\n            } else {\n                if (data.versions.length > 0) {\n                    useCreateAutoSaveHook.mutate({\n                        data\n                    }, {\n                        onSuccess: (data)=>{\n                            setIsSavedArticle(data.articleId);\n                            localStorage.setItem(\"savedArticle\", data.articleId);\n                        }\n                    });\n                }\n            }\n        } catch (error) {\n            console.warn(\"Auto-save failed:\", error);\n        }\n    };\n    // Optimized debounce timing: 2 minutes instead of 2 minutes (120000ms)\n    const handleChange = lodash_debounce__WEBPACK_IMPORTED_MODULE_15___default()(autosave, 30000); // 30 seconds for better UX\n    const handleClear = ()=>{\n        formikRefAll.current?.resetForm();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"heading-h2 semi-bold\",\n                children: t(\"createArticle:addArticle\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                lineNumber: 472,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"container\",\n                className: \"recent-application-pentabell\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `main-content`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"commun\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"label-form\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: selectedLanguages.en,\n                                                onChange: ()=>setSelectedLanguages((prev)=>({\n                                                            ...prev,\n                                                            en: !prev.en\n                                                        }))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                lineNumber: 479,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"English\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"label-form\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: selectedLanguages.fr,\n                                                onChange: ()=>setSelectedLanguages((prev)=>({\n                                                            ...prev,\n                                                            fr: !prev.fr\n                                                        }))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"French\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                lineNumber: 477,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                id: \"experiences\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    id: \"form\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.Formik, {\n                                        initialValues: initialValues,\n                                        validationSchema: validationSchema,\n                                        innerRef: formikRefAll,\n                                        onSubmit: handleSubmit,\n                                        className: \"formik-form\",\n                                        children: (param)=>{\n                                            let { errors, touched, setFieldValue, values } = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.Form, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        id: \"accordion\",\n                                                        disableGutters: true,\n                                                        expanded: expanded === `panel`,\n                                                        onChange: handleChangeAccordion(`panel`),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                    lineNumber: 524,\n                                                                    columnNumber: 39\n                                                                }, void 0),\n                                                                \"aria-controls\": `panel-content`,\n                                                                id: `panel-header`,\n                                                                children: t(\"createArticle:settings\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                lineNumber: 523,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"accordion-detail\",\n                                                                elevation: 0,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"inline-group\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                    className: \"label-form\",\n                                                                                    children: [\n                                                                                        \"Robots meta\",\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                            className: \"select-pentabell\",\n                                                                                            variant: \"standard\",\n                                                                                            sx: {\n                                                                                                m: 1,\n                                                                                                minWidth: 120\n                                                                                            },\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                                value: _utils_constants__WEBPACK_IMPORTED_MODULE_5__.RobotsMeta.filter((option)=>values.robotsMeta === option),\n                                                                                                selected: values.robotsMeta,\n                                                                                                onChange: (event)=>{\n                                                                                                    setFieldValue(\"robotsMeta\", event.target.value);\n                                                                                                },\n                                                                                                children: _utils_constants__WEBPACK_IMPORTED_MODULE_5__.RobotsMeta.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                                        value: item,\n                                                                                                        children: item\n                                                                                                    }, item, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                                        lineNumber: 557,\n                                                                                                        columnNumber: 41\n                                                                                                    }, undefined))\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                                lineNumber: 544,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                            lineNumber: 539,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.ErrorMessage, {\n                                                                                            className: \"label-error\",\n                                                                                            name: \"robotsMeta\",\n                                                                                            component: \"div\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                            lineNumber: 563,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                    lineNumber: 537,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                lineNumber: 536,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.ErrorMessage, {\n                                                                                name: \"robotsMeta\",\n                                                                                component: \"div\",\n                                                                                className: \"label-error\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                lineNumber: 571,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                        lineNumber: 535,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                    lineNumber: 534,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                lineNumber: 530,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, `panel`, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    selectedLanguages.en && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddArticleEN__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        errors: errors,\n                                                        touched: touched,\n                                                        setFieldValue: setFieldValue,\n                                                        values: values,\n                                                        onImageSelect: handleImageSelect,\n                                                        debounce: handleChange,\n                                                        categories: categoriesEN,\n                                                        filteredCategories: filteredCategoriesEN,\n                                                        onCategoriesSelect: handleCategoriesENSelect\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                        lineNumber: 581,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    selectedLanguages.fr && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddArticleFR__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        errors: errors,\n                                                        touched: touched,\n                                                        setFieldValue: setFieldValue,\n                                                        values: values,\n                                                        onImageSelect: handleImageSelect,\n                                                        categories: categoriesFR,\n                                                        filteredCategories: filteredCategoriesFR,\n                                                        onCategoriesSelect: handleCategoriesFRSelect,\n                                                        debounce: handleChange\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                        lineNumber: 594,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"btn-container\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                type: \"button\",\n                                                                text: \"Clear\",\n                                                                className: \"btn btn-filled\",\n                                                                onClick: handleClear\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                lineNumber: 607,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                type: \"submit\",\n                                                                text: \"Save\",\n                                                                className: \"btn btn-filled\",\n                                                                onClick: ()=>{}\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                lineNumber: 613,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                        lineNumber: 606,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                lineNumber: 515,\n                                                columnNumber: 21\n                                            }, undefined);\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                lineNumber: 505,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                        lineNumber: 476,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                    lineNumber: 475,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                lineNumber: 474,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(AddArticle, \"rnB4oaCXGsNbzt/ZpNSvh2lS5Yo=\", false, function() {\n    return [\n        _features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useUpdateAutoSave,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useCreateArticle,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useCreateAutoSave,\n        _opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_9__.useSaveFile,\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories\n    ];\n});\n_c = AddArticle;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AddArticle);\nvar _c;\n$RefreshReg$(_c, \"AddArticle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/AddArticleFroala.jsx\n"));

/***/ })

});