"use client";
import { useState, useEffect, useRef } from "react";
import { Container, Box, Grid, useMediaQuery, useTheme } from "@mui/material";
import s1 from "@/assets/images/website/coporate-profile/slides/s1.jpg";
import s2 from "@/assets/images/website/coporate-profile/slides/s2.jpg";
import s3 from "@/assets/images/website/coporate-profile/slides/s3.jpg";
import s4 from "@/assets/images/website/coporate-profile/slides/s4.jpg";
import s5 from "@/assets/images/website/coporate-profile/slides/s5.jpg";
import s1m from "@/assets/images/website/coporate-profile/slides/s1m.jpg";
import s2m from "@/assets/images/website/coporate-profile/slides/s2m.jpg";
import s3m from "@/assets/images/website/coporate-profile/slides/s3m.jpg";
import s4m from "@/assets/images/website/coporate-profile/slides/s4m.jpg";
import s5m from "@/assets/images/website/coporate-profile/slides/s5m.jpg";
import CustomButton from "@/components/ui/CustomButton";
import SDGs from "@/assets/images/website/coporate-profile/SDGs.png";
import iso from "@/assets/images/website/coporate-profile/iso.png";
import ArrowLeft from "@/assets/images/icons/arrow-left.svg";
import ArrowRight from "@/assets/images/icons/arrow-right.svg";

const images = [s1.src, s2.src, s3.src, s4.src, s5.src];
const imagesMobile = [s1m.src, s2m.src, s3m.src, s4m.src, s5m.src];


function SustinabilitySection() {
      const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const [current, setCurrent] = useState(0);
  const [gallery, setGallery] = useState(images);

  const intervalRef = useRef(null);
  useEffect(() => {
   isMobile ? setGallery(imagesMobile)  : setGallery(images);
  }, [isMobile])
  
  const startAutoplay = () => {
    stopAutoplay();
    intervalRef.current = setInterval(() => {
      setCurrent((prev) => (prev + 1) % gallery.length);
    }, 5000);
  };

  const stopAutoplay = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
  };

  useEffect(() => {
    startAutoplay();
    return () => stopAutoplay();
  }, []);

  const handlePrev = () => {
    setCurrent((prev) => (prev - 1 + gallery.length) % gallery.length);
    startAutoplay();
  };

  const handleNext = () => {
    setCurrent((prev) => (prev + 1) % gallery.length);
    startAutoplay();
  };

  return (
    <div>
      <Box id="sustinability-section">
        <Box
          className="sustinability-slides"
          sx={{
            backgroundImage: `url(${gallery[current]})`,
          }}
        />

        <Container className="relative-section custom-max-width">
          <Grid
            container
            spacing={2}
            alignItems="flex-end"
            height={"100%"}
            className="m-auto"
            justifyContent={"space-between"}
          >
            <Grid item xs={12} sm={8}  sx={{ zIndex: 1, paddingLeft:"0 !important", paddingTop:"0 !important" }} >
              <Box className="relative-div">
                <h2 className="heading-h2 text-white semi-bold mb-0">
                  Pentabell Commitment to People, Partners & Sustainability
                </h2>
                <Box
                  sx={{
                    display: "flex",
                    gap: 2,
                    mt: 1,
                  }}
                >
                  <CustomButton
                    onClick={handlePrev}
                    icon={<ArrowLeft />}
                    className={`slider-control`}
                  />
                  <CustomButton
                    onClick={handleNext}
                    icon={<ArrowRight />}
                    className={`slider-control`}
                  />
                </Box>
              </Box>
            </Grid>

            <Grid item xs={0} sm={4} className="why-matters-section d-none-sm">
              <p className="sub-heading text-white semi-bold">
                The Bigger Picture
              </p>
              <p className="paragraph text-white">
                Pentabell operations impact hundreds of lives every day. We are
                investing in social sustainability, not only to reduce risk
                categories like discrimination and unsafe work conditions, but
                to unfold potential-- to bring workforce systems to life as
                singular pathways of opportunity.
        
              </p>  <br/>
              <p className="paragraph text-white">
                Any action we take is actively shapes
                how an entire industry hires, leads, and takes care of its
                workers in an ever-changing ecosystem.
              </p>

              <p className="sub-heading text-white semi-bold">SDGS</p>
              <img className="sdg-img" src={SDGs.src} alt="sdg5"  />

              <p className="sub-heading text-white semi-bold">ISO</p>
              <img className="iso-img" src={iso.src} alt="iso1" />
            </Grid>
          </Grid>
        </Container>
      </Box>
      <Container>
        <Grid container className="why-matters-section d-block-sm">
          <p className="sub-heading text-white semi-bold">The Bigger Picture</p>
          <p className="paragraph text-white">
            Pentabell operations impact hundreds of lives every day. We are
            investing in social sustainability, not only to reduce risk
            categories like discrimination and unsafe work conditions, but to
            unfold potential-- to bring workforce systems to life as singular
            pathways of opportunity.
          </p>
          <br/>
          <p className="paragraph text-white">
            Any action we take is actively shapes how
            an entire industry hires, leads, and takes care of its workers in an
            ever-changing ecosystem.
          </p>

          <p className="sub-heading text-white semi-bold">SDGS</p>
          <img src={SDGs.src} alt="sdg5" height={50} />

          <p className="sub-heading text-white semi-bold">ISO</p>
          <img src={iso.src} alt="iso1" height={50} />
        </Grid>
      </Container>
    </div>
  );
}

export default SustinabilitySection;
