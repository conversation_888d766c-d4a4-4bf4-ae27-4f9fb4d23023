"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const constants_1 = require("@/utils/helpers/constants");
const mongoose_1 = require("mongoose");
const slideVersionSchema = new mongoose_1.Schema({
    language: { type: String, enum: constants_1.Language, required: true },
    title: String,
    link: String,
    img: String,
    altImg: String,
    linkBtn: String,
    imgMobile: String,
    visibility: {
        type: String,
        enum: constants_1.Visibility,
        default: constants_1.Visibility.Draft,
    },
    isArchived: { type: Boolean, default: false },
    createdAt: { type: Date, required: true, default: Date.now },
    updatedAt: { type: Date, required: true, default: Date.now },
});
const slideSchema = new mongoose_1.Schema({
    versionslide: { type: [slideVersionSchema], required: true },
    createdBy: { type: mongoose_1.Types.ObjectId, ref: 'User' },
    order: { type: Number }
}, {
    timestamps: true,
    toJSON: {
        transform: function (doc, ret) {
            delete ret.__v;
        },
    },
});
exports.default = (0, mongoose_1.model)('Slide', slideSchema);
//# sourceMappingURL=slide.model.js.map