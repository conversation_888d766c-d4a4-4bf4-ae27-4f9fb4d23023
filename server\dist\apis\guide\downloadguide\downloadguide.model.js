"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.downloadModel = void 0;
const mongoose_1 = require("mongoose");
const downloadSchema = new mongoose_1.Schema({
    guides: [
        {
            type: mongoose_1.Types.ObjectId,
            ref: 'Guide',
        },
    ],
    email: { type: String },
    firstName: { type: String },
    lastName: { type: String },
    companysize: { type: String },
    Headquarters: { type: String },
}, {
    timestamps: true,
});
exports.downloadModel = (0, mongoose_1.model)('Download', downloadSchema);
//# sourceMappingURL=downloadguide.model.js.map