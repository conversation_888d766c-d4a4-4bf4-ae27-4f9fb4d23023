"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const authentication_middleware_1 = __importDefault(require("@/middlewares/authentication.middleware"));
const mongoId_validation_middleware_1 = __importDefault(require("@/middlewares/mongoId-validation.middleware"));
const queries_validation_middleware_1 = __importDefault(require("@/middlewares/queries-validation.middleware"));
const event_service_1 = __importDefault(require("./event.service"));
const validateApiKey_middleware_1 = __importDefault(require("@/middlewares/validateApiKey.middleware"));
const cache_middleware_1 = require("@/middlewares/cache.middleware");
const authorization_middleware_1 = require("@/middlewares/authorization.middleware");
const constants_1 = require("@/utils/helpers/constants");
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const messages_1 = require("@/utils/helpers/messages");
class EventController {
    constructor() {
        this.path = '/events';
        this.router = (0, express_1.Router)();
        this.eventService = new event_service_1.default();
        this.getEvents = async (request, response, next) => {
            try {
                const queries = request.query;
                const result = await this.eventService.getAll(queries);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.getEventBySlug = async (request, response, next) => {
            try {
                const slug = request.params.slug;
                const language = request.params.language;
                const result = await this.eventService.getEventBySlug(slug, language);
                response.send({ data: result, status: 200 });
            }
            catch (error) {
                next(error);
            }
        };
        this.deleteEvent = async (request, response, next) => {
            try {
                const { eventId } = request.params;
                const event = await this.eventService.deleteevent(eventId);
                if (!event) {
                    throw new http_exception_1.default(404, messages_1.MESSAGES.EVENT.NOT_FOUND);
                }
                response.status(204).send();
            }
            catch (error) {
                next(error);
            }
        };
        this.getSlugBySlug = async (req, res, next) => {
            try {
                const { slug, language } = req.params;
                const event = await this.eventService.getSlugBySlug(language, slug);
                res.json(event);
            }
            catch (error) {
                next(error);
            }
        };
        this.getEventById = async (request, response, next) => {
            try {
                const id = request.params.id;
                const result = await this.eventService.getEventTagById(id);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.create = async (request, response, next) => {
            try {
                const data = request.body;
                const result = await this.eventService.create(data);
                response.status(201).send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.update = async (request, response, next) => {
            try {
                const id = request.params.id;
                const data = request.body;
                const language = request.params.language;
                const result = await this.eventService.update(id, data);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.archiveVersion = async (request, response, next) => {
            try {
                const { id, language } = request.params;
                const { status } = request.query;
                const result = await this.eventService.archiveVersion(id, language, status);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.archiveAll = async (request, response, next) => {
            try {
                const { id } = request.params;
                const { status } = request.query;
                const result = await this.eventService.archiveAll(id, status);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.initialiseRoutes();
    }
    initialiseRoutes() {
        this.router.get(`${this.path}`, validateApiKey_middleware_1.default, queries_validation_middleware_1.default, this.getEvents);
        this.router.get(`${this.path}/:id`, validateApiKey_middleware_1.default, queries_validation_middleware_1.default, cache_middleware_1.validateCache, this.getEventById);
        this.router.get(`${this.path}/opposite/:language/:slug`, validateApiKey_middleware_1.default, cache_middleware_1.validateCache, this.getSlugBySlug);
        this.router.get(`${this.path}/:language/:slug`, validateApiKey_middleware_1.default, queries_validation_middleware_1.default, cache_middleware_1.validateCache, this.getEventBySlug);
        this.router.post(`${this.path}`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), cache_middleware_1.invalidateCache, this.create);
        this.router.put(`${this.path}/:id`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), mongoId_validation_middleware_1.default, cache_middleware_1.invalidateCache, this.update);
        this.router.delete(`${this.path}/:eventId`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), this.deleteEvent);
        this.router.put(`${this.path}/archiveall/:id`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), mongoId_validation_middleware_1.default, cache_middleware_1.invalidateCache, this.archiveAll);
        this.router.put(`${this.path}/:id/:language`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), mongoId_validation_middleware_1.default, cache_middleware_1.invalidateCache, this.archiveVersion);
    }
}
exports.default = EventController;
//# sourceMappingURL=event.controller.js.map