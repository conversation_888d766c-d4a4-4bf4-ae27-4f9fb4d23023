"use client";

import Link from "next/link";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import { useTheme, useMediaQuery, Grid } from "@mui/material";
import moment from "moment";
import "moment/locale/fr";
import countries from "i18n-iso-countries";
import enLocale from "i18n-iso-countries/langs/en.json";
import { useTranslation } from "react-i18next";

import CustomButton from "@/components/ui/CustomButton";
import {
  findIndustryClassname,
  findIndustryColoredIcon,
  findIndustryLabel,
  findIndustryLink,
  industryExists,
} from "@/utils/functions";
import GTM from "../../../../../components/GTM";
import DialogModal from "@/features/user/component/updateProfile/experience/DialogModal";
import SvgTime from "@/assets/images/icons/time.svg";
import SvgDeadline from "@/assets/images/icons/deadline.svg";
import SvgFileText from "@/assets/images/icons/FileText.svg";
import SvgLocalisationIcon from "@/assets/images/icons/locationIcon.svg";
import SvgBookmark from "@/assets/images/icons/bookmark.svg";
import useCurrentUser from "../../../../auth/hooks/currentUser.hooks";
import { Role } from "@/utils/constants";
import { useAddToFavourite } from "../../../hooks/opportunity.hooks";
import {
  capitalizeFirstLetter,
  truncateByCharacter,
} from "../../../../../utils/functions";
import { websiteRoutesList } from "@/helpers/routesList";
import { axiosGetJson } from "@/config/axios";
import CustomTooltip from "../../../../../components/ui/CustomTooltip";

function OpportunityItemByList({ opportunity, language }) {
  const { t, i18n } = useTranslation();
  countries.registerLocale(enLocale);
  const theme = useTheme();
  const { user } = useCurrentUser();
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [jobsTodelete, setJobsTodelete] = useState(false);

  const handeleDeleteconfirmation = () => {
    setJobsTodelete(opportunity?._id);
    setShowDeleteConfirmation(true);
  };

  const handlecanceldelete = () => {
    setShowDeleteConfirmation(false);
  };

  const deleteOpportunityFromShortlist = async (opportunityId) => {
    try {
      await axiosGetJson.delete(`/favourite/${opportunityId}`, {
        data: { type: "opportunity" },
      });
      setIsSaved(false);
    } catch (error) {}
  };

  const handleToggleOpportunity = async () => {
    try {
      await deleteOpportunityFromShortlist(jobsTodelete);
    } catch (error) {}
    setShowDeleteConfirmation(false);
  };

  moment.locale(i18n.language || "en");

  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const useAddTofavouriteHook = useAddToFavourite();
  const [isSaved, setIsSaved] = useState(false);

  useEffect(() => {
    const checkIfSaved = async () => {
      if (opportunity?._id) {
        try {
          const response = await axiosGetJson.get(
            `/favourite/is-saved/${opportunity?._id}`
          );

          setIsSaved(response.data);
        } catch (error) {
          if (process.env.NODE_ENV === "dev")
            console.error("Failed to check if article is saved:", error);
        }
      }
    };

    checkIfSaved();
  }, [opportunity?._id]);

  const handleSaveClick = () => {
    if (!user) {
      toast.warning("Login or create account to save opportunity.");
    } else {
      useAddTofavouriteHook.mutate(
        {
          id: opportunity?._id,
          title: opportunity?.versions[language]?.title,
          typeOfFavourite: "opportunity",
        },
        {
          onSuccess: () => {
            setIsSaved(true);
          },
        }
      );
    }
  };

  const handleClick = (link) => {
    window.dataLayer = window.dataLayer || [];
    window.dataLayer.push({
      event: `opportunity_view`,
      button_id: "my_button",
    });
    setTimeout(() => {
      window.location.href = link;
    }, 300);
  };

  const summaryLabel = t("createOpportunity:summary");
  const regex = new RegExp(
    `<strong>${summaryLabel}:<\/strong><br>([\\s\\S]*?)(?=<br>)`,
    "i"
  );

  const summary =
    opportunity?.versions[i18n.language]?.jobDescription
      ?.match(regex)?.[1]
      ?.trim() ?? "";

  return (
    <div
      className="button-pointer"
      onClick={() => {
        if (isMobile)
          handleClick(
            `/${websiteRoutesList.opportunities.route}/${opportunity?.versions[language]?.url}`
          );
      }}
    >
      <GTM />
      <Grid
        key={opportunity?._id}
        className="container opportunity-item"
        container
        spacing={0}
      >
        <Grid container spacing={0} className="flex-item row">
          <CustomTooltip
            title={
              opportunity?.versions?.[language]?.title || opportunity?.title
            }
            child={
              <a
                href={
                  language === "en"
                    ? `/${websiteRoutesList.opportunities.route}/${opportunity?.versions[language]?.url}`
                    : `/${language}/${websiteRoutesList.opportunities.route}/${opportunity?.versions[language]?.url}`
                }
                className={"btn p-0 job-title"}
              >
                {truncateByCharacter(
                  opportunity?.versions?.[language]?.title ||
                    opportunity?.title,
                  80
                )}
              </a>
            }
          />
          <div className="flex-item">
            {industryExists(opportunity?.industry) ? (
              <Link
                style={{ textDecoration: "none" }}
                href={`/${
                  websiteRoutesList.jobCategory.route
                }/${findIndustryLink(opportunity?.industry)}`}
              >
                <p
                  className={`job-industry border ${findIndustryClassname(
                    opportunity?.industry
                  )}`}
                >
                  {findIndustryColoredIcon(opportunity?.industry)}{" "}
                  {findIndustryLabel(opportunity?.industry)}
                </p>
              </Link>
            ) : null}
            {!isMobile && (!user || user?.roles?.includes(Role.CANDIDATE)) ? (
              <CustomButton
                icon={
                  <SvgBookmark
                    className={`${isSaved ? "btn-filled-yellow" : ""}`}
                  />
                }
                onClick={isSaved ? handeleDeleteconfirmation : handleSaveClick}
                className={"btn btn-ghost bookmark"}
              />
            ) : (
              <div></div>
            )}
            {isMobile && (!user || user?.roles?.includes(Role.CANDIDATE)) && (
              <CustomButton
                icon={
                  <SvgBookmark
                    className={`${isSaved ? "btn-filled-yellow " : ""}`}
                  />
                }
                className={"btn btn-ghost bookmark"}
              />
            )}
          </div>
        </Grid>
        <Grid container spacing={0} className="flex-item margin-section-item">
          <p className="job-ref">Ref: {opportunity?.reference}</p>
          <a
            className="location"
            href={`/${
              websiteRoutesList.jobLocation.route
            }/${opportunity?.country.toLowerCase()}`}
          >
            <SvgLocalisationIcon />
            <p className="location-text">{opportunity?.country}</p>
          </a>
        </Grid>
        <Grid container spacing={0} className="flex-item margin-section-item">
          <div
            className="job-description"
            dangerouslySetInnerHTML={{
              __html: summary,
            }}
          />
        </Grid>
        <Grid container spacing={0} className="flex-item row">
          <div className="flex-apply">
            <div className="job-contrat-time">
              <p className="job-contract">
                <SvgFileText />
                {opportunity?.contractType || "Agreement"}
              </p>
              <p className="job-deadline">
                <SvgDeadline />
                {opportunity?.dateOfExpiration
                  ? capitalizeFirstLetter(
                      moment(opportunity?.dateOfExpiration).format(
                        "DD MMMM YYYY"
                      )
                    )
                  : "N/A"}
              </p>
              <p className="job-time">
                <SvgTime />
                {opportunity?.versions[language]?.createdAt
                  ? capitalizeFirstLetter(
                      moment(opportunity?.versions[language]?.createdAt).format(
                        "DD MMMM YYYY"
                      )
                    )
                  : "N/A"}
              </p>
            </div>
          </div>
          <div className="item-btns">
            <CustomButton
              text={t("global:applyNow")}
              className={"btn btn-search btn-filled apply"}
              onClick={() =>
                handleClick(
                  language === "en"
                    ? `/${websiteRoutesList.opportunities.route}/${opportunity?.versions[language]?.url}`
                    : `/${language}/${websiteRoutesList.opportunities.route}/${opportunity?.versions[language]?.url}`
                )
              }
            />
          </div>
        </Grid>
      </Grid>
      <DialogModal
        open={showDeleteConfirmation}
        message={t("messages:supprimeropportunityfavoris")}
        onClose={handlecanceldelete}
        onConfirm={handleToggleOpportunity}
      />
    </div>
  );
}

export default OpportunityItemByList;
