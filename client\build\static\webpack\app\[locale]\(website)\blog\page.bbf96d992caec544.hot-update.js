"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/blog/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/ArticlesList.jsx":
/*!*******************************************************!*\
  !*** ./src/features/blog/components/ArticlesList.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ArticlesList; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid,InputAdornment,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid,InputAdornment,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid,InputAdornment,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid,InputAdornment,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/InputAdornment/InputAdornment.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var embla_carousel_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! embla-carousel-react */ \"(app-pages-browser)/./node_modules/embla-carousel-react/esm/embla-carousel-react.esm.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* harmony import */ var _utils_urls__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/urls */ \"(app-pages-browser)/./src/utils/urls.js\");\n/* harmony import */ var _config_axios__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/config/axios */ \"(app-pages-browser)/./src/config/axios.js\");\n/* harmony import */ var _components_ui_CustomPagination__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/CustomPagination */ \"(app-pages-browser)/./src/components/ui/CustomPagination.jsx\");\n/* harmony import */ var _assets_images_icons_searchIcon_svg__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/assets/images/icons/searchIcon.svg */ \"(app-pages-browser)/./src/assets/images/icons/searchIcon.svg\");\n/* harmony import */ var _assets_images_icons_refreshIcon_svg__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/assets/images/icons/refreshIcon.svg */ \"(app-pages-browser)/./src/assets/images/icons/refreshIcon.svg\");\n/* harmony import */ var _BlogItem__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./BlogItem */ \"(app-pages-browser)/./src/features/blog/components/BlogItem.jsx\");\n/* harmony import */ var _guides_components_GuideItem__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../guides/components/GuideItem */ \"(app-pages-browser)/./src/features/guides/components/GuideItem.jsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var embla_carousel_autoplay__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! embla-carousel-autoplay */ \"(app-pages-browser)/./node_modules/embla-carousel-autoplay/esm/embla-carousel-autoplay.esm.js\");\n/* harmony import */ var _helpers_routesList__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/helpers/routesList */ \"(app-pages-browser)/./src/helpers/routesList.js\");\n/* harmony import */ var html_to_text__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! html-to-text */ \"(app-pages-browser)/./node_modules/html-to-text/lib/html-to-text.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// import SvgCategoriesIcon from \"../../../assets/images/icons/categoriesIcon.svg\";\n\n\n\n\nfunction ArticlesList(param) {\n    let { data, language, searchParams, isCategory } = param;\n    _s();\n    const [pageNumber, setPageNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(parseInt(searchParams?.pageNumber || 1));\n    const [keyword, setkeyword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(searchParams?.keyword);\n    const searchQueryParams = new URLSearchParams();\n    const searchParamsVar = (0,next_navigation__WEBPACK_IMPORTED_MODULE_11__.useSearchParams)();\n    const searchParamsContent = searchParamsVar.toString();\n    const [slides, setSlides] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const gradients = [\n        \"linear-gradient(to bottom, rgba(214, 155, 25, 0), rgba(214, 155, 25, 0.6))\",\n        \"linear-gradient(to bottom, rgba(116, 55, 148, 0), rgba(116, 55, 148, 0.6))\",\n        \"linear-gradient(to bottom, rgba(0, 153, 102, 0), rgba(0, 153, 102, 0.6))\",\n        \"linear-gradient(to bottom, rgba(204, 50, 51, 0), rgba(204, 50, 51, 0.6))\"\n    ];\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_11__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_11__.usePathname)();\n    const truncateDescription = (title)=>{\n        title = (0,html_to_text__WEBPACK_IMPORTED_MODULE_13__.htmlToText)(title, {\n            wordwrap: false\n        });\n        const words = title.split(\" \");\n        if (words?.length >= 20) {\n            return words.slice(0, 20).join(\" \");\n        } else {\n            return title;\n        }\n    };\n    const updateUrlWithParams = ()=>{\n        if (keyword) searchQueryParams.set(\"keyword\", keyword);\n        if (pageNumber) searchQueryParams.set(\"pageNumber\", 1);\n        router.push(`${pathname}?${searchQueryParams.toString()}`);\n    };\n    const resetSearch = ()=>{\n        setkeyword(\"\");\n        setPageNumber(1);\n        searchQueryParams.set(\"pageNumber\", 1);\n        router.push(`${pathname}?${searchQueryParams.toString()}`);\n    };\n    const handlePageChange = (page)=>{\n        setPageNumber(page);\n    };\n    let lastColor = \"\";\n    const getRandomGradient = ()=>{\n        // return gradients[Math.floor(Math.random() * gradients.length)];\n        let randomColor;\n        do {\n            randomColor = gradients[Math.floor(Math.random() * gradients.length)];\n        }while (randomColor === lastColor);\n        lastColor = randomColor;\n        return randomColor;\n    };\n    const fetchCategories = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _config_axios__WEBPACK_IMPORTED_MODULE_5__.axiosGetJsonSSR.get(`${language}${_utils_urls__WEBPACK_IMPORTED_MODULE_4__.API_URLS.category}`);\n            const categoriesData = response.data.categoriesData.map((category)=>{\n                const firstVersion = category?.versionscategory[0];\n                return {\n                    img: firstVersion?.image ? `${\"http://localhost:4000/api/v1\"}/files/${firstVersion?.image}` : null,\n                    link: `${firstVersion?.url}`,\n                    name: firstVersion?.name || \"N/A\"\n                };\n            });\n            setSlides(categoriesData);\n        } catch (error) {\n            setError(t(\"messages:fetchCategoriesFailed\"));\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchCategories();\n    }, []);\n    const OPTIONS = {\n        loop: true,\n        align: \"start\"\n    };\n    const [emblaRef] = (0,embla_carousel_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(OPTIONS, [\n        (0,embla_carousel_autoplay__WEBPACK_IMPORTED_MODULE_16__[\"default\"])({\n            playOnInit: true,\n            delay: 1000,\n            stopOnMouseEnter: true\n        })\n    ]);\n    const OPTIONS_Right = {\n        loop: true,\n        align: \"start\",\n        direction: \"rtl\"\n    };\n    const [emblaRefRight] = (0,embla_carousel_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(OPTIONS_Right, [\n        (0,embla_carousel_autoplay__WEBPACK_IMPORTED_MODULE_16__[\"default\"])({\n            playOnInit: true,\n            delay: 1000,\n            stopOnMouseEnter: true\n        })\n    ]);\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\") {\n            updateUrlWithParams();\n        }\n    };\n    console.log(\"data\", data);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"blogs-page\",\n        children: [\n            !isCategory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"search-bar-blogs\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    className: \"custom-max-width\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        className: \"container\",\n                        container: true,\n                        spacing: 0,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 8,\n                                container: true,\n                                spacing: 0,\n                                className: \"filter-inputs\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 12,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"input-pentabell\",\n                                        autoComplete: \"off\",\n                                        slotProps: {\n                                            input: {\n                                                startAdornment: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    position: \"start\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_searchIcon_svg__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 29\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 27\n                                                }, void 0)\n                                            }\n                                        },\n                                        variant: \"standard\",\n                                        type: \"text\",\n                                        value: keyword,\n                                        onChange: (e)=>setkeyword(e.target.value),\n                                        onKeyDown: handleKeyDown,\n                                        placeholder: \"Keywords\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                lineNumber: 146,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 4,\n                                className: \"btns-filter\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_refreshIcon_svg__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 25\n                                        }, void 0),\n                                        className: \"btn btn-outlined\",\n                                        onClick: resetSearch\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        text: \"Search\",\n                                        onClick: updateUrlWithParams,\n                                        className: \"btn btn-filled full-width\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                lineNumber: 176,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                        lineNumber: 145,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                    lineNumber: 144,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                lineNumber: 143,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                className: \"custom-max-width\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"heading-h1\",\n                        children: \"Browse by topic\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"embla\",\n                        id: \"blog__categories__slider\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"embla__viewport\",\n                            ref: emblaRef,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"embla__container categories-list\",\n                                children: slides.map((category, index)=>category?.link && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"category-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: `${language === \"en\" ? `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.blog.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.category.route}/${category.link}` : `/${language}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.blog.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.category.route}/${category.link}`}/`,\n                                            children: category.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, category.link, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 21\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                        lineNumber: 194,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"embla\",\n                        style: {\n                            marginBottom: \"20px\"\n                        },\n                        id: \"blog__categories__slider\",\n                        dir: \"rtl\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"embla__viewport\",\n                            ref: emblaRefRight,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"embla__container categories-list\",\n                                children: slides.map((category, index)=>category?.link && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"category-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: `${language === \"en\" ? `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.blog.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.category.route}/${category.link}` : `/${language}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.blog.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.category.route}/${category.link}`}/`,\n                                            children: category?.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, category.link, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 21\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                className: \"custom-max-width\",\n                children: [\n                    data?.firstArticle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"first-blog\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"last-blog\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        style: {\n                                            textDecoration: \"none\"\n                                        },\n                                        locale: language === \"en\" ? \"en\" : \"fr\",\n                                        href: `${language === \"en\" ? `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.blog.route}/${data?.firstArticle?.versions[0]?.url}` : `/${language}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.blog.route}/${data?.firstArticle?.versions[0]?.url}`}/`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"title featured-title\",\n                                            children: data?.firstArticle?.versions[0]?.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"description\",\n                                        children: data?.firstArticle?.versions[0]?.description ? data?.firstArticle?.versions[0]?.description : truncateDescription(data?.firstArticle?.versions[0]?.content)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        text: t(\"global:readMore\"),\n                                        className: \"btn btn-outlined\",\n                                        link: `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.blog.route}/${data?.firstArticle?.versions[0]?.url}`,\n                                        aHref: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                style: {\n                                    textDecoration: \"none\"\n                                },\n                                locale: language === \"en\" ? \"en\" : \"fr\",\n                                href: `${language === \"en\" ? `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.blog.route}/${data?.firstArticle?.versions[0]?.url}` : `/${language}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_12__.websiteRoutesList.blog.route}/${data?.firstArticle?.versions[0]?.url}`}/`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"blog-img-section\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"img-section\",\n                                        style: {\n                                            backgroundImage: `url(${\"http://localhost:4000/api/v1\"}/files/${data?.firstArticle?.versions[0]?.image})`\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                lineNumber: 272,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                        lineNumber: 244,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_InputAdornment_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        container: true,\n                        rowSpacing: 0,\n                        columnSpacing: 2,\n                        children: data?.items?.map((item, index)=>{\n                            if (item.type === \"guide\") {\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_guides_components_GuideItem__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    guideData: item,\n                                    language: language\n                                }, `guide-${index}`, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 17\n                                }, this);\n                            }\n                            if (item.type === \"article\") {\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BlogItem__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    blogData: item,\n                                    language: language\n                                }, `article-${index}`, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 17\n                                }, this);\n                            }\n                            return null;\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                        lineNumber: 291,\n                        columnNumber: 9\n                    }, this),\n                    data?.totalItems > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomPagination__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        type: \"ssr\",\n                        totalPages: Math.ceil(data?.totalItems / data?.pageSize),\n                        currentPage: pageNumber,\n                        onPageChange: handlePageChange,\n                        searchQueryParams: searchParamsContent\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                        lineNumber: 318,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n                lineNumber: 242,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\ArticlesList.jsx\",\n        lineNumber: 141,\n        columnNumber: 5\n    }, this);\n}\n_s(ArticlesList, \"kCCrGwijBur6MosTsACv7Pj+5/c=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_11__.useSearchParams,\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        next_navigation__WEBPACK_IMPORTED_MODULE_11__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_11__.usePathname,\n        embla_carousel_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        embla_carousel_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n    ];\n});\n_c = ArticlesList;\nvar _c;\n$RefreshReg$(_c, \"ArticlesList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/ArticlesList.jsx\n"));

/***/ })

});