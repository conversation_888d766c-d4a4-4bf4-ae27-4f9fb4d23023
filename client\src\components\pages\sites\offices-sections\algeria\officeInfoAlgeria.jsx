import { Container } from "@mui/material";
import SvgUsers from "@/assets/images/icons/yellow/Users.svg";
import Svglanguage from "@/assets/images/icons/yellow/language.svg";
import Svggdp from "@/assets/images/icons/yellow/gdp.svg";
import Svgcurrency from "@/assets/images/icons/yellow/currency.svg";
import SvgcapitalCity from "@/assets/images/icons/yellow/capitalCity.svg";
import Svggross from "@/assets/images/icons/yellow/gross.svg";
import OfficeInfo from "@/components/ui/OfficeInfo";

function OfficeInfoAlgeria({ t }) {
  const data = [
    {
      icon: <SvgUsers />,
      titleKey: "Algeria:officeInfoTN:title1",
      descriptionKey: "Algeria:officeInfoTN:description1"
    },
    {
      icon: <Svglanguage />,
      titleKey: "Algeria:officeInfoTN:title2",
      descriptionKey: "Algeria:officeInfoTN:description2"
    },
    {
      icon: <Svggdp />,
      titleKey: "Algeria:officeInfoTN:title3",
      descriptionKey: "Algeria:officeInfoTN:description3"
    },
    {
      icon: <Svgcurrency />,
      titleKey: "Algeria:officeInfoTN:title4",
      descriptionKey: "Algeria:officeInfoTN:description4"
    },
    {
      icon: <SvgcapitalCity />,
      titleKey: "Algeria:officeInfoTN:title5",
      descriptionKey: "Algeria:officeInfoTN:description5"
    },
    {
      icon: <Svggross />,
      titleKey: "Algeria:officeInfoTN:title6",
      descriptionKey: "Algeria:officeInfoTN:description6"
    }
  ];
  return <OfficeInfo data={data} t={t} />;
}

export default OfficeInfoAlgeria;
