import React from 'react';
import { useTranslation } from 'react-i18next';
import userIcon from "@/assets/images/icon-eye.png";
import profileviews from "@/assets/images/profileviews.png";
import CustomButton from '../../../components/ui/CustomButton';
import { baseUrlFrontoffice } from '@/helpers/routesList';

function ProfileViews({applicationData}) {
    const { t } = useTranslation();

    return (
        <div className="progress-container">
            <p className="profile-title"> {t("HomeDashboard:profileviews")}</p>
            <div className="gauge-container">
                <img src={profileviews.src} alt="Profile View Icon" className="profile-background" loading="lazy" />
                <div className="view-info">
                    <div className="view-icon">
                        <img src={userIcon.src} className="user-icon-progress" alt="User Icon" loading="lazy" />
                    </div>  
                     <span className="view-percentage">{applicationData}</span>
                </div>
            </div>

            <div className="upgrade-button">
                <CustomButton
                    className="btn btn-homepage btn-filled"
                    text= {t("HomeDashboard:Details")}
                    link={`/${baseUrlFrontoffice.baseURL.route}/my-applications`}
                />
            </div>
        </div>
    );
}

export default ProfileViews;
