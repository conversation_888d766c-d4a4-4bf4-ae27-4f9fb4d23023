import { Container } from "@mui/material";
import SvgUsers from "@/assets/images/icons/yellow/Users.svg";
import Svglanguage from "@/assets/images/icons/yellow/language.svg";
import Svggdp from "@/assets/images/icons/yellow/gdp.svg";
import Svgcurrency from "@/assets/images/icons/yellow/currency.svg";
import SvgcapitalCity from "@/assets/images/icons/yellow/capitalCity.svg";
import Svggross from "@/assets/images/icons/yellow/gross.svg";
import OfficeInfo from "@/components/ui/OfficeInfo";

function OfficeInfoLibya({ t }) {
  const data = [
    {
      icon: <SvgUsers />,
      titleKey: "libya:officeInfoLibya:title1",
      descriptionKey: "libya:officeInfoLibya:description1"
    },
    {
      icon: <Svglanguage />,
      titleKey: "libya:officeInfoLibya:title2",
      descriptionKey: "libya:officeInfoLibya:description2"
    },
    {
      icon: <Svggdp />,
      titleKey: "libya:officeInfoLibya:title3",
      descriptionKey: "libya:officeInfoLibya:description3"
    },
    {
      icon: <Svgcurrency />,
      titleKey: "libya:officeInfoLibya:title4",
      descriptionKey: "libya:officeInfoLibya:description4"
    },
    {
      icon: <SvgcapitalCity />,
      titleKey: "libya:officeInfoLibya:title5",
      descriptionKey: "libya:officeInfoLibya:description5"
    },
    {
      icon: <Svggross />,
      titleKey: "libya:officeInfoLibya:title6",
      descriptionKey: "libya:officeInfoLibya:description6"
    }
  ];

  return <OfficeInfo data={data} t={t} />;
}


export default OfficeInfoLibya;
