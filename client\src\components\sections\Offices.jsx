"use client";
import { Container, Grid } from "@mui/material";
import CustomButton from "@/components/ui/CustomButton";
import SvgArrow from "../../assets/images/icons/arrow.svg";
import officesImg from "../../assets/images/website/offices10.png";

import people from "../../assets/images/website/people.jpg";
import Image from "next/image";
import { useTranslation } from "react-i18next";
import { websiteRoutesList } from "@/helpers/routesList";
function Offices() {
  const { t } = useTranslation();
  return (
    <div id="offices-section">
      <Container id="our-location-section" className="custom-max-width">
        <Grid className="container align-items-center" container spacing={2}>
          <Grid item xs={12} sm={6}>
            <h2 className="heading-h1 text-white">
              {t("aboutUs:offices:office:title")}
            </h2>
            <p className="sub-heading">
              {t("aboutUs:offices:office:description")}
            </p>
            <CustomButton
              text={t("aboutUs:offices:office:view")}
              className={"btn btn-ghost white"}
              icon={<SvgArrow />}
              link={`/${websiteRoutesList.contact.route}`}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <img
              alt={t("aboutUs:offices:office:altImg")}
              src={officesImg.src}
              className="offices-img"
              loading="lazy"
            />
          </Grid>
        </Grid>
      </Container>

      <Container id="our-culture-section" className="custom-max-width">
        <Grid className="container" container columnSpacing={2}>
          <Grid item xs={12} sm={5}>
            <div className="text-section banking-color">
              <h2 className="heading-h1 text-white">
                {t("aboutUs:offices:people:title")}
              </h2>
              <p className="sub-heading">
                {t("aboutUs:offices:people:description")}{" "}
              </p>
              <CustomButton
                text={t("aboutUs:offices:people:joinUs")}
                className={"btn btn btn-filled full-width"}
                link={`/${websiteRoutesList.joinUs.route}`}
              />
            </div>
          </Grid>
          <Grid item xs={12} sm={7} className="text-section-img">
            <img
              alt={t("aboutUs:offices:people:altImg")}
              src={people.src}
              loading="lazy"
            />
          </Grid>
        </Grid>
      </Container>
    </div>
  );
}

export default Offices;
