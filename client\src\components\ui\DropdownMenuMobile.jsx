import { useState } from "react";
import {
  Collapse,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
} from "@mui/material";

import SvgArrowDown from "../../assets/images/icons/arrowDown.svg";
import SvgArrowUp from "../../assets/images/icons/arrowUp.svg";
import Link from "next/link";
import { generateLocalizedSlug } from "@/utils/functions";
import Image from "next/image";
import { useTranslation } from "react-i18next";
import { usePathname } from "next/navigation";

const DropdownMenuMobile = ({
  buttonLabel,
  buttonHref,
  menuItems,
  locale,
  withFlag,
  selectedFlag,
  subMenu,
}) => {
  const [openSubMenu, setOpenSubMenu] = useState(false);

  const { t } = useTranslation();
  const pathname = usePathname();

  const handleClick = (event, item) => {
    event.stopPropagation(); // Prevent event bubbling
    if (item.onClick) {
      item.onClick(); // Call the onClick handler if provided
    } else {
      if (item.subItems == undefined) setOpenSubMenu(!openSubMenu);
    }
  };

  return (
    <>
      <ListItemButton
        className={subMenu ? "sub-menu dropdown-menu" : "dropdown-menu"}
        onClick={(e) => handleClick(e, { href: buttonHref })}
      >
        <Link
          href={generateLocalizedSlug(locale, buttonHref)}
          locale={locale}
          className="menu-item"
        >
          {withFlag ? (
            <img
              className="flag-lang"
              src={selectedFlag?.src}
              width={26}
              height={22}
              loading="lazy"
            />
          ) : (
            <ListItemText primary={buttonLabel} />
          )}
        </Link>
        {menuItems && menuItems.length > 0 && (
          <div
            className="submenu-toggle"
            onClick={(e) => {
              e.stopPropagation();
              setOpenSubMenu(!openSubMenu);
            }}
          >
            {openSubMenu ? <SvgArrowDown /> : <SvgArrowUp />}
          </div>
        )}
        {/* <div onClick={(e) => { e.stopPropagation(); setOpenSubMenu(!openSubMenu); }}>
          {openSubMenu ? <SvgArrowDown /> : <SvgArrowUp />}
        </div> */}
      </ListItemButton>
      <Collapse in={openSubMenu} timeout="auto" unmountOnExit>
        <List component="div" disablePadding>
          {menuItems.map((item, index) => (
            <ListItem
              className={item.subMenu ? "sub-menu menu-item" : "menu-item"}
              key={index}
              sx={{ pl: 4 }}
              onClick={(e) => handleClick(e, item)}
            >
              {item.subItems ? (
                // Recursive DropdownMenuMobile for submenus
                <DropdownMenuMobile
                  locale={locale}
                  buttonLabel={item?.i18nName ? t(item?.i18nName) : item.name}
                  buttonHref={item.route}
                  menuItems={item.subItems}
                  withFlag={withFlag}
                  selectedFlag={item.flag}
                  subMenu={true}
                />
              ) : (
                // Simple link item
                <ListItemButton
                  href={
                    item.route
                      ? generateLocalizedSlug(locale, item.route).replace("/en/", "/")
                      : null
                  }
                  onClick={(e) => handleClick(e, item)}
                  className={"dropdown-item-link"}
                >
                  {item.icon && item.icon}
                  {withFlag ? (
                    <img
                      className="flag-lang"
                      src={item.flag?.src}
                      width={26}
                      height={22}
                      loading="lazy"
                    />
                  ) : item?.i18nName ? (
                    t(item?.i18nName)
                  ) : (
                    item.name
                  )}
                </ListItemButton>
              )}
            </ListItem>
          ))}
        </List>
      </Collapse>
    </>
  );
};

export default DropdownMenuMobile;
