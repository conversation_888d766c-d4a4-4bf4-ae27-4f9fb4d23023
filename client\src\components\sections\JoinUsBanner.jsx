"use client";
import { Container } from "@mui/material";
import CustomButton from "@/components/ui/CustomButton";
import team1 from "../../assets/images/team/team1.png";
import team2 from "../../assets/images/team/team2.png";

import { useTranslation } from "react-i18next";
import Image from "next/image";
import { websiteRoutesList } from "@/helpers/routesList";
import Link from "next/link";

function JoinUsBanner({ bannerImg, height, altImg }) {
  const { t } = useTranslation();
  return (
    <div
      id="join-us-banner"
      className={"center-banner"}
      style={{ backgroundImage: `url(${bannerImg.src})`, height: height }}
    >
      {altImg && (
        <img
          width={0}
          height={0}
          alt={altImg}
          src=""
          style={{ display: "none" }}
          loading="lazy"
        />
      )}
      <Container className="top-section custom-max-width">
        <h1 className="heading-h1 text-white">
          {t("joinUs:intro:title1")}
          <br />
          <span className="heading-h2 text-yellow">
            {t("joinUs:intro:title2")}
          </span>
        </h1>
        <p className="sub-heading text-white  ">
          {t("joinUs:intro:description")}
        </p>
        <Link href={`#service-page-form`} style={{textDecoration : "none"}}>
          <CustomButton
            text={t("joinUs:intro:button")}
            className={"btn btn-filled"}
          />
        </Link>
      </Container>
      <div
        className="avatars"
        style={{
          backgroundImage: `url(${team1.src})`,
        }}
      ></div>
      <div
        className="avatars last-one"
        style={{
          backgroundImage: `url(${team2.src})`,
        }}
      ></div>
    </div>
  );
}

export default JoinUsBanner;
