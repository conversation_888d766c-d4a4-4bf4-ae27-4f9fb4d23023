import { Container } from "@mui/material";
function OfficeInfo({ data, t }) {
    return (
        <Container className="custom-max-width">
            <div id="office-info-tn">
                {data.map((item, index) => (
                    <div key={index} className="info">
                        <div className="icon">{item.icon}</div>
                        <div>
                            <p className="label sub-heading text-white">{t(item.titleKey)}</p>
                            <p className="value paragraph text-white">{t(item.descriptionKey)}</p>
                        </div>
                    </div>
                ))}
            </div>
        </Container>
    );
}

export default OfficeInfo;
