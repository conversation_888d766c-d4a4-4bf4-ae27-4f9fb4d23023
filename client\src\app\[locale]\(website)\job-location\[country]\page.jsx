import OpportunityCard from "@/features/opportunity/components/opportunityFrontOffice/OpportunityCard";
import initTranslations from "@/app/i18n";
import { axiosGetJsonSSR } from "@/config/axios";
import BannerComponents from "@/components/pages/sites/sections/BannerComponents";
import banner from "@/assets/images/industries/banner/bannerOpportunities.png";

export async function generateMetadata({
  params: { locale, country },
  searchParams,
}) {
  const canonicalUrl = `https://www.pentabell.com/${
    locale !== "en" ? `${locale}/` : ""
  }job-location/${country ? `${country}/` : ``}`;
  const languages = {
    fr: `https://www.pentabell.com/fr/job-location/${
      country ? `${country}/` : ``
    }`,
    en: `https://www.pentabell.com/job-location/${
      country ? `${country}/` : ``
    }`,
    "x-default": `https://www.pentabell.com/job-location/${
      country ? `${country}/` : ``
    }`,
  };

  const { t } = await initTranslations(locale, ["aboutUs", "global"]);

  return {
    title: t("global:metaTitleOpportunity"),
    description: t("global:metaDescriptionOpportunity"),
    alternates: {
      canonical: canonicalUrl,
      languages,
    },
    robots:
      Object.keys(searchParams).length > 0
        ? "follow, noindex"
        : "follow, index, max-snippet:-1, max-image-preview:large",
  };
}

export async function page({ searchParams, params }) {
  const { t } = await initTranslations(params.locale, [
    "opportunities",
    "global",
  ]);

  const countryName = decodeURIComponent(params.country)
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");

  const res = await axiosGetJsonSSR.get("/opportunities", {
    params: {
      pageSize: 10,
      pageNumber: searchParams.pageNumber,
      country: countryName,
      keyWord: searchParams.keyWord || "",
      visibility: "Public",
      industry: searchParams.industry || "",
      language: params.locale,
      contractType: searchParams.contractType || "",
      jobDescriptionLanguages: searchParams.jobDescriptionLanguages,
      opportunityType:
        searchParams.opportunityType === "In House"
          ? searchParams.opportunityType
          : "",
      exclude: searchParams.opportunityType !== "In House" && "true",
      levelOfExperience: searchParams.levelOfExperience | "",
    },
  });

  const initialOpportunities = res.data;

  return (
    <div id="opportunities-page">
      <BannerComponents
        bannerImg={banner}
        height={"70vh"}
        title={
          t("global:titleOpportunityCountry") +
          decodeURIComponent(params.country)
            .split(" ")
            .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
            .join(" ")
        }
        altImg={t("global:altOpportunity")}
      />
      <OpportunityCard
        jobLocation={true}
        countrySlug={params.country}
        countryName={countryName}
        language={params.locale}
        initialOpportunities={initialOpportunities}
        searchParams={searchParams}
        typeCategory
      />
    </div>
  );
}

export default page;
