import React from "react";
import { <PERSON><PERSON><PERSON> } from "@mui/x-charts/PieChart";
import NoData from "@/assets/images/icons/noDataPie.svg";
import { useTranslation } from "react-i18next";
const CustomPieChart = ({ chart, donuts }) => {
  const { t } = useTranslation();
  const hasData = chart.dataset?.some((item) => item["value"] > 0);
  const hasntData = chart.dataset?.length === 0;

  return (
    <div style={{ display: "flex" }}>
      {hasData ? (
        <PieChart
          series={[
            {
              data: chart.dataset,
              arcLabel: (item) =>
                item.value > 0 ? `${t(`statsDash:${item.value}`)}` : "",
              innerRadius: donuts ? 50 : 90,
              outerRadius: donuts ? 90 : 0,
              paddingAngle: 1,
              cornerRadius: 4,

              highlightScope: {
                fade: "global",
                highlight: "item",
              },
              faded: {
                innerRadius: donuts ? 30 : 70,
                additionalRadius: donuts ? -30 : -20,
                color: "gray",
              },
            },
          ]}
          slotProps={{
            legend: { hidden: true },
            // legend: {
            //   direction: 'column',
            //   position: { vertical: 'bottom', horizontal: 'middle' },
            // },
          }}
          width={300}
          height={200}
          colors={chart.colors}
          sx={{ marginLeft: "30%" }}
        />
      ) : (
        (!hasData || hasntData) && <NoData />
      )}
    </div>
  );
};

export default CustomPieChart;
