"use client";
import { Container, Grid, useTheme } from "@mui/material";
import InfoSection from '@/components/ui/InfoSection';
import ProcessHtml from "./ProcessHtml";
import EventsCard from "./EventsCard";
import image1 from "@/assets/images/StrategicMeetingMorocco/image1.png";
import image2 from "@/assets/images/StrategicMeetingMorocco/image2.png";
import image3 from "@/assets/images/StrategicMeetingMorocco/image3.png";
import image4 from "@/assets/images/StrategicMeetingMorocco/image4.png";
import image5 from "@/assets/images/StrategicMeetingMorocco/image5.png";
import image6 from "@/assets/images/StrategicMeetingMorocco/image6.png";
import image7 from "@/assets/images/StrategicMeetingMorocco/image7.png";
import image8 from "@/assets/images/StrategicMeetingMorocco/image8.png";
import image9 from "@/assets/images/StrategicMeetingMorocco/image9.png";
import image10 from "@/assets/images/StrategicMeetingMorocco/image10.png";
import image11 from "@/assets/images/StrategicMeetingMorocco/image11.png";
import image12 from "@/assets/images/StrategicMeetingMorocco/image12.png";
import image13 from "@/assets/images/StrategicMeetingMorocco/image13.png";
import SvgQUote from "@/assets/images/icons/quote.svg";
import SvgQUoteRight from "@/assets/images/icons/quoteRight.svg";
import bannerQHSE from "../../assets/images/events/bannerQHSE.png";
import Pentabellsalestraining from "../../assets/images/events/pentabellsalestraining.png";
import eventafricaforum from "../../assets/images/events/eventafricaforum.png";
import SvgQuoteMobile from "@/assets/images/icons/quotemobile.svg";
import SvgQuoteMobileRight from "@/assets/images/icons/quotemobileRight.svg";
import imagemobile1 from "@/assets/images/StrategicMeetingMorocco/imagemobile1.png";
import imagemobile2 from "@/assets/images/StrategicMeetingMorocco/imagemobile2.png";
import imagemobile3 from "@/assets/images/StrategicMeetingMorocco/imagemobile3.png";
import imagemobile4 from "@/assets/images/StrategicMeetingMorocco/imagemobile4.png";
import imagemobile5 from "@/assets/images/StrategicMeetingMorocco/imagemobile5.png";
import imagemobile6 from "@/assets/images/StrategicMeetingMorocco/imagemobile6.png";
import imagemobile7 from "@/assets/images/StrategicMeetingMorocco/imagemobile7.png";
import imagemobile8 from "@/assets/images/StrategicMeetingMorocco/imagemobile8.png";
import imagemobile9 from "@/assets/images/StrategicMeetingMorocco/imagemobile9.png";
import imagemobile10 from "@/assets/images/StrategicMeetingMorocco/imagemobile10.png";
import imagemobile11 from "@/assets/images/StrategicMeetingMorocco/imagemobile11.png";
import imagemobile12 from "@/assets/images/StrategicMeetingMorocco/imagemobile12.png";
import imagemobile13 from "@/assets/images/StrategicMeetingMorocco/imagemobile13.png";
import EmblaCarousel from "@/components/embla_slider/EmblaCarousel";
import { API_URLS } from "@/utils/urls";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { axiosGetJson } from "@/config/axios";
export default function StrategicCommitteeMeetingCasablanca({ language, event, isMobileSSR }) {
    const { t } = useTranslation();
    const [events, setEvents] = useState();
    const theme = useTheme();

    const fetchEvents = async () => {
        try {
            const response = await axiosGetJson.get(API_URLS.events, {
                params: {
                    visibility: "Public",
                    pageNumber: 1,
                    pageSize: 4,
                    language: language,
                },
            });
            let { Events } = response.data;

            const eventsFiltered = Events?.filter(
                (currentEvent) => currentEvent._id !== event?._id
            ).slice(0, 3);
            setEvents(eventsFiltered);
        } catch (error) {
            console.error("Failed to fetch events:", error);
        }
    };

    useEffect(() => {
        fetchEvents();
    }, [event, language]);

    const OPTIONS = {};
    const SLIDES = [
        image1,
        image2,
        image3,
        image4,
        image5,
        image6,
        image7,
        image8,
        image9,
        image10,
        image11,
        image12,
        image13,


    ];
    const SlidesMobile = [
        imagemobile1,
        imagemobile2,
        imagemobile3,
        imagemobile4,
        imagemobile5,
        imagemobile6,
        imagemobile7,
        imagemobile8,
        imagemobile9,
        imagemobile10,
        imagemobile11,
        imagemobile12,
        imagemobile13,
    ]
    const decarbonizationSection = {
        sector: t("StrategicMeetingMo:sectorValue"),
        country: t("StrategicMeetingMo:locationValue"),
        countryConcerned: t("StrategicMeetingMo:countryConcernedValue"),
        organiser: t("StrategicMeetingMo:organiserValue"),
    };
    let infoSection;
    if (event) {
        infoSection = {
            sector: event?.versions[0]?.sector,
            country: t(`country:${event?.country?.replace(/\s+/g, "")}`),
            countryConcerned: event?.versions[0]?.countryConcerned,
            organiser: event?.versions[0]?.organiser,
        };
    }
    const eventData = [

        {
            title: t("event:events.QHSE.title"),
            eventDate: t("event:events.QHSE.eventDate"),
            postingDate: t("event:events.QHSE.postingDate"),
            exactPlace: t("event:events.QHSE.exactPlace"),
            link: "QHSE-EXPO-2025",
            type: "events",
            image: bannerQHSE,
        }, {
            title: t("event:events.Pentabellsalestraining.title"),
            eventDate: t("event:events.Pentabellsalestraining.eventDate"),
            postingDate: t("event:events.Pentabellsalestraining.postingDate"),
            exactPlace: t("event:events.Pentabellsalestraining.exactPlace"),
            link: "Pentabell-sales-training-and-workshop",
            type: "events",
            image: Pentabellsalestraining,
        }, {
            title: t("event:events.AfricaforumFrance.title"),
            eventDate: t("event:events.AfricaforumFrance.eventDate"),
            postingDate: t("event:events.AfricaforumFrance.postingDate"),
            exactPlace: t("event:events.AfricaforumFrance.exactPlace"),
            link: "Africa-France-forum-on-ecological-and-energy-transition-2025",
            type: "events",
            image: eventafricaforum,
        },
    ];
    return (
        <div id="event-page">
            {event ? (
                <div id="event-detail">
                    <div className="custom-max-width">
                        <InfoSection t={t} infos={infoSection} />{" "}
                        <ProcessHtml htmlString={event?.versions[0]?.content} />
                        <Grid
                            className="more-events-section"
                            container
                            rowSpacing={0}
                            columnSpacing={3}
                        >
                            {" "}
                            {events?.length > 0 &&
                                events?.map((event, index) => (
                                    <EventsCard
                                        key={index}
                                        eventData={event}
                                        language={language}
                                        isEvent={true}
                                    />
                                ))}{" "}
                        </Grid>
                    </div>{" "}
                </div>
            ) : (
                <div id="event-detail">
                    <Container className="custom-max-width">
                        <InfoSection t={t} infos={decarbonizationSection} />
                        <div className="details">
                            <h1 className="heading-h1">
                                {t("StrategicMeetingMo:Description:title")}

                            </h1>
                            <p className="text">
                                {t("StrategicMeetingMo:Description:description1")}
                            </p>

                            <h2 className="heading-h2 bold">
                                {t("StrategicMeetingMo:Description2:title")}

                            </h2>
                            <p className="text">
                                {t("StrategicMeetingMo:Description2:description2")}
                            </p>



                            <h3 className="heading-h2 bold">{t("StrategicMeetingMo:eventProgram2:title")}</h3>
                            <p className="text">
                                {t("StrategicMeetingMo:eventProgram2:eventProgram2")}
                            </p>


                            <ul>   <li className="text">
                                {" "}
                                {t("StrategicMeetingMo:eventProgram2:puce2")}
                            </li>
                                <li className="text">{t("StrategicMeetingMo:eventProgram2:puce1")}</li>

                                <li className="text">{t("StrategicMeetingMo:eventProgram2:puce3")}</li>
                                <li className="text">{t("StrategicMeetingMo:eventProgram2:puce4")}</li>
                                <li className="text">{t("StrategicMeetingMo:eventProgram2:puce5")}</li>
                            </ul>
                            <div class="quote-box">

                                <div class="quote-icon-top">
                                    {isMobileSSR ? <SvgQuoteMobile /> : <SvgQUote />}
                                </div>

                                <div class="quote-border top-border"></div>


                                <div class="quote-content">
                                    <p>
                                        {t("StrategicMeetingMo:quote:description")}
                                    </p>
                                    <div class="quote-author">
                                        — Wissem Zarrouk, <span>{t("StrategicMeetingMo:quote:authorrole")}</span>
                                    </div>
                                </div>


                                <div class="quote-border bottom-border"></div>


                                <div class="quote-icon-bottom">
                                    {isMobileSSR ? <SvgQuoteMobileRight /> : <SvgQUoteRight />}

                                </div>
                            </div>


                            <h2 className="heading-h2 bold">
                                {t("StrategicMeetingMo:eventProgram:eventProgram")}
                            </h2>
                            <p className="text">
                                {t("StrategicMeetingMo:eventProgram:description")}
                            </p>

                            <EmblaCarousel slides={SLIDES} options={OPTIONS} slidesMobile={SlidesMobile} />
                            <p className="heading-h1">{t("StrategicMeetingMo:moreEvents")}</p>
                        </div>
                        <Grid
                            className="more-events-section"
                            container
                            rowSpacing={0}
                            columnSpacing={3}
                        >
                            {eventData?.map((event, index) => (
                                <EventsCard
                                    key={index}
                                    eventData={event}
                                    language={language}
                                    isEvent={true}
                                />
                            ))}
                        </Grid>
                    </Container>
                </div>
            )}
        </div>
    );
}
