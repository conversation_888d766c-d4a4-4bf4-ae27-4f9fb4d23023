import { Container } from "@mui/material";

import SvgComprehensiveEmploymentIcon from "@/assets/images/services/icons/ComprehensiveEmploymentIcon.svg";
import SvgGlobalPayrollIcon from "@/assets/images/services/icons/GlobalPayrollIcon.svg";
import SvgtickIcon from "@/assets/images/services/icons/tickIcon.svg";
import SvgMultiCurrencyIcon from "@/assets/images/services/icons/MultiCurrencyIcon.svg";
import SvglegalIcon from "@/assets/images/services/icons/legalIcon.svg";
import SvgtaxManagementIcon from "@/assets/images/services/icons/taxManagementIcon.svg";

async function WhychooseFrance({ t }) {
  

  return (
    <Container id="approach-payroll-section" className="custom-max-width">
      <h2 className="heading-h1 text-center">
      {t("france:complexity:title1")} <br /> {t("france:complexity:title2")}
   
      </h2>  
      <div className="locations">
        <div className="location-item">
          <p className="label">
            {" "}
            <span>
              {" "}
              <SvgComprehensiveEmploymentIcon />{" "}
            </span>{" "}
            {t("Tunisia:complexity:dataS1:title")}{" "}
          </p>
          <p className="value paragraph">
            {t("Tunisia:complexity:dataS1:description")}
          </p>
        </div>
        <div className="location-item">
          <p className="label">
            {" "}
            <span>
              {" "}
              <SvgGlobalPayrollIcon />{" "}
            </span>{" "}
            {t("Tunisia:complexity:dataS2:title")}
          </p>
          <p className="value paragraph">
            {t("Tunisia:complexity:dataS2:description")}
          </p>
        </div>
        <div className="location-item">
          <p className="label">
            {" "}
            <span>
              <SvgtickIcon />{" "}
            </span>
            {t("Tunisia:complexity:dataS3:title")}
          </p>
          <p className="value paragraph">
            {t("Tunisia:complexity:dataS3:description")}
          </p>
        </div>

        <div className="location-item">
          <p className="label">
            {" "}
            <span>
              <SvgtaxManagementIcon />
            </span>{" "}
            {t("Tunisia:complexity:dataS4:title")}
          </p>
          <p className="value paragraph">
            {t("Tunisia:complexity:dataS4:description")}{" "}
          </p>
        </div>
        <div className="location-item">
          <p className="label">
            {" "}
            <span>
              <SvglegalIcon />
            </span>{" "}
            {t("Tunisia:complexity:dataS5:title")}
          </p>
          <p className="value paragraph">
            {t("Tunisia:complexity:dataS5:description")}
          </p>
        </div>
        <div className="location-item">
          <p className="label">
            {" "}
            <span>
              <SvgMultiCurrencyIcon />
            </span>{" "}
            {t("Tunisia:complexity:dataS6:title")}
          </p>
          <p className="value paragraph">
            {t("Tunisia:complexity:dataS6:description")}
          </p>
        </div>
      </div>
    </Container>
  );
}

export default WhychooseFrance;
