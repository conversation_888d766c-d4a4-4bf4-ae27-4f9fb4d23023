"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
const crypto = __importStar(require("crypto"));
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const files_model_1 = __importDefault(require("./files.model"));
const sharp_1 = __importDefault(require("sharp"));
const messages_1 = require("@/utils/helpers/messages");
const candidat_model_1 = __importDefault(require("../candidat/candidat.model"));
const constants_1 = require("@/utils/helpers/constants");
class FilesService {
    constructor() {
        this.File = files_model_1.default;
        this.Candidat = candidat_model_1.default;
        this.calculateHash = (filePath, algorithm) => {
            return new Promise((resolve, reject) => {
                const hash = crypto.createHash(algorithm);
                const stream = fs.createReadStream(filePath);
                stream.on('data', data => hash.update(data));
                stream.on('end', () => resolve(hash.digest('hex')));
                stream.on('error', error => reject(error));
            });
        };
    }
    async createFile(fileData) {
        return await this.File.create({
            ...fileData,
        });
    }
    async findByresource(resource, queries) {
        const { dateFrom, dateTo } = queries;
        const matchConditions = { resource };
        if (dateFrom && dateTo) {
            const from = new Date(dateFrom);
            const to = new Date(dateTo);
            matchConditions['createdAt'] = { $gte: from, $lte: to };
        }
        try {
            const results = await this.File.aggregate([
                { $match: matchConditions },
                {
                    $group: {
                        _id: {
                            year: { $year: '$createdAt' },
                            month: { $month: '$createdAt' },
                        },
                        count: { $sum: 1 },
                    },
                },
                {
                    $sort: { '_id.year': 1, '_id.month': 1 },
                },
                {
                    $project: {
                        _id: 0,
                        year: '$_id.year',
                        month: '$_id.month',
                        count: 1,
                    },
                },
            ]);
            return results;
        }
        catch (error) {
            throw new http_exception_1.default(500, messages_1.MESSAGES.GENERAL.SERVER_ERROR);
        }
    }
    async getFilesByChecksum(checksum) {
        return this.File.find({ checksum }).exec();
    }
    async findFile(filename, currentUser = null) {
        const file = await this.File.findOne({ fileName: filename }).lean();
        if (!file)
            throw new http_exception_1.default(404, messages_1.MESSAGES.FILE.NOT_FOUND);
        const resource = file.resource;
        const folder = file.folder;
        if (!resource || !folder) {
            throw new http_exception_1.default(400, 'Invalid file metadata');
        }
        const directoryPath = path.join(__dirname, '/../../../uploads/', resource, folder);
        return path.join(directoryPath, `${filename}`);
    }
    async findResume(filename, currentUser = null) {
        const file = await this.File.findOne({ fileName: filename }).lean();
        if (!file)
            throw new http_exception_1.default(404, messages_1.MESSAGES.FILE.NOT_FOUND);
        const resource = file.resource;
        const folder = file.folder;
        if (!resource || !folder) {
            throw new http_exception_1.default(400, 'Invalid file metadata');
        }
        const directoryPath = path.join(__dirname, '/../../../uploads/', resource, folder);
        if (resource === 'candidates') {
            if (!currentUser)
                throw new http_exception_1.default(403, 'Access Forbidden!');
            const ownerFileNameExist = await this.Candidat.findOne({
                user: currentUser?._id,
                cv: { $elemMatch: { fileName: filename } },
            }).lean();
            if (!ownerFileNameExist && !currentUser?.roles?.includes(constants_1.Role.ADMIN))
                throw new http_exception_1.default(403, 'Access Forbidden!');
        }
        return path.join(directoryPath, `${filename}`);
    }
    async findFileeventMaps(originalName) {
        const file = await this.File.findOne({ originalName: originalName });
        if (!file)
            throw new http_exception_1.default(404, messages_1.MESSAGES.FILE.NOT_FOUND);
        const resource = file.resource;
        const folder = file.folder;
        const directoryPath = path.join(__dirname, '/../../../uploads/', resource, folder);
        return path.join(directoryPath, file.fileName);
    }
    async findFileByName(originalName) {
        const file = await this.File.findOne({ originalName: originalName });
        if (!file)
            throw new http_exception_1.default(404, messages_1.MESSAGES.FILE.NOT_FOUND);
        const resource = file.resource;
        const folder = file.folder;
        const directoryPath = path.join(__dirname, '/../../../uploads/', resource, folder);
        return path.join(directoryPath, file.fileName);
    }
    async getFileByUUID(uuid) {
        return await this.File.findOne({ uuid });
    }
    async getFileByChecksum(checksum) {
        const file = await files_model_1.default.findOne({ checksum: checksum });
        return file;
    }
    async findFileCandidateBase64(filename) {
        const file = await this.File.findOne({ fileName: filename });
        if (!file)
            throw new http_exception_1.default(404, messages_1.MESSAGES.FILE.NOT_FOUND);
        const folder = file.folder;
        const directoryPath = path.join(__dirname, '../../../uploads/candidates', folder);
        const filePath = path.join(directoryPath, filename);
        if (fs.existsSync(filePath)) {
            try {
                const fileContent = await fs.promises.readFile(filePath);
                return fileContent.toString('base64');
            }
            catch (error) {
                throw new http_exception_1.default(500, messages_1.MESSAGES.FILE.READ_ERROR_FILE + filePath);
            }
        }
        else {
            throw new http_exception_1.default(404, messages_1.MESSAGES.FILE.NOT_FOUND + filePath);
        }
    }
    async updateFile(filename, fileData) {
        const file = await this.File.findOne({ uuid: filename.split('.')[0] });
        if (!file) {
            throw new http_exception_1.default(404, messages_1.MESSAGES.FILE.NOT_FOUND);
        }
        const resource = file.resource;
        const folder = file.folder;
        const directoryPath = path.join(__dirname, '/../../../uploads/', resource, folder);
        const filePath = path.join(directoryPath, `${file.fileName}`);
        try {
            await fs.promises.unlink(filePath);
        }
        catch (err) {
            throw new http_exception_1.default(409, messages_1.MESSAGES.FILE.DELETE_FAILED);
        }
        return await this.File.findByIdAndUpdate(file._id, fileData, { new: true });
    }
    async deleteFile(filename) {
        const file = await this.File.findOne({ uuid: filename.split('.')[0] });
        if (!file) {
            throw new http_exception_1.default(404, messages_1.MESSAGES.FILE.NOT_FOUND);
        }
        const resource = file.resource;
        const folder = file.folder;
        const uuid = file.uuid;
        const fileName = file.fileName;
        const directoryPath = path.join(__dirname, '/../../../uploads/', resource, folder);
        const filePath = path.join(directoryPath, `${uuid}.${fileName.split('.')[1]}`);
        try {
            await fs.promises.unlink(filePath);
        }
        catch (err) {
            throw new http_exception_1.default(409, messages_1.MESSAGES.FILE.DELETE_FAILED);
        }
        await this.File.deleteOne({ uuid });
        return file.toObject();
    }
    async countFiles(directoryPath, query) {
        const { startYear, endYear } = query;
        const database = query.database;
        const queryConditions = {};
        if (startYear || endYear) {
            queryConditions['folder'] = {};
            if (startYear) {
                queryConditions['folder'].$gte = startYear;
            }
            if (endYear) {
                queryConditions['folder'].$lte = endYear;
            }
        }
        if (database) {
            const countByFileType = async (type, resource) => {
                let filter = { fileName: RegExp(`.${type}$`), ...queryConditions };
                if (resource)
                    filter = { fileName: RegExp(`.${type}$`), resource: resource, ...queryConditions };
                return await this.File.countDocuments(filter);
            };
            const filesSize = async (type, resource) => {
                let filter = { fileName: RegExp(`.${type}$`), ...queryConditions };
                if (resource)
                    filter = { fileName: RegExp(`.${type}$`), resource: resource, ...queryConditions };
                const files = await this.File.find(filter);
                let size = 0;
                files.map((file) => {
                    return (size += Number(file.fileSize));
                });
                return Number(size);
            };
            const convertSize = (size) => {
                const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
                if (size === 0)
                    return '0 B';
                const i = parseInt(Math.floor(Math.log(size) / Math.log(1000)).toString());
                return Math.round(size / Math.pow(1000, i)) + ' ' + sizes[i];
            };
            return {
                pdf: {
                    total: await countByFileType('pdf'),
                    totalSize: convertSize(await filesSize('pdf')),
                },
                docx: {
                    total: await countByFileType('docx'),
                    totalSize: convertSize(await filesSize('docx')),
                },
                doc: {
                    total: await countByFileType('doc'),
                    totalSize: convertSize(await filesSize('doc')),
                },
                png: {
                    total: await countByFileType('png'),
                    totalSize: convertSize(await filesSize('png')),
                },
                jpg: {
                    total: await countByFileType('jpg'),
                    totalSize: convertSize(await filesSize('jpg')),
                },
                jpeg: {
                    total: await countByFileType('jpeg'),
                    totalSize: convertSize(await filesSize('jpeg')),
                },
                candidates: {
                    totalOfPng: await countByFileType('png', 'candidates'),
                    sizeOfPng: convertSize(await filesSize('png', 'candidates')),
                    totalOfJpg: await countByFileType('jpg', 'candidates'),
                    sizeOfJpg: convertSize(await filesSize('png', 'candidates')),
                    totalOfJpeg: await countByFileType('jpeg', 'candidates'),
                    sizeOfJpeg: convertSize(await filesSize('jpeg', 'candidates')),
                    totalFiles: (await countByFileType('png', 'candidates')) +
                        (await countByFileType('jpg', 'candidates')) +
                        (await countByFileType('jpeg', 'candidates')),
                    totalSize: convertSize((await filesSize('png', 'candidates')) + (await filesSize('jpeg', 'candidates')) + (await filesSize('jpg', 'candidates'))),
                },
                resumes: {
                    totalOfPdf: await countByFileType('pdf', 'resumes'),
                    sizeOfPdf: convertSize(await filesSize('pdf', 'resumes')),
                    totalOfDocx: await countByFileType('docx', 'resumes'),
                    sizeOfDocx: convertSize(await filesSize('docx', 'resumes')),
                    totalOfDoc: await countByFileType('doc', 'resumes'),
                    sizeOfDoc: convertSize(await filesSize('doc', 'resumes')),
                    totalFiles: (await countByFileType('pdf', 'resumes')) +
                        (await countByFileType('docx', 'resumes')) +
                        (await countByFileType('doc', 'resumes')),
                    totalSize: convertSize((await filesSize('pdf', 'resumes')) + (await filesSize('doc', 'resumes')) + (await filesSize('docx', 'resumes'))),
                },
                users: {
                    totalOfPng: await countByFileType('png', 'users'),
                    sizeOfPng: convertSize(await filesSize('png', 'users')),
                    totalOfJpg: await countByFileType('jpg', 'users'),
                    sizeOfJpg: convertSize(await filesSize('jpg', 'users')),
                    totalOfJpeg: await countByFileType('jpeg', 'users'),
                    sizeOfJpeg: convertSize(await filesSize('jpeg', 'users')),
                    totalFiles: (await countByFileType('png', 'users')) + (await countByFileType('jpg', 'users')) + (await countByFileType('jpeg', 'users')),
                    totalSize: convertSize((await filesSize('png', 'users')) + (await filesSize('jpeg', 'users')) + (await filesSize('jpg', 'users'))),
                },
            };
        }
        else {
            const folderCounts = [];
            const files = await fs.promises.readdir(directoryPath);
            for (const file of files) {
                const filePath = path.join(directoryPath, file);
                const stat = await fs.promises.stat(filePath);
                if (stat.isDirectory()) {
                    const subFolderResult = await this.countFiles(filePath);
                    const subfolderCounts = [];
                    let totalSubfolderFileCount = 0;
                    let subfolderFilenames = [];
                    for (const subfolderResult of subFolderResult) {
                        subfolderCounts.push({
                            subfolderName: subfolderResult.folderName,
                            fileCount: subfolderResult.fileCount,
                            filenames: subfolderResult.filenames,
                        });
                        totalSubfolderFileCount += subfolderResult.fileCount;
                        subfolderFilenames = subfolderFilenames.concat(subfolderResult.filenames);
                    }
                    folderCounts.push({
                        folderName: file,
                        fileCount: totalSubfolderFileCount,
                        subfolders: subfolderCounts,
                        filenames: subfolderFilenames,
                    });
                }
                else {
                    folderCounts.push({
                        folderName: file,
                        fileCount: 1,
                        subfolders: [],
                        filenames: [file],
                    });
                }
            }
            return folderCounts;
        }
    }
    async compressImage(resource, folder, quality = 80) {
        try {
            let count = 0;
            const directoryPath = path.join(__dirname, '/../../../uploads/', resource, folder);
            const files = await this.File.find({ resource, folder });
            for (let file of files) {
                const filePath = path.join(directoryPath, `${file.fileName}`);
                if (fs.existsSync(filePath) && file.fileType !== 'application/pdf') {
                    await (0, sharp_1.default)(filePath)
                        .jpeg({ quality })
                        .toFile(filePath + '.temp');
                    fs.renameSync(filePath + '.temp', filePath);
                    count++;
                    console.log(`Compressed image saved at: ${filePath}`);
                }
                else {
                    console.log(`File does not exist: ${filePath}`);
                }
            }
            return { message: `${count} ${count > 0 && count === 1 ? 'Image' : 'Images'} compressed successfully` };
        }
        catch (error) {
            throw new http_exception_1.default(500, messages_1.MESSAGES.GENERAL.SERVER_ERROR);
        }
    }
    async convertImagesToWebp(filePaths) {
        for (const filePath of filePaths) {
            const ext = path.extname(filePath).toLowerCase();
            if (!['.png', '.jpg', '.jpeg'].includes(ext)) {
                console.warn(`Skipping unsupported file: ${filePath}`);
                continue;
            }
            const outputFilePath = filePath.replace(/\.(png|jpe?g)$/i, '.webp');
            try {
                await (0, sharp_1.default)(filePath).webp({ quality: 80 }).toFile(outputFilePath);
                console.log(`Converted: ${filePath} → ${outputFilePath}`);
            }
            catch (error) {
                console.error(`Failed to convert ${filePath}:`, error);
            }
        }
    }
}
exports.default = FilesService;
//# sourceMappingURL=files.service.js.map