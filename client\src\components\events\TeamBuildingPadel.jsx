"use client";
import { Container, Grid, useTheme } from "@mui/material";
import InfoSection from '@/components/ui/InfoSection';
import ProcessHtml from "./ProcessHtml";
import EventsCard from "./EventsCard";
import image1 from "@/assets/images/TeamBuildingPadel/image1.png";
import image2 from "@/assets/images/TeamBuildingPadel/image2.png";
import image3 from "@/assets/images/TeamBuildingPadel/image3.png";
import image4 from "@/assets/images/TeamBuildingPadel/image4.png";
import image5 from "@/assets/images/TeamBuildingPadel/image5.png";
import image6 from "@/assets/images/TeamBuildingPadel/image6.png";
import image7 from "@/assets/images/TeamBuildingPadel/image7.png";
import image8 from "@/assets/images/TeamBuildingPadel/image8.png";
import image9 from "@/assets/images/TeamBuildingPadel/image9.png";
import image10 from "@/assets/images/TeamBuildingPadel/image10.png";
import image11 from "@/assets/images/TeamBuildingPadel/image11.png";
import image12 from "@/assets/images/TeamBuildingPadel/image12.png";
import image13 from "@/assets/images/TeamBuildingPadel/image13.png";
import image14 from "@/assets/images/TeamBuildingPadel/image14.png";
import image15 from "@/assets/images/TeamBuildingPadel/image15.png";
import image16 from "@/assets/images/TeamBuildingPadel/image16.png";
import image17 from "@/assets/images/TeamBuildingPadel/image17.png";
import image18 from "@/assets/images/TeamBuildingPadel/image18.png";
import image19 from "@/assets/images/TeamBuildingPadel/image19.png";
import image20 from "@/assets/images/TeamBuildingPadel/image20.png";
import image21 from "@/assets/images/TeamBuildingPadel/image21.png";
import StrategicMeetingMaroc from "../../assets/images/events/StrategicMeetingMarocImageList.png";
import bannerQHSE from "../../assets/images/events/bannerQHSE.png";
import Pentabellsalestraining from "../../assets/images/events/pentabellsalestraining.png";
import imagemobile1 from "@/assets/images/TeamBuildingPadel/imagemobile1.png";
import imagemobile2 from "@/assets/images/TeamBuildingPadel/imagemobile2.png";
import imagemobile3 from "@/assets/images/TeamBuildingPadel/imagemobile3.png";
import imagemobile4 from "@/assets/images/TeamBuildingPadel/imagemobile4.png";
import imagemobile5 from "@/assets/images/TeamBuildingPadel/imagemobile5.png";
import imagemobile7 from "@/assets/images/TeamBuildingPadel/imagemobile7.png";
import imagemobile8 from "@/assets/images/TeamBuildingPadel/imagemobile8.png";
import imagemobile9 from "@/assets/images/TeamBuildingPadel/imagemobile9.png";
import imagemobile10 from "@/assets/images/TeamBuildingPadel/imagemobile10.png";
import imagemobile11 from "@/assets/images/TeamBuildingPadel/imagemobile11.png";
import imagemobile12 from "@/assets/images/TeamBuildingPadel/imagemobile12.png";
import imagemobile13 from "@/assets/images/TeamBuildingPadel/imagemobile13.png";
import imagemobile14 from "@/assets/images/TeamBuildingPadel/imagemobile14.png";
import imagemobile15 from "@/assets/images/TeamBuildingPadel/imagemobile15.png";
import imagemobile16 from "@/assets/images/TeamBuildingPadel/imagemobile16.png";
import imagemobile17 from "@/assets/images/TeamBuildingPadel/imagemobile17.png";
import imagemobile18 from "@/assets/images/TeamBuildingPadel/imagemobile18.png";
import imagemobile19 from "@/assets/images/TeamBuildingPadel/imagemobile19.png";
import imagemobile20 from "@/assets/images/TeamBuildingPadel/imagemobile20.png";
import imagemobile21 from "@/assets/images/TeamBuildingPadel/imagemobile21.png";
import imagemobile22 from "@/assets/images/TeamBuildingPadel/imagemobile22.png";
import EmblaCarousel from "@/components/embla_slider/EmblaCarousel";
import { API_URLS } from "@/utils/urls";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { axiosGetJson } from "@/config/axios";
export default function TeamBuildingPadel({ language, event, isMobileSSR }) {
    const { t } = useTranslation();
    const [events, setEvents] = useState();
    const theme = useTheme();

    const fetchEvents = async () => {
        try {
            const response = await axiosGetJson.get(API_URLS.events, {
                params: {
                    visibility: "Public",
                    pageNumber: 1,
                    pageSize: 4,
                    language: language,
                },
            });
            let { Events } = response.data;

            const eventsFiltered = Events?.filter(
                (currentEvent) => currentEvent._id !== event?._id
            ).slice(0, 3);
            setEvents(eventsFiltered);
        } catch (error) {
            console.error("Failed to fetch events:", error);
        }
    };

    useEffect(() => {
        fetchEvents();
    }, [event, language]);

    const OPTIONS = {};
    const SLIDES = [
        image1,
        image2,
        image3,
        image4,
        image5,
        image6,
        image7,
        image8,
        image9,
        image10,
        image11,
        image12,
        image13,
        image14,
        image15,
        image16,
        image17,
        image18,
        image19,
        image20,
        image21,


    ];
    const SlidesMobile = [
        imagemobile1,
        imagemobile2,
        imagemobile3,
        imagemobile4,
        imagemobile5,
        imagemobile7,
        imagemobile8,
        imagemobile9,
        imagemobile10,
        imagemobile11,
        imagemobile12,
        imagemobile13,
        imagemobile14,
        imagemobile15,
        imagemobile16,
        imagemobile17,
        imagemobile18,
        imagemobile19,
        imagemobile20,
        imagemobile21,
        imagemobile22,
    ]
    const decarbonizationSection = {
        sector: t("TeamBuildingPadel:sectorValue"),
        country: t("TeamBuildingPadel:locationValue"),
        countryConcerned: t("TeamBuildingPadel:countryConcernedValue"),
        organiser: t("TeamBuildingPadel:organiserValue"),
    };
    let infoSection;
    if (event) {
        infoSection = {
            sector: event?.versions[0]?.sector,
            country: t(`country:${event?.country?.replace(/\s+/g, "")}`),
            countryConcerned: event?.versions[0]?.countryConcerned,
            organiser: event?.versions[0]?.organiser,
        };
    }
    const eventData = [

        {
            title: t("event:events.StrategicMeetingMoroocco.title"),
            eventDate: t("event:events.StrategicMeetingMoroocco.eventDate"),
            postingDate: t("event:events.StrategicMeetingMoroocco.postingDate"),
            exactPlace: t("event:events.StrategicMeetingMoroocco.exactPlace"),
            type: "events",
            link: `strategic-committee-meeting-casablanca-2025`,
            image: StrategicMeetingMaroc,
        },
        {
            title: t("event:events.QHSE.title"),
            eventDate: t("event:events.QHSE.eventDate"),
            postingDate: t("event:events.QHSE.postingDate"),
            exactPlace: t("event:events.QHSE.exactPlace"),
            link: "QHSE-EXPO-2025",
            type: "events",
            image: bannerQHSE,
        }, {
            title: t("event:events.Pentabellsalestraining.title"),
            eventDate: t("event:events.Pentabellsalestraining.eventDate"),
            postingDate: t("event:events.Pentabellsalestraining.postingDate"),
            exactPlace: t("event:events.Pentabellsalestraining.exactPlace"),
            link: "Pentabell-sales-training-and-workshop",
            type: "events",
            image: Pentabellsalestraining,
        },
    ];
    return (
        <div id="event-page">
            {event ? (
                <div id="event-detail">
                    <div className="custom-max-width">
                        <InfoSection t={t} infos={infoSection} />{" "}
                        <ProcessHtml htmlString={event?.versions[0]?.content} />
                        <Grid
                            className="more-events-section"
                            container
                            rowSpacing={0}
                            columnSpacing={3}
                        >
                            {" "}
                            {events?.length > 0 &&
                                events?.map((event, index) => (
                                    <EventsCard
                                        key={index}
                                        eventData={event}
                                        language={language}
                                        isEvent={true}
                                    />
                                ))}{" "}
                        </Grid>
                    </div>{" "}
                </div>
            ) : (
                <div id="event-detail">
                    <Container className="custom-max-width">
                        <InfoSection t={t} infos={decarbonizationSection} />
                        <div className="details">
                            <h1 className="heading-h1">
                                {t("TeamBuildingPadel:Description:title")}

                            </h1>
                            <p className="text">
                                {t("TeamBuildingPadel:Description:description1")}
                            </p>


                            <h2 className="heading-h2 bold">
                                {t("TeamBuildingPadel:Description2:title")}

                            </h2>
                            <p className="text">
                                {t("TeamBuildingPadel:Description2:description2")}
                            </p>

                            <h2 className="heading-h2 bold">
                                {t("TeamBuildingPadel:Description3:title")}

                            </h2>
                            <p className="text">
                                {t("TeamBuildingPadel:Description3:description2")}
                            </p>


                            <h3 className="heading-h2 bold">{t("TeamBuildingPadel:eventProgram2:title")}</h3>
                            <p className="text">
                                {t("TeamBuildingPadel:eventProgram2:eventProgram2")}
                            </p>


                            <ul>   <li className="text">
                                {" "}
                                {t("TeamBuildingPadel:eventProgram2:puce2")}
                            </li>
                                <li className="text">{t("TeamBuildingPadel:eventProgram2:puce1")}</li>

                                <li className="text">{t("TeamBuildingPadel:eventProgram2:puce3")}</li>

                            </ul>
                            <p className="text">
                                {t("TeamBuildingPadel:eventProgram2:description")}
                            </p>


                            <h2 className="heading-h2 bold">
                                {t("TeamBuildingPadel:eventProgram:eventProgram")}
                            </h2>


                            <EmblaCarousel slides={SLIDES} options={OPTIONS} slidesMobile={SlidesMobile} />
                            <p className="heading-h1">{t("TeamBuildingPadel:moreEvents")}</p>
                        </div>
                        <Grid
                            className="more-events-section"
                            container
                            rowSpacing={0}
                            columnSpacing={3}
                        >
                            {eventData?.map((event, index) => (
                                <EventsCard
                                    key={index}
                                    eventData={event}
                                    language={language}
                                    isEvent={true}
                                />
                            ))}
                        </Grid>
                    </Container>
                </div>
            )}
        </div>
    );
}
