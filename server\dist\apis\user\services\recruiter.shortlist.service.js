"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const candidat_model_1 = __importDefault(require("../../candidat/candidat.model"));
const user_model_1 = __importDefault(require("../user.model"));
const messages_1 = require("@/utils/helpers/messages");
class RecruiterShortlistService {
    constructor() {
        this.Candidate = candidat_model_1.default;
        this.Recruiter = user_model_1.default;
    }
    async addCandidateToShortlist(recruiterId, candidateId) {
        const candidate = await this.Candidate.findById(candidateId);
        if (!candidate)
            throw new http_exception_1.default(404, messages_1.MESSAGES.RECRUITER_SHORTLIST.CANDIDATE_NOT_FOUND);
        const existingcandidate = await this.Recruiter.findOne({
            shortList: candidateId,
        });
        if (existingcandidate) {
            throw new http_exception_1.default(409, messages_1.MESSAGES.RECRUITER_SHORTLIST.ALREADY_APPLIED);
        }
        return await this.Recruiter.findByIdAndUpdate(recruiterId, { $push: { shortList: candidate?._id } });
    }
    async getAll(queries, recruiterId) {
        const recruiter = await this.Recruiter.findById(recruiterId);
        const shortlist = recruiter?.shortList;
        const { paginated, keyWord, industry } = queries;
        const pageNumber = Number(queries.pageNumber) || 1;
        const pageSize = Number(queries.pageSize) || 8;
        const queryConditions = { isArchived: false };
        if (industry) {
            queryConditions['industry'] = industry;
        }
        const shortlistCondition = shortlist ? { _id: { $in: recruiter?.shortList } } : {};
        const candidateSearchRequest = keyWord ? {
            $text: { $search: keyWord },
            ...queryConditions,
            ...shortlistCondition
        }
            : { ...queryConditions,
                ...shortlistCondition };
        const candidateMetadata = keyWord
            ? {
                score: { $meta: 'textScore' },
            }
            : null;
        const sortCreteria = candidateMetadata ? candidateMetadata : {};
        if (paginated && paginated === "false") {
            return await this.Candidate.find(candidateSearchRequest, candidateMetadata)
                .select('jobTitleAng summary skills experiences')
                .sort(sortCreteria)
                .populate({
                path: 'user',
                select: 'firstName lastName email country'
            });
        }
        const totalCandidates = await this.Candidate.countDocuments(candidateSearchRequest);
        const totalPages = Math.ceil(totalCandidates / pageSize);
        const Candidates = await this.Candidate.find(candidateSearchRequest, candidateMetadata)
            .select('jobTitleAng summary skills experiences')
            .sort(sortCreteria)
            .populate({
            path: 'user',
            select: 'firstName lastName email country'
        })
            .skip((pageNumber - 1) * pageSize)
            .limit(pageSize || 10);
        return {
            pageNumber,
            pageSize,
            totalPages,
            totalCandidates,
            Candidates,
        };
    }
    ;
    async deleteFromShortlist(recruiterId, candidateId) {
        const candidateInShortList = await this.Recruiter.findOne({ _id: recruiterId, shortList: candidateId });
        if (!candidateInShortList)
            throw new http_exception_1.default(404, messages_1.MESSAGES.RECRUITER_SHORTLIST.CANDIDATE_NOT_FOUND);
        await this.Recruiter.findByIdAndUpdate(recruiterId, { $pull: { shortList: candidateId } });
    }
}
exports.default = RecruiterShortlistService;
//# sourceMappingURL=recruiter.shortlist.service.js.map