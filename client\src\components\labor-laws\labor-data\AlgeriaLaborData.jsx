"use client";
import LaborLaws from "../LaborLaws";
const algeriaLaborData = {
  title: "Algeria:AlgeriaLabor:title",
  sections: [
    {
      id: 1,
      title: "Algeria:AlgeriaLabor:workingHours:title",
      subsections: [
        {
          title: "Algeria:AlgeriaLabor:workingHours:title1",
          description: "Algeria:AlgeriaLabor:workingHours:description1",
        },
        {
          title: "Algeria:AlgeriaLabor:workingHours:title2",
          description: "Algeria:AlgeriaLabor:workingHours:description2",
        },
      ],
    },
    {
      id: 2,
      title: "Algeria:AlgeriaLabor:employmentContracts:title",
      subsections: [
        {
          description: "Algeria:AlgeriaLabor:employmentContracts:description1",
        },
        {
          description: "Algeria:AlgeriaLabor:employmentContracts:description2",
        },
        {

          list: [
            {
              text: "Algeria:AlgeriaLabor:employmentContracts:data1",
              sublist: [
                "Algeria:AlgeriaLabor:employmentContracts:data2",
                "Algeria:AlgeriaLabor:employmentContracts:data3",
                "Algeria:AlgeriaLabor:employmentContracts:data4",
              ],
            },
            {
              text: "Algeria:AlgeriaLabor:employmentContracts:data5",
              sublist: [
                "Algeria:AlgeriaLabor:employmentContracts:data6",
                "Algeria:AlgeriaLabor:employmentContracts:data7",
                "Algeria:AlgeriaLabor:employmentContracts:data8",
              ],
            },
          ],
        },
      ],
    }, {
      id: 3,
      title: "Algeria:AlgeriaLabor:payroll:title",
      type: "payroll",
      titleKey: "Algeria:AlgeriaLabor:payroll:title1",
      descriptionKey: "Algeria:AlgeriaLabor:payroll:description",
      items: [
        {
          titleKey: "Algeria:AlgeriaLabor:payroll:fiscalYear:title",
          dateKeys: [
            "Algeria:AlgeriaLabor:payroll:fiscalYear:date1",
            "Algeria:AlgeriaLabor:payroll:fiscalYear:date2",
          ],
          descriptionKey:
            "Algeria:AlgeriaLabor:payroll:fiscalYear:description",
        },
        {
          titleKey: "Algeria:AlgeriaLabor:payroll:payrollCycle:title",
          dateKeys: ["Algeria:AlgeriaLabor:payroll:payrollCycle:date"],
          descriptionKey:
            "Algeria:AlgeriaLabor:payroll:payrollCycle:description",
        },
        {
          titleKey: "Algeria:AlgeriaLabor:payroll:minimumWage:title",
          dateKeys: [
            "Algeria:AlgeriaLabor:payroll:minimumWage:wage",
            "Algeria:AlgeriaLabor:payroll:minimumWage:date",
          ],
          descriptionKey:
            "Algeria:AlgeriaLabor:payroll:minimumWage:description",
        },
        {
          titleKey: "Algeria:AlgeriaLabor:payroll:payrollManagement:title",
          dateKeys: [
            "Algeria:AlgeriaLabor:payroll:payrollManagement:date1",
            "Algeria:AlgeriaLabor:payroll:payrollManagement:date2",
          ],
          descriptionKey:
            "Algeria:AlgeriaLabor:payroll:payrollManagement:description",
        },
      ],
    },
    {
      id: 4,
      title: "Algeria:AlgeriaLabor:termination:title",
      subsections: [
        {
          title: "Algeria:AlgeriaLabor:termination:title1",
          description: "Algeria:AlgeriaLabor:termination:description1",
        },
        {
          title: "Algeria:AlgeriaLabor:termination:title2",
          description: "Algeria:AlgeriaLabor:termination:description2",
        },
        {
          title: "Algeria:AlgeriaLabor:termination:title3",
          description: "Algeria:AlgeriaLabor:termination:description3",
        },
        {

          list: [
            {
              text: "Algeria:AlgeriaLabor:termination:data1",
            },
            {
              text: "Algeria:AlgeriaLabor:termination:data2",
            },
          ],
        },
        {
          title: "Algeria:AlgeriaLabor:termination:title4",
          description: "Algeria:AlgeriaLabor:termination:description4",
        },
      ],
    },

    {
      id: 5,
      title: "Algeria:AlgeriaLabor:leaveEntitlements:title",
      type: "leaveEntitlements",
      data: {
        description: "Algeria:AlgeriaLabor:leaveEntitlements:description",
        leaves: [
          {
            date: "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS1:date",
            title: "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS1:title",
          },
          {
            date: "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS2:date",
            title: "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS2:title",
          },
          {
            date: "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS3:date",
            title: "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS3:title",
          },
          {
            date: "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS4:date",
            title: "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS4:title",
          },
          {
            date: "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS5:date",
            title: "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS5:title",
          },
          {
            date: "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS6:date",
            title: "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS6:title",
          },
          {
            date: "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS7:date",
            title: "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS7:title",
          },
          {
            date: "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS8:date",
            title: "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS8:title",
          },
          {
            date: "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS9:date",
            title: "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS9:title",
          },
          {
            date: "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS10:date",
            title: "Algeria:AlgeriaLabor:leaveEntitlements:leaves:dataS10:title",
          },
        ],
        annualLeave: {
          title: "Algeria:AlgeriaLabor:leaveEntitlements:leaves:annualLeave:title",
          description: [
            "Algeria:AlgeriaLabor:leaveEntitlements:leaves:annualLeave:description1"
          ],
        },
        maternityLeave: {
          title: "Algeria:AlgeriaLabor:leaveEntitlements:leaves:maternityLeave:title",
          description: ["Algeria:AlgeriaLabor:leaveEntitlements:leaves:maternityLeave:description2"],
        },
        paternityLeave: {
          title: "Algeria:AlgeriaLabor:leaveEntitlements:leaves:paternityLeave:title",
          description: "Algeria:AlgeriaLabor:leaveEntitlements:leaves:paternityLeave:description",
        },
        sickLeave: {
          title: "Algeria:AlgeriaLabor:leaveEntitlements:leaves:sickLeave:title",
          description: "Algeria:AlgeriaLabor:leaveEntitlements:leaves:sickLeave:description",
        },
        parentalLeave: {
          title: "Algeria:AlgeriaLabor:leaveEntitlements:leaves:parentalLeave:title",
          description: "Algeria:AlgeriaLabor:leaveEntitlements:leaves:parentalLeave:description",
        },
      },
    }
    ,
    {
      id: 6,
      title: "Algeria:AlgeriaLabor:tax:title",
      subsections: [
        {
          description: "Algeria:AlgeriaLabor:tax:description",
        }, {
          title: "Algeria:AlgeriaLabor:tax:title1",
          description: "Algeria:AlgeriaLabor:tax:description1"
        },
        {
          title: "",
          list: [
            {
              text: "Algeria:AlgeriaLabor:tax:data1",

            },
            {
              text: "Algeria:AlgeriaLabor:tax:data2",

            },
            {
              text: "Algeria:AlgeriaLabor:tax:data3",

            },
            {
              text: "Algeria:AlgeriaLabor:tax:data4",

            },
          ],
        },
        {
          title: "Algeria:AlgeriaLabor:tax:title2",
          description: "Algeria:AlgeriaLabor:tax:description2",
          list: [
            {
              text: "Algeria:AlgeriaLabor:tax:dataS1",
            },
            {
              text: "Algeria:AlgeriaLabor:tax:dataS2",
            },
            {
              text: "Algeria:AlgeriaLabor:tax:dataS3",
            },

          ],
        },
      ]
    },
    {
      id: 7,
      title: "Algeria:AlgeriaLabor:visa:title",
      subsections: [
        {
          description: "Algeria:AlgeriaLabor:visa:description1",
        }, {

          description: "Algeria:AlgeriaLabor:visa:description2"
        },
        {
          title: "Algeria:AlgeriaLabor:visa:title1",
        },
        {
          title: "Algeria:AlgeriaLabor:visa:title2",
        },
        {
          description: "Algeria:AlgeriaLabor:visa:description3"
        },
        {
          title: "Algeria:AlgeriaLabor:visa:title3",
        },
        {

          list: [
            {
              text: "Algeria:AlgeriaLabor:visa:data1",
            },
            {
              text: "Algeria:AlgeriaLabor:visa:data2",
            },
            {
              text: "Algeria:AlgeriaLabor:visa:data3",
            },
            {
              text: "Algeria:AlgeriaLabor:visa:data4",
            },
            {
              text: "Algeria:AlgeriaLabor:visa:data5",
            },
            {
              text: "Algeria:AlgeriaLabor:visa:data6",
            },
            {
              text: "Algeria:AlgeriaLabor:visa:data7",
            },
            {
              text: "Algeria:AlgeriaLabor:visa:data8",
            },
            {
              text: "Algeria:AlgeriaLabor:visa:data9",
            },
            {
              text: "Algeria:AlgeriaLabor:visa:data10",
            },
            {
              text: "Algeria:AlgeriaLabor:visa:data11",
            },
          ],
        },
      ]



    },
  ],
};

export default function AlgeriaLaborData() {
  return <LaborLaws data={algeriaLaborData} />;
}
