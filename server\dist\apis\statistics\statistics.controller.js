"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StatisticsController = void 0;
const express_1 = require("express");
const authentication_middleware_1 = __importDefault(require("@/middlewares/authentication.middleware"));
const validateApiKey_middleware_1 = __importDefault(require("@/middlewares/validateApiKey.middleware"));
const statistics_service_1 = require("./statistics.service");
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const messages_1 = require("@/utils/helpers/messages");
class StatisticsController {
    constructor() {
        this.path = '/statistics';
        this.router = (0, express_1.Router)();
        this.statisticsService = new statistics_service_1.StatisticsService();
        this.getTotalStats = async (request, response, next) => {
            try {
                response.send(await this.statisticsService.getTotalStats());
            }
            catch (error) {
                next(error);
            }
        };
        this.getApplicationsStats = async (request, response, next) => {
            try {
                response.send(await this.statisticsService.getApplicationsStats(request.query));
            }
            catch (error) {
                next(error);
            }
        };
        this.getArticleStats = async (request, response, next) => {
            try {
                response.send(await this.statisticsService.getArticleStats(request.query));
            }
            catch (error) {
                next(error);
            }
        };
        this.getOpportunitiesStats = async (request, response, next) => {
            try {
                response.send(await this.statisticsService.getOpportunitiesStats(request.query));
            }
            catch (error) {
                next(error);
            }
        };
        this.getResourcesStats = async (request, response, next) => {
            try {
                if (!request.params.resource)
                    return next(new http_exception_1.default(400, messages_1.MESSAGES.STATISTICS.MISSING_RESSOURCE));
                response.send(await this.statisticsService.getResourcesStats(request.params.resource, request.query));
            }
            catch (error) {
                next(error);
            }
        };
        this.getLoginsRecordStats = async (request, response, next) => {
            try {
                response.send(await this.statisticsService.getLoginsRecordStats(request.query));
            }
            catch (error) {
                next(error);
            }
        };
        this.getContactRecordStats = async (request, response, next) => {
            try {
                response.send(await this.statisticsService.getContactRecordStats(request.query));
            }
            catch (error) {
                next(error);
            }
        };
        this.getNewsLettersRecordStats = async (request, response, next) => {
            try {
                response.send(await this.statisticsService.getNewsLettersRecordStats(request.query));
            }
            catch (error) {
                next(error);
            }
        };
        this.getUserActivityStats = async (request, response, next) => {
            try {
                response.send(await this.statisticsService.getUserActivityStats(request.query));
            }
            catch (error) {
                next(error);
            }
        };
        this.getPlatformActivityStats = async (request, response, next) => {
            try {
                response.send(await this.statisticsService.getPlatformActivityStats(request.query));
            }
            catch (error) {
                next(error);
            }
        };
        this.getCommentsStats = async (request, response, next) => {
            try {
                response.send(await this.statisticsService.getCommentsStatistics(request.query));
            }
            catch (error) {
                next(error);
            }
        };
        this.getNewAccountsStats = async (request, response, next) => {
            try {
                response.send(await this.statisticsService.getNewAccountsStats(request.query));
            }
            catch (error) {
                next(error);
            }
        };
        this.initialiseRoutes();
    }
    initialiseRoutes() {
        this.router.get(`${this.path}`, authentication_middleware_1.default, validateApiKey_middleware_1.default, this.getTotalStats);
        this.router.get(`${this.path}/applications`, authentication_middleware_1.default, validateApiKey_middleware_1.default, this.getApplicationsStats);
        this.router.get(`${this.path}/articles`, authentication_middleware_1.default, validateApiKey_middleware_1.default, this.getArticleStats);
        this.router.get(`${this.path}/opportunities`, authentication_middleware_1.default, validateApiKey_middleware_1.default, this.getOpportunitiesStats);
        this.router.get(`${this.path}/logins`, authentication_middleware_1.default, validateApiKey_middleware_1.default, this.getLoginsRecordStats);
        this.router.get(`${this.path}/contacts`, authentication_middleware_1.default, validateApiKey_middleware_1.default, this.getContactRecordStats);
        this.router.get(`${this.path}/newsletters`, authentication_middleware_1.default, validateApiKey_middleware_1.default, this.getNewsLettersRecordStats);
        this.router.get(`${this.path}/resources/:resource`, authentication_middleware_1.default, validateApiKey_middleware_1.default, this.getResourcesStats);
        this.router.get(`${this.path}/useractivity`, authentication_middleware_1.default, validateApiKey_middleware_1.default, this.getUserActivityStats);
        this.router.get(`${this.path}/platformactivity`, authentication_middleware_1.default, validateApiKey_middleware_1.default, this.getPlatformActivityStats);
        this.router.get(`${this.path}/comments`, authentication_middleware_1.default, validateApiKey_middleware_1.default, this.getCommentsStats);
        this.router.get(`${this.path}/new-accounts`, authentication_middleware_1.default, validateApiKey_middleware_1.default, this.getNewAccountsStats);
    }
}
exports.StatisticsController = StatisticsController;
//# sourceMappingURL=statistics.controller.js.map