import { useState } from "react";
import { Collapse, List, ListItemButton } from "@mui/material";
import Link from "next/link";
import SvgArrowDown from "../../assets/images/icons/arrowDown.svg";
import SvgArrowUp from "../../assets/images/icons/arrowUp.svg";
import SvgLogoutIcon from "../../assets/images/icons/logoutIcon24.svg";
import SvgUserIcon from "../../assets/images/icons/userIcon24.svg";
import useCurrentUser from "@/features/auth/hooks/currentUser.hooks";
import { API_URLS } from "@/utils/urls";
import { useTranslation } from "react-i18next";
import { generateLocalizedSlug } from "@/utils/functions";
import { authRoutes, baseUrlBackoffice, baseUrlFrontoffice, commonRoutes } from "@/helpers/routesList";
import { Role } from "@/utils/constants";

function MyAccountDropdownMobile({ locale }) {
  const { t } = useTranslation();
  const { user } = useCurrentUser();
  const [openSubMenu, setOpenSubMenu] = useState(false);

  const myDashUrl = user?.roles?.includes(Role.CANDIDATE)
    ? `/${baseUrlFrontoffice.baseURL.route}`
    : `/${baseUrlBackoffice.baseURL.route}`;
  return (
    <>
      <ListItemButton className="dropdown-menu">
        <Link
          locale={locale === "en" ? "en" : "fr"}
          href={generateLocalizedSlug(locale, myDashUrl)}
          className="menu-item my-profile-img"
          passHref
        >
          {user?.profilePicture ? (
            <img
              src={`${process.env.NEXT_PUBLIC_BASE_API_URL}${API_URLS.files}/${user?.profilePicture}`}
              width={25}
              height={25}
              loading="lazy"
            />
          ) : (
            <SvgUserIcon />
          )}
        </Link>
        <div
          onClick={(e) => setOpenSubMenu(!openSubMenu)}
        >
          {openSubMenu ? <SvgArrowDown /> : <SvgArrowUp />}
        </div>
      </ListItemButton>
      <Collapse
        id="collapse-section"
        in={openSubMenu}
        timeout="auto"
        unmountOnExit
      >
        <List component="div" disablePadding>
          <ListItemButton
            className={"menu-item"}
            key={"ii"}
            sx={{ pl: 4 }}
            locale={locale === "en" ? "en" : "fr"}
            href={generateLocalizedSlug(locale, `${myDashUrl}/${commonRoutes.myProfile.route}`)}
          >
            <SvgUserIcon /> {t("menu:profile")}
          </ListItemButton>

          <ListItemButton
            className={"menu-item"}
            key={"ii"}
            sx={{ pl: 4 }}
            locale={locale === "en" ? "en" : "fr"}
            href={generateLocalizedSlug(locale, `/${authRoutes.logout.route}`)}
          >
            <SvgLogoutIcon /> {t("menu:logout")}
          </ListItemButton>
        </List>
      </Collapse>
    </>
  );
}

export default MyAccountDropdownMobile;
