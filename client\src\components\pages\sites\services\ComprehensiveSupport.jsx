import { Container } from "@mui/material";
import supportTopImg1 from "@/assets/images/services/supportTopImg1.png";
import supportTopImg2 from "@/assets/images/services/supportTopImg2.png";
import supportTopImg3 from "@/assets/images/services/supportTopImg3.png";
import initTranslations from "@/app/i18n";

async function ComprehensiveSupport({ locale }) {
  const { t, resources } = await initTranslations(locale, [
    "technicalAssistanceService",
    "global",
  ]);
  return (
    <div id="comprehensive-support-section">
      <Container className="custom-max-width">
        <h2 className="heading-h1 text-white text-center">
          {t(
            "technicalAssistanceService:comprehensiveTechnicalAssistance:title"
          )}
        </h2>
        <div className="support-reasons">
          <div className="support-reason">
            <img width={600} height={61} src={supportTopImg1.src} alt="Support Reason 1" loading="lazy" />
            <h3 className="support-reason-title">
              {" "}
              {t(
                "technicalAssistanceService:comprehensiveTechnicalAssistance:advisory:title"
              )}
            </h3>
            <ul>
              <li className="support-reason-item">
                {t(
                  "technicalAssistanceService:comprehensiveTechnicalAssistance:advisory:description:item1"
                )}
              </li>
              <li className="support-reason-item">
                {t(
                  "technicalAssistanceService:comprehensiveTechnicalAssistance:advisory:description:item2"
                )}
              </li>
              <li className="support-reason-item">
                {t(
                  "technicalAssistanceService:comprehensiveTechnicalAssistance:advisory:description:item3"
                )}
              </li>
              <li className="support-reason-item">
                {t(
                  "technicalAssistanceService:comprehensiveTechnicalAssistance:advisory:description:item4"
                )}
              </li>
              <li className="support-reason-item">
                {t(
                  "technicalAssistanceService:comprehensiveTechnicalAssistance:advisory:description:item5"
                )}
              </li>
              <li className="support-reason-item">
                {t(
                  "technicalAssistanceService:comprehensiveTechnicalAssistance:advisory:description:item6"
                )}
              </li>
            </ul>
          </div>
          <div className="support-reason">
            <img width={600} height={61} src={supportTopImg2.src} alt="Support Reason 2" loading="lazy" />
            <h3 className="support-reason-title">
              {t(
                "technicalAssistanceService:comprehensiveTechnicalAssistance:operationalSupport:title"
              )}
            </h3>
            <ul>
              <li className="support-reason-item">
                {t(
                  "technicalAssistanceService:comprehensiveTechnicalAssistance:operationalSupport:description:item1"
                )}
              </li>
              <li className="support-reason-item">
                {t(
                  "technicalAssistanceService:comprehensiveTechnicalAssistance:operationalSupport:description:item2"
                )}
              </li>
              <li className="support-reason-item">
                {t(
                  "technicalAssistanceService:comprehensiveTechnicalAssistance:operationalSupport:description:item3"
                )}
              </li>
              <li className="support-reason-item">
                {t(
                  "technicalAssistanceService:comprehensiveTechnicalAssistance:operationalSupport:description:item4"
                )}
              </li>
            </ul>
          </div>
          <div className="support-reason">
            <img width={600} height={61} src={supportTopImg3.src} alt="Support Reason 3" loading="lazy" />
            <h3 className="support-reason-title">
              {t(
                "technicalAssistanceService:comprehensiveTechnicalAssistance:technicalSupports:title"
              )}
            </h3>
            <ul>
              <li className="support-reason-item">
                {t(
                  "technicalAssistanceService:comprehensiveTechnicalAssistance:technicalSupports:description:item1"
                )}
              </li>
              <li className="support-reason-item">
                {t(
                  "technicalAssistanceService:comprehensiveTechnicalAssistance:technicalSupports:description:item2"
                )}
              </li>
              <li className="support-reason-item">
                {t(
                  "technicalAssistanceService:comprehensiveTechnicalAssistance:technicalSupports:description:item3"
                )}
              </li>
            </ul>
          </div>
        </div>
      </Container>
    </div>
  );
}

export default ComprehensiveSupport;
