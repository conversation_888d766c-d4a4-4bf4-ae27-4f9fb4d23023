import {
  Card,
  CardContent,
  TextField,
  Select,
  MenuItem,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  FormGroup,
  Autocomplete,
  Grid,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useEffect, useState } from "react";

import CustomButton from "@/components/ui/CustomButton";
import SvgexpandIcon from "@/assets/images/icons/arrowUp.svg";
import SvgRefreshIcon2 from "@/assets/images/icons/refreshIcon2.svg";
import CustomPieChart from "@/components/ui/charts/CustomPieChart";
import { useGetCommentsStat } from "../stats.hooks";

export default function CommentByCategory({ transformedCategories }) {
  const { t } = useTranslation();

  const [filteredCategories, setFilteredCategories] = useState([]);
  const [approve, setApprove] = useState(true);
  const [categories, setCategories] = useState([]);
  const [dateFromComment, setDateFromComment] = useState(() => {
    const fourMonthsBefore = new Date();
    fourMonthsBefore.setMonth(fourMonthsBefore.getMonth() - 4);
    fourMonthsBefore.setDate(1);
    return fourMonthsBefore.toISOString().split("T")[0];
  });
  const [dateToComment, setDateToComment] = useState(() => {
    const today = new Date();
    return today.toISOString().split("T")[0];
  });
  const [searchComment, setSearchComment] = useState(false);

  const resetSearchComments = () => {
    setDateToComment(() => {
      const today = new Date();
      return today.toISOString().split("T")[0];
    });
    setDateFromComment("2024-09-01");
    setFilteredCategories([]);
    setApprove(true);
    setCategories([]);
    setSearchComment(!searchComment);
  };

  const getDataPieComments = useGetCommentsStat({
    dateFrom: dateFromComment,
    dateTo: dateToComment,
    approve: approve,
    categories: filteredCategories,
  });

  const pieChart = {
    title: t("statsDash:commentsByCategory"),
    dataset:
      getDataPieComments?.data?.map((comment) => ({
        label: comment.category,
        value: comment.total,
      })) || [],
    colors: [
      "#673ab7",
      "#009688",
      "#8bc34a",
      "#ffc107",
      "#ff9800",
      "#ffc107",
      "#3f51b5",
      "#009688",
      "#4caf50",
      "#03a9f4",
      "#ff9800",
      "#8bc34a",
      "#673ab7",
    ],
  };

  useEffect(() => {
    getDataPieComments.refetch();
  }, [searchComment]);

  return (
    <Card className="card">
      <CardContent>
        <p className="heading-h3" gutterBottom>
          {pieChart?.title}
        </p>
        <Accordion elevation={0} disableGutters={true}>
          <AccordionSummary
            aria-controls="panel1bh-content"
            id="panel1bh-header"
            className="svg-accordion"
            expandIcon={<SvgexpandIcon />}
          >
            <h3 className="label-pentabell">{t("statsDash:filters")}</h3>
          </AccordionSummary>
          <AccordionDetails elevation={0}>
            <Grid container className="chart-grid" spacing={1}>
              <Grid item xs={12} sm={12}>
                <FormGroup>
                  <span className="blue-text">
                    {" "}
                    {t("statsDash:categories")} :
                  </span>
                  <Autocomplete
                    multiple
                    id={`category`}
                    options={transformedCategories ? transformedCategories : []}
                    getOptionLabel={(option) => option.name}
                    value={
                      categories.length > 0
                        ? transformedCategories.filter((category) =>
                            categories.includes(category.name)
                          )
                        : []
                    }
                    onChange={(event, selectedOptions) => {
                      const categoryNames = selectedOptions.map(
                        (category) => category.name
                      );
                      setCategories(categoryNames);
                      setFilteredCategories(categoryNames.join(","));
                    }}
                    renderInput={(params) => (
                      <TextField {...params} className="" variant="standard" />
                    )}
                  />
                </FormGroup>
              </Grid>
              <Grid item xs={12} md={12}>
                <span className="blue-text">
                  {t("statsDash:approvedComments")} :
                </span>
                <Select
                  className="select-pentabell blue-text"
                  value={approve}
                  defaultValue={""}
                  onChange={(event) => setApprove(event.target.value)}
                >
                  <MenuItem value="" selected disabled>
                    <em>{t("statsDash:approvedComments")}</em>
                  </MenuItem>
                  <MenuItem className="blue-text" value="">
                    {t("statsDash:all")}
                  </MenuItem>
                  <MenuItem className="blue-text" value={true}>
                    {t("statsDash:approved")}
                  </MenuItem>{" "}
                  <MenuItem className="blue-text" value={false}>
                    {t("statsDash:notApproved")}
                  </MenuItem>
                </Select>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label={t("statsDash:fromDate")}
                  type="date"
                  value={dateFromComment}
                  onChange={(e) => setDateFromComment(e.target.value)}
                  fullWidth
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label={t("statsDash:toDate")}
                  type="date"
                  value={dateToComment}
                  onChange={(e) => setDateToComment(e.target.value)}
                  fullWidth
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={3} sm={1} md={4} className="btns-filter dashboard">
                <CustomButton
                  icon={<SvgRefreshIcon2 />}
                  className={"btn btn-outlined btn-refresh full-width"}
                  onClick={resetSearchComments}
                />
              </Grid>
              <Grid item xs={11} sm={11} md={8}>
                <CustomButton
                  text={t("statsDash:filter")}
                  onClick={() => {
                    setSearchComment(!searchComment);
                  }}
                  className={"btn btn-outlined btn-filter-stat full-width"}
                />
              </Grid>
            </Grid>
          </AccordionDetails>
        </Accordion>
        <div className="chart-wrapper">
          <CustomPieChart donuts={true} chart={pieChart} />
        </div>
      </CardContent>
    </Card>
  );
}
