"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const authentication_middleware_1 = __importDefault(require("@/middlewares/authentication.middleware"));
const mongoId_validation_middleware_1 = __importDefault(require("@/middlewares/mongoId-validation.middleware"));
const authorization_middleware_1 = require("@/middlewares/authorization.middleware");
const constants_1 = require("@/utils/helpers/constants");
const messages_1 = require("@/utils/helpers/messages");
const alert_service_1 = require("./alert.service");
const queries_validation_middleware_1 = __importDefault(require("@/middlewares/queries-validation.middleware"));
const cache_middleware_1 = require("./../../middlewares/cache.middleware");
class AlertController {
    constructor() {
        this.path = '/alerts';
        this.router = (0, express_1.Router)();
        this.alertService = new alert_service_1.AlertService();
        this.create = async (request, response, next) => {
            try {
                const currentUser = request.user;
                const alertData = request.body;
                const result = await this.alertService.create(alertData, currentUser);
                response.status(201).send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.update = async (request, response, next) => {
            try {
                const alertId = request.params.id;
                const currentUser = request.user;
                const alertData = request.body;
                const result = await this.alertService.update(currentUser, alertId, alertData);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.toggle = async (request, response, next) => {
            try {
                const alertId = request.params.id;
                let status = request.query.status;
                status === 'true' ? (status = true) : (status = false);
                const result = await this.alertService.toggle(alertId, status);
                response.send({
                    data: result,
                    message: messages_1.MESSAGES.ALERT.DISABLED,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.get = async (request, response, next) => {
            try {
                const alertId = request.params.id;
                const result = await this.alertService.get(alertId);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.getbyiD = async (request, response, next) => {
            try {
                const alertId = request.params.id;
                const result = await this.alertService.getById(alertId);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.getAll = async (request, response, next) => {
            try {
                const queries = request.query;
                const result = await this.alertService.getAll(queries);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.getUserAlerts = async (req, res, next) => {
            try {
                const currentUser = req.user;
                const alerts = await this.alertService.getAlerts(currentUser);
                res.status(200).json(alerts);
            }
            catch (error) {
                ;
            }
        };
        this.initializeRoutes();
    }
    initializeRoutes() {
        this.router.post(`${this.path}`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.CANDIDATE]), cache_middleware_1.invalidateCache, this.create);
        this.router.get(`${this.path}/user`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.CANDIDATE]), this.getUserAlerts);
        this.router.get(`${this.path}/userswithalerts`, queries_validation_middleware_1.default, cache_middleware_1.validateCache, this.getAll);
        this.router.get(`${this.path}/:id`, authentication_middleware_1.default, mongoId_validation_middleware_1.default, cache_middleware_1.validateCache, this.get);
        this.router.get(`${this.path}/user/:id`, mongoId_validation_middleware_1.default, cache_middleware_1.validateCache, this.getbyiD);
        this.router.put(`${this.path}/:id/:status`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.CANDIDATE]), mongoId_validation_middleware_1.default, cache_middleware_1.invalidateCache, this.toggle);
        this.router.put(`${this.path}/:id`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.CANDIDATE]), mongoId_validation_middleware_1.default, cache_middleware_1.invalidateCache, this.update);
    }
}
exports.default = AlertController;
//# sourceMappingURL=alert.controller.js.map