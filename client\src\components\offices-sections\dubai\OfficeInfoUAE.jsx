
import SvgUsers from "@/assets/images/icons/yellow/Users.svg";
import Svglanguage from "@/assets/images/icons/yellow/language.svg";
import Svggdp from "@/assets/images/icons/yellow/gdp.svg";
import Svgcurrency from "@/assets/images/icons/yellow/currency.svg";
import SvgcapitalCity from "@/assets/images/icons/yellow/capitalCity.svg";
import Svggross from "@/assets/images/icons/yellow/gross.svg";
import OfficeInfo from "@/components/ui/OfficeInfo";

async function OfficeInfoUAE({ t }) {
  const data = [
    {
      icon: <SvgUsers />,
      titleKey: "dubai:officeInfoTN:title1",
      descriptionKey: "dubai:officeInfoTN:description1"
    },
    {
      icon: <Svglanguage />,
      titleKey: "dubai:officeInfoTN:title2",
      descriptionKey: "dubai:officeInfoTN:description2"
    },
    {
      icon: <Svggdp />,
      titleKey: "dubai:officeInfoTN:title3",
      descriptionKey: "dubai:officeInfoTN:description3"
    },
    {
      icon: <Svgcurrency />,
      titleKey: "dubai:officeInfoTN:title4",
      descriptionKey: "dubai:officeInfoTN:description4"
    },
    {
      icon: <SvgcapitalCity />,
      titleKey: "dubai:officeInfoTN:title5",
      descriptionKey: "dubai:officeInfoTN:description5"
    },
    {
      icon: <Svggross />,
      titleKey: "dubai:officeInfoTN:title6",
      descriptionKey: "dubai:officeInfoTN:description6"
    }
  ];

  return <OfficeInfo data={data} t={t} />;
}


export default OfficeInfoUAE;
