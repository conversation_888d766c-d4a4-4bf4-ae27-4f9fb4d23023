"use client";
import {
  adminRoutes,
  baseUrlBackoffice,
  baseUrlFrontoffice,
  editorRoutes,
  websiteRoutesList,
  candidateRoutes,
  authRoutes,
} from "./routesList";
import SvgDashboard from "@/assets/images/icons/menu-items.svg";
import SvgProfileIcon from "@/assets/images/icons/profilecandidat.svg";
import SvgApplicationIcon from "@/assets/images/icons/applicationIcon.svg";
import SvgFavoritsIcon from "@/assets/images/icons/favoritsIcon.svg";
import SvgArticles from "@/assets/images/icons/articles-icon-svg.svg";
import SvgNotificationsIcon from "@/assets/images/icons/svgnotifdashboard.svg";
import SvgCategories from "@/assets/images/icons/categoriesdasyhboard.svg";
import SvgOpportunite from "@/assets/images/icons/opportunitydashboard.svg";
import Svgparametres from "@/assets/images/icons/settintgs-icon.svg";
// import GlossaryIcon from "@/assets/images/icons/glossaries-icon.svg";
import Svgusers from "@/assets/images/icons/users-icons.svg";
import SvgResumeIcon from "@/assets/images/icons/svgResume.svg";
import SvgMail from "@/assets/images/icons/mail.svg";
import SvgLogoutIcon from "@/assets/images/icons/logoutIcon.svg";
import SvgStatsIcon from "@/assets/images/icons/StatisticsIcon.svg";

export const MenuList = {
  website: [
    {
      route: `/${websiteRoutesList.aboutUs.route}`,
      name: websiteRoutesList.aboutUs.name,
      key: websiteRoutesList.aboutUs.key,
      i18nName: websiteRoutesList.aboutUs.i18nName,
      subItems: [
        {
          route: `/${websiteRoutesList.corporatProfile.route}`,
          name: websiteRoutesList.corporatProfile.name,
          key: websiteRoutesList.corporatProfile.key,
          i18nName: websiteRoutesList.corporatProfile.i18nName,
        },
      ],
    },

    {
      route: `/${websiteRoutesList.services.route}`,
      name: websiteRoutesList.services.name,
      key: websiteRoutesList.services.key,
      i18nName: websiteRoutesList.services.i18nName,
      subItems: [
        {
          route: `/${websiteRoutesList.services.route}/${websiteRoutesList.payrollServices.route}`,
          name: websiteRoutesList.payrollServices.name,
          key: websiteRoutesList.payrollServices.key,
          i18nName: websiteRoutesList.payrollServices.i18nName,
        },
        {
          route: `/${websiteRoutesList.services.route}/${websiteRoutesList.consultingServices.route}`,
          name: websiteRoutesList.consultingServices.name,
          key: websiteRoutesList.consultingServices.key,
          i18nName: websiteRoutesList.consultingServices.i18nName,
        },
        {
          route: `/${websiteRoutesList.services.route}/${websiteRoutesList.technicalAssistance.route}`,
          name: websiteRoutesList.technicalAssistance.name,
          key: websiteRoutesList.technicalAssistance.key,
          i18nName: websiteRoutesList.technicalAssistance.i18nName,
        },
        {
          route: `/${websiteRoutesList.services.route}/${websiteRoutesList.directHiring.route}`,
          name: websiteRoutesList.directHiring.name,
          key: websiteRoutesList.directHiring.key,
          i18nName: websiteRoutesList.directHiring.i18nName,
        },
        {
          route: `/${websiteRoutesList.services.route}/${websiteRoutesList.aiSourcing.route}`,
          name: websiteRoutesList.aiSourcing.name,
          key: websiteRoutesList.aiSourcing.key,
          i18nName: websiteRoutesList.aiSourcing.i18nName,
        },
      ],
    },

    {
      route: `/${websiteRoutesList.opportunities.route}`,
      name: websiteRoutesList.opportunities.name,
      key: websiteRoutesList.opportunities.key,
      i18nName: websiteRoutesList.opportunities.i18nName,
      subItems: [
        {
          route: `/${websiteRoutesList.jobCategory.route}/${websiteRoutesList.transportation.route}`,
          name: websiteRoutesList.transportation.name,
          key: websiteRoutesList.transportation.key,
          i18nName: websiteRoutesList.transportation.i18nName,
          icon: <span className="color-industry transport"></span>,
        },
        {
          route: `/${websiteRoutesList.jobCategory.route}/${websiteRoutesList.itTelecom.route}`,
          name: websiteRoutesList.itTelecom.name,
          key: websiteRoutesList.itTelecom.key,
          i18nName: websiteRoutesList.itTelecom.i18nName,
          icon: <span className="color-industry it-telecom"></span>,
        },
        {
          route: `/${websiteRoutesList.jobCategory.route}/${websiteRoutesList.insuranceBanking.route}`,
          name: websiteRoutesList.insuranceBanking.name,
          key: websiteRoutesList.insuranceBanking.key,
          i18nName: websiteRoutesList.insuranceBanking.i18nName,
          icon: <span className="color-industry banking-insurance"></span>,
        },
        {
          route: `/${websiteRoutesList.jobCategory.route}/${websiteRoutesList.energies.route}`,
          name: websiteRoutesList.energies.name,
          key: websiteRoutesList.energies.key,
          i18nName: websiteRoutesList.energies.i18nName,
          icon: <span className="color-industry energy"></span>,
        },

        {
          route: `/${websiteRoutesList.jobCategory.route}/${websiteRoutesList.pharmaceutical.route}`,
          name: websiteRoutesList.pharmaceutical.name,
          key: websiteRoutesList.pharmaceutical.key,
          i18nName: websiteRoutesList.pharmaceutical.i18nName,
          icon: <span className="color-industry pharmaceutical"></span>,
        },
        {
          route: `/${websiteRoutesList.jobCategory.route}/${websiteRoutesList.others.route}`,
          name: websiteRoutesList.others.name,
          key: websiteRoutesList.others.key,
          i18nName: websiteRoutesList.others.i18nName,
          icon: <span className="color-industry other"></span>,
        },
      ],
    },

    {
      route: `/${websiteRoutesList.resources.route}`,
      name: websiteRoutesList.resources.name,
      key: websiteRoutesList.resources.key,
      i18nName: websiteRoutesList.resources.i18nName,
      subItems: [
        {
          route: `/${websiteRoutesList.blog.route}`,
          name: websiteRoutesList.blog.name,
          key: websiteRoutesList.blog.key,
          i18nName: websiteRoutesList.blog.i18nName,
        },
        {
          route: `/${websiteRoutesList.events.route}`,
          name: websiteRoutesList.events.name,
          key: websiteRoutesList.events.key,
          i18nName: websiteRoutesList.events.i18nName,
          // icon: <span className="color-industry it-telecom"></span>,
        },
        {
          route: `/${websiteRoutesList.guide.route}`,
          name: websiteRoutesList.guide.name,
          key: websiteRoutesList.guide.key,
          i18nName: websiteRoutesList.guide.i18nName,
        },
        // {
        //   route: `/${websiteRoutesList.glossaries.route}`,
        //   name: websiteRoutesList.glossaries.name,
        //   key: websiteRoutesList.glossaries.key,
        //   i18nName: websiteRoutesList.glossaries.i18nName,
        // },
      ],
    },
    // {
    //   route: `/${websiteRoutesList.blog.route}`,
    //   name: websiteRoutesList.blog.name,
    //   key: websiteRoutesList.blog.key,
    //   i18nName: websiteRoutesList.blog.i18nName,
    // },
    {
      route: `/${websiteRoutesList.joinUs.route}`,
      name: websiteRoutesList.joinUs.name,
      key: websiteRoutesList.joinUs.key,
      i18nName: websiteRoutesList.joinUs.i18nName,
    },
    {
      route: `/${websiteRoutesList.contact.route}`,
      name: websiteRoutesList.contact.name,
      key: websiteRoutesList.contact.key,
      i18nName: websiteRoutesList.contact.i18nName,
    },
  ],
  candidate: [
    {
      route: `/${baseUrlFrontoffice.baseURL.route}/${candidateRoutes.home.route}`,
      name: candidateRoutes.home.name,
      key: candidateRoutes.home.key,
      i18nName: candidateRoutes.home.i18nName,
      svgIcon: <SvgDashboard />,
    },
    {
      route: `/${baseUrlFrontoffice.baseURL.route}/${candidateRoutes.myApplications.route}`,
      name: candidateRoutes.myApplications.name,
      key: candidateRoutes.myApplications.key,
      i18nName: candidateRoutes.myApplications.i18nName,
      svgIcon: <SvgApplicationIcon />,
    },
    {
      route: `/${baseUrlFrontoffice.baseURL.route}/${candidateRoutes.resumes.route}`,
      name: candidateRoutes.resumes.name,
      key: candidateRoutes.resumes.key,
      i18nName: candidateRoutes.resumes.i18nName,
      svgIcon: <SvgResumeIcon />,
    },
    {
      route: `/${baseUrlFrontoffice.baseURL.route}/${candidateRoutes.favoris.route}`,
      name: candidateRoutes.favoris.name,
      key: candidateRoutes.favoris.key,
      i18nName: candidateRoutes.favoris.i18nName,
      svgIcon: <SvgFavoritsIcon />,
    },
    {
      route: `/${baseUrlFrontoffice.baseURL.route}/${candidateRoutes.notifications.route}`,
      name: candidateRoutes.notifications.name,
      key: candidateRoutes.notifications.key,
      i18nName: candidateRoutes.notifications.i18nName,
      svgIcon: <SvgNotificationsIcon />,
    },
    {
      route: `/${baseUrlFrontoffice.baseURL.route}/${candidateRoutes.myProfile.route}`,
      name: candidateRoutes.myProfile.name,
      key: candidateRoutes.myProfile.key,
      i18nName: candidateRoutes.myProfile.i18nName,
      svgIcon: <SvgProfileIcon />,
    },
    {
      route: `/${baseUrlFrontoffice.baseURL.route}/${candidateRoutes.settings.route}`,
      name: candidateRoutes.settings.name,
      key: candidateRoutes.settings.key,
      i18nName: candidateRoutes.settings.i18nName,
      svgIcon: <Svgparametres />,
    },
    {
      route: `/${authRoutes.logout.route}`,
      name: authRoutes.logout.name,
      key: authRoutes.logout.key,
      i18nName: authRoutes.logout.i18nName,
      svgIcon: <SvgLogoutIcon />,
    },
  ],
  admin: [
    {
      route: `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.home.route}`,
      name: editorRoutes.home.name,
      key: editorRoutes.home.key,
      i18nName: editorRoutes.home.i18nName,
      svgIcon: <SvgDashboard />,
    },
    {
      route: `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.statistics.route}`,
      name: adminRoutes.statistics.name,
      key: adminRoutes.statistics.key,
      i18nName: adminRoutes.statistics.i18nName,
      svgIcon: <SvgStatsIcon />,
    },
    {
      route: `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.myProfile.route}`,
      name: adminRoutes.myProfile.name,
      key: adminRoutes.myProfile.key,
      i18nName: adminRoutes.myProfile.i18nName,
      svgIcon: <SvgProfileIcon />,
    },

    {
      route: `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.guides.route}`,
      name: adminRoutes.guides.name,
      key: adminRoutes.guides.key,
      i18nName: adminRoutes.guides.i18nName,
      svgIcon: <SvgDashboard />,
    },
    {
      route: `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.blogs.route}`,
      name: adminRoutes.blogs.name,
      key: adminRoutes.blogs.key,
      i18nName: adminRoutes.blogs.i18nName,
      svgIcon: <SvgArticles />,
    },
    {
      route: `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.guides.route}/${adminRoutes.categories.route}`,
      name: adminRoutes.categoriesguide.name,
      key: adminRoutes.categoriesguide.key,
      i18nName: adminRoutes.categoriesguide.i18nName,
      svgIcon: <SvgCategories />,
    },
    {
      route: `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.comments.route}`,
      name: adminRoutes.comments.name,
      key: adminRoutes.comments.key,
      i18nName: adminRoutes.comments.i18nName,
      svgIcon: <SvgApplicationIcon />,
    },
    {
      route: `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.opportunities.route}`,
      name: adminRoutes.opportunities.name,
      key: adminRoutes.opportunities.key,
      i18nName: adminRoutes.opportunities.i18nName,
      svgIcon: <SvgOpportunite />,
    },
    {
      route: `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.events.route}`,
      name: editorRoutes.events.name,
      key: editorRoutes.events.key,
      i18nName: editorRoutes.events.i18nName,
      svgIcon: <SvgArticles />,
    },
    {
      route: `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.sliders.route}`,
      name: adminRoutes.sliders.name,
      key: adminRoutes.sliders.key,
      i18nName: adminRoutes.sliders.i18nName,
      svgIcon: <Svgparametres />,
    },
    // {
    //   route: `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.glossaries.route}`,
    //   name: editorRoutes.glossaries.name,
    //   key: editorRoutes.glossaries.key,
    //   i18nName: editorRoutes.glossaries.i18nName,
    //   svgIcon: <GlossaryIcon />,
    // },
    {
      route: `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.categories.route}`,
      name: adminRoutes.categories.name,
      key: adminRoutes.categories.key,
      i18nName: adminRoutes.categories.i18nName,
      svgIcon: <SvgCategories />,
    },
    {
      route: `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.notifications.route}`,
      name: adminRoutes.notifications.name,
      key: adminRoutes.notifications.key,
      i18nName: adminRoutes.notifications.i18nName,
      svgIcon: <SvgNotificationsIcon />,
    },

    {
      route: `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.settings.route}`,
      name: adminRoutes.settings.name,
      key: adminRoutes.settings.key,
      i18nName: adminRoutes.settings.i18nName,
      svgIcon: <Svgparametres />,
    },
    {
      route: `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.contacts.route}`,
      name: adminRoutes.contacts.name,
      key: adminRoutes.contacts.key,
      i18nName: adminRoutes.contacts.i18nName,
      svgIcon: <SvgApplicationIcon />,
    },
    {
      route: `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.newsletters.route}`,
      name: adminRoutes.newsletters.name,
      key: adminRoutes.newsletters.key,
      i18nName: adminRoutes.newsletters.i18nName,
      svgIcon: <SvgMail />,
    },
    {
      route: `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.seoSettings.route}`,
      name: editorRoutes.seoSettings.name,
      key: editorRoutes.seoSettings.key,
      i18nName: editorRoutes.seoSettings.i18nName,
      svgIcon: <Svgparametres />,
    },
    {
      route: `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.users.route}`,
      name: adminRoutes.users.name,
      key: adminRoutes.users.key,
      i18nName: adminRoutes.users.i18nName,
      svgIcon: <Svgusers />,
    },
    {
      route: `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.applications.route}`,
      name: adminRoutes.applications.name,
      key: adminRoutes.applications.key,
      i18nName: adminRoutes.applications.i18nName,
      svgIcon: <SvgOpportunite />,
    },
    {
      route: `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.sliders.route}`,
      name: adminRoutes.sliders.name,
      key: adminRoutes.sliders.key,
      i18nName: adminRoutes.sliders.i18nName,
      svgIcon: <Svgparametres />,
    },
    {
      route: `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.downloadReport.route}`,
      name: adminRoutes.downloadReport.name,
      key: adminRoutes.downloadReport.key,
      i18nName: adminRoutes.downloadReport.i18nName,
      svgIcon: <SvgApplicationIcon />,
    },
    {
      route: `/${authRoutes.logout.route}`,
      name: authRoutes.logout.name,
      key: authRoutes.logout.key,
      i18nName: authRoutes.logout.i18nName,
      svgIcon: <SvgLogoutIcon />,
    },
  ],
  editor: [
    {
      route: `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.home.route}`,
      name: editorRoutes.home.name,
      key: editorRoutes.home.key,
      i18nName: editorRoutes.home.i18nName,
      svgIcon: <SvgDashboard />,
    },
    {
      route: `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.myProfile.route}`,
      name: editorRoutes.myProfile.name,
      key: editorRoutes.myProfile.key,
      i18nName: editorRoutes.myProfile.i18nName,
      svgIcon: <SvgDashboard />,
    },
    {
      route: `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.blogs.route}`,
      name: editorRoutes.blogs.name,
      key: editorRoutes.blogs.key,
      i18nName: editorRoutes.blogs.i18nName,
      svgIcon: <SvgArticles />,
    },
    {
      route: `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.guides.route}`,
      name: editorRoutes.guides.name,
      key: editorRoutes.guides.key,
      i18nName: editorRoutes.guides.i18nName,
      svgIcon: <SvgDashboard />,
    },
    {
      route: `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.comments.route}`,
      name: editorRoutes.comments.name,
      key: editorRoutes.comments.key,
      i18nName: editorRoutes.comments.i18nName,
      svgIcon: <SvgApplicationIcon />,
    },
    {
      route: `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.opportunities.route}`,
      name: editorRoutes.opportunities.name,
      key: editorRoutes.opportunities.key,
      i18nName: editorRoutes.opportunities.i18nName,
      svgIcon: <SvgOpportunite />,
    },
    {
      route: `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.events.route}`,
      name: editorRoutes.events.name,
      key: editorRoutes.events.key,
      i18nName: editorRoutes.events.i18nName,
      svgIcon: <SvgArticles />,
    },
    {
      route: `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.sliders.route}`,
      name: editorRoutes.sliders.name,
      key: editorRoutes.sliders.key,
      i18nName: editorRoutes.sliders.i18nName,
      svgIcon: <Svgparametres />,
    },
    // {
    //   route: `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.glossaries.route}`,
    //   name: editorRoutes.glossaries.name,
    //   key: editorRoutes.glossaries.key,
    //   i18nName: editorRoutes.glossaries.i18nName,
    //   svgIcon: <GlossaryIcon />,
    // },
    {
      route: `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.categories.route}`,
      name: editorRoutes.categories.name,
      key: editorRoutes.categories.key,
      i18nName: editorRoutes.categories.i18nName,
      svgIcon: <SvgCategories />,
    },
    {
      route: `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.notifications.route}`,
      name: editorRoutes.notifications.name,
      key: editorRoutes.notifications.key,
      i18nName: editorRoutes.notifications.i18nName,
      svgIcon: <SvgNotificationsIcon />,
    },

    {
      route: `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.settings.route}`,
      name: editorRoutes.settings.name,
      key: editorRoutes.settings.key,
      i18nName: editorRoutes.settings.i18nName,
      svgIcon: <Svgparametres />,
    },
    {
      route: `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.contacts.route}`,
      name: editorRoutes.contacts.name,
      key: editorRoutes.contacts.key,
      i18nName: editorRoutes.contacts.i18nName,
      svgIcon: <SvgApplicationIcon />,
    },
    {
      route: `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.newsletters.route}`,
      name: editorRoutes.newsletters.name,
      key: editorRoutes.newsletters.key,
      i18nName: editorRoutes.newsletters.i18nName,
      svgIcon: <SvgMail />,
    },
    {
      route: `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.seoSettings.route}`,
      name: editorRoutes.seoSettings.name,
      key: editorRoutes.seoSettings.key,
      i18nName: editorRoutes.seoSettings.i18nName,
      svgIcon: <Svgparametres />,
    },
    {
      route: `/${authRoutes.logout.route}`,
      name: authRoutes.logout.name,
      key: authRoutes.logout.key,
      i18nName: authRoutes.logout.i18nName,
      svgIcon: <SvgLogoutIcon />,
    },
  ],
};
