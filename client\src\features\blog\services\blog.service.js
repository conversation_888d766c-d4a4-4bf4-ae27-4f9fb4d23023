import { API_URLS, baseURL } from "../../../utils/urls";
import { axiosGetJson, axiosGetJsonSSR } from "../../../config/axios";
import { toast } from "react-toastify";

export const createArticle = (body) => {
  let t = body.t;
  return new Promise(async (resolve, reject) => {
    // const { t, i18n } = useTranslation();
    axiosGetJson
      .post(API_URLS.articles, body.data)
      .then((valid) => {
        toast.success("Article added successfully");
        // toast.success('msg..');
        if (valid?.data) {
          resolve(valid.data);
        }
      })
      .catch((err) => {
        if (err && err.response && err.response.data) {
        }
        if (err) {
          reject(err);
        }
      });
  });
};

export const createAutoSave = (body) => {
  let t = body.t;
  return new Promise(async (resolve, reject) => {
    // const { t, i18n } = useTranslation();
    axiosGetJson
      .post(`${API_URLS.articles}/auto`, body.data)
      .then((valid) => {
        // toast.success("Article added successfully");
        // toast.success('msg..');
        if (valid?.data) {
          resolve(valid.data);
        }
      })
      .catch((err) => {
        if (err && err.response && err.response.data) {
        }
        if (err) {
          reject(err);
        }
      });
  });
};

export const updateAutoSave = ({ data, id }) => {
  return new Promise(async (resolve, reject) => {
    axiosGetJson
      .put(`${API_URLS.articles}/${id}/auto`, data)
      .then((valid) => {
        // toast.success(`article Commun fields updated successfully`);

        if (valid?.data) {
          resolve(valid.data);
        }
      })
      .catch((err) => {
        if (err && err.response && err.response.data) {
        }
        if (err) {
          reject(err);
        }
      });
  });
};

export const updatearticleall = ({ data, id }) => {
  return new Promise(async (resolve, reject) => {
    axiosGetJson
      .put(`${API_URLS.articles}/${id}`, data)
      .then((valid) => {
        toast.success(`article Commun fields updated successfully`);

        if (valid?.data) {
          resolve(valid.data);
        }
      })
      .catch((err) => {
        if (err && err.response && err.response.data) {
        }
        if (err) {
          reject(err);
        }
      });
  });
};

export const getArticleByUrlANDlanguages = (body) => {
  return new Promise(async (resolve, reject) => {
    try {
      let response = {};
      response = await axiosGetJson.get(
        `${API_URLS.articles}/${body.language}/blog/${body.urlArticle}`
      );
      resolve(response.data);
    } catch (err) {
      if (err && err.response && err.response.data) {
        if (err.response.status === 404) {
        }
      }
      reject(err);
    }
  });
};

export const getArticleWithPrevAndNext = (body) => {
  return new Promise(async (resolve, reject) => {
    try {
      let response = {};
      response = await axiosGetJson.get(
        `${API_URLS.articles}/${body.language}/article/${body.urlArticle}`
      );
      resolve(response.data);
    } catch (err) {
      if (err && err.response && err.response.data) {
        if (err.response.status === 404) {
        }
      }
      reject(err);
    }
  });
};

export const getArticleByCategory = (body) => {
  return new Promise(async (resolve, reject) => {
    try {
      let response = {};

      response = await axiosGetJson.get(
        `${API_URLS.articles}/${body.language}/blog/category/${body.urlCategory}`,
        {
          params: {
            title: body.title,
            paginated: true,
            pageNumber: body.pageNumber,
            pageSize: body.pageSize,
          },
        }
      );
      resolve(response.data);
    } catch (err) {
      if (err && err.response && err.response.data) {
        if (err.response.status === 404) {
        }
      }
      reject(err);
    }
  });
};

export const getSlugBySlug = (body) => {
  return new Promise(async (resolve, reject) => {
    try {
      let response = {};
      response = await axiosGetJson.get(
        `${API_URLS.articles}/${body.language}/${body.urlArticle}`
      );
      resolve(response.data);
    } catch (err) {
      if (err && err.response && err.response.data) {
        if (err.response.status === 404) {
        }
      }
      reject(err);
    }
  });
};

export const updateArticle = ({ data, language, id }) => {
  return new Promise(async (resolve, reject) => {
    axiosGetJson
      .post(`${API_URLS.articles}/${language}/${id}`, data)
      .then((valid) => {
        language === "en" &&
          toast.success(`Article english updated successfully`);
        language === "fr" &&
          toast.success(`Article french updated successfully`);

        if (valid?.data) {
          resolve(valid.data);
        }
      })
      .catch((err) => {
        if (err && err.response && err.response.status === 409) {
          toast.error(err.response.data.message);
        } else if (err && err.response && err.response.status === 500) {
          toast.error("Internal Server Error");
        } else {
          toast.error("Error updating article");
        }
      });
  });
};

export const desarchiveversions = (language, id, archive) => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await axiosGetJsonSSR.put(
        `${API_URLS.articles}/${language}/${id}/desarchiver`,
        { archive }
      );
      if (response?.data) {
        toast.success(
          `Article ${archive ? "archived" : "desarchived"} successfully`
        );
        resolve(response.data);
      }
    } catch (err) {
      toast.error(
        `Failed to ${archive ? "archive" : "desarchive"} the article.`
      );
      reject(err);
    }
  });
};

export const getCategories = (language) => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await axiosGetJson.get(
        `${API_URLS.categories}/${language}/all`
      );
      resolve(response.data);
    } catch (err) {
      reject(err);
    }
  });
};

export const getarchivedArticles = (body) => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await axiosGetJson.get(`${baseURL}/articles/archived`, {
        params: {
          language: body.language,
          pageSize: body.pageSize,
          pageNumber: body.pageNumber,
          sortOrder: body.sortOrder,
          searchQuery: body.searchQuery,
          visibility: body.visibility,
          createdAt: body.createdAt,
          isArchived: body.isArchived,
          publishDate: body.publishDate,
          paginated: body.paginated,
          categoryName: body.categoryName,
        },
      });
      resolve(response.data);
    } catch (err) {
      reject(err);
    }
  });
};

export const getArticles = (body) => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await axiosGetJson.get(`${API_URLS.articles}`, {
        params: {
          paginated: body.paginated,
          language: body.language,
          pageSize: body.pageSize,
          pageNumber: body.pageNumber,
          sortOrder: body.sortOrder,
          searchQuery: body.searchQuery,
          visibility: body.visibility,
          createdAt: body.createdAt,
          publishDate: body.publishDate,
          isThreeLastArticles: body.isThreeLastArticles,
          isArchived: body.isArchived,
          categoryName: body.categoryName,
        },
      });
      resolve(response.data);
    } catch (err) {
      reject(err);
    }
  });
};

export const getArticlesDashboard = (body) => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await axiosGetJson.get(
        `${API_URLS.articles}/dashboard`,
        {
          params: {
            paginated: body.paginated,
            language: body.language,
            pageSize: body.pageSize,
            pageNumber: body.pageNumber,
            sortOrder: body.sortOrder,
            searchQuery: body.searchQuery,
            visibility: body.visibility,
            createdAt: body.createdAt,
            publishDate: body.publishDate,
            isArchived: body.isArchived,
            categoryName: body.categoryName,
          },
        }
      );
      resolve(response.data);
    } catch (err) {
      reject(err);
    }
  });
};

export const getArticlesTitles = (body) => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await axiosGetJson.get(
        `${API_URLS.articles}/${body.language}/listarticle`
      );
      resolve(response.data);
    } catch (err) {
      reject(err);
    }
  });
};

export const getComments = ({
  articleId,
  pageNumber,
  pageSize,
  sortOrder,
  name,
  approved,
  createdAt,
  paginated,
}) => {
  return new Promise(async (resolve, reject) => {
    try {
      let url = `${baseURL}/comments/${articleId}?pageSize=${encodeURIComponent(
        pageSize
      )}&pageNumber=${encodeURIComponent(
        pageNumber
      )}&sortOrder=${encodeURIComponent(
        sortOrder
      )}&paginated=${encodeURIComponent(paginated)}`;

      if (name) {
        url += `&name=${encodeURIComponent(name)}`;
      }
      if (approved) {
        url += `&approved=${encodeURIComponent(approved)}`;
      }
      if (createdAt) {
        url += `&createdAt=${encodeURIComponent(
          new Date(createdAt).toISOString()
        )}`;
      }

      const response = await axiosGetJson.get(url);
      resolve(response.data);
    } catch (err) {
      reject(err);
    }
  });
};

export const getArticleById = (id, language) => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await axiosGetJson.get(
        `${API_URLS.articles}/${language}/${id}`
      );
      resolve(response.data);
    } catch (err) {
      reject(err);
    }
  });
};

export const getCommentByiD = (id) => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await axiosGetJsonSSR.get(
        `${API_URLS.comments}/detail/${id}`
      );
      resolve(response.data);
    } catch (err) {
      reject(err);
    }
  });
};

export const getArticleByIdAll = (id, language) => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await axiosGetJson.get(`${API_URLS.articles}/${id}`);
      resolve(response.data);
    } catch (err) {
      reject(err);
    }
  });
};

export const deleteArticle = (language, id) => {
  return new Promise(async (resolve, reject) => {
    try {
      const response = await axiosGetJsonSSR.delete(
        `${API_URLS.articles}/${language}/${id}/article`
      );
      if (response?.data) {
        toast.success(`Article ${language} version deleted successfully`);
        resolve(response.data);
      }
    } catch (err) {
      toast.error(`Failed to delete the article ${language} version.`);
      reject(err);
    }
  });
};
