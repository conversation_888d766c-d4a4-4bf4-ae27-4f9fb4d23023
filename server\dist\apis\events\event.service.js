"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const event_model_1 = __importDefault(require("./event.model"));
const messages_1 = require("@/utils/helpers/messages");
const constants_1 = require("@/utils/helpers/constants");
const pako_1 = __importDefault(require("pako"));
class EventsService {
    constructor() {
        this.Event = event_model_1.default;
    }
    async create(eventData) {
        const existingslugs = new Set();
        const existingEvents = await event_model_1.default.find({}, 'versions.slug');
        existingEvents.forEach((existingEvent) => {
            existingEvent.versions.forEach((version) => {
                existingslugs.add(version.slug);
            });
        });
        const defaultLanguage = constants_1.Language.ENGLISH;
        for (const version of eventData.versions) {
            if (!version.language) {
                version.language = defaultLanguage;
            }
            const binaryString = atob(version.content);
            const byteArray = new Uint8Array(binaryString.length);
            for (let i = 0; i < binaryString.length; i++) {
                byteArray[i] = binaryString.charCodeAt(i);
            }
            const decompressedContent = pako_1.default.inflate(byteArray, { to: 'string' });
            version.content = decompressedContent;
            let slug = version.slug || version.title.toLowerCase().replace(/\s+/g, '-');
            let count = 0;
            if (existingslugs.has(slug)) {
                count = 1;
                while (existingslugs.has(`${slug}-${count}`)) {
                    count++;
                }
                slug = `${slug}-${count}`;
            }
            existingslugs.add(slug);
            version.slug = slug.toLowerCase();
            eventData.alt = eventData.alt || version.title;
        }
        return await this.Event.create(eventData);
    }
    async update(id, eventData) {
        const existingEvent = await this.Event.findOne({ _id: id });
        const existingEvents = await event_model_1.default.find({ _id: { $ne: id } }, 'versions.slug');
        if (!existingEvent) {
            throw new http_exception_1.default(404, messages_1.MESSAGES.EVENT.NOT_FOUND);
        }
        const existingslugs = new Set();
        existingEvents.forEach((existingEvent) => {
            existingEvent.versions.forEach((version) => {
                existingslugs.add(version.slug);
            });
        });
        if (eventData.versions) {
            for (const newVersion of eventData.versions) {
                const index = existingEvent.versions.findIndex(v => v.language === newVersion.language);
                let slug = newVersion.slug || newVersion.title.toLowerCase().replace(/\s+/g, '-');
                let count = 0;
                while (existingslugs.has(slug)) {
                    count++;
                    slug = `${slug}-${count}`;
                }
                existingslugs.add(slug);
                newVersion.slug = slug.toLowerCase();
                if (index !== -1) {
                    existingEvent.versions[index] = {
                        ...existingEvent.versions[index],
                        ...newVersion,
                    };
                }
                else {
                    existingEvent.versions.push(newVersion);
                }
            }
        }
        if (eventData.robotsMeta) {
            existingEvent.robotsMeta = eventData.robotsMeta;
        }
        if (eventData.image) {
            existingEvent.image = eventData.image;
        }
        if (eventData.imageList) {
            existingEvent.imageList = eventData.imageList;
        }
        if (eventData.mobileImage) {
            existingEvent.mobileImage = eventData.mobileImage;
        }
        if (eventData.country) {
            existingEvent.country = eventData.country;
        }
        if (eventData.eventDate) {
            existingEvent.eventDate = eventData.eventDate;
        }
        return await existingEvent.save();
    }
    async getSlugBySlug(language, slug) {
        const event = await event_model_1.default.findOne({ 'versions.language': language, 'versions.slug': slug.toLocaleLowerCase() });
        if (!event)
            throw new http_exception_1.default(404, messages_1.MESSAGES.EVENT.NOT_FOUND);
        const targetLanguage = language === 'en' ? 'fr' : 'en';
        const selectedVersions = event.versions.filter(version => version.language === targetLanguage);
        if (selectedVersions.length === 0)
            throw new http_exception_1.default(404, messages_1.MESSAGES.EVENT.EVENT_VERSION_NOT_FOUND);
        return {
            slug: selectedVersions[0].slug,
        };
    }
    async getEventBySlug(slug, language) {
        const projection = {
            robotsMeta: 1,
            createdAt: 1,
            updatedAt: 1,
            image: 1,
            mobileImage: 1,
            country: 1,
            eventDate: 1,
            versions: { $elemMatch: { language: language } },
        };
        const events = await this.Event.findOne({ versions: { $elemMatch: { slug, language } } }, projection);
        if (!events)
            throw new http_exception_1.default(204, messages_1.MESSAGES.EVENT.NOT_FOUND + slug);
        return events;
    }
    async deleteevent(eventId) {
        try {
            const deleteevent = await this.Event.findByIdAndDelete(eventId).exec();
            if (!deleteevent) {
                throw new http_exception_1.default(404, messages_1.MESSAGES.EVENT.NOT_FOUND);
            }
            return {
                message: messages_1.MESSAGES.EVENT.DELETED,
                deleteevent,
            };
        }
        catch (error) {
        }
    }
    async getEventTagById(id) {
        const event = await this.Event.findById(id);
        if (!event)
            throw new http_exception_1.default(204, messages_1.MESSAGES.EVENT.NOT_FOUND + id);
        return event;
    }
    async getAll(queries) {
        const { slug, paginated, language, sortOrder, isArchived, visibility } = queries;
        const pageNumber = Number(queries.pageNumber) || 1;
        const pageSize = Number(queries.pageSize) || 10;
        const targetLanguage = language || 'en';
        const query = {
            'versions.language': targetLanguage,
        };
        if (visibility)
            query['versions.visibility'] = visibility;
        if (isArchived)
            query['versions.isArchived'] = isArchived;
        if (slug) {
            query['versions.slug'] = new RegExp(`.*${slug}.*`, 'i');
        }
        const sortCriteria = {};
        if (sortOrder) {
            sortCriteria['createdAt'] = sortOrder === 'asc' ? 1 : -1;
        }
        let Events = [];
        let totalEvents;
        let totalPages;
        const projection = {
            slug: 1,
            robotsMeta: 1,
            createdAt: 1,
            updatedAt: 1,
            image: 1,
            mobileImage: 1,
            imageList: 1,
            country: 1,
            eventDate: 1,
            versions: { $elemMatch: { language: targetLanguage } },
        };
        if (paginated && paginated === 'false') {
            Events = await this.Event.find(query, projection).sort(sortCriteria);
            totalEvents = await this.Event.countDocuments(query);
            return { totalEvents, Event };
        }
        else {
            Events = await this.Event.find(query, projection)
                .sort(sortCriteria)
                .skip((pageNumber - 1) * pageSize)
                .limit(pageSize);
            totalEvents = await this.Event.countDocuments(query);
            totalPages = Math.ceil(totalEvents / pageSize);
            return {
                pageNumber,
                pageSize,
                totalEvents,
                totalPages,
                Events,
            };
        }
    }
    async archiveVersion(id, language, status) {
        const existingEvent = await this.Event.findOne({ _id: id });
        if (!existingEvent) {
            throw new http_exception_1.default(404, messages_1.MESSAGES.EVENT.NOT_FOUND);
        }
        const versionIndex = existingEvent.versions.findIndex(v => v.language === language);
        if (versionIndex === -1) {
            throw new http_exception_1.default(404, messages_1.MESSAGES.EVENT.EVENT_VERSION_NOT_FOUND);
        }
        existingEvent.versions[versionIndex].isArchived = status;
        return await existingEvent.save();
    }
    async archiveAll(id, status) {
        const existingEvent = await this.Event.findOne({ _id: id });
        if (!existingEvent) {
            throw new http_exception_1.default(404, messages_1.MESSAGES.EVENT.NOT_FOUND);
        }
        existingEvent.versions.forEach(version => {
            version.isArchived = status;
        });
        return await existingEvent.save();
    }
}
exports.default = EventsService;
//# sourceMappingURL=event.service.js.map