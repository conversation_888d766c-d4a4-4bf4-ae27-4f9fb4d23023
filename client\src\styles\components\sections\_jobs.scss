#latest-offers {
  padding: 50px 0;

  .container {
    justify-content: center;
  }

  .items {
    margin: 20px 0;
  }

  .no-results-message {
    margin: 80px 0;
    text-align: center;
    font-size: 18px;
  }

  .spinner {
    margin: 80px 0;
  }

  .center-div {
    margin-top: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .job-item {
    margin: 10px;
    border-radius: 12px;
    padding: 15px !important;
    background-color: $lightBlue2;
    display: flex;

    .left-section {
      width: 60%;
      justify-content: space-between;
    }

    .right-section {
      width: 40%;
      display: "flex";
      flex-direction: "column";
      align-items: "flex-end";
      justify-content: center;
    }

    .job-title {
      font-size: 18px;
      color: $blue;
      font-family: "Proxima-Nova-Semibold" !important;
    }

    .job-ref {
      font-size: 14px;
      color: $grey;
    }

    .job-industry {
      font-size: 12px;
      width: fit-content;
      font-family: "Proxima-Nova-Semibold" !important;
      text-transform: capitalize;
      color: $blue;
      display: flex;
      align-items: center;
      @include industriesTextColors();
    }

    .btn.apply {
      padding: 8px 18px !important;
      margin-top: 10px;
      width: fit-content;
    }

    .map-img {
      height: auto;
      margin: auto;
      max-height: 200px;
      max-width: fit-content;
    }
  }
}

.map-home-page {
  color: transparent;
  width: 100px;
  height: 80px;
}

#filter-btns {
  display: flex;
  justify-content: center;

  .btn {
    margin: 10px;
  }
}

#search-bar-opportunities {
  .sub-heading {
    font-family: "Proxima-Nova-Medium" !important;
  }

  background-color: $lightBlue2;

  padding-top: 10px;
  padding-bottom: 25px;

  .container {
    padding: 0;
    // @include dropShadow();
  }

  input {
    padding: 15px;
  }

  .MuiInputAdornment-root {
    margin-left: 10px;
  }
  .btns-filter {
    @include media-query(mobile) {
      flex-direction: row;
      display: flex;
      background-color: transparent !important;
      /* justify-content: center; */
      /* align-items: stretch; */
      /* flex-direction: column; */
    }
  }
  .filter-inputs {
    @include dropShadow();

    @include media-query(mobile) {
      box-shadow: none;
    }

    .MuiGrid-item {
      @include media-query(mobile) {
        margin: 10px 0;
      }
    }

    .input-pentabell {
      // @include dropShadow();
    }

    //
    .MuiGrid-item:first-child,
    .MuiGrid-item:nth-child(2) {
      // border-right: 1px solid $grey;
      // padding-right: 5px;

      @include media-query(mobile) {
        border-right: 0;
        padding-right: 0;
        // border-bottom: 1px solid $grey;
      }
    }
  }

  .MuiInputBase-root,
  .css-r5in8e-MuiInputBase-root-MuiInput-root,
  .css-10yf2i8-MuiInputBase-root-MuiInput-root {
    &.Mui-focused::before,
    &::after,
    &:focus,
    &::before,
    &:hover {
      border-bottom: 0;
    }
  }
}

.btns-filter {
  // background-color: $lightBlue2;
  // @include dropShadow();
  align-items: center;

  &.dashboard {
    background: transparent;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-direction: row;

    .btn-filled {
      width: 80%;
      text-align: center;
      display: block;
    }
  }

  @include media-query(mobile) {
    background-color: transparent !important;
    justify-content: center;
    align-items: stretch;
    flex-direction: column;
  }

  .btn {
    padding: 10px 16px !important;

    @include media-query(mobile) {
      margin-bottom: 10px;
      display: flex;
      justify-content: center;
    }
  }

  .btn-search {
    padding: 10px 55px !important;
  }

  .btn-refresh {
    svg {
      margin: 0 !important;
    }
  }

  display: flex;
  justify-content: space-evenly;

  @include media-query(mobile) {
    margin-top: 10px;
    margin-right: 10px;
  }
}

.job-title {
  font-size: 18px;
  color: $blue;
  font-family: "Proxima-Nova-Semibold" !important;
}

.job-ref {
  font-size: 14px;
  color: $grey;

  @include media-query(mobile) {
    margin-bottom: 10px;
  }
}

.job-industry {
  font-size: 12px;
  width: fit-content;
  font-family: "Proxima-Nova-Semibold" !important;
  text-transform: capitalize;
  color: $blue;
  display: flex;
  align-items: center;
  @include industriesTextColors();

  &.banner {
    @include industriesBgColors();
    color: $white !important;
    font-size: 14px;
    margin-left: 0 !important;
  }

  &.border {
    margin-left: 10px;
    @include industriesBorderColors();
    padding: 1px 10px;
    border-radius: 20px;
    border: 1px solid;
  }
}

#opportunities {
  margin: 50px 0;

  @include media-query(mobile) {
    margin: 20px 0;
  }

  .items {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;

    @include media-query(mobile) {
      margin: auto;
    }
  }

  .grid {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-content: flex-start;
    justify-content: space-between;

    @include media-query(mobile, tablet) {
      margin-top: 20px;
    }
  }

  .sub-heading {
    font-family: "Proxima-Nova-Medium" !important;
  }
}

.opportunities-nbr {
  margin-left: 10px;
  background-color: $yellow;
  padding: 2px 4px;
}

.button-pointer {
  display: contents;
}

.opportunity-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  @include dropShadow();
  padding: 30px;
  max-width: 100%;
  margin: 10px auto;

  .apply {
    font-size: 16px;
    padding: 10px 15px;
    cursor: pointer;

    @include media-query(mobile) {
      margin-top: 15px;
    }
  }

  .item-image {
    padding-right: 10px;
    display: flex;
    justify-content: center;
    align-items: center;

    &.opportunity-apply {
      @include media-query(mobile) {
        margin-top: 20px;
      }
    }

    .map-img {
      width: "110px";
      height: "70px";
      margin: auto;
      max-height: "64px";

      @include media-query(mobile) {
        text-align: center;
        display: flex;
        max-height: 108px;
      }
    }
  }

  .margin-section-item {
    margin-bottom: 12px;
  }

  .job-ref {
    font-size: 14px;
    padding: 2px;
    color: $grey;
  }

  .location {
    border: 1px solid $blue;
    border-radius: 20px;
    padding: 2px 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: fit-content;
    margin-left: 10px;
    text-decoration: none;

    .location-text {
      font-size: 12px;
      margin-left: 5px;
      color: $blue;
    }
  }

  .job-description {
    font-size: 14px;
    color: $blue;
    max-width: 80%;

    @include media-query(mobile) {
      max-width: 100%;
    }
  }

  .flex {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 10px;

    @include media-query(mobile) {
      margin-bottom: 5px;
    }

    &.mobile-col {
      @include media-query(mobile) {
        flex-direction: column;
      }

      .border {
        @include media-query(mobile) {
          margin-top: 5px;
          margin-left: 0;
        }
      }
    }

    &.row {
      flex-direction: row;
      flex-wrap: nowrap;
      justify-content: space-between;
    }
  }

  .item-content,
  .item-btns {
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    &.opportunity-apply {
      @include media-query(mobile) {
        margin-top: 20px;
      }
    }
  }

  .job-contract {
    @include media-query(mobile) {
      margin-bottom: 10px;
    }
    @include job-badge(rgba(61, 184, 78, 0.1), $contractColor);
  }

  .job-time {
    margin-left: 10px;

    @include media-query(mobile) {
      margin-left: 0px;
    }
    @include job-badge(rgba(29, 90, 159, 0.18), $creationDateColor);
  }

  .job-time-apply,
  .job-contract-apply {
    @include media-query(mobile) {
      margin-bottom: 10px;
      margin-left: 0px !important;
    }
    font-size: 14px;
    display: flex;
    align-items: center;
    background: "transparent";
    padding: 2px;
    margin-right: 10px;
    color: $grey;

    svg {
      margin-right: 5px;

      path {
        fill: $grey;
      }
    }
  }

  .job-deadline {
    margin-left: 10px;

    @include media-query(mobile) {
      margin-left: 0px;
    }
    @include job-badge(rgba(252, 1, 1, 0.16), $deadlineColor);
  }

  .item-btns {
    align-items: flex-end;

    @include media-query(mobile) {
      flex-direction: row-reverse;
      justify-content: space-between;
    }
  }

  .btn-outlined {
    padding: 10px 15px;

    @include media-query(mobile) {
      text-align: center;
      display: flex;
      justify-content: center;
      width: -webkit-fill-available;
    }
  }

  .bookmark {
    padding: 0px;

    @include media-query(mobile) {
      // width: 10%;
      padding: 5px;
    }
  }
}

.opportunity-grid-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  @include dropShadow();
  padding: 15px;
  max-width: 49%;
  margin: 10px 0;

  &.website {
    max-width: 40%;
    margin: 10px;
    background: #dbe8f6;
    box-shadow: none;

    @include media-query(mobile, tablet, laptops) {
      max-width: 100%;
      align-items: flex-start;
      margin: 10px 30px;
    }
  }

  @include media-query(mobile, tablet, laptops) {
    max-width: 100%;
    align-items: flex-start;
  }

  .apply {
    font-size: 14px;
    padding: 5px 15px;
    cursor: pointer;

    @include media-query(mobile) {
      font-size: 16px;
      margin-top: 0px;
    }
  }

  .item-image {
    padding-right: 10px;
    display: flex;
    justify-content: center;
    align-items: center;

    @include media-query(mobile) {
      justify-content: flex-start;
    }

    @include media-query(tablet) {
      justify-content: flex-start;
      padding-left: 15px;
    }

    .map-img {
      width: "110px";
      height: "70px";
      margin: auto;
      max-height: "64px";

      @include media-query(mobile) {
        text-align: center;
        display: flex;
        max-height: 108px;
      }
    }
  }

  .margin-section-item {
    margin-bottom: 12px;
  }

  .job-ref {
    font-size: 13px;
    padding: 2px;
    color: $grey;
  }

  .location {
    border: 1px solid $blue;
    border-radius: 20px;
    padding: 2px 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: fit-content;
    text-decoration: none;

    .location-text {
      font-size: 14px;
      margin-left: 5px;
      color: $blue;
    }
  }

  .job-description {
    font-size: 14px;
    color: $blue;
    max-width: 80%;

    @include media-query(mobile) {
      max-width: 100%;
    }
  }

  .flex {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 10px;
    align-items: center;

    @include media-query(mobile) {
      justify-content: space-between;
      margin-bottom: 0px;
    }

    &.contract {
      @include media-query(mobile) {
        margin-top: 5px;
      }
    }

    &.mobile-col {
      @include media-query(mobile) {
        flex-direction: column;
      }

      .border {
        @include media-query(mobile) {
          margin-top: 5px;
          margin-left: 0;
        }
      }
    }

    &.row {
      flex-direction: row;
      flex-wrap: nowrap;
      justify-content: space-between;
      align-items: center;

      @include media-query(mobile) {
        align-items: flex-start;
      }
    }
  }

  .item-content,
  .item-btns {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .item-apply {
    @include media-query(tablet) {
      display: flex;
      justify-content: space-between;
    }
  }

  .job-contract,
  .job-deadline {
    @include media-query(mobile) {
      margin-bottom: 10px;
      margin-left: 0px !important;
    }
    font-size: 13px;
    display: flex;
    align-items: center;
    background: "transparent";
    padding: 2px;
    margin-left: 10px;
    color: $grey;

    svg {
      margin-right: 5px;

      path {
        fill: $grey;
      }
    }
  }

  .item-btns {
    align-items: flex-end;

    @include media-query(mobile) {
      flex-direction: row-reverse;
      justify-content: space-between;
    }
  }

  .btn-outlined {
    padding: 5px 15px;

    @include media-query(mobile) {
      text-align: center;
      display: flex;
      justify-content: center;
      width: -webkit-fill-available;
      padding: 15px;
    }
  }

  .bookmark {
    padding: 0px;

    @include media-query(mobile) {
      padding: 0px;
    }
  }
}

.sidebar {
  .section {
    background-color: #e5f0fc;
    padding: 15px;
    margin-bottom: 20px;

    .title {
      font-size: 20px;
      font-family: "Proxima-Nova-Semibold" !important;
      color: $bankingColor;
      margin: 10px 0;
    }

    .description {
      color: $bankingColor;
      margin-bottom: 10px;
      font-size: 16px;
    }

    .btn {
      margin-top: 20px;
      width: -webkit-fill-available;
      text-align: center;
      display: flex;
      justify-content: center;
    }

    .last-blog {
      position: relative;

      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          to top,
          $bankingColor,
          $bankingColorOpacity0
        );
        opacity: 0.6;
        pointer-events: none;
        /* Ensure gradient doesn't interfere with clicks */
      }

      .card-media {
        height: 185px;

        @include media-query(tablet) {
          height: 136px;
        }
      }

      .card-text {
        color: $white;
        position: absolute;
        bottom: 10px;
        left: 10px;
        font-size: 16px;
        padding: 0px 2px;
        font-family: "Proxima-Nova-Semibold" !important;

        button {
          margin: 0;
          padding: 10px 0px;
          justify-content: left;
        }

        a {
          margin: 0;
          padding: 10px 0px;
          justify-content: left;
        }
      }
    }
  }
}

#one-opportunity-page {
  .container {
    margin-top: 20px;
  }

  .job-info {
    display: flex;
    flex-direction: row;
    margin: 10px 0;
  }

  #banner-job-info {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: wrap;

    .job-info {
      a {
        text-decoration: none;
      }

      // margin-right: 20px;
      margin: 4px 20px 4px 0 !important;
    }
  }

  .children-componenent,
  #banner-job-info {
    p {
      color: $white;
      font-size: 18px;
    }

    svg {
      margin-right: 8px;
      transform: scale(1.4);
      margin: auto 8px auto 0;

      path {
        fill: $white;
      }
    }
  }

  .flex {
    display: flex;
    flex-direction: column;
  }

  .flex-apply {
    display: flex;
    flex-direction: column;
  }

  .related-opportunities {
    @extend #opportunities;
    margin-top: 20px !important;
    margin-bottom: 20px !important;
  }

  .opportunity-content {
    padding: 15px;
    @include dropShadow();

    p,
    ul,
    div,
    li {
      background: $lightBlue !important;
    }
  }

  .apply-section {
    margin-top: 20px;
    @include dropShadow();
    background-color: $blue;
    padding: 20px;

    & > div:first-child {
      margin: auto;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      width: 60%;

      @include media-query(mobile) {
        width: 100%;
      }

      // background-color: $lightBlue;
    }

    .btns {
      flex-direction: row;
      display: flex;

      .btn {
        margin: auto 10px;
      }
    }
  }

  .opportunity-info {
    // background-color: $bankingColor;
    margin-bottom: 20px;
    @include linearGradientBlueBanking();
    padding: 15px;
    color: $white;

    .title {
      margin-top: 10px !important;
      font-size: 24px;
      font-family: "Proxima-Nova-Semibold" !important;
    }

    .job-ref {
      font-size: 20px;
      color: $white;
      margin-bottom: 20px;
      margin-top: 10px;
    }
  }

  .job-btns {
    display: flex;
    width: 100%;
    justify-content: space-between;

    @include media-query(tablet) {
      flex-wrap: wrap;
    }

    .btn {
      margin: 8px 0;

      &:first-child {
        margin-right: 8px;
      }

      &:last-child {
        margin-left: 8px;
      }

      width: -webkit-fill-available;
      text-align: center;
      display: flex;
      justify-content: center;

      @include media-query(tablet) {
        margin: 8px 0 !important;
      }

      &.apply {
        margin: 8px 0;

        @include media-query(tablet) {
          margin: 8px 0 !important;
        }
      }
    }
  }
}

#last-opportunities-inhouse-section {
  padding-top: 50px;
  padding-bottom: 50px;

  .heading-h1 {
    margin-bottom: 50px !important;
  }

  .view-more {
    margin: 30px auto auto auto !important;
    width: fit-content;
  }
}

.flex-apply {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10px;

  @include media-query(mobile) {
    margin-bottom: 5px;
  }

  &.mobile-col {
    @include media-query(mobile) {
      flex-direction: column;
    }

    .border {
      @include media-query(mobile) {
        margin-top: 5px;
        margin-left: 0;
      }
    }
  }

  &.row {
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
  }
}

.flex-item {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 5px;

  @include media-query(mobile) {
    margin-bottom: 5px;
  }

  &.mobile-col {
    @include media-query(mobile) {
      flex-direction: column;
    }

    .border {
      @include media-query(mobile) {
        margin-top: 5px;
        margin-left: 0;
      }
    }
  }

  &.row {
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
  }
}

@include media-query(tablet, mobile) {
  .job-contrat-time {
    display: flex;
    flex-direction: column;
    align-content: stretch;
    flex-wrap: wrap;

    align-items: baseline;
    align-items: flex-start;
  }

  .job-time {
    margin-top: 10px;
    margin-left: 0px !important;
  }

  .map-img {
    max-height: 102;
  }
}

.job-contrat-time {
  display: flex;
  flex-wrap: wrap;
  align-content: stretch;
  justify-content: flex-start;

  &.contract {
  }
}

.filter-options {
  display: flex;
  flex-direction: column;
}

.filter-popup-content {
  display: flex;
  border: 1px;
  box-shadow: 0px 10px 40px rgba(10, 48, 82, 0.0784313725);
  /* padding: 10px; */
  margin: 10px 4px 0px 4px;
  flex-direction: column;
  height: auto;
  overflow: auto;
  /* scroll-behavior: smooth; */
  scrollbar-width: none;

  .css-7iurs7-MuiAccordionSummary-content {
    margin: 0px 0 !important;
  }
}

.title-filter {
  font-size: 18px;
  color: #0b3051;
  font-family: "Proxima-Nova-Semibold" !important;
}

.label-filter {
  font-size: 16px;
  color: #798ba3;
  font-family: "Proxima-Nova-Medium" !important;
}

.chip {
  background-color: transparent;
  border-color: #1d5a9f !important;
  padding: 8px 20px !important;
  border: 1px solid;
  margin-top: 10px !important;

  .css-1dybbl5-MuiChip-label {
    color: #1d5a9f !important;

    font-family: "Proxima-Nova-Medium" !important;
    font-size: 14px;
  }
}

.display {
  display: flex;
  justify-content: space-between;
}

.titre {
  margin-right: 26%;
}

.opportunity-chip {
  display: flex;
  flex-direction: column;
}

.grid-list-buttons {
  display: flex;
  width: 70px;
  justify-content: space-between;
  align-items: center;

  button {
    padding: 0px;

    &.active {
      svg path {
        fill: $yellow;
      }
    }
  }

  svg:hover {
    path {
      fill: $yellow;
    }
  }
}

#filter-actions {
  .filter-actions {
    display: flex;
    flex-direction: row;
    justify-content: space-evenly;
    padding: 10px;

    .btn {
      padding: 10px 35px !important;
    }

    @include media-query(mobile) {
      flex-direction: column;

      .btn {
        @include media-query(mobile) {
          margin-bottom: 10px;
          display: flex;
          padding: 10px;
          justify-content: center;
        }
      }
    }
  }

  .css-1ll44ll-MuiOutlinedInput-notchedOutline {
    box-shadow: 0px 10px 40px rgba(10, 48, 82, 0.0784313725) !important;
    border-color: transparent !important;
  }
}

.MuiCheckbox-root {
  z-index: 1;
}
#guide-item {
  .blog-description {
    margin-bottom: 20px !important;
  }
}
