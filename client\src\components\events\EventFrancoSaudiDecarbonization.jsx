"use client";
import { Container, Grid } from "@mui/material";
import pentabellFrance from "../../assets/images/events/pentabellFrance.png";
import leap from "../../assets/images/events/leap.png";
import ProcessHtml from "./ProcessHtml";
import EventsCard from "./EventsCard";
import InfoSection from '@/components/ui/InfoSection';
import Link from "next/link";
import image1 from "@/assets/images/french-decarbonization/image1.png";
import image2 from "@/assets/images/french-decarbonization/image2.png";
import image3 from "@/assets/images/french-decarbonization/image3.png";
import image4 from "@/assets/images/french-decarbonization/image4.png";
import image5 from "@/assets/images/french-decarbonization/image5.png";
import image6 from "@/assets/images/french-decarbonization/image6.png";
import image7 from "@/assets/images/french-decarbonization/image7.png";
import image8 from "@/assets/images/french-decarbonization/image8.png";
import image9 from "@/assets/images/french-decarbonization/image9.png";
import image10 from "@/assets/images/french-decarbonization/image10.png";
import imagemobile1 from "@/assets/images/french-decarbonization/imagemobile1.png";
import imagemobile2 from "@/assets/images/french-decarbonization/imagemobile2.png";
import imagemobile3 from "@/assets/images/french-decarbonization/imagemobile3.png";
import imagemobile4 from "@/assets/images/french-decarbonization/imagemobile4.png";
import imagemobile5 from "@/assets/images/french-decarbonization/imagemobile5.png";
import imagemobile6 from "@/assets/images/french-decarbonization/imagemobile6.png";
import imagemobile7 from "@/assets/images/french-decarbonization/imagemobile8.png";
import imagemobile8 from "@/assets/images/french-decarbonization/imagemobile9.png";
import EmblaCarousel from "@/components/embla_slider/EmblaCarousel";
import libya from "../../assets/images/events/libya.png";
import { API_URLS } from "@/utils/urls";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { axiosGetJson } from "@/config/axios";

export default function EventFrancoSaudiDecarbonization({ language, event }) {
    const { t } = useTranslation();
    const [events, setEvents] = useState();
    const fetchEvents = async () => {
        try {
            const response = await axiosGetJson.get(API_URLS.events, {
                params: {
                    visibility: "Public",
                    pageNumber: 1,
                    pageSize: 4,
                    language: language,
                },
            });
            let { Events } = response.data;

            const eventsFiltered = Events?.filter(
                (currentEvent) => currentEvent._id !== event?._id
            ).slice(0, 3);
            setEvents(eventsFiltered);
        } catch (error) {
            if (process.env.NODE_ENV === "dev")
                console.error("Failed to fetch events:", error);
        }
    };

    useEffect(() => {
        fetchEvents();
    }, [event, language]);

    const OPTIONS = {};
    const SLIDES = [
        image1,
        image2,
        image3,
        image4,
        image5,
        image6,
        image7,
        image8,
        image9,
        image10,
    ];
    const SlidesMobile = [
        imagemobile1,
        imagemobile2,
        imagemobile3,
        imagemobile4,
        imagemobile5,
        imagemobile6,
        imagemobile7,
        imagemobile8,

    ]


    let infoSection;
    if (event) {
        infoSection = {
            sector: event?.versions[0]?.sector,
            country: t(`country:${event?.country?.replace(/\s+/g, "")}`),
            countryConcerned: event?.versions[0]?.countryConcerned,
            organiser: event?.versions[0]?.organiser,
        };
    }
    const eventData = [
        {
            title: t("event:events.eventLibya.title"),
            eventDate: t("event:events.eventLibya.eventDate"),
            postingDate: t("event:events.eventLibya.postingDate"),
            exactPlace: t("event:events.eventLibya.exactPlace"),
            link: "Libyan-French-economic-forum-2025",
            type: "events",
            image: libya,
        },
        {
            title: t("event:events.event11.title"),
            eventDate: t("event:events.event11.eventDate"),
            postingDate: t("event:events.event11.postingDate"),
            exactPlace: t("event:events.event11.exactPlace"),
            type: "events",
            link: `leap-tech-conference-2025-riyadh`,
            image: leap,
        },
        {
            title: t("event:events.event2.title"),
            eventDate: t("event:events.event2.eventDate"),
            postingDate: t("event:events.event2.postingDate"),
            exactPlace: t("event:events.event2.exactPlace"),
            link:
                language === "en"
                    ? `pentabell-salon-sme-and-european-microwave-week`
                    : "pentabell-salon-sme-and-european-microwave-week",
            type: "blog",
            image: pentabellFrance,
        },
    ];
    const decarbonizationSection = {
        sector: t("eventDetails:sectorValue"),
        country: t("eventDetails:locationValue"),
        countryConcerned: t("eventDetails:countryConcernedValue"),
        organiser: t("eventDetails:organiserValue"),
    };
    return (
        <div id="event-page">
            {event ? (
                <div id="event-detail">
                    <div className="custom-max-width">
                        <InfoSection t={t} infos={infoSection} />{" "}
                        <ProcessHtml htmlString={event?.versions[0]?.content} />
                        <Grid
                            className="more-events-section"
                            container
                            rowSpacing={0}
                            columnSpacing={3}
                        >
                            {" "}
                            {events?.length > 0 &&
                                events?.map((event, index) => (
                                    <EventsCard
                                        key={index}
                                        eventData={event}
                                        language={language}
                                        isEvent={true}
                                    />
                                ))}{" "}
                        </Grid>
                    </div>{" "}
                </div>
            ) : (
                <div id="event-detail">
                    <Container className="custom-max-width">
                        <InfoSection t={t} infos={decarbonizationSection} />
                        <div className="details">
                            <p className="heading-h1">
                                {t("eventDetails:aboutEvent:aboutEvent")}
                            </p>
                            <p className="text">
                                <Link href={"https://www.businessfrance.fr/"}>
                                    {t("eventDetails:aboutEvent:business")}
                                </Link>
                                {t("eventDetails:aboutEvent:description1")}
                                <Link href={"https://misa.gov.sa/"}>
                                    {t("eventDetails:aboutEvent:ministry")}
                                </Link>
                                {t("eventDetails:aboutEvent:description11")}
                            </p>
                            <p className="text">
                                {t("eventDetails:aboutEvent:description2")}
                            </p>
                            <p className="text">
                                {t("eventDetails:aboutEvent:description3")}
                            </p>
                            <p className="text">
                                {t("eventDetails:aboutEvent:description4")}
                            </p>
                            <p className="text">
                                {t("eventDetails:aboutEvent:description5")}
                            </p>
                            <p className="heading-h1">
                                {t("eventDetails:eventProgram:eventProgram")}
                            </p>
                            <p className="text">
                                {t("eventDetails:eventProgram:data11")}{" "}
                                <Link
                                    href={
                                        language === "en"
                                            ? `/hr-services/`
                                            : `/${language}/hr-services/`
                                    }
                                >
                                    {t("eventDetails:eventProgram:data12")}
                                </Link>
                                {t("eventDetails:eventProgram:data13")}
                            </p>

                            <p className="text">{t("eventDetails:eventProgram:data2")}</p>
                            <ul>
                                <li className="text">{t("eventDetails:eventProgram:puce1")}</li>
                                <li className="text">
                                    {" "}
                                    {t("eventDetails:eventProgram:puce2")}
                                </li>
                                <li className="text">{t("eventDetails:eventProgram:puce3")}</li>
                            </ul>
                            <p className="text">{t("eventDetails:eventProgram:data3")}</p>
                            <EmblaCarousel slides={SLIDES} options={OPTIONS} slidesMobile={SlidesMobile} />
                            <p className="heading-h1">{t("eventDetails:moreEvents")}</p>
                        </div>
                        <Grid
                            className="more-events-section"
                            container
                            rowSpacing={0}
                            columnSpacing={3}
                        >
                            {eventData?.map((event, index) => (
                                <EventsCard
                                    key={index}

                                    eventData={event}
                                    language={language}
                                    isEvent={true}
                                />
                            ))}
                        </Grid>
                    </Container>
                </div>
            )}
        </div>
    );
}