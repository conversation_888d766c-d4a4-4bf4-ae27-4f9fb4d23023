import { Container } from "@mui/material";

function HrSolutionsSection({ title, items }) {
  if (!items || items.length === 0) return null;

  return (
    <Container id="hr-solution-africa-section" className="custom-max-width">
      <h2 className="heading-h1 text-center">{title}</h2>
      <div className="locations">
        {items.map((item, index) => (
          <div key={index} className="location-item four-items">
            <h3 className="label">{item.title}</h3>
            <p className="value paragraph">{item.description}</p>
          </div>
        ))}
      </div>
    </Container>
  );
}

export default HrSolutionsSection;
