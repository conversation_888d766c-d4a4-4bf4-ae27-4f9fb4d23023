"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.io = void 0;
exports.initialiseSocket = initialiseSocket;
exports.sendNotification = sendNotification;
const socket_io_1 = require("socket.io");
const http_exception_1 = __importDefault(require("../exceptions/http.exception"));
const services_1 = require("../services");
const functions_1 = require("../helpers/functions");
const notification_model_1 = __importDefault(require("@/apis/notifications/notification.model"));
const users = {};
const Notification = notification_model_1.default;
function initialiseSocket(server) {
    exports.io = new socket_io_1.Server(server, {
        cors: {
            origin: process.env.CORS_DOMAIN,
            methods: ['GET', 'POST'],
            credentials: true,
        },
        path: '/api/v1/socket.io',
    });
    const getTokenFromSocket = (socket, tokenName) => {
        return (socket.request.headers.cookie
            ?.split('; ')
            .find((cookie) => cookie.startsWith(`${tokenName}=`))
            ?.split('=')[1] || null);
    };
    exports.io.use(async (socket, next) => {
        try {
            const accessToken = getTokenFromSocket(socket, 'accessToken');
            const refreshToken = getTokenFromSocket(socket, 'refreshToken');
            if (!accessToken || !refreshToken) {
                throw new http_exception_1.default(401, 'Unauthorized');
            }
            const { payload } = await services_1.auth.verifyToken(accessToken, String(process.env.ACCESS_TOKEN_PRIVATE_KEY));
            if (payload) {
                socket.request.user = payload;
                return next();
            }
        }
        catch (err) {
            next(new http_exception_1.default(500, `Socket.IO Authentication error: ${err.message}`));
        }
    });
    exports.io.on('connection', async (socket) => {
        const user = socket.request.user;
        if (user) {
            const userId = user._id;
            users[userId] = socket.id;
            socket.on('disconnect', () => {
                delete users[userId];
            });
            const notifications = await Notification.find({ receiver: userId }).sort({ createdAt: -1 }).limit(4);
            const mappedNotifications = await (0, functions_1.mapNotifications)(notifications);
            exports.io.to(socket.id).emit('initial-notifications', mappedNotifications);
        }
        else {
            socket.disconnect();
        }
    });
}
async function sendNotification(data) {
    const { receiver } = data;
    const userSocketId = users[receiver];
    const createdNotification = await Notification.create({
        sender: data.sender,
        receiver: data.receiver,
        message: data.message,
        description: data.description,
        type: data.type,
        link: data.link,
    });
    const mappedNotification = await (0, functions_1.mapNotifications)([createdNotification]);
    if (userSocketId) {
        exports.io.to(userSocketId).emit('notification', mappedNotification);
    }
}
//# sourceMappingURL=socket.js.map