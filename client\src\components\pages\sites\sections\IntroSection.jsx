"use client";
import { useTranslation } from "react-i18next";
import { Container } from "@mui/material";
import PentabellByNumbers from "./PentabellByNumbers";

function IntroSection({ locale }) {
  const { t } = useTranslation();

  return (
    <>
      <Container className="responsive-row-title-text custom-max-width" >
        <div className="grid-item">
          <h1 className="section heading-h1">
            {t("homePage:introSection:title")}
          </h1>
        </div>
        <div className="grid-item">
          <p className={`sub-heading bold`}>
            {t("homePage:introSection:description")}
          </p>
          <p className={"sub-heading"}>
            {t("homePage:introSection:description1")}
          </p>
        </div>
      </Container>

      <PentabellByNumbers locale={locale} />
    </>
  );
}

export default IntroSection;
