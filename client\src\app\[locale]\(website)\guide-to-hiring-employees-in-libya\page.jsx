import banner from "@/assets/images/website/banner/Pentabell-Libya.webp";
import BannerComponents from "@/components/pages/sites/sections/BannerComponents";
import OurPartners from "@/components/pages/sites/sections/OurPartners";
import ResponsiveRowTitleText from "@/components/ui/ResponsiveRowTitleText";
import GlobalHRServicesSection from "@/components/pages/sites/sections/GlobalHRServicesSection";
import r1 from "@/assets/images/services/recrutement1.png";
import r2 from "@/assets/images/services/recrutement2.png";
import r3 from "@/assets/images/services/recrutement3.png";
import r4 from "@/assets/images/services/recrutement4.png";
import serviceImg from "@/assets/images/services/service1.png";
import TunisiaOfficePageForm from "@/features/forms/components/TunisiaOfficePageForm";
import ComplexityControlSectionPanel from "@/components/pages/sites/offices-sections/ComplexityControlSectionPanel";
import BusinessInLibya from "@/components/pages/sites/offices-sections/libya/BusinessInLibya";
import OfficeInfoLibya from "@/components/pages/sites/offices-sections/libya/OfficeInfoLibya";
import EORServicesLibya from "@/components/pages/sites/offices-sections/libya/EORServicesLibya";
import OfficeLocationMapLibya from "@/components/pages/sites/offices-sections/libya/OfficeLocationMapLibya";
import initTranslations from "@/app/i18n";
import LibyaLaborData from "@/components/pages/sites/offices-sections/labor-laws/labor-data/LibyaLaborData";
import { websiteRoutesList } from "@/helpers/routesList";
import { axiosGetJsonSSR } from "@/config/axios";
import serviceimgS1 from "@/assets/images/services/service1.png";

export async function generateMetadata({ params: { locale } }) {
  const canonicalUrl = `https://www.pentabell.com/${locale !== "en" ? `${locale}/` : ""
    }guide-to-hiring-employees-in-libya/`;

  const languages = {
    fr: `https://www.pentabell.com/fr/guide-to-hiring-employees-in-libya/`,
    en: `https://www.pentabell.com/guide-to-hiring-employees-in-libya/`,
    "x-default": `https://www.pentabell.com/guide-to-hiring-employees-in-libya/`,
  };
  const { t } = await initTranslations(locale, ["servicesByCountry"]);

  try {
    const res = await axiosGetJsonSSR.get(
      `${process.env.NEXT_PUBLIC_BASE_API_URL_SSR}/seoTags/${locale}/guide-to-hiring-employees-in-libya`
    );
    const seoTags = res?.data?.status === 200;
    if (seoTags) {
      return {
        title: res?.data?.data?.versions[0]?.metaTitle,
        description: res?.data?.data?.versions[0]?.metaDescription,
        robots: res?.data?.data?.robotMeta,
        alternates: {
          canonical: canonicalUrl,
          languages,
        },
      };
    }
  } catch (error) {
    console.error("Error fetching SEO tags:", error);
  }
  return {
    title: t("servicesByCountry:libya:metaTitle"),
    description: t("servicesByCountry:libya:metaDescription"),
    alternates: {
      canonical: canonicalUrl,
      languages,
    },
    robots: "follow, index, max-snippet:-1, max-image-preview:large",
  };
}

async function hiringEmployeesLibyeGuide({ params: { locale } }) {
  const { t } = await initTranslations(locale, ["Tunisia", "libya"]);

  const SERVICES = [
    {
      id: "s1",
      title: t("Tunisia:services:dataS1:title"),
      description: t("Tunisia:services:dataS1:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.payrollServices.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: serviceImg,
      altImg: t("Tunisia:services:dataS1:altImg"),
    },
    {
      id: "s2",
      title: t("Tunisia:services:dataS2:title"),
      description: t("Tunisia:services:dataS2:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.consultingServices.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r4,
      altImg: t("Tunisia:services:dataS2:altImg"),
    },
    {
      id: "s3",
      title: t("Tunisia:services:dataS3:title"),
      description: t("Tunisia:services:dataS3:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.technicalAssistance.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r3,
      altImg: t("Tunisia:services:dataS3:altImg"),
    },
    {
      id: "s4",
      title: t("Tunisia:services:dataS4:title"),
      description: t("Tunisia:services:dataS4:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.aiSourcing.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r2,
      altImg: t("Tunisia:services:dataS4:altImg"),
    },
    {
      id: "s5",
      title: t("Tunisia:services:dataS5:title"),
      description: t("Tunisia:services:dataS5:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.directHiring.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r1,
      altImg: t("Tunisia:services:dataS5:altImg"),
    },
  ];

  return (
    <div>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Organization",
            name: t("contactUs:bureux:contacts:lybia"),
            address: {
              "@type": "PostalAddress",
              streetAddress: "Al Serraj, AlMawashi Street P.O.Box 3000",
              addressLocality: "Tripoli",
              addressCountry: "LY",
            },
            telephone: "+33 1 73 07 42 54",
            email: "<EMAIL>",
            url:
              locale === "en"
                ? "https://www.pentabell.com/guide-to-hiring-employees-in-libya/"
                : `https://www.pentabell.com/${locale}/guide-to-hiring-employees-in-libya/`,
          }),
        }}
      />
      <BannerComponents
        title={t("libya:title")}
        description={t("libya:description")}
        bannerImg={banner}
        height={"100vh"}
        altImg={t("libya:altImg")}
      />
      <OurPartners disableTxt={true} />
      <ResponsiveRowTitleText
        title={t("libya:intro:title")}
        paragraph={t("libya:intro:description")}
        paragraph2={t("libya:intro:description2")}
      />
      <OfficeInfoLibya t={t} />
      <BusinessInLibya t={t} />
      <EORServicesLibya t={t} />
      <OfficeLocationMapLibya t={t} />
      <ComplexityControlSectionPanel t={t} />
      <GlobalHRServicesSection
        title={t("libya:services:title")}
        SERVICES={SERVICES}
        defaultImage={serviceimgS1}
      />
      <LibyaLaborData />
      <TunisiaOfficePageForm country={"Libya"} defaultCountryPhone="ly" />
    </div>
  );
}

export default hiringEmployeesLibyeGuide;
