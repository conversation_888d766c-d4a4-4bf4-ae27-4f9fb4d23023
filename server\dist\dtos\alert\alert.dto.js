"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AlertDTO = void 0;
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
class AlertDTO {
    constructor(alertDTO = {}) {
        Object.assign(this, alertDTO);
    }
}
exports.AlertDTO = AlertDTO;
__decorate([
    (0, class_transformer_1.Expose)({ name: '_id' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AlertDTO.prototype, "id", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'createdBy' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AlertDTO.prototype, "userId", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'industry' }),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], AlertDTO.prototype, "sector", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'country' }),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], AlertDTO.prototype, "location", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'isActive' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], AlertDTO.prototype, "enabled", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'createdAt' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], AlertDTO.prototype, "creationDate", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Date)
], AlertDTO.prototype, "updatedAt", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Number)
], AlertDTO.prototype, "__v", void 0);
//# sourceMappingURL=alert.dto.js.map