import { FormLabel, Stack, TextField } from "@mui/material";
import Autocomplete from "@mui/material/Autocomplete";
import { ErrorMessage } from "formik";

const CustomSelectMultiple = ({
  label,
  name,
  options = [],
  value = [],
  onChange,
  errors,
  touched,
  getOptionLabel = (option) => option,
  placeholder = "",
}) => {
  return (
    <FormLabel className="label-form">
      {label}
      <Stack>
        <Autocomplete
          multiple
          id={`${name}-multiple`}
          options={options}
          value={value}
          getOptionLabel={getOptionLabel}
          onChange={(event, newValue) => {
            onChange(name, newValue);
          }}
          renderInput={(params) => (
            <TextField
              {...params}
              className="input-pentabell multiple-select"
              variant="standard"
              placeholder={placeholder}
            />
          )}
          className={
            "input-pentabell" +
            (errors && touched ? " is-invalid" : "")
          }
        />
      </Stack>
      <ErrorMessage
        className="label-error"
        name={name}
        component="div"
      />
    </FormLabel>
  );
};

export default CustomSelectMultiple;
