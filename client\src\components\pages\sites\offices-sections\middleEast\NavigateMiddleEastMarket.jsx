import { Container, Grid } from "@mui/material";
function NavigateMiddleEastMarket({ t }) {
    return (
        <div id="navigate-african-market-section">
            <Container className="custom-max-width">
                <Grid className="container" container columnSpacing={0}>
                    <Grid item xs={12} sm={6}>
                        <div className="left-section">
                            <h2 className="heading-h1">
                                {t("middleeast:intro:title")}
                            </h2>
                            <p className="paragraph">
                                {t("middleeast:intro:description")}

                            </p>
                            <p className="sub-heading text-blue bold">
                                {t("middleeast:intro:label")}
                            </p>
                        </div>
                    </Grid>
                    <Grid item xs={12} sm={5}>
                    </Grid>
                </Grid>
            </Container>
        </div>
    );
}

export default NavigateMiddleEastMarket;
