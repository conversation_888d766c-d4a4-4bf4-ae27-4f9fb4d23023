
import SvgUsers from "@/assets/images/icons/yellow/Users.svg";
import Svglanguage from "@/assets/images/icons/yellow/language.svg";
import Svggdp from "@/assets/images/icons/yellow/gdp.svg";
import Svgcurrency from "@/assets/images/icons/yellow/currency.svg";
import SvgcapitalCity from "@/assets/images/icons/yellow/capitalCity.svg";
import Svggross from "@/assets/images/icons/yellow/gross.svg";
import OfficeInfo from "@/components/ui/OfficeInfo";

function OfficeInfoMaroc({ t }) {
  const data = [
    {
      icon: <SvgUsers />,
      titleKey: "morocco:officeInfoMorocco:title1",
      descriptionKey: "morocco:officeInfoMorocco:description1"
    },
    {
      icon: <Svglanguage />,
      titleKey: "morocco:officeInfoMorocco:title2",
      descriptionKey: "morocco:officeInfoMorocco:description2"
    },
    {
      icon: <Svggdp />,
      titleKey: "morocco:officeInfoMorocco:title3",
      descriptionKey: "morocco:officeInfoMorocco:description3"
    },
    {
      icon: <Svgcurrency />,
      titleKey: "morocco:officeInfoMorocco:title4",
      descriptionKey: "morocco:officeInfoMorocco:description4"
    },
    {
      icon: <SvgcapitalCity />,
      titleKey: "morocco:officeInfoMorocco:title5",
      descriptionKey: "morocco:officeInfoMorocco:description5"
    },
    {
      icon: <Svggross />,
      titleKey: "morocco:officeInfoMorocco:title6",
      descriptionKey: "morocco:officeInfoMorocco:description6"
    }
  ];

  return <OfficeInfo data={data} t={t} />;
}

export default OfficeInfoMaroc;
