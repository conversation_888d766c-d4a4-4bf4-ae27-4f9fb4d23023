"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const axios_1 = __importDefault(require("axios"));
const services_1 = require("@/utils/services");
const newsletter_model_1 = __importDefault(require("./newsletter.model"));
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const user_model_1 = __importDefault(require("../user/user.model"));
const messages_1 = require("@/utils/helpers/messages");
class NewsletterService {
    constructor() {
        this.Newsletter = newsletter_model_1.default;
        this.User = user_model_1.default;
    }
    async subscribeToNewsletter(email) {
        const existedNewsletter = await this.Newsletter.findOne({ email });
        if (existedNewsletter)
            throw new http_exception_1.default(404, messages_1.MESSAGES.NEWSLETTER.ALREADY_SUBSCRIBED);
        const existingUser = await this.User.findOne({ email });
        try {
            await this.Newsletter.create({ email });
            await axios_1.default.post('https://api.brevo.com/v3/contacts', {
                listIds: [95],
                email,
                attributes: {
                    FNAME: existingUser?.firstName,
                    LNAME: existingUser?.lastName,
                },
                updateEnabled: true,
            }, {
                headers: {
                    'Content-Type': 'application/json',
                    'api-key': process.env.BREVO_API_KEY,
                },
            });
            (0, services_1.sendEmail)({
                to: email,
                subject: 'Thank You for Subscribing!',
                template: 'subscriptionToNewsletter',
                context: {
                    email,
                },
            });
            return { message: messages_1.MESSAGES.NEWSLETTER.SUBSCRIBE };
        }
        catch (error) {
            throw new http_exception_1.default(500, messages_1.MESSAGES.GENERAL.SERVER_ERROR);
        }
    }
    async fillGetInTouchFrom(fromContent) {
        const { firstName, lastName, email, phone, message } = fromContent;
        (0, services_1.sendEmail)({
            to: email,
            subject: `New Contact Request from ${firstName} ${lastName}`,
            template: 'contactForm',
            context: {
                firstName,
                lastName,
                email,
                phone,
                message,
                team: 'Digital',
            },
        });
    }
    async getAllList(newsletter) {
        const newslettres = await this.Newsletter.find();
    }
    async getAllNewsletters(queries) {
        const { email, paginated } = queries;
        const pageNumber = Number(queries.pageNumber) || 1;
        const pageSize = Number(queries.pageSize) || 3;
        const queryConditions = {};
        if (email) {
            queryConditions['email'] = { $regex: new RegExp('.*' + email + '.*', 'i') };
        }
        const totalNewsletters = await this.Newsletter.countDocuments(queryConditions);
        const totalPages = Math.ceil(totalNewsletters / pageSize);
        let newsletters;
        if (!paginated) {
            newsletters = await this.Newsletter.find(queryConditions).sort({ createdAt: 'desc' });
            return {
                totalNewsletters,
                newsletters,
            };
        }
        else {
            newsletters = await this.Newsletter.find(queryConditions)
                .sort({ createdAt: 'desc' })
                .skip((pageNumber - 1) * pageSize)
                .limit(pageSize);
        }
        return {
            pageNumber,
            pageSize,
            totalPages,
            totalNewsletters,
            newsletters,
        };
    }
    async unsubscribeFromNewsletter(email) {
        const existedNewsletter = await this.Newsletter.findOne({ email });
        if (!existedNewsletter)
            throw new http_exception_1.default(404, messages_1.MESSAGES.NEWSLETTER.NOT_FOUND);
        await this.Newsletter.findOneAndUpdate({ email: email }, { isActive: false }, { new: true });
        return { message: messages_1.MESSAGES.NEWSLETTER.UNSUBSCRIBE };
    }
}
exports.default = NewsletterService;
//# sourceMappingURL=newsletter.service.js.map