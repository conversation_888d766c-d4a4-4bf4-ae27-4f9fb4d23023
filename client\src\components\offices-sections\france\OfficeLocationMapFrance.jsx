import OfficeLocationMap from "@/components/ui/OfficeLocationMap";
function OfficeLocationMapFrance({ t }) {
  return (
    <OfficeLocationMap
      title={t("france:officeLocation:label")}
      subtitle={t("france:officeLocation:title")}
      address={t("france:officeLocation:address")}
      tel={t("france:officeLocation:tel1")}
      email={t("france:officeLocation:mail")}
      linkText={t("france:officeLocation:talk")}
      mapSrc="https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d84032.69737423373!2d2.317292!3d48.838723!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x47e671857129906d%3A0xecb063098b068e88!2sPentabell%20France!5e0!3m2!1sfr!2sus!4v1728638942092!5m2!1sfr!2sus"
    
    />
  );
}
export default OfficeLocationMapFrance;
