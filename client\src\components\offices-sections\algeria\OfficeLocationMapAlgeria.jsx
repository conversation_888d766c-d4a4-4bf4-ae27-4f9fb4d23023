
import OfficeLocationMap from "@/components/ui/OfficeLocationMap";

function OfficeLocationMapAlgeria({ t }) {
  return (
    <OfficeLocationMap
      title={t("Algeria:officeLocation1:label")}
      subtitle={t("Algeria:officeLocation1:title")}
      address={t("Algeria:officeLocation1:address")}
      tel={t("Algeria:officeLocation1:tel1")}
      email={t("Algeria:officeLocation1:mail")}
      linkText={t("Algeria:officeLocation1:talk")}
      mapSrc="https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d25577.502753053846!2d3.038734!3d36.742062!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x128fad8ab92e9313%3A0x7228405fce638a07!2sPentabell%20Alg%C3%A9rie-%20Cabinet%20de%20Recrutement%2C%20Portage%20Salarial%20et%20Assistance%20Technique!5e0!3m2!1sfr!2sus!4v1728577693998!5m2!1sfr!2sus"
    />
  );
}
export default OfficeLocationMapAlgeria;
