"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const opportunity_model_1 = __importDefault(require("../opportunity/model/opportunity.model"));
const user_model_1 = __importDefault(require("../user/user.model"));
const article_model_1 = __importDefault(require("@/apis/article/article.model"));
const favourite_model_1 = __importDefault(require("./favourite.model"));
const constants_1 = require("@/utils/helpers/constants");
const messages_1 = require("@/utils/helpers/messages");
class FavouriteService {
    constructor() {
        this.Opportunity = opportunity_model_1.default;
        this.Article = article_model_1.default;
        this.User = user_model_1.default;
        this.Favourite = favourite_model_1.default;
    }
    async addToFavourite(favouriteId, favouriteType, currentUser) {
        let item;
        if (favouriteType === 'opportunity') {
            item = await this.Opportunity.findById(favouriteId);
        }
        else if (favouriteType === 'article') {
            item = await this.Article.findById(favouriteId);
        }
        if (!item) {
            throw new http_exception_1.default(404, messages_1.MESSAGES.CANDIDATE_SHORTLIST.NOT_FOUND);
        }
        const existingFavourite = await this.Favourite.findOne({
            user: currentUser._id,
            favouriteType: favouriteType,
            favouriteId: favouriteId,
        });
        if (existingFavourite) {
            throw new http_exception_1.default(409, messages_1.MESSAGES.CANDIDATE_SHORTLIST.ALREADY_EXISTS);
        }
        const newFavourite = await this.Favourite.create({
            user: currentUser._id,
            favouriteType: favouriteType,
            favouriteId: favouriteId,
        });
        return newFavourite;
    }
    async getAllFavourites(queries, currentUserId) {
        const { keyWord, industry, genre, createdAt, country, sortOrder, isPublished, pageNumber: queryPageNumber, pageSize: queryPageSize, typeOfFavourite, } = queries;
        const pageNumber = Number(queryPageNumber) || 1;
        const pageSize = Number(queryPageSize) || 8;
        const userFavourites = await this.Favourite.find({ user: currentUserId }).lean();
        if (!userFavourites || userFavourites.length === 0) {
            return { totalFavourites: 0, favourites: [], pageNumber, totalPages: 0, totalFavouriteOpportunities: 0, totalFavouriteArticles: 0 };
        }
        let totalItems = 0;
        let totalPages = 0;
        let favourites = [];
        const favouriteType = typeOfFavourite === constants_1.FavouriteType.ARTICLE ? constants_1.FavouriteType.ARTICLE : constants_1.FavouriteType.OPPORTUNITY;
        if (favouriteType === 'opportunity') {
            const opportunityIds = userFavourites.filter(fav => fav.favouriteType === 'opportunity').map(fav => fav.favouriteId);
            const queryConditions = {};
            if (isPublished !== undefined)
                queryConditions.$or = [{ 'versions.fr.isPublished': true }, { 'versions.en.isPublished': true }];
            if (createdAt) {
                const date = new Date(createdAt);
                queryConditions['createdAt'] = { $gte: date.toISOString(), $lte: new Date().toISOString() };
            }
            if (genre)
                queryConditions['genre'] = genre;
            if (industry)
                queryConditions['industry'] = industry;
            if (country)
                queryConditions['country'] = country;
            const shortlistCondition = { _id: { $in: opportunityIds } };
            const opportunitySearchRequest = keyWord
                ? { $text: { $search: keyWord }, ...shortlistCondition, ...queryConditions }
                : { ...shortlistCondition, ...queryConditions };
            const sortCriteria = {};
            if (sortOrder) {
                sortCriteria['createdAt'] = sortOrder === 'asc' ? 1 : -1;
            }
            totalItems = await this.Opportunity.countDocuments(opportunitySearchRequest);
            totalPages = Math.ceil(totalItems / pageSize);
            favourites = await this.Opportunity.find(opportunitySearchRequest)
                .sort(sortCriteria)
                .select('versions industry country status reference createdAt dateOfExpiration')
                .skip((pageNumber - 1) * pageSize)
                .limit(pageSize);
        }
        else if (favouriteType === 'article') {
            const articleIds = userFavourites.filter(fav => fav.favouriteType === 'article').map(fav => fav.favouriteId);
            totalItems = await this.Article.countDocuments({
                _id: { $in: articleIds },
            });
            totalPages = Math.ceil(totalItems / pageSize);
            favourites = await this.Article.find({
                _id: { $in: articleIds },
            })
                .select('versions')
                .skip((pageNumber - 1) * pageSize)
                .limit(pageSize)
                .lean();
        }
        const totalFavouriteOpportunities = userFavourites.filter(fav => fav.favouriteType === 'opportunity').length;
        const totalFavouriteArticles = userFavourites.filter(fav => fav.favouriteType === 'article').length;
        return {
            pageNumber,
            pageSize,
            totalPages,
            totalFavourites: totalItems,
            favourites,
            totalFavouriteOpportunities,
            totalFavouriteArticles,
        };
    }
    async removeFavourite(currentUserId, favouriteId, favouriteType) {
        try {
            if (!currentUserId) {
                throw new http_exception_1.default(400, messages_1.MESSAGES.CANDIDATE_SHORTLIST.ID_REQUIRED);
            }
            if (!favouriteId) {
                throw new http_exception_1.default(400, messages_1.MESSAGES.CANDIDATE_SHORTLIST.FAVORITE_ID_REQUIRED);
            }
            if (!favouriteType) {
                throw new http_exception_1.default(400, messages_1.MESSAGES.CANDIDATE_SHORTLIST.TYPE_REQUIRED);
            }
            const existingFavourite = await this.Favourite.findOne({
                user: currentUserId,
                favouriteId,
                favouriteType,
            });
            if (!existingFavourite) {
                throw new http_exception_1.default(404, messages_1.MESSAGES.CANDIDATE_SHORTLIST.NOT_FOUND);
            }
            const result = await this.Favourite.deleteOne({
                user: currentUserId,
                favouriteId,
                favouriteType,
            });
            if (result.deletedCount === 0) {
                throw new http_exception_1.default(500, messages_1.MESSAGES.CANDIDATE_SHORTLIST.DELETE_FAILED);
            }
        }
        catch (error) {
            throw new http_exception_1.default(500, messages_1.MESSAGES.GENERAL.SERVER_ERROR);
        }
    }
    async isItemSaved(itemId, currentUserId) {
        const isSaved = await this.Favourite.exists({
            user: currentUserId,
            favouriteId: itemId,
        });
        return isSaved === null ? false : true;
    }
}
exports.default = FavouriteService;
//# sourceMappingURL=favourite.service.js.map