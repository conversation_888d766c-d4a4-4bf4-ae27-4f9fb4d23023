import OfficeLocationMap from "@/components/ui/OfficeLocationMap";

function OfficeLocationMapMaroc({ t }) {
  return (
    <OfficeLocationMap
      title={t("morocco:officeLocation:label")}
      subtitle={t("morocco:officeLocation:title")}
      address={t("morocco:officeLocation:address")}
      tel={t("morocco:officeLocation:tel1")}
      email={t("morocco:officeLocation:mail")}
      linkText={t("morocco:officeLocation:talk")}
      mapSrc="https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d6650.947303873112!2d-7.641964000000001!3d33.541068!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0xda62d4e3244c1e9%3A0xddd61c5e88c8e24d!2sPentabell%20Morocco%20-%20International%20Recruitment%20%26%20Staffing%20Agency!5e0!3m2!1sfr!2sus!4v1728633920689!5m2!1sfr!2sus"

    />
  );
}
export default OfficeLocationMapMaroc;
