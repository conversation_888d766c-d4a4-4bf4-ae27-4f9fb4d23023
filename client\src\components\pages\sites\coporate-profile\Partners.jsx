"use client";

import { Container } from "@mui/material";
import { useTranslation } from "react-i18next";
import useEmblaCarousel from "embla-carousel-react";
import Autoplay from "embla-carousel-autoplay";
import PartnerSlide from "@/components/pages/sites/sections/PartnersSlideShow";
import danimex from "../../../../../public/images/partners/danimex.webp";
import ebz from "../../../../../public/images/partners/ebz.webp";
import gemtec from "../../../../../public/images/partners/gemtec.webp";
import hillEegineering from "../../../../../public/images/partners/hill-egineering.webp";
import TRACTEBEL from "../../../../../public/images/partners/logo-tractebel.webp";
import cepsa from "../../../../../public/images/partners/cepsa.webp";
import navalLogo from "../../../../../public/images/partners/logo-navalgrp.webp";
import Alstom from "../../../../../public/images/partners/logo-Alstom.webp";
import eni from "../../../../../public/images/partners/eni.webp";
import visa from "../../../../../public/images/partners/logo-visa.webp";
import ge from "../../../../../public/images/partners/logo-ge.webp";
import webuild from "../../../../../public/images/partners/webuild.webp";
import tecnimont from "../../../../../public/images/partners/logo-tecnimont.webp";
import petrovietnam from "../../../../../public/images/partners/petrovietnam.webp";
import totalLogo from "../../../../../public/images/partners/logo-Total.webp";
import gse from "../../../../../public/images/partners/gse.webp";
import cnooc from "../../../../../public/images/partners/cnooc.webp";
import pttep from "../../../../../public/images/partners/pttep.webp";
import timmoun from "../../../../../public/images/partners/timmoun.webp";
import sonatrach from "../../../../../public/images/partners/sonatrach.webp";
import metsooutotec from "../../../../../public/images/partners/logo-metsooutotec.webp";
import jesa from "../../../../../public/images/partners/logo-jesa.webp";

function Partners() {
  const { t } = useTranslation();
  const OPTIONS = { loop: true, align: "start" };

  const [emblaRef1] = useEmblaCarousel(OPTIONS, [
    Autoplay({ playOnInit: true, delay: 2000 }),
  ]);
  const [emblaRef2] = useEmblaCarousel(OPTIONS, [
    Autoplay({ playOnInit: true, delay: 2000 }),
  ]);

  const partners1 = [
    { label: "danimex", logo: danimex },
    { label: "ebz", logo: ebz },
    { label: "gemtec", logo: gemtec },
    { label: "hillEegineering", logo: hillEegineering },
    { label: "TRACTEBEL", logo: TRACTEBEL },
    { label: "cepsa", logo: cepsa },
    { label: "navalLogo", logo: navalLogo },
    { label: "Alstom", logo: Alstom },
    { label: "eni", logo: eni },
    { label: "Visa", logo: visa },
    { label: "metsooutotec", logo: metsooutotec },
  ];

  const partners2 = [
    { label: "GE", logo: ge },
    { label: "webuild", logo: webuild },
    { label: "Tecnimont", logo: tecnimont },
    { label: "petrovietnam", logo: petrovietnam },
    { label: "totalLogo", logo: totalLogo },
    { label: "gse", logo: gse },
    { label: "cnooc", logo: cnooc },
    { label: "pttep", logo: pttep },
    { label: "timmoun", logo: timmoun },
    { label: "sonatrach", logo: sonatrach },
    { label: "jesa", logo: jesa },
  ];

  return (
    <div id="coporate-profile-partners">
      <Container className="custom-max-width white-bg">
        <section className="embla" id="partners__slider">
          <div className="embla__viewport" ref={emblaRef1}>
            <div className="embla__container">
              {partners1.map((item, index) => (
                <PartnerSlide key={index} item={item} t={t} />
              ))}
            </div>
          </div>
        </section>

        <section
          className="embla"
          id="partners__slider"
          style={{ marginTop: 10 }}
        >
          <div className="embla__viewport" ref={emblaRef2}>
            <div className="embla__container">
              {partners2.map((item, index) => (
                <PartnerSlide key={index} item={item} t={t} />
              ))}
            </div>
          </div>
        </section>
      </Container>
    </div>
  );
}

export default Partners;
