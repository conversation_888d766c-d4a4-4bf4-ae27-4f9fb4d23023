"use client";



import Svgcircle from "@/assets/images/Checkcircle.svg"

import ReusableConfirmation from "../ReusableConfirmation";

function ConfirmApplication({ t }) {
 
    const handleGoHome = () => {
      window.location.href = "/opportunities";
    };
  return (
   
    <ReusableConfirmation
    icon={Svgcircle}  
    message="Application received"
    text="Thank you for submitting your application."
    buttons={[
      {
        text: "Go Back To Home Page",
        type: "submit",
        onClick: handleGoHome,
      },
    ]}
  />

  );
}

export default ConfirmApplication;
