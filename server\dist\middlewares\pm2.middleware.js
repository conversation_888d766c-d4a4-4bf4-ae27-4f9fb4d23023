"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.requestCountMiddleware = requestCountMiddleware;
exports.requestFrequencyMiddleware = requestFrequencyMiddleware;
exports.latencyMiddleware = latencyMiddleware;
exports.responseStatusMiddleware = responseStatusMiddleware;
exports.requestUrlMiddleware = requestUrlMiddleware;
exports.requestMethodMiddleware = requestMethodMiddleware;
exports.trackRequestCountsMiddleware = trackRequestCountsMiddleware;
const io = require("@pm2/io");
const currentReqs = io.counter({
    name: 'Realtime Requests Count',
    id: 'app/realtime/requests',
});
const reqSec = io.meter({
    name: 'req/sec',
    id: 'app/requests/volume',
});
const latencyMetric = io.metric({
    name: 'Average Latency (ms)',
    id: 'app/latency/average',
});
let totalLatency = 0;
let totalRequests = 0;
function requestCountMiddleware(req, res, next) {
    currentReqs.inc();
    res.on('finish', () => currentReqs.dec());
    next();
}
function requestFrequencyMiddleware(req, res, next) {
    reqSec.mark();
    next();
}
function latencyMiddleware(req, res, next) {
    const startTime = process.hrtime();
    res.on('finish', () => {
        const duration = process.hrtime(startTime);
        const latencyValue = duration[0] * 1e3 + duration[1] / 1e6;
        totalLatency += latencyValue;
        totalRequests += 1;
        const averageLatency = totalRequests > 0 ? totalLatency / totalRequests : 0;
        latencyMetric.set(averageLatency);
    });
    next();
}
function responseStatusMiddleware(req, res, next) {
    res.on('finish', () => {
        const statusCode = res.statusCode;
        const statusMetric = io.metric({
            name: `Response Status ${statusCode}`,
            id: `app/response/status/${statusCode}`,
        });
        statusMetric.set(1);
    });
    next();
}
function requestUrlMiddleware(req, res, next) {
    const url = req.originalUrl || req.url;
    const urlMetric = io.metric({
        name: `Request URL ${url}`,
        id: `app/request/url/${url}`,
    });
    urlMetric.set(1);
    next();
}
function requestMethodMiddleware(req, res, next) {
    const method = req.method;
    const methodMetric = io.metric({
        name: `Request Method ${method}`,
        id: `app/request/method/${method}`,
    });
    methodMetric.set(1);
    next();
}
const requestCountsByUrl = new Map();
const requestCountsByMethod = new Map();
function trackRequestCountsMiddleware(req, res, next) {
    const url = req.originalUrl || req.url;
    const method = req.method;
    requestCountsByUrl.set(url, (requestCountsByUrl.get(url) || 0) + 1);
    io.metric({
        name: `Total Requests for URL ${url}`,
        id: `app/request/count/url/${url}`,
    }).set(requestCountsByUrl.get(url));
    requestCountsByMethod.set(method, (requestCountsByMethod.get(method) || 0) + 1);
    io.metric({
        name: `Total Requests for Method ${method}`,
        id: `app/request/count/method/${method}`,
    }).set(requestCountsByMethod.get(method));
    next();
}
//# sourceMappingURL=pm2.middleware.js.map