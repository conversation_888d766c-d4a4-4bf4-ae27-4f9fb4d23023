import BannerComponents from "@/components/pages/sites/sections/BannerComponents";
import banner from "@/assets/images/website/banner/Hire-Management-Pentabell-Services.webp";
import ResponsiveRowTitleText from "@/components/ui/ResponsiveRowTitleText";
import ServiceRow from "@/components/ui/ServiceRow";

import directHiringServiceImgS1 from "@/assets/images/services/directHiringServiceImgS1.png";
import directHiringServiceImgS2 from "@/assets/images/services/directHiringServiceImgS2.png";
import directHiringServiceImgS3 from "@/assets/images/services/directHiringServiceImgS3.png";
import directHiringServiceImgS4 from "@/assets/images/services/directHiringServiceImgS4.png";
import WhyPentabell from "@/components/pages/sites/services/WhyPentabell";
import AskQuestions from "@/components/pages/sites/services/AskQuestions";
import DirectHireServicePageForm from "@/features/forms/components/DirectHireServicePageForm";
import ApproachToDirectHire from "@/components/pages/sites/services/ApproachToDirectHire";
import initTranslations from "@/app/i18n";
import { axiosGetJsonSSR } from "@/config/axios";

export async function generateMetadata({ params: { locale } }) {
  const canonicalUrl = `https://www.pentabell.com/${locale !== "en" ? `${locale}/` : ""
    }hr-services/direct-hiring-solutions/`;

  const { t } = await initTranslations(locale, [
    "directHiringService",
    "global",
  ]);
  const languages = {
    fr: `https://www.pentabell.com/fr/hr-services/direct-hiring-solutions/`,
    en: `https://www.pentabell.com/hr-services/direct-hiring-solutions/`,
    "x-default": `https://www.pentabell.com/hr-services/direct-hiring-solutions/`,
  };

  try {
    const encodedSlug = encodeURIComponent(
      "hr-services/direct-hiring-solutions"
    );
    const res = await axiosGetJsonSSR.get(
      `${process.env.NEXT_PUBLIC_BASE_API_URL_SSR}/seoTags/${locale}/${encodedSlug}`
    );
    const seoTags = res?.data?.status === 200;
    if (seoTags) {
      return {
        title: res?.data?.data?.versions[0]?.metaTitle,
        description: res?.data?.data?.versions[0]?.metaDescription,
        robots: res?.data?.data?.robotMeta,
        alternates: {
          canonical: canonicalUrl,
          languages,
        },
      };
    }
  } catch (error) {
    return {
      title: t("directHiringService:metaTitle"),
      description: t("directHiringService:metaDescription"),
      alternates: {
        canonical: canonicalUrl,
        languages,
      },
      robots: "follow, index, max-snippet:-1, max-image-preview:large",
    };
  }
  return {
    title: t("directHiringService:metaTitle"),
    description: t("directHiringService:metaDescription"),
    alternates: {
      canonical: canonicalUrl,
      languages,
    },
    robots: "follow, index, max-snippet:-1, max-image-preview:large",
  };
}
async function directHiringSolutions({ params: { locale } }) {
  const { t } = await initTranslations(locale, [
    "directHiringService",
    "global",
  ]);
  const dataS1 = {
    label: t("directHiringService:dataS1:label"),
    title: t("directHiringService:dataS1:title"),
    paragraph: t("directHiringService:dataS1:paragraph"),
    altImg: t("directHiringService:dataS1:altImg"),
    featureImg: directHiringServiceImgS1,
  };
  const dataS2 = {
    label: t("directHiringService:dataS2:label"),
    title: t("directHiringService:dataS2:title"),
    paragraph: t("directHiringService:dataS2:paragraph"),
    altImg: t("directHiringService:dataS2:altImg"),
    featureImg: directHiringServiceImgS2,
  };
  const dataS3 = {
    label: t("directHiringService:dataS3:label"),
    title: t("directHiringService:dataS3:title"),
    paragraph: t("directHiringService:dataS3:paragraph"),
    altImg: t("directHiringService:dataS3:altImg"),
    featureImg: directHiringServiceImgS3,
  };
  const dataS4 = {
    label: t("directHiringService:dataS4:label"),
    title: t("directHiringService:dataS4:title"),
    paragraph: t("directHiringService:dataS4:paragraph"),
    altImg: t("directHiringService:dataS4:altImg"),
    featureImg: directHiringServiceImgS4,
  };

  const ASK_QUESTIONS = [
    {
      id: "s1",
      title: t("directHiringService:questions:QA1:question"),
      description: t("directHiringService:questions:QA1:answer"),
    },
    {
      id: "s2",
      title: t("directHiringService:questions:QA2:question"),
      description: t("directHiringService:questions:QA2:answer"),
    },
    {
      id: "s3",
      title: t("directHiringService:questions:QA3:question"),
      description: t("directHiringService:questions:QA3:answer"),
    },
    {
      id: "s4",
      title: t("directHiringService:questions:QA4:question"),
      description: t("directHiringService:questions:QA4:answer"),
    },
    {
      id: "s5",
      title: t("directHiringService:questions:QA5:question"),
      description: t("directHiringService:questions:QA5:answer"),
    },
  ];

  const reasons = [
    {
      heading: t("directHiringService:whyPentabell:reasons:reason1:title"),
      text: t("directHiringService:whyPentabell:reasons:reason1:description"),
    },
    {
      heading: t("directHiringService:whyPentabell:reasons:reason2:title"),
      text: t("directHiringService:whyPentabell:reasons:reason2:description"),
    },
    {
      heading: t("directHiringService:whyPentabell:reasons:reason3:title"),
      text: t("directHiringService:whyPentabell:reasons:reason3:description"),
    },
    {
      heading: t("directHiringService:whyPentabell:reasons:reason4:title"),
      text: t("directHiringService:whyPentabell:reasons:reason4:description"),
    },
  ];
  return (
    <div>
      <BannerComponents
        title={t("directHiringService:intro:title")}
        description={t("directHiringService:intro:description")}
        bannerImg={banner}
        height={"70vh"}
        altImg={t("directHiringService:intro:altImg")}
      />

      <ResponsiveRowTitleText
        title={t("directHiringService:overview:title")}
        paragraph={t("directHiringService:overview:description1")}
        paragraph2={t("directHiringService:overview:description2")}
      />
      <p className="heading-h1 text-center text-banking">
        {t("directHiringService:howWeHelp")}
      </p>

      <ServiceRow data={dataS1} />
      <ServiceRow data={dataS2} reverse={true} darkBg={true} />
      <ServiceRow data={dataS3} />
      <ServiceRow data={dataS4} reverse={true} darkBg={true} />
      <ApproachToDirectHire locale={locale} />
      <WhyPentabell
        title1={t("direct-hiring-solutions:whyPentabell:title1")}
        title2={t("direct-hiring-solutions:whyPentabell:title2")}
        reasons={reasons}
      />
      <AskQuestions
        title={t("direct-hiring-solutions:questions:title")}
        ASK_QUESTIONS={ASK_QUESTIONS}
      />
      {/* <InsightsSection /> */}
      <DirectHireServicePageForm />
    </div>
  );
}
export default directHiringSolutions;
