import { Container, Grid } from "@mui/material";
import CustomButton from "@/components/ui/CustomButton";
import africaMap from "@/assets/images/offices/AfricaMap.png";
import europeMap from "@/assets/images/offices/europeMap.png";
import { websiteRoutesList } from "@/helpers/routesList";
function CTAContact({ t, country }) {
  return (
    <div id="cta-contact">
      <Container className="custom-max-width">
        <Grid container className="container" spacing={2}>
          <Grid item xs={12} sm={6} className="left-section">
            <p className="heading-h2 text-white">
              {t(`${country}:contact:title`)}
            </p>

            <p className="paragraph text-white">
              {t(`${country}:contact:description`)}
            </p>
            <CustomButton
              text={t(`${country}:contact:contactUs`)}
              className={"btn btn-filled"}
              link={`/${websiteRoutesList.contact.route}`}
            />
          </Grid>

          <Grid item xs={12} sm={5} className="africa-img">
            <img
              width={275}
              height={324}
              src={country === "africa" ? africaMap.src : europeMap.src}
              alt={country === "africa" ? t("africa:locations:altImage") : t("europe:locations:altImage")}
              loading="lazy"
            />
          </Grid>

        </Grid>
      </Container>
    </div>
  );
}

export default CTAContact;
