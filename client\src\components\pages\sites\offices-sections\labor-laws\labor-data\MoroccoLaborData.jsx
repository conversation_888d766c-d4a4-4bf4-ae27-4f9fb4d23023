"use client";
import LaborLaws from "../LaborLaws";
import { createList } from '@/utils/functions';
export const LABOR_KEYS = {
    workingHours: {
        title: "morocco:MoroccoLabor:workingHours:title",
    },
    employmentContracts: {
        title: "morocco:MoroccoLabor:employmentContracts:title",
    },

};
export const moroccoLaborData = {
    title: "morocco:MoroccoLabor:title",
    sections: [
        {
            id: 1,
            title: LABOR_KEYS.workingHours.title,
            subsections: [
                {
                    title: "morocco:MoroccoLabor:workingHours:title1",
                    description: "morocco:MoroccoLabor:workingHours:description1",
                },
                {
                    title: "morocco:MoroccoLabor:workingHours:title2",
                    description: "morocco:MoroccoLabor:workingHours:description2",
                },
                {
                    list: createList(["morocco:MoroccoLabor:workingHours:data1", "morocco:MoroccoLabor:workingHours:data2", "morocco:MoroccoLabor:workingHours:data3"]),
                },
                {
                    list: createList(["morocco:MoroccoLabor:workingHours:item1", "morocco:MoroccoLabor:workingHours:item2", "morocco:MoroccoLabor:workingHours:item3"]),
                },
                {
                    list: createList(["morocco:MoroccoLabor:workingHours:data4"]),
                },
            ],
        },
        {
            id: 2,
            title: LABOR_KEYS.employmentContracts.title,
            subsections: [

                {
                    description: "morocco:MoroccoLabor:employmentContracts:description",
                    list: createList(["morocco:MoroccoLabor:employmentContracts:data1", "morocco:MoroccoLabor:employmentContracts:data2", "morocco:MoroccoLabor:employmentContracts:data3"]),
                },

            ],
        },
        {
            id: 3,
            title: "morocco:MoroccoLabor:payroll:title",
            type: "payroll",
            titleKey: "morocco:MoroccoLabor:payroll:title1",
            descriptionKey: "morocco:MoroccoLabor:payroll:description",
            items: [
                {
                    titleKey: "morocco:MoroccoLabor:payroll:fiscalYear:title",
                    dateKeys: ["morocco:MoroccoLabor:payroll:fiscalYear:date1", "morocco:MoroccoLabor:payroll:fiscalYear:date2"],
                    descriptionKey: "morocco:MoroccoLabor:payroll:fiscalYear:description",
                },
                {
                    titleKey: "morocco:MoroccoLabor:payroll:payrollCycle:title",
                    dateKeys: ["morocco:MoroccoLabor:payroll:payrollCycle:date"],
                    descriptionKey: "morocco:MoroccoLabor:payroll:payrollCycle:description",
                },
                {
                    titleKey: "morocco:MoroccoLabor:payroll:minimumWage:title",
                    dateKeys: ["morocco:MoroccoLabor:payroll:minimumWage:wage", "morocco:MoroccoLabor:payroll:minimumWage:date"],
                    descriptionKey: "morocco:MoroccoLabor:payroll:minimumWage:description",
                },
                {
                    titleKey: "morocco:MoroccoLabor:payroll:payrollManagement:title",
                    dateKeys: ["morocco:MoroccoLabor:payroll:payrollManagement:date1", "morocco:MoroccoLabor:payroll:payrollManagement:date2"],
                    descriptionKey: "morocco:MoroccoLabor:payroll:payrollManagement:description",
                },


            ],
        },
        {
            id: 4,
            title: "morocco:MoroccoLabor:termination:title",
            subsections: [
                {
                    title: "morocco:MoroccoLabor:termination:title1",
                    description: "morocco:MoroccoLabor:termination:description1",
                },

                {
                    list: createList(["morocco:MoroccoLabor:termination:data1", "morocco:MoroccoLabor:termination:data2", "morocco:MoroccoLabor:termination:data3", "morocco:MoroccoLabor:termination:data4"]),
                },

                {
                    title: "morocco:MoroccoLabor:termination:title2",
                    description: "morocco:MoroccoLabor:termination:description2",
                },

                {

                    description: "morocco:MoroccoLabor:termination:data5",

                },
                {
                    list: createList(["morocco:MoroccoLabor:termination:item1", "morocco:MoroccoLabor:termination:item2", "morocco:MoroccoLabor:termination:item3"]),
                },

                {

                    description: "morocco:MoroccoLabor:termination:data6",

                },
                {
                    list: createList(["morocco:MoroccoLabor:termination:item4", "morocco:MoroccoLabor:termination:item5", "morocco:MoroccoLabor:termination:item6"]),
                },
                {
                    title: "morocco:MoroccoLabor:termination:title3",

                    description: "morocco:MoroccoLabor:termination:description3",

                    list: createList(["morocco:MoroccoLabor:termination:data7", "morocco:MoroccoLabor:termination:data8", "morocco:MoroccoLabor:termination:data9", "morocco:MoroccoLabor:termination:data10"]),

                },
                {
                    description: "morocco:MoroccoLabor:termination:description4"
                }
            ],
        },
        {
            id: 5,
            title: "morocco:MoroccoLabor:leaveEntitlements:title",
            type: "leaveEntitlements",
            data: {
                subTitle: "morocco:MoroccoLabor:leaveEntitlements:description",


                leaves: [
                    { date: "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS1:date", title: "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS1:title" },
                    { date: "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS2:date", title: "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS2:title" },
                    { date: "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS3:date", title: "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS3:title" },
                    { date: "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS4:date", title: "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS4:title" },
                    { date: "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS5:date", title: "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS5:title" },
                    { date: "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS6:date", title: "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS6:title" },
                    { date: "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS7:date", title: "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS7:title" },
                    { date: "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS8:date", title: "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS8:title" },
                    { date: "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS9:date", title: "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS9:title" },
                    { date: "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS10:date", title: "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS10:title" },
                    { date: "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS11:date", title: "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS11:title" },
                    { date: "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS12:date", title: "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS12:title" },
                    { date: "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS13:date", title: "morocco:MoroccoLabor:leaveEntitlements:leaves:dataS13:title" },


                ],
                annualLeave: {
                    title: "morocco:MoroccoLabor:leaveEntitlements:leaves:annualLeave:title",
                    description: "morocco:MoroccoLabor:leaveEntitlements:leaves:annualLeave:description1",


                },
                paternityLeave: {
                    title: "morocco:MoroccoLabor:leaveEntitlements:leaves:paternityLeave:title",
                    description: "morocco:MoroccoLabor:leaveEntitlements:leaves:paternityLeave:description",
                },
                maternityLeave: {
                    title: "morocco:MoroccoLabor:leaveEntitlements:leaves:maternityLeave:title",
                    description: ["morocco:MoroccoLabor:leaveEntitlements:leaves:maternityLeave:description1"],
                },
                sickLeave: {
                    title: "morocco:MoroccoLabor:leaveEntitlements:leaves:sickLeave:title",
                    description: "morocco:MoroccoLabor:leaveEntitlements:leaves:sickLeave:description",
                },
                marriageLeave: {
                    title: "morocco:MoroccoLabor:leaveEntitlements:leaves:marrigageLeave:title",
                    description: ["morocco:MoroccoLabor:leaveEntitlements:leaves:marrigageLeave:description"],
                },
                bereavementLeave: {
                    title: "morocco:MoroccoLabor:leaveEntitlements:leaves:bereavementLeave:title",
                    description: ["morocco:MoroccoLabor:leaveEntitlements:leaves:bereavementLeave:description"],
                },

            },
        },
        {
            id: 6,
            title: "morocco:MoroccoLabor:tax:title",
            subsections: [
                { description: "morocco:MoroccoLabor:tax:description" },
                {
                    title: "morocco:MoroccoLabor:tax:title1",
                    description: "morocco:MoroccoLabor:tax:description1"

                },
                {
                    list: createList(["morocco:MoroccoLabor:tax:data1"]),
                },
                {
                    title: "morocco:MoroccoLabor:tax:title2",
                    description: "morocco:MoroccoLabor:tax:description2",
                    list: createList(["morocco:MoroccoLabor:tax:dataS1"]),
                },
            ],
        },
        {
            id: 7,
            title: "morocco:MoroccoLabor:visa:title",
            subsections: [
                { description: "morocco:MoroccoLabor:visa:description1" },
                { description: "morocco:MoroccoLabor:visa:description2" },
                { title: "morocco:MoroccoLabor:visa:title1" },
                {
                    title: "morocco:MoroccoLabor:visa:title2",
                    description: "morocco:MoroccoLabor:visa:description3"
                },
                { title: "morocco:MoroccoLabor:visa:title3" },
                {
                    list: createList(["morocco:MoroccoLabor:visa:data1", "morocco:MoroccoLabor:visa:data2", "morocco:MoroccoLabor:visa:data3", "morocco:MoroccoLabor:visa:data4", "morocco:MoroccoLabor:visa:data5", "morocco:MoroccoLabor:visa:data6", "morocco:MoroccoLabor:visa:data7", "morocco:MoroccoLabor:visa:data8", "morocco:MoroccoLabor:visa:data9"]),
                },

            ],
        },
    ],
};


export default function MoroccoLaborData() {
    return <LaborLaws data={moroccoLaborData} />;
}
