"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.mongoConnect = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const logger_1 = require("../logger");
const user_model_1 = __importDefault(require("@/apis/user/user.model"));
const mongoConnect = async () => {
    const { DB_USER, DB_PASSWORD, DB_HOST, DB_PORT, DB_NAME } = process.env;
    const mongoURL = `mongodb://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}`;
    //const mongoURL = `mongodb://0.0.0.0:27017/Pentabell`;
    const mongoConnection = {
        url: mongoURL,
        options: {
            dbName: DB_NAME,
        },
    };
    try {
        await mongoose_1.default.connect(mongoConnection.url, mongoConnection.options);
        logger_1.logger.info(`Connected to MongoDB -- Database: ${mongoConnection.options.dbName}`);
        createAdminUserIfNeeded();
    }
    catch (error) {
        logger_1.logger.error(`MongoDB not connected: ${error}`);
        process.exit(1);
    }
};
exports.mongoConnect = mongoConnect;
async function createAdminUserIfNeeded() {
    const adminUser = await user_model_1.default.findOne({ roles: 'Admin' });
    if (!adminUser) {
        const firstName = 'farah';
        const lastName = 'hasnaoui';
        const adminEmail = '<EMAIL>';
        const adminPassword = 'kenzafarah';
        const jobTitle = 'admin';
        const phone = '+216 58126757';
        const country = 'Tunisia';
        const hashedPassword = await bcryptjs_1.default.hash(adminPassword, 10);
        const newAdmin = new user_model_1.default({
            email: adminEmail,
            password: hashedPassword,
            lastName: lastName,
            firstName: firstName,
            jobTitle: jobTitle,
            phone: phone,
            country: country,
            roles: ['Admin'],
        });
        await newAdmin.save();
    }
}
exports.default = { mongoConnect: exports.mongoConnect };
//# sourceMappingURL=index.js.map