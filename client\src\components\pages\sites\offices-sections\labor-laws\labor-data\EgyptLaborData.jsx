"use client";
import LaborL<PERSON> from "../LaborLaws";
import { createList } from '@/utils/functions';
export const LABOR_KEYS = {
    workingHours: {
        title: "Egypte:EgypteLabor:workingHours:title",
    },
    employmentContracts: {
        title: "Egypte:EgypteLabor:employmentContracts:title",
    },
    visa: {
        data1: "Egypte:EgypteLabor:visa:data1",
        data2: "Egypte:EgypteLabor:visa:data2",
        data3: "Egypte:EgypteLabor:visa:data3",
        data4: "Egypte:EgypteLabor:visa:data4",
        data5: "Egypte:EgypteLabor:visa:data5",
        data6: "Egypte:EgypteLabor:visa:data6",
        data7: "Egypte:EgypteLabor:visa:data7",
        data8: "Egypte:EgypteLabor:visa:data8",
        data9: "Egypte:EgypteLabor:visa:data9",
        data10: "Egypte:EgypteLabor:visa:data10",
        data11: "Egypte:EgypteLabor:visa:data11",
        data12: "Egypte:EgypteLabor:visa:data12",
        data13: "Egypte:EgypteLabor:visa:data13"
    }

};
export const egyptLaborData = {
    title: "Egypte:EgypteLabor:title",
    sections: [
        {
            id: 1,
            title: LABOR_KEYS.workingHours.title,
            subsections: [
                {
                    title: "Egypte:EgypteLabor:workingHours:title1",
                    description: "Egypte:EgypteLabor:workingHours:description1",
                },
                {
                    title: "Egypte:EgypteLabor:workingHours:title2",
                    description: "gypte:EgypteLabor:employmentContracts:description",
                },


            ],
        },
        {
            id: 2,
            title: LABOR_KEYS.employmentContracts.title,
            subsections: [
                {
                    title: "Egypte:EgypteLabor:employmentContracts:title",
                    description: "Egypte:EgypteLabor:employmentContracts:data"
                },
                {
                    title: "Egypte:EgypteLabor:employmentContracts:title1",
                    description: "Egypte:EgypteLabor:employmentContracts:data1"
                },
                {
                    title: "Egypte:EgypteLabor:employmentContracts:title2",
                    description: "Egypte:EgypteLabor:employmentContracts:description"
                },
            ],
        },
        {
            id: 3,
            title: "Egypte:EgypteLabor:payroll:title",
            type: "payroll",
            titleKey: "Egypte:EgypteLabor:payroll:title1",
            items: [
                {
                    titleKey: "Egypte:EgypteLabor:payroll:fiscalYear:title",
                    dateKeys: ["Egypte:EgypteLabor:payroll:fiscalYear:date1", "Egypte:EgypteLabor:payroll:fiscalYear:date2"],
                    descriptionKey: "Egypte:EgypteLabor:payroll:fiscalYear:description",
                },
                {
                    titleKey: "Egypte:EgypteLabor:payroll:payrollCycle:title",
                    dateKeys: ["Egypte:EgypteLabor:payroll:payrollCycle:date"],
                    descriptionKey: "Egypte:EgypteLabor:payroll:payrollCycle:description",
                },
                {
                    titleKey: "Egypte:EgypteLabor:payroll:minimumWage:title",
                    dateKeys: ["Egypte:EgypteLabor:payroll:minimumWage:wage", "Egypte:EgypteLabor:payroll:minimumWage:date"],
                    descriptionKey: "Egypte:EgypteLabor:payroll:minimumWage:description",
                },

            ],
        },
        {
            id: 4,
            title: "Egypte:EgypteLabor:termination:title",
            subsections: [
                {
                    title: "Egypte:EgypteLabor:termination:title1",
                    description: "Egypte:EgypteLabor:termination:description1",
                },
                {
                    title: "Egypte:EgypteLabor:termination:titleTermination",
                    description: "Egypte:EgypteLabor:termination:descriptionTermination",
                },
                {
                    list: createList(["Egypte:EgypteLabor:termination:data1", "Egypte:EgypteLabor:termination:data2", "Egypte:EgypteLabor:termination:data3", "Egypte:EgypteLabor:termination:data4"]),
                },
                {
                    title: "Egypte:EgypteLabor:termination:title2",
                    description: "Egypte:EgypteLabor:termination:description2",

                },
                {
                    title: "Egypte:EgypteLabor:termination:title3",
                    description: "Egypte:EgypteLabor:termination:description3",
                },
            ],
        },
        {
            id: 5,
            title: "Egypte:EgypteLabor:leaveEntitlements:title",
            type: "leaveEntitlements",
            data: {
                description: "Egypte:EgypteLabor:leaveEntitlements:description",
                leaves: [
                    { date: "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS1:date", title: "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS1:title" },
                    { date: "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS2:date", title: "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS2:title" },
                    { date: "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS3:date", title: "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS3:title" },
                    { date: "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS4:date", title: "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS4:title" },
                    { date: "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS5:date", title: "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS5:title" },
                    { date: "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS6:date", title: "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS6:title" },
                    { date: "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS7:date", title: "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS7:title" },
                    { date: "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS8:date", title: "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS8:title" },
                    { date: "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS9:date", title: "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS9:title" },
                    { date: "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS10:date", title: "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS10:title" },
                    { date: "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS11:date", title: "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS11:title" },
                    { date: "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS12:date", title: "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS12:title" },

                ],
                annualLeave: {
                    title: "Egypte:EgypteLabor:leaveEntitlements:leaves:annualLeave:title",
                    description: [
                        "Egypte:EgypteLabor:leaveEntitlements:leaves:annualLeave:description1"
                    ],
                },
                maternityLeave: {
                    title: "Egypte:EgypteLabor:leaveEntitlements:leaves:maternityLeave:title",
                    description: ["Egypte:EgypteLabor:leaveEntitlements:leaves:maternityLeave:description1"],
                },
                sickLeave: {
                    title: "Egypte:EgypteLabor:leaveEntitlements:leaves:sickLeave:title",
                    description: "Egypte:EgypteLabor:leaveEntitlements:leaves:sickLeave:description",
                },

                pilgrimageleave: {
                    title: "Egypte:EgypteLabor:leaveEntitlements:leaves:PilgrimageLeave:title",
                    description: "Egypte:EgypteLabor:leaveEntitlements:leaves:PilgrimageLeave:description",
                },
                parentalLeave: {
                    title: "Egypte:EgypteLabor:leaveEntitlements:leaves:parentalLeave:title",
                    description: "Egypte:EgypteLabor:leaveEntitlements:leaves:parentalLeave:description",
                },
                casualLeave: {
                    title: "Egypte:EgypteLabor:leaveEntitlements:leaves:casualLeave:title",
                    description: "Egypte:EgypteLabor:leaveEntitlements:leaves:casualLeave:description",
                },
            },
        },
        {
            id: 6,
            title: "Egypte:EgypteLabor:tax:title",
            subsections: [
                { description: "Egypte:EgypteLabor:tax:description" },
                {
                    title: "Egypte:EgypteLabor:tax:title1",
                    description: "Egypte:EgypteLabor:tax:description1",
                },
                {
                    list: createList(["Egypte:EgypteLabor:tax:data1", "Egypte:EgypteLabor:tax:data2", "Egypte:EgypteLabor:tax:data3", "Egypte:EgypteLabor:tax:data4"]),
                },
                {
                    title: "Egypte:EgypteLabor:tax:title2",
                    description: "Egypte:EgypteLabor:tax:description2",
                    list: createList(["Egypte:EgypteLabor:tax:dataS1", "Egypte:EgypteLabor:tax:dataS2", "Egypte:EgypteLabor:tax:dataS3"]),
                },
            ],
        },
        {
            id: 7,
            title: "Egypte:EgypteLabor:visa:title",
            subsections: [
                { description: "Egypte:EgypteLabor:visa:description1" },
                { description: "Egypte:EgypteLabor:visa:description2" },
                { title: "Egypte:EgypteLabor:visa:title1" },
                {
                    title: "Egypte:EgypteLabor:visa:title2",
                    description: "Egypte:EgypteLabor:visa:description3"
                },
                { title: "Egypte:EgypteLabor:visa:title3" },
                {
                    list: createList([LABOR_KEYS.visa.data1, LABOR_KEYS.visa.data2, LABOR_KEYS.visa.data3, LABOR_KEYS.visa.data5, LABOR_KEYS.visa.data6, LABOR_KEYS.visa.data7, LABOR_KEYS.visa.data8, LABOR_KEYS.visa.data9, LABOR_KEYS.visa.data10, LABOR_KEYS.visa.data11, LABOR_KEYS.visa.data12, LABOR_KEYS.visa.data13]),
                },

            ],
        },
    ],
};


export default function EgyptLaborData() {
    return <LaborLaws data={egyptLaborData} />;
}
