"use client";

import { useEffect, useRef, useState } from "react";
import { v4 as uuidv4 } from "uuid";
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  FormGroup,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { useTranslation } from "react-i18next";
import * as Yup from "yup";
import { toast } from "react-toastify";
import { Formik, Form } from "formik";
import { debounce } from "lodash";

import { RobotsMeta } from "../../../utils/constants";
import { API_URLS } from "../../../utils/urls";
import AddArticleEN from "./AddArticleEN";
import AddArticleFR from "./AddArticleFR";
import { useSaveFile } from "../../opportunity/hooks/opportunity.hooks";
import {
  useCreateArticle,
  useCreateAutoSave,
  useGetCategories,
  useUpdateAutoSave,
} from "../hooks/blog.hook";
import "react-datepicker/dist/react-datepicker.css";
import { axiosGetJson } from "@/config/axios";
import useCurrentUser from "@/features/auth/hooks/currentUser.hooks";
import CustomButton from "@/components/ui/CustomButton";
import { adminRoutes, baseUrlBackoffice } from "@/helpers/routesList";
import CustomSelect from "@/components/ui/CustomSelect";

const AddArticle = () => {
  const { user } = useCurrentUser();
  const savedArticle = localStorage.getItem("savedArticle");

  const useUpdateAutoSaveHook = useUpdateAutoSave();
  const useCreateArticleHook = useCreateArticle();
  const useCreateAutoSaveHook = useCreateAutoSave();
  const useSaveFileHook = useSaveFile();

  const { t } = useTranslation();
  const currentYear = new Date().getFullYear();
  const formikRefAll = useRef(null);

  const [expanded, setExpanded] = useState(`panel`);
  const [formdataEN, setFormDataEN] = useState(new FormData());
  const [formdataFR, setFormDataFR] = useState(new FormData());
  const [uuidPhotoFileNameEN, setUuidPhotoFileNameEN] = useState("");
  const [uuidPhotoFileNameFR, setUuidPhotoFileNameFR] = useState("");

  const [uuidCTSBannerImageFileNameEN, setUuidCTSBannerImageFileNameEN] =
    useState("");
  const [uuidCTSBannerImageFileNameFR, setUuidCTSBannerImageFileNameFR] =
    useState("");
  const [formdataCTSBannerImageEN, setFormDataCTSBannerImageEN] = useState(
    new FormData()
  );
  const [formdataCTSBannerImageFR, setFormDataCTSBannerImageFR] = useState(
    new FormData()
  );

  const [selectedLanguages, setSelectedLanguages] = useState({
    en: true,
    fr: false,
  });

  const [categoriesEN, setCategoriesEN] = useState([]);
  const [categoriesFR, setCategoriesFR] = useState([]);
  const [filteredCategoriesEN, setFilteredCategoriesEN] = useState([]);

  const [filteredCategoriesFR, setFilteredCategoriesFR] = useState([]);
  const [selectedCategoriesEN, setSelectedCategoriesEN] = useState([]);
  const [selectedCategoriesFR, setSelectedCategoriesFR] = useState([]);

  const getCategoriesEN = useGetCategories("en");
  const getCategoriesFR = useGetCategories("fr");

  useEffect(() => {
    if (getCategoriesEN.data?.categories) {
      const transformedCategories = getCategoriesEN.data.categories.map(
        (category) => ({
          id: category.versionscategory[0]?.id,
          name: category.versionscategory[0]?.name,
        })
      );
      setCategoriesEN(transformedCategories);
    }
  }, [getCategoriesEN.data]);

  useEffect(() => {
    if (getCategoriesFR.data?.categories) {
      const transformedCategories = getCategoriesFR.data.categories.map(
        (category) => ({
          id: category?.versionscategory[0]?.id,
          name: category.versionscategory[0]?.name,
        })
      );
      setCategoriesFR(transformedCategories);
    }
  }, [getCategoriesFR.data]);

  useEffect(() => {
    if (selectedCategoriesEN.length > 0) {
      fetchTranslatedCategories(selectedCategoriesEN, "en");
    } else if (selectedCategoriesFR.length > 0) {
      fetchTranslatedCategories(selectedCategoriesFR, "fr");
    }
  }, [selectedCategoriesEN, selectedCategoriesFR]);

  const fetchTranslatedCategories = async (selectedCategories, language) => {
    try {
      const response = await axiosGetJson.get(
        `${API_URLS.categories}/${language}/${selectedCategories}`
      );

      if (language === "en") {
        const transformedCategories = response.data.map((category) => ({
          id: category._id,
          name: category.name,
        }));
        setFilteredCategoriesFR(transformedCategories);
      } else {
        const transformedCategories = response.data.map((category) => ({
          id: category._id,
          name: category.name,
        }));
        setFilteredCategoriesEN(transformedCategories);
      }
    } catch (error) {
      console.error("Error fetching translated categories:", error);
    }
  };

  const handleImageSelect = async (selectedFile, language) => {
    if (language === "en") {
      const uuidPhotos = uuidv4().replace(/-/g, "");
      const newFormData = new FormData();
      newFormData.append("file", selectedFile);
      setFormDataEN(newFormData);
      setUuidPhotoFileNameEN(`${uuidPhotos}`);
    } else if (language === "fr") {
      const uuidPhotos = uuidv4().replace(/-/g, "");
      const newFormData = new FormData();
      newFormData.append("file", selectedFile);
      setFormDataFR(newFormData);
      setUuidPhotoFileNameFR(`${uuidPhotos}`);
    }
  };

  const handleCTSBannerImageSelect = async (selectedFile, language) => {
    if (language === "en") {
      const uuidPhotos = uuidv4().replace(/-/g, "");
      const newFormData = new FormData();
      newFormData.append("file", selectedFile);
      setFormDataCTSBannerImageEN(newFormData);
      setUuidCTSBannerImageFileNameEN(`${uuidPhotos}`);
    } else if (language === "fr") {
      const uuidPhotos = uuidv4().replace(/-/g, "");
      const newFormData = new FormData();
      newFormData.append("file", selectedFile);
      setFormDataCTSBannerImageFR(newFormData);
      setUuidCTSBannerImageFileNameFR(`${uuidPhotos}`);
    }
  };

  const getLocalStorageItem = (key) => {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : "";
    } catch (error) {
      console.warn(`Error parsing localStorage item "${key}":`, error);
      return "";
    }
  };

  const titleEN = getLocalStorageItem("title");
  const metaTitleEN = getLocalStorageItem("metatitle");
  const metaDescriptionEN = getLocalStorageItem("metaDescription");
  const contentEN = getLocalStorageItem("content");
  const contentFR = getLocalStorageItem("contentfr");
  const titleFR = getLocalStorageItem("titlefr");
  const metaDescriptionFR = getLocalStorageItem("metaDescriptionfr");
  const metaTitleFR = getLocalStorageItem("metatitlefr");

  const initialValues = {
    robotsMeta: "index",
    metaTitleEN: metaTitleEN,
    metaDescriptionEN: metaDescriptionEN,
    descriptionEN: "",
    visibilityEN: "",
    categoryEN: [],
    imageEN: null,
    keywordsEN: "",
    titleEN: titleEN,
    urlEN: "",
    altEN: "",
    contentEN: contentEN,
    highlightsEN: [],
    publishDateEN: new Date(),
    faqTitleEN: "",
    faqEN: [],
    ctsBannerImageEN: null,
    ctsBannerLinkEN: "",
    titleFR: titleFR,
    metaTitleFR: metaTitleFR,
    metaDescriptionFR: metaDescriptionFR,
    descriptionFR: "",
    visibilityFR: "",
    categoryFR: [],
    imageFR: null,
    keywordsFR: "",
    urlFR: "",
    altFR: "",
    contentFR: contentFR,
    publishDateFR: new Date(),
    highlightsFR: [],
    faqTitleFR: "",
    faqFR: [],
    ctsBannerImageFR: null,
    ctsBannerLinkFR: "",
  };

  const validationSchema = Yup.object().shape({
    ...(selectedLanguages.en && {
      metaTitleEN: Yup.string().required(t("validations:emptyField")),
      metaDescriptionEN: Yup.string().required(t("validations:emptyField")),
      titleEN: Yup.string().required(t("validations:emptyField")),
      category: Yup.array().min(1, t("validations:minCategory")),
      visibilityEN: Yup.string().required(t("validations:emptyField")),
    }),
    ...(selectedLanguages.fr && {
      metaTitleFR: Yup.string().required(t("validations:emptyField")),
      metaDescriptionFR: Yup.string().required(t("validations:emptyField")),
      titleFR: Yup.string().required(t("validations:emptyField")),
      visibilityFR: Yup.string().required(t("validations:emptyField")),
    }),
  });

  const uploadFile = (filename, formData, lang) => {
    return new Promise((resolve) => {
      useSaveFileHook.mutate(
        {
          resource: "blogs",
          folder: currentYear,
          filename,
          body: { formData, t },
        },
        {
          onSuccess: (data) => {
            if (data.message === "uuid exist") {
              resolve({ lang, uuid: data.uuid });
            } else {
              resolve({ lang, uuid: data.uuid });
            }
          },
          onError: (error) => {
            console.error(`Error uploading ${lang} image:`, error);
            resolve({ lang, uuid: null });
          },
        }
      );
    });
  };

  const clearLocalStorage = () => {
    const keysToRemove = [
      "title",
      "content",
      "titlefr",
      "contentfr",
      "metaDescription",
      "metaDescriptionfr",
      "metatitle",
      "metatitlefr",
      "savedArticle",
    ];
    keysToRemove.forEach((key) => {
      try {
        localStorage.removeItem(key);
      } catch (error) {
        console.warn(`Error removing localStorage item "${key}":`, error);
      }
    });
  };

  const handleSubmit = async (values) => {
    const data = {
      robotsMeta: values.robotsMeta,
      versions: [],
    };

    if (!selectedLanguages.en && !selectedLanguages.fr) {
      toast.error("Please select at least one version!");
      return;
    }

    try {
      let resultEN, resultFR, resultENCTSBannerImage, resultFRCTSBannerImage;

      if (selectedLanguages.en && (!uuidPhotoFileNameEN || !formdataEN)) {
        toast.error("Please select an image for the English version!");
        return;
      }

      if (selectedLanguages.fr && (!uuidPhotoFileNameFR || !formdataFR)) {
        toast.error("Please select an image for the French version!");
        return;
      }

      if (selectedLanguages.en) {
        resultEN = await uploadFile(uuidPhotoFileNameEN, formdataEN, "English");
        resultENCTSBannerImage = await uploadFile(
          uuidCTSBannerImageFileNameEN,
          formdataCTSBannerImageEN,
          "English"
        );
      }

      if (selectedLanguages.fr) {
        resultFR = await uploadFile(uuidPhotoFileNameFR, formdataFR, "French");
        resultFRCTSBannerImage = await uploadFile(
          uuidCTSBannerImageFileNameFR,
          formdataCTSBannerImageFR,
          "French"
        );
      }

      if (selectedLanguages.en && resultEN.uuid) {
        data.versions.push({
          language: "en",
          metaTitle: values.metaTitleEN,
          title: values.titleEN,
          metaDescription: values.metaDescriptionEN,
          description: values.descriptionEN,
          url: values.urlEN,
          visibility: values.visibilityEN,
          publishDate: values.publishDateEN,
          content: values.contentEN,
          alt: values.altEN,
          keywords: values.keywordsEN,
          highlights: values.highlightsEN,
          image: resultEN.uuid,
          category: values.categoryEN === "" ? [] : values.categoryEN,
          createdBy: user?.firstName + " " + user?.lastName,
          faqTitle: values.faqTitleEN || "",
          faq: values.faqEN || [],
          ctsBanner: {
            image: resultENCTSBannerImage.uuid,
            link: values.ctsBannerLinkEN,
          },
        });
      }

      if (selectedLanguages.fr && resultFR.uuid) {
        data.versions.push({
          language: "fr",
          metaTitle: values.metaTitleFR,
          title: values.titleFR,
          metaDescription: values.metaDescriptionFR,
          url: values.urlFR,
          visibility: values.visibilityFR,
          description: values.descriptionFR,
          publishDate: values.publishDateFR,
          content: values.contentFR,
          alt: values.altFR,
          keywords: values.keywordsFR,
          highlights: values.highlightsFR,
          image: resultFR.uuid,
          category: values.categoryFR === "" ? [] : values.categoryFR,
          createdBy: user?.firstName + " " + user?.lastName,
          faqTitle: values.faqTitleFR || "",
          faq: values.faqFR || [],
          ctsBanner: {
            image: resultFRCTSBannerImage.uuid,
            link: values.ctsBannerLinkFR,
          },
        });
      }

      if (data.versions.length > 0) {
        // if (isSavedArticle !== "") {
        //   useUpdateAutoSaveHook.mutate(
        //     {
        //       data: data,
        //       id: isSavedArticle,
        //     },
        //     {
        //       onSuccess: () => {
        //         clearLocalStorage();
        //         window.location.href = `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.blogs.route}/`;
        //       },
        //     }
        //   );
        // } else {
        useCreateArticleHook.mutate(
          {
            data,
          },
          {
            onSuccess: () => {
              clearLocalStorage();
              window.location.href = `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.blogs.route}/`;
            },
            onError: (error) => {
              if (error.response && error.response.status === 409) {
                toast.error(error.response.data.message);
              } else {
                toast.error("An error occurred while processing the request.");
              }
            },
          }
        );
        // }
      } else {
        toast.error("No valid image uploads found. Article not created.");
      }
    } catch (error) {
      toast.error("An error occurred while processing uploads.");
    }
  };

  const handleChangeAccordion = (panel) => (_, newExpanded) => {
    setExpanded(newExpanded ? panel : false);
  };

  const handleCategoriesENSelect = (selectedCategories) => {
    setSelectedCategoriesEN(selectedCategories);
  };

  const handleCategoriesFRSelect = (selectedCategories) => {
    setSelectedCategoriesFR(selectedCategories);
  };

  const [isSavedArticle, setIsSavedArticle] = useState(
    savedArticle ? savedArticle : ""
  );

  const isSavedArticleRef = useRef(isSavedArticle);

  useEffect(() => {
    isSavedArticleRef.current = isSavedArticle;
  }, [isSavedArticle]);

  const autosave = async () => {
    try {
      const values = formikRefAll.current?.values;
      if (!values) return;

      const data = {
        robotsMeta: values?.robotsMeta,
        versions: [],
      };

      if (selectedLanguages.en) {
        data.versions.push({
          language: "en",
          metaTitle: values.metaTitleEN,
          title: values.titleEN,
          metaDescription: values.metaDescriptionEN,
          url: values.urlEN,
          visibility: "Draft",
          publishDate: values.publishDateEN,
          content: values.contentEN,
          alt: values.altEN,
          keywords: values.keywordsEN,
          category: values.categoryEN,
          highlights: values.highlightsEN,
          faqTitle: values.faqTitleEN || "",
          faq: values.faqEN || [],
        });
      }

      if (selectedLanguages.fr) {
        data.versions.push({
          language: "fr",
          metaTitle: values.metaTitleFR,
          title: values.titleFR,
          metaDescription: values.metaDescriptionFR,
          url: values.urlFR,
          visibility: "Draft",
          publishDate: values.publishDateFR,
          content: values.contentFR,
          alt: values.altFR,
          keywords: values.keywordsFR,
          category: values.categoryFR,
          highlights: values.highlightsFR,
          faqTitle: values.faqTitleFR || "",
          faq: values.faqFR || [],
        });
      }

      if (isSavedArticleRef.current != "") {
        useUpdateAutoSaveHook.mutate({
          data: data,
          id: isSavedArticleRef.current,
        });
      }
      // else {
      //   if (data.versions.length > 0) {
      //     useCreateAutoSaveHook.mutate(
      //       {
      //         data,
      //       },
      //       {
      //         onSuccess: (data) => {
      //           setIsSavedArticle(data.articleId);
      //           localStorage.setItem("savedArticle", data.articleId);
      //         },
      //       }
      //     );
      //   }
      // }
    } catch (error) {
      console.warn("Auto-save failed:", error);
    }
  };

  // Optimized debounce timing: 2 minutes instead of 2 minutes (120000ms)
  const handleChange = debounce(autosave, 30000); // 30 seconds for better UX
  const handleClear = () => {
    formikRefAll.current?.resetForm();
  };

  return (
    <>
      <p className="heading-h2 semi-bold">{t("createArticle:addArticle")}</p>
      <div id="container" className="recent-application-pentabell">
        <div className={`main-content`}>
          <div className="commun">
            <div className="inline-group">
              <label className="label-form">
                <input
                  type="checkbox"
                  checked={selectedLanguages.en}
                  onChange={() =>
                    setSelectedLanguages((prev) => ({
                      ...prev,
                      en: !prev.en,
                    }))
                  }
                />
                English
              </label>
              <label className="label-form">
                <input
                  type="checkbox"
                  checked={selectedLanguages.fr}
                  onChange={() =>
                    setSelectedLanguages((prev) => ({
                      ...prev,
                      fr: !prev.fr,
                    }))
                  }
                />
                French
              </label>
            </div>
            <div id="experiences">
              <div id="form">
                <Formik
                  initialValues={initialValues}
                  validationSchema={validationSchema}
                  innerRef={formikRefAll}
                  onSubmit={handleSubmit}
                  className="formik-form"
                >
                  {({ errors, touched, setFieldValue, values }) => (
                    <Form>
                      <Accordion
                        key={`panel`}
                        id="accordion"
                        disableGutters={true}
                        expanded={expanded === `panel`}
                        onChange={handleChangeAccordion(`panel`)}
                      >
                        <AccordionSummary
                          expandIcon={<ExpandMoreIcon />}
                          aria-controls={`panel-content`}
                          id={`panel-header`}
                        >
                          {t("createArticle:settings")}
                        </AccordionSummary>
                        <AccordionDetails
                          className="accordion-detail"
                          elevation={0}
                        >
                          <div className="inline-group">
                            <div>
                              <FormGroup>
                                <CustomSelect
                                  label="Robots meta"
                                  name="robotsMeta"
                                  value={values.robotsMeta}
                                  onChange={(e) =>
                                    setFieldValue("robotsMeta", e.target.value)
                                  }
                                  options={RobotsMeta}
                                />
                              </FormGroup>
                            </div>
                          </div>
                        </AccordionDetails>
                      </Accordion>
                      {selectedLanguages.en && (
                        <AddArticleEN
                          errors={errors}
                          touched={touched}
                          setFieldValue={setFieldValue}
                          values={values}
                          onImageSelect={handleImageSelect}
                          debounce={handleChange}
                          categories={categoriesEN}
                          filteredCategories={filteredCategoriesEN}
                          onCategoriesSelect={handleCategoriesENSelect}
                          onCTSBannerImageSelect={handleCTSBannerImageSelect}
                        />
                      )}
                      {selectedLanguages.fr && (
                        <AddArticleFR
                          errors={errors}
                          touched={touched}
                          setFieldValue={setFieldValue}
                          values={values}
                          onImageSelect={handleImageSelect}
                          categories={categoriesFR}
                          filteredCategories={filteredCategoriesFR}
                          onCategoriesSelect={handleCategoriesFRSelect}
                          debounce={handleChange}
                          onCTSBannerImageSelect={handleCTSBannerImageSelect}
                        />
                      )}
                      <div className="btn-container">
                        <CustomButton
                          type="button"
                          text={"Clear"}
                          className={"btn btn-filled"}
                          onClick={handleClear}
                        />
                        <CustomButton
                          type="submit"
                          text={"Save"}
                          className={"btn btn-filled"}
                          onClick={() => {}}
                        />
                      </div>
                    </Form>
                  )}
                </Formik>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default AddArticle;
