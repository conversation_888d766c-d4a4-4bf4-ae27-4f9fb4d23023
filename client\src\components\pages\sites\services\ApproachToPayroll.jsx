import SvgpayrollIcon from "@/assets/images/services/icons/payrollIcon.svg";
import SvgtaxIcon from "@/assets/images/services/icons/taxIcon.svg";
import SvgpayrollReportsIcon from "@/assets/images/services/icons/payrollReportsIcon.svg";
import SvgworkersIcon from "@/assets/images/services/icons/workersIcon.svg";
import SvgpayrollTaxIcon from "@/assets/images/services/icons/payrollTaxIcon.svg";
import SvghrSupportIcon from "@/assets/images/services/icons/hrSupportIcon.svg";
import initTranslations from "@/app/i18n";
import ComplexityControlSection from "@/components/ui/ComplexityControlSection";

async function ApproachToPayroll({ locale }) {
  const { t, resources } = await initTranslations(locale, ["payrollService", "global"]);
  const items = [
    {
      icon: SvgpayrollIcon,
      title: t("payrollService:approachToPayroll:payrollProcessing:title"),
      description: t("payrollService:approachToPayroll:payrollProcessing:description"),
    },
    {
      icon: SvgtaxIcon,
      title: t("payrollService:approachToPayroll:taxCompliance:title"),
      description: t("payrollService:approachToPayroll:taxCompliance:description"),
    },
    {
      icon: SvgpayrollReportsIcon,
      title: t("payrollService:approachToPayroll:payrollReports:title"),
      description: t("payrollService:approachToPayroll:payrollReports:description"),
    },
    {
      icon: SvgworkersIcon,
      title: t("payrollService:approachToPayroll:workersCompensation:title"),
      description: t("payrollService:approachToPayroll:workersCompensation:description"),
    },
    {
      icon: SvgpayrollTaxIcon,
      title: t("payrollService:approachToPayroll:payrollTax:title"),
      description: t("payrollService:approachToPayroll:payrollTax:description"),
    },
    {
      icon: SvghrSupportIcon,
      title: t("payrollService:approachToPayroll:hrSupport:title"),
      description: t("payrollService:approachToPayroll:hrSupport:description"),
    },
  ];

  return (
    <ComplexityControlSection
      title1={t("payrollService:approachToPayroll:title")}
      title2=""
      description={t("payrollService:approachToPayroll:description")}
      items={items}
    />
  );
}

export default ApproachToPayroll;
