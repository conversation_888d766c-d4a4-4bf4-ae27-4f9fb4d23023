"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const authentication_middleware_1 = __importDefault(require("@/middlewares/authentication.middleware"));
const mongoId_validation_middleware_1 = __importDefault(require("@/middlewares/mongoId-validation.middleware"));
const cache_middleware_1 = require("./../../middlewares/cache.middleware");
const settings_service_1 = require("./settings.service");
class UserSettingsController {
    constructor() {
        this.path = '/settings';
        this.router = (0, express_1.Router)();
        this.UserSettingsService = new settings_service_1.UserSettingsService();
        this.updateSettingsData = async (request, response, next) => {
            try {
                const settingsId = request.params.id;
                const currentUser = request.user;
                const settingsData = request.body;
                const result = await this.UserSettingsService.updateSettingsData(currentUser, settingsId, settingsData);
                return response.status(200).json(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.getUsersSettings = async (req, res, next) => {
            try {
                const currentUser = req.user;
                const setings = await this.UserSettingsService.getSettings(currentUser);
                res.status(200).json(setings);
            }
            catch (error) {
                ;
            }
        };
        this.initializeRoutes();
    }
    initializeRoutes() {
        this.router.put(`${this.path}/:id`, authentication_middleware_1.default, mongoId_validation_middleware_1.default, cache_middleware_1.invalidateCache, this.updateSettingsData);
        this.router.get(`${this.path}`, authentication_middleware_1.default, this.getUsersSettings);
    }
}
exports.default = UserSettingsController;
//# sourceMappingURL=settings.controller.js.map