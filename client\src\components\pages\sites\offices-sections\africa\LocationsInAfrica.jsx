import morroccoImg from "@/assets/images/offices/morroccoImg.png";
import HydraImg from "@/assets/images/offices/HydraImg.png";
import tunisiaImg from "@/assets/images/offices/tunisiaImg.png";
import libyaImg from "@/assets/images/offices/libyaImg.png";
import egypteImg from "@/assets/images/offices/egypteImg.png";
import hassiImg from "@/assets/images/offices/hassiImg.png";
import { OfficesCountries } from "@/config/countries";
import React from "react";
import { websiteRoutesList } from "@/helpers/routesList";
import LocationsList from "@/components/ui/LocationsList";

function LocationsInAfrica({ t }) {
  const contacts = [
    {
      title: t("africa:locations:morocco:title"),
      locations: [t("africa:locations:morocco:address")],
      phones: ["+212 5 22 78 63 66"],
      email: "<EMAIL>",
      link: `/${websiteRoutesList.moroccoPage.route}`,
      img: morroccoImg,
      country: OfficesCountries.MOROCCO,
      alt: t("africa:locations:morocco:alt")
    },
    {
      title: t("africa:locations:algeria1:title"),
      locations: [t("africa:locations:algeria1:address")],
      phones: ["+213 982 30 13 29"],
      email: "<EMAIL>",
      link: `/${websiteRoutesList.algeriaPage.route}`,
      img: HydraImg,
      country: OfficesCountries.ALGERIAHYDRA,
      alt: t("africa:locations:algeria1:alt")
    },
    {
      title: t("africa:locations:algeria2:title"),
      locations: [t("africa:locations:algeria2:address")],
      phones: ["+33 1 73 07 42 54"],
      email: "<EMAIL>",
      link: `/${websiteRoutesList.algeriaPage.route}`,
      img: hassiImg,
      country: OfficesCountries.ALGERIAHASSI,
      alt: t("africa:locations:algeria2:alt")
    },
    {
      title: t("africa:locations:tunisia:title"),
      locations: [t("africa:locations:tunisia:address")],
      phones: ["+216 31 385 510"],
      email: "<EMAIL>",
      link: `/${websiteRoutesList.tunisiaPage.route}`,
      img: tunisiaImg,
      country: OfficesCountries.TUNISIA,
      alt: t("africa:locations:tunisia:alt")
    },
    {
      title: t("africa:locations:libya:title"),
      locations: [t("africa:locations:libya:address")],
      phones: ["+33 1 73 07 42 54"],
      email: "<EMAIL>",
      link: `/${websiteRoutesList.libyaPage.route}`,
      img: libyaImg,
      country: OfficesCountries.LIBYA,
      alt: t("africa:locations:libya:alt")
    },
    {
      title: t("africa:locations:egypt:title"),
      locations: [t("africa:locations:egypt:address")],
      phones: ["+213(0)23485910", "+213(0)23485144"],
      email: "<EMAIL>",
      link: `/${websiteRoutesList.egyptePage.route}`,
      img: egypteImg,
      country: OfficesCountries.EGYPT,
      alt: t("africa:locations:egypt:alt")
    },
  ];

  return (
    <LocationsList
      t={t}
      contacts={contacts}
      sectionSubtitleKey="africa:locations:subTitle"
      sectionTitleKey="africa:locations:title"
    />
  );
}

export default LocationsInAfrica;
