"use client";
import banner from "@/assets/images/website/banner/industries.png";
import { Container } from "@mui/material";
import useEmblaCarousel from "embla-carousel-react";

import {
  findIndustryClassname,
  findIndustryIcon,
  findIndustryLabel,
} from "@/utils/functions";
import { useTranslation } from "react-i18next";
import Autoplay from "embla-carousel-autoplay";

function OurIndustries() {
  const { t } = useTranslation();

  const OPTIONS = { loop: false, align: "start" };
  const slides = [
    {
      description: t("mainService:industries:industry1:description"),
      link: "/services",
      industry: "OIL_GAS",
      label : t("mainService:industries:industry1:label")
    },
    {
      description: t("mainService:industries:industry2:description"),
      link: "/services",
      industry: "TRANSPORT",
      label : t("mainService:industries:industry2:label")

    },
    {
      description: t("mainService:industries:industry3:description"),
      link: "/services",
      industry: "IT_TELECOM",
      label : t("mainService:industries:industry3:label")

    },
    {
      description: t("mainService:industries:industry4:description"),
      link: "/services",
      industry: "ENERGY",
      label : t("mainService:industries:industry4:label")

    },
    {
      description: t("mainService:industries:industry5:description"),
      link: "/services",
      industry:  "Banking",
      label : t("mainService:industries:industry5:label")

    },
  ];
  const [emblaRef] =  useEmblaCarousel(OPTIONS, [
    Autoplay({ playOnInit: true, delay: 3000 }),
  ]);

  return (
    <div id="our-industries" style={{ backgroundImage: `url(${banner.src})` }}>
      <Container  className="custom-max-width">
        <h2 className="heading-h1 text-white">{t("mainService:industries:title")}</h2>

        <section className="embla" id={"industries__slider"}>
          <div className="embla__viewport" ref={emblaRef}>
            <div className="embla__container">
              {slides.map((item, index) => (
                <div className={`embla__slide`} key={index}>
                  <Container
                    className={`slide__container smart-square test-${item.industry} ${findIndustryClassname(
                      item.industry
                    )}`}
                  >
                    <div className={`embla__slide__content`}>
                      <p className="embla__slide__industry-icon">
                        {findIndustryIcon(item.industry)}
                      </p>

                      <p className="embla__slide__industry-name">
                        {item.label}
                      </p>

                      <p className="embla__slide__description">
                        {item.description}
                      </p>
                      {/* <CustomButton
                        text={"discover"}
                        className={"btn btn-ghost text-white"}
                        link={item.link}
                        icon={<SvgArrowDown />}
                      /> */}
                    </div>
                  </Container>
                </div>
              ))}
            </div>
          </div>
        </section>
      </Container>
    </div>
  );
}

export default OurIndustries;
