"use client";
import LaborLaws from "../LaborLaws";
import { createList } from '@/utils/functions';
export const LABOR_KEYS = {
    workingHours: {
        title: "dubai:dubaiLabor:workingHours:title",
    },
    employmentContracts: {
        title: "dubai:dubaiLabor:employmentContracts:title",
    },

};
export const uaeLaborData = {
    title: "dubai:dubaiLabor:title",
    sections: [
        {
            id: 1,
            title: LABOR_KEYS.workingHours.title,
            subsections: [
                {
                    title: "dubai:dubaiLabor:workingHours:title1",
                    description: "dubai:dubaiLabor:workingHours:description1",
                },
                {
                    title: "dubai:dubaiLabor:workingHours:title2",
                    list: createList(["dubai:dubaiLabor:workingHours:description2", "dubai:dubaiLabor:workingHours:description3"]),

                },

            ],
        },
        {
            id: 2,
            title: LABOR_KEYS.employmentContracts.title,
            subsections: [

                {
                    description: "dubai:dubaiLabor:employmentContracts:title1",
                    list: createList(["dubai:dubaiLabor:employmentContracts:data1", "dubai:dubaiLabor:employmentContracts:data2"]),
                },

            ],
        },
        {
            id: 3,
            title: "dubai:dubaiLabor:payroll:title",
            type: "payroll",
            titleKey: "dubai:dubaiLabor:payroll:title1",
            descriptionKey: "dubai:dubaiLabor:payroll:description",
            items: [
                {
                    titleKey: "dubai:dubaiLabor:payroll:fiscalYear:title",
                    dateKeys: ["dubai:dubaiLabor:payroll:fiscalYear:date1", "dubai:dubaiLabor:payroll:fiscalYear:date2"],
                    descriptionKey: "dubai:dubaiLabor:payroll:fiscalYear:description",
                },
                {
                    titleKey: "dubai:dubaiLabor:payroll:payrollCycle:title",
                    dateKeys: ["dubai:dubaiLabor:payroll:payrollCycle:date"],
                    descriptionKey: "dubai:dubaiLabor:payroll:payrollCycle:description",
                },
                {
                    titleKey: "dubai:dubaiLabor:payroll:minimumWage:title",
                    dateKeys: ["dubai:dubaiLabor:payroll:minimumWage:wage", "dubai:dubaiLabor:payroll:minimumWage:date"],
                    descriptionKey: "dubai:dubaiLabor:payroll:minimumWage:description",
                },
                {
                    titleKey: "dubai:dubaiLabor:payroll:payrollManagement:title",
                    dateKeys: ["dubai:dubaiLabor:payroll:payrollManagement:date1", "dubai:dubaiLabor:payroll:payrollManagement:date2"],
                    descriptionKey: "dubai:dubaiLabor:payroll:payrollManagement:description",
                },


            ],
        },
        {
            id: 4,
            title: "dubai:dubaiLabor:termination:title",
            subsections: [
                {
                    title: "dubai:dubaiLabor:termination:title1",
                    description: "dubai:dubaiLabor:termination:description1",
                },
                {
                    title: "dubai:dubaiLabor:termination:title2",
                    description: "dubai:dubaiLabor:termination:description2",
                },
                {
                    list: createList(["dubai:dubaiLabor:termination:data1", "dubai:dubaiLabor:termination:data2", "dubai:dubaiLabor:termination:data3", "dubai:dubaiLabor:termination:data4", "dubai:dubaiLabor:termination:data5", "dubai:dubaiLabor:termination:data6", "dubai:dubaiLabor:termination:data7", "dubai:dubaiLabor:termination:data8", "dubai:dubaiLabor:termination:data9"]),
                },

                {
                    title: "dubai:dubaiLabor:termination:title3",
                    description: "dubai:dubaiLabor:termination:description3",
                },
                {
                    title: "dubai:dubaiLabor:termination:title4",
                    description: "dubai:dubaiLabor:termination:description4"
                }
            ],
        },
        {
            id: 5,
            title: "dubai:dubaiLabor:leaveEntitlements:title",
            type: "leaveEntitlements",
            data: {
                subTitle: "dubai:dubaiLabor:leaveEntitlements:description",


                leaves: [
                    { date: "dubai:dubaiLabor:leaveEntitlements:leaves:dataS1:date", title: "dubai:dubaiLabor:leaveEntitlements:leaves:dataS1:title" },
                    { date: "dubai:dubaiLabor:leaveEntitlements:leaves:dataS2:date", title: "dubai:dubaiLabor:leaveEntitlements:leaves:dataS2:title" },
                    { date: "dubai:dubaiLabor:leaveEntitlements:leaves:dataS3:date", title: "dubai:dubaiLabor:leaveEntitlements:leaves:dataS3:title" },
                    { date: "dubai:dubaiLabor:leaveEntitlements:leaves:dataS4:date", title: "dubai:dubaiLabor:leaveEntitlements:leaves:dataS4:title" },
                    { date: "dubai:dubaiLabor:leaveEntitlements:leaves:dataS5:date", title: "dubai:dubaiLabor:leaveEntitlements:leaves:dataS5:title" },
                    { date: "dubai:dubaiLabor:leaveEntitlements:leaves:dataS6:date", title: "dubai:dubaiLabor:leaveEntitlements:leaves:dataS6:title" },
                    { date: "dubai:dubaiLabor:leaveEntitlements:leaves:dataS7:date", title: "dubai:dubaiLabor:leaveEntitlements:leaves:dataS7:title" },
                    { date: "dubai:dubaiLabor:leaveEntitlements:leaves:dataS8:date", title: "dubai:dubaiLabor:leaveEntitlements:leaves:dataS8:title" },
                ],

                maternityLeave: {
                    title: "dubai:dubaiLabor:leaveEntitlements:leaves:maternityLeave:title",
                    description: ["dubai:dubaiLabor:leaveEntitlements:leaves:maternityLeave:description1"],
                },
                sickLeave: {
                    title: "dubai:dubaiLabor:leaveEntitlements:leaves:sickLeave:title",
                    description: "dubai:dubaiLabor:leaveEntitlements:leaves:sickLeave:description",
                },

            },
        },
        {
            id: 6,
            title: "dubai:dubaiLabor:tax:title",
            subsections: [
                { description: "dubai:dubaiLabor:tax:description" },
                {
                    title: "dubai:dubaiLabor:tax:title1",
                    description: "dubai:dubaiLabor:tax:description1"

                },
                {
                    list: createList(["dubai:dubaiLabor:tax:data1", "dubai:dubaiLabor:tax:data2", "dubai:dubaiLabor:tax:data3"]),
                },
                {
                    title: "dubai:dubaiLabor:tax:title2",
                    description: "dubai:dubaiLabor:tax:description2",
                    list: createList(["dubai:dubaiLabor:tax:dataS1", "dubai:dubaiLabor:tax:dataS2", "dubai:dubaiLabor:tax:dataS3"]),
                },
            ],
        },
        {
            id: 7,
            title: "dubai:dubaiLabor:visa:title",
            subsections: [
                { description: "dubai:dubaiLabor:visa:description1" },
                { description: "dubai:dubaiLabor:visa:description2" },
                {
                    title: "dubai:dubaiLabor:visa:titleResidence",
                    description: "dubai:dubaiLabor:visa:descriptionResidence"
                },
                { title: "dubai:dubaiLabor:visa:title1" },
                {
                    title: "dubai:dubaiLabor:visa:title2",
                    description: "dubai:dubaiLabor:visa:description3"
                },
                {
                    title: "dubai:dubaiLabor:visa:title3",
                    list: createList(["dubai:dubaiLabor:visa:data1", "dubai:dubaiLabor:visa:data2", "dubai:dubaiLabor:visa:data3", "dubai:dubaiLabor:visa:data4", "dubai:dubaiLabor:visa:data5", "dubai:dubaiLabor:visa:data6", "dubai:dubaiLabor:visa:data7", "dubai:dubaiLabor:visa:data8"]),

                },

                { description: "dubai:dubaiLabor:visa:note" }
            ],
        },
    ],
};


export default function UAELaborData() {
    return <LaborLaws data={uaeLaborData} />;
}
