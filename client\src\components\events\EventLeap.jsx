import { Container, Grid } from "@mui/material";
import EmblaCarousel from "@/components/embla_slider/EmblaCarousel";
import image1 from "@/assets/images/leap/image1.png";
import image2 from "@/assets/images/leap/image2.png";
import image3 from "@/assets/images/leap/image3.png";
import libya from "../../assets/images/events/libya.png";
import image4 from "@/assets/images/leap/image4.png";
import image5 from "@/assets/images/leap/image5.png";
import image6 from "@/assets/images/leap/image6.png";
import pentabellFrance from "../../assets/images/events/pentabellFrance.png";
import leap from "../../assets/images/events/leap.png";
import imagemobile1 from "@/assets/images/leap/leap1.png";
import imagemobile2 from "@/assets/images/leap/leap2.png";
import imagemobile3 from "@/assets/images/leap/leap3.png";
import imagemobile4 from "@/assets/images/leap/leap4.png";
import imagemobile5 from "@/assets/images/leap/leap5.png";
import imagemobile6 from "@/assets/images/leap/leap6.png";
import EventsCard from "./EventsCard";
import initTranslations from "@/app/i18n";
import InfoSection from "@/components/ui/InfoSection";
export default async function EventLeap({ language }) {
  const { t } = await initTranslations(language, [
    "eventDetailsLeap",
    "eventDetails",
    "event",
    "global",
  ]);
  const OPTIONS = {};
  const SLIDES = [image1, image2, image3, image4, image5, image6];
  const SlidesMobile = [
    imagemobile1,
    imagemobile2,
    imagemobile3,
    imagemobile4,
    imagemobile5,
    imagemobile6,
  ]
  const leapSection = {
    sector: t("eventDetailsLeap:sectorValue"),
    country: t("eventDetailsLeap:locationValue"),
    countryConcerned: t("eventDetailsLeap:countryConcernedValue"),
    organiser: t("eventDetailsLeap:organiserValue"),
  }

  const eventData = [
    {
      title: t("event:events.eventLibya.title"),
      eventDate: t("event:events.eventLibya.eventDate"),
      postingDate: t("event:events.eventLibya.postingDate"),
      exactPlace: t("event:events.eventLibya.exactPlace"),
      link: "Libyan-French-economic-forum-2025",
      type: "events",
      image: libya,
    },
    {
      title: t("event:events.event11.title"),
      eventDate: t("event:events.event11.eventDate"),
      postingDate: t("event:events.event11.postingDate"),
      exactPlace: t("event:events.event11.exactPlace"),
      type: "events",
      link: `leap-tech-conference-2025-riyadh`,
      image: leap,
    },
    {
      title: t("event:events.event2.title"),
      eventDate: t("event:events.event2.eventDate"),
      postingDate: t("event:events.event2.postingDate"),
      exactPlace: t("event:events.event2.exactPlace"),
      link:
        language === "en"
          ? `pentabell-salon-sme-and-european-microwave-week`
          : "pentabell-salon-sme-and-european-microwave-week",
      type: "blog",
      image: pentabellFrance,
    },
  ];
  return (
    <div id="event-page">
      <div id="event-detail">
        <Container className="custom-max-width">
          <InfoSection t={t} infos={leapSection} />
          <div className="details" >
            <p className="text" >  {t("eventDetails:description")} </p>
            <p className="heading-h1">
              {t("eventDetailsLeap:aboutEvent:aboutEvent")}
            </p>
            <p className="text">
              {t("eventDetailsLeap:aboutEvent:description1")}
            </p>
            <EmblaCarousel slides={SLIDES} options={OPTIONS} slidesMobile={SlidesMobile} />
            <p className="heading-h1">{t("eventDetailsLeap:moreEvents")}</p>
          </div>
          <Grid
            className="more-events-section"
            container
            rowSpacing={0}
            columnSpacing={3}

          >
            {eventData?.map((event, index) => (
              <EventsCard
                key={index}
                eventData={event}
                language={language}
                isEvent={true}
              />
            ))}
          </Grid>
        </Container>
      </div>
    </div>
  );
}
