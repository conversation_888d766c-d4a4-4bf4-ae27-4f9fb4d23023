"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const skill_model_1 = __importDefault(require("./skill.model"));
const functions_1 = require("@/utils/helpers/functions");
const messages_1 = require("@/utils/helpers/messages");
class SkillService {
    constructor() {
        this.Skill = skill_model_1.default;
    }
    async get(id) {
        const skill = await this.Skill.findById(id);
        if (!skill)
            throw new http_exception_1.default(404, messages_1.MESSAGES.SKILLS.NOT_FOUND);
        return skill;
    }
    async getAll(queries) {
        const { industry, industries, name, paginated, isTopTen } = queries;
        let skills = {};
        let totalSkills;
        let totalPages;
        const pageNumber = Number(queries.pageNumber) || 1;
        const pageSize = Number(queries.pageSize) || 10;
        const query = {};
        if (isTopTen) {
            query['isTopTen'] = isTopTen;
        }
        if (industry) {
            query['industry'] = { $in: industry };
        }
        if (industries) {
            const industriesArray = industries.split(',');
            query['industry'] = { $in: industriesArray };
        }
        if (name) {
            query['name'] = RegExp(`.*${name}.*`, 'i');
        }
        if (paginated && paginated === 'false') {
            skills = await this.Skill.find(query).sort({ createdAt: -1 });
            totalSkills = await this.Skill.countDocuments(query);
            return { totalSkills, skills };
        }
        else {
            skills = await this.Skill.find(query)
                .sort({ createdAt: -1 })
                .skip((pageNumber - 1) * pageSize)
                .limit(pageSize || 10);
            totalSkills = await this.Skill.countDocuments(query);
            totalPages = Math.ceil(totalSkills / pageSize);
            return {
                pageNumber,
                pageSize,
                totalSkills,
                totalPages,
                skills,
            };
        }
    }
    async create(skillData) {
        const oldSkill = await this.Skill.findOne({
            name: { $regex: new RegExp(`^${skillData.name}$`, 'i') },
            industry: { $regex: new RegExp(`^${skillData.industry}$`, 'i') },
        });
        if (oldSkill) {
            throw new http_exception_1.default(409, `Skill: '${oldSkill.name}' already exists in industry '${oldSkill.industry}'`);
        }
        return await this.Skill.create(skillData);
    }
    async createMany(file) {
        const skillsData = JSON.parse(file.buffer.toString());
        console.log(`found ${skillsData.length} skills`);
        const mapIndustry = (industry) => {
            const industries = {
                'IT & TELECOM': 'It & Telecom',
                TRANSPORT: 'Transport',
                'OIL & GAS': 'Oil & gas',
                ENERGY: 'Energy',
                OTHERS: 'Others',
                BANKING: 'Banking',
                PHARMACEUTICAL: 'PHARMACEUTICAL',
            };
            return industries[industry];
        };
        for (const skill of skillsData) {
            const oldSkill = await this.Skill.findOne({
                name: { $regex: new RegExp(`^${skill.name}$`, 'i') },
                industry: { $regex: new RegExp(`^${mapIndustry(skill.industry)}$`, 'i') },
            });
            if (!oldSkill) {
                const createdSkill = await this.Skill.create({ name: skill.name, industry: mapIndustry(skill.industry) });
                console.log(`Created ${createdSkill.name}`);
            }
            await (0, functions_1.delay)(200);
        }
    }
    async delete(id) {
        const skill = await this.get(id);
        await this.Skill.findByIdAndDelete(id);
        return skill;
    }
    async update(id, skillData) {
        return await this.Skill.findByIdAndUpdate(id, skillData, { new: true });
    }
}
exports.default = SkillService;
//# sourceMappingURL=skill.service.js.map