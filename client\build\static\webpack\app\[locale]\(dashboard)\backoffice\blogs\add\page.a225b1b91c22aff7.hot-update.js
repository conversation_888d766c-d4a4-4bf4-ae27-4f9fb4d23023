"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/add/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/AddArticleEN.jsx":
/*!*******************************************************!*\
  !*** ./src/features/blog/components/AddArticleEN.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! formik */ \"(app-pages-browser)/./node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-datepicker/dist/react-datepicker.css */ \"(app-pages-browser)/./node_modules/react-datepicker/dist/react-datepicker.css\");\n/* harmony import */ var _utils_urls__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../utils/urls */ \"(app-pages-browser)/./src/utils/urls.js\");\n/* harmony import */ var _assets_images_add_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/assets/images/add.png */ \"(app-pages-browser)/./src/assets/images/add.png\");\n/* harmony import */ var _feelinglovelynow_slug__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @feelinglovelynow/slug */ \"(app-pages-browser)/./node_modules/@feelinglovelynow/slug/dist/index.js\");\n/* harmony import */ var _FaqSection__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./FaqSection */ \"(app-pages-browser)/./src/features/blog/components/FaqSection.jsx\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormGroup/FormGroup.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormLabel/FormLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Stack/Stack.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Autocomplete/Autocomplete.js\");\n/* harmony import */ var _barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Autocomplete,FormGroup,FormLabel,Stack,TextField!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var react_tag_input__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! react-tag-input */ \"(app-pages-browser)/./node_modules/react-tag-input/dist/index.js\");\n/* harmony import */ var _features_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/features/opportunity/hooks/opportunity.hooks */ \"(app-pages-browser)/./src/features/opportunity/hooks/opportunity.hooks.js\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _components_ui_CustomTextInput__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/CustomTextInput */ \"(app-pages-browser)/./src/components/ui/CustomTextInput.jsx\");\n/* harmony import */ var _components_ui_CustomSunEditor__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/CustomSunEditor */ \"(app-pages-browser)/./src/components/ui/CustomSunEditor.jsx\");\n/* harmony import */ var _components_ui_CustomDatePicker__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/CustomDatePicker */ \"(app-pages-browser)/./src/components/ui/CustomDatePicker.jsx\");\n/* harmony import */ var _DocumentImporter__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./DocumentImporter */ \"(app-pages-browser)/./src/features/blog/components/DocumentImporter.jsx\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var _components_ui_CustomSelect__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/CustomSelect */ \"(app-pages-browser)/./src/components/ui/CustomSelect.jsx\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction AddArticleEN(param) {\n    let { errors, touched, setFieldValue, values, onImageSelect, filteredCategories, categories, onCategoriesSelect, debounce } = param;\n    _s();\n    const KeyCodes = {\n        comma: 188,\n        enter: 13\n    };\n    const delimiters = [\n        KeyCodes.comma,\n        KeyCodes.enter\n    ];\n    const [tags, setTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [highlights, setHighlights] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const [description, setDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(values.descriptionEN || \"\");\n    const [url, setUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(values.urlEN || \"\");\n    const [publishNow, setPublishNow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [publishDate, setPublishDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const imageInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const language = \"en\";\n    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedTitle = localStorage.getItem(\"title\");\n        return savedTitle ? JSON.parse(savedTitle) : \"\";\n    });\n    const [metatitle, setMetatitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedMetatitle = localStorage.getItem(\"metatitle\");\n        return savedMetatitle ? JSON.parse(savedMetatitle) : \"\";\n    });\n    const [metaDescription, setMetaDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedMetadescription = localStorage.getItem(\"metaDescription\");\n        return savedMetadescription ? JSON.parse(savedMetadescription) : \"\";\n    });\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const savedContent = localStorage.getItem(\"content\");\n        return savedContent ? JSON.parse(savedContent) : \"\";\n    });\n    const handlePaste = (event, cleanData, maxCharCount)=>{\n        let html = cleanData;\n        html = html.replace(/<strong>(.*?)$/g, \"<strong>$1</strong>\");\n        return html;\n    };\n    const handleEditorChange = (newContent)=>{\n        debounce();\n        setContent(newContent);\n        setFieldValue(\"contentEN\", newContent);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (title) {\n            localStorage.setItem(\"title\", JSON.stringify(title));\n        }\n        if (content) {\n            localStorage.setItem(\"content\", JSON.stringify(content));\n        }\n        if (metatitle) {\n            localStorage.setItem(\"metatitle\", JSON.stringify(metatitle));\n        }\n        if (metaDescription) {\n            localStorage.setItem(\"metaDescription\", JSON.stringify(metaDescription));\n        }\n    }, [\n        title,\n        content,\n        metatitle,\n        metaDescription\n    ]);\n    const handlePhotoChange = async ()=>{\n        const selectedFile = imageInputRef.current.files[0];\n        setSelectedImage(imageInputRef.current.files[0]);\n        if (selectedFile) {\n            onImageSelect(selectedFile, language);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setFieldValue(\"titleEN\", title);\n        setFieldValue(\"descriptionEN\", description);\n        setFieldValue(\"urlEN\", url);\n        setFieldValue(\"keywordsEN\", tags.map((t)=>t.text));\n        setFieldValue(\"highlightsEN\", highlights.map((h)=>h.text));\n        setFieldValue(\"contentEN\", content);\n        setFieldValue(\"metaTitleEN\", metatitle);\n        setFieldValue(\"metaDescriptionEN\", metaDescription);\n    }, [\n        title,\n        description,\n        url,\n        tags,\n        highlights,\n        content,\n        metatitle,\n        metaDescription\n    ]);\n    const useSaveFileHook = (0,_features_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_8__.useSaveFile)();\n    let uuidPhoto;\n    uuidPhoto = (0,uuid__WEBPACK_IMPORTED_MODULE_15__[\"default\"])().replace(/-/g, \"\");\n    const handlePhotoBlogChange = async (file, info, core, uploadHandler)=>{\n        if (file instanceof HTMLImageElement) {\n            const src = file.src;\n            if (src.startsWith(\"data:image\")) {\n                const base64Data = src.split(\",\")[1];\n                const contentType = src.match(/data:(.*?);base64/)[1];\n                const byteCharacters = atob(base64Data);\n                const byteNumbers = new Array(byteCharacters.length).fill(0).map((_, i)=>byteCharacters.charCodeAt(i));\n                const byteArray = new Uint8Array(byteNumbers);\n                const blob = new Blob([\n                    byteArray\n                ], {\n                    type: contentType\n                });\n                const fileName = `image_${Date.now()}.${contentType.split(\"/\")[1]}`;\n                const selectedFile = new File([\n                    blob\n                ], fileName, {\n                    type: contentType\n                });\n                await uploadFile(selectedFile, uploadHandler, core, file);\n            } else {\n                fetch(src).then((response)=>response.blob()).then((blob)=>{\n                    const contentType = blob.type;\n                    const fileName = `image_${Date.now()}.${contentType.split(\"/\")[1]}`;\n                    const selectedFile = new File([\n                        blob\n                    ], fileName, {\n                        type: contentType\n                    });\n                    uploadFile(selectedFile, uploadHandler, core, file);\n                }).catch((error)=>console.error(\"Error converting image URL to Blob:\", error));\n            }\n        } else {\n            console.error(\"File is not an HTMLImageElement.\");\n        }\n    };\n    const uploadFile = (selectedFile, uploadHandler, core, originalImage)=>{\n        let uuidPhoto;\n        uuidPhoto = (0,uuid__WEBPACK_IMPORTED_MODULE_15__[\"default\"])().replace(/-/g, \"\");\n        const formData = new FormData();\n        formData.append(\"file\", selectedFile);\n        const extension = selectedFile.name.split(\".\").pop();\n        const currentYear = new Date().getFullYear();\n        useSaveFileHook.mutate({\n            resource: \"blogs\",\n            folder: currentYear.toString(),\n            filename: uuidPhoto,\n            body: {\n                formData,\n                t\n            }\n        }, {\n            onSuccess: (dataUUID)=>{\n                const uuidPhotoFileName = dataUUID.message === \"uuid exist\" ? dataUUID.uuid : `${uuidPhoto}.${extension}`;\n                const imageUrl = `${\"http://localhost:4000/api/v1\"}/files/${uuidPhotoFileName}`;\n                originalImage.src = imageUrl;\n                uploadHandler({\n                    result: [\n                        {\n                            id: uuidPhotoFileName,\n                            url: imageUrl\n                        }\n                    ]\n                });\n            },\n            onError: (error)=>{\n                console.error(\"Error uploading file:\", error);\n            }\n        });\n    };\n    const handleContentExtracted = (extractedContent)=>{\n        console.log(\"extractedContent\", extractedContent);\n        console.log(\"values\", values);\n        console.log(\"content\", content);\n        console.log(\"setFieldValue\", setFieldValue);\n        setFieldValue(\"contentEN\", extractedContent);\n        setContent(extractedContent);\n        localStorage.setItem(\"content\", JSON.stringify(extractedContent));\n        debounce();\n    };\n    const handleMetadataExtracted = (metadata)=>{\n        if (metadata.title && !values.titleEN) {\n            setFieldValue(\"titleEN\", metadata.title);\n            const url = (0,_feelinglovelynow_slug__WEBPACK_IMPORTED_MODULE_6__.slug)(metadata.title);\n            setFieldValue(\"urlEN\", url);\n        }\n        if (metadata.description && !values.descriptionEN) {\n            setFieldValue(\"descriptionEN\", metadata.description);\n        }\n        if (metadata.keywords && metadata.keywords.length > 0) {\n            const keywordTags = metadata.keywords.map((keyword, index)=>({\n                    id: `extracted-${index}`,\n                    text: keyword\n                }));\n            const existingKeywords = values.keywordsEN || [];\n            const mergedKeywords = [\n                ...existingKeywords,\n                ...keywordTags\n            ];\n            setFieldValue(\"keywordsEN\", mergedKeywords.map((tag)=>tag.text));\n            const existingTags = tags || [];\n            const mergedTags = [\n                ...existingTags,\n                ...keywordTags\n            ];\n            setTags(mergedTags);\n        }\n        debounce();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"label-pentabell\",\n                children: \"Add article English : \"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 269,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTextInput__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    label: t(\"createArticle:title\"),\n                                    name: \"titleEN\",\n                                    value: title,\n                                    onChange: (e)=>{\n                                        const v = e.target.value;\n                                        setTitle(v);\n                                        setUrl((0,_feelinglovelynow_slug__WEBPACK_IMPORTED_MODULE_6__.slug)(v));\n                                        debounce();\n                                    },\n                                    error: touched.titleEN && errors.titleEN\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 273,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 271,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"label-form\",\n                                    children: [\n                                        t(\"createArticle:categories\"),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                multiple: true,\n                                                className: \"input-pentabell\",\n                                                id: \"tags-standard\",\n                                                options: filteredCategories.length > 0 ? filteredCategories : categories,\n                                                getOptionLabel: (option)=>option.name,\n                                                selected: values.categoryEN.length > 0 ? (filteredCategories.length > 0 ? filteredCategories : categories).filter((category)=>values.categoryEN.includes(category.id)) : [],\n                                                onChange: (event, selectedOptions)=>{\n                                                    const categoryIds = selectedOptions.map((category)=>category.id);\n                                                    setFieldValue(\"categoryEN\", categoryIds);\n                                                    onCategoriesSelect(categoryIds);\n                                                },\n                                                renderInput: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        ...params,\n                                                        className: \"input-pentabell  multiple-select\",\n                                                        variant: \"standard\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 21\n                                                    }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 289,\n                                columnNumber: 11\n                            }, this),\n                            touched.category && errors.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"label-error\",\n                                children: errors.category\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 333,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 270,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTextInput__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            label: \"Description\",\n                            name: \"descriptionEN\",\n                            rows: 3,\n                            multiline: true,\n                            value: values.descriptionEN,\n                            onChange: (e)=>{\n                                const descriptionEN = e.target.value;\n                                setFieldValue(\"descriptionEN\", descriptionEN);\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 340,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 339,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                    lineNumber: 338,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 337,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"label-form\",\n                            children: [\n                                \"Highlights\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    id: \"tags\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tag_input__WEBPACK_IMPORTED_MODULE_21__.WithContext, {\n                                        tags: highlights,\n                                        className: \"input-pentabell\" + (errors.highlightsEN && touched.highlightsEN ? \" is-invalid\" : \"\"),\n                                        delimiters: delimiters,\n                                        handleDelete: (i)=>{\n                                            const updatedTags = highlights.filter((tag, index)=>index !== i);\n                                            setHighlights(updatedTags);\n                                            setFieldValue(\"highlightsEN\", updatedTags.map((tag)=>tag.text));\n                                        },\n                                        handleAddition: (tag)=>{\n                                            setHighlights([\n                                                ...highlights,\n                                                tag\n                                            ]);\n                                            setFieldValue(\"highlightsEN\", [\n                                                ...highlights,\n                                                tag\n                                            ].map((item)=>item.text));\n                                        },\n                                        inputFieldPosition: \"bottom\",\n                                        autocomplete: true,\n                                        allowDragDrop: false\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_22__.ErrorMessage, {\n                                    className: \"label-error\",\n                                    name: \"keywordsEN\",\n                                    component: \"div\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 357,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 356,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                    lineNumber: 355,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 354,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DocumentImporter__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        onContentExtracted: handleContentExtracted,\n                        onMetadataExtracted: handleMetadataExtracted,\n                        language: \"EN\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 401,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomSunEditor__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        content: content || values?.contentEN || \"\",\n                        onChange: (newContent)=>{\n                            setContent(newContent);\n                            setFieldValue(\"contentEN\", newContent);\n                            debounce();\n                        },\n                        onPaste: handlePaste,\n                        onImageUpload: handlePhotoBlogChange\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 406,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 400,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 418,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FaqSection__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                values: values,\n                setFieldValue: setFieldValue,\n                errors: errors,\n                touched: touched,\n                language: \"EN\",\n                debounce: debounce\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 419,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 427,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTextInput__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    label: t(\"createArticle:metaTitle\"),\n                                    name: \"metaTitleEN\",\n                                    value: metatitle || values.metaTitleEN,\n                                    onChange: (e)=>{\n                                        const metaTitleEN = e.target.value;\n                                        setFieldValue(\"metaTitleEN\", metaTitleEN);\n                                        setMetatitle(metaTitleEN);\n                                        debounce();\n                                    },\n                                    showLength: true,\n                                    maxLength: 65\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 432,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 431,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 429,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTextInput__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                label: t(\"createArticle:url\"),\n                                name: \"urlEN\",\n                                value: url,\n                                onChange: (e)=>setUrl(e.target.value),\n                                error: touched.urlEN && errors.urlEN\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 449,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 448,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 447,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 428,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTextInput__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            label: t(\"createArticle:metaDescription\"),\n                            name: \"metaDescriptionEN\",\n                            value: metaDescription || values.metaDescriptionEN,\n                            onChange: (e)=>{\n                                const metaDescriptionEN = e.target.value;\n                                setFieldValue(\"metaDescriptionEN\", metaDescriptionEN);\n                                setMetaDescription(metaDescriptionEN);\n                                debounce();\n                            },\n                            showLength: true,\n                            maxLength: 160\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 462,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 461,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                    lineNumber: 460,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 459,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"label-form\",\n                            children: [\n                                t(\"createArticle:featuredImage\"),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"upload-container\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: `image-upload-en`,\n                                        className: \"file-labels\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"file\",\n                                                id: `image-upload-en`,\n                                                name: \"imageEN\",\n                                                accept: \".png, .jpg, .jpeg, .webp\",\n                                                ref: imageInputRef,\n                                                onChange: (e)=>{\n                                                    setFieldValue(\"imageEN\", e.target.files[0]);\n                                                    handlePhotoChange();\n                                                },\n                                                className: \"file-input\" + (errors.imageEN && touched.imageEN ? \" is-invalid\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"upload-area\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"icon-pic\",\n                                                            style: {\n                                                                backgroundImage: `url(\"${selectedImage ? URL.createObjectURL(selectedImage) : values.imageEN ? `${process.env.REACT_APP_API_URL}${_utils_urls__WEBPACK_IMPORTED_MODULE_4__.API_URLS.files}/${values.imageEN}` : _assets_images_add_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"].src}\")`,\n                                                                backgroundSize: \"cover\",\n                                                                backgroundRepeat: \"no-repeat\",\n                                                                backgroundPosition: \"center\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                            lineNumber: 503,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"upload-text\",\n                                                                children: t(\"createArticle:addFeatImg\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                                lineNumber: 521,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"upload-description\",\n                                                                children: t(\"createArticle:clickBox\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                                lineNumber: 524,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                        lineNumber: 520,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_22__.ErrorMessage, {\n                                                name: \"image\",\n                                                component: \"div\",\n                                                className: \"invalid-feedback error\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                lineNumber: 529,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 481,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 480,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                    lineNumber: 479,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 478,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomTextInput__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                label: t(\"createArticle:alt\"),\n                                name: \"altEN\",\n                                value: values.altEN,\n                                onChange: (e)=>{\n                                    setFieldValue(\"altEN\", e.target.value);\n                                    debounce();\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 544,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 543,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 542,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomSelect__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    label: t(\"createArticle:visibility\"),\n                                    name: \"visibilityEN\",\n                                    value: values.visibilityEN,\n                                    onChange: (e)=>setFieldValue(\"visibilityEN\", e.target.value),\n                                    options: _utils_constants__WEBPACK_IMPORTED_MODULE_13__.Visibility,\n                                    error: touched.visibilityEN && errors.visibilityEN,\n                                    getOptionLabel: (item)=>item,\n                                    getOptionValue: (item)=>item\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 558,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 557,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 555,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"label-form\",\n                                    children: [\n                                        t(\"createArticle:keyword\"),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            id: \"tags\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_tag_input__WEBPACK_IMPORTED_MODULE_21__.WithContext, {\n                                                tags: tags,\n                                                className: \"input-pentabell\" + (errors.keywordsEN && touched.keywordsEN ? \" is-invalid\" : \"\") + (tags.length === 0 ? \" no-tags\" : \"\"),\n                                                delimiters: delimiters,\n                                                handleDelete: (i)=>{\n                                                    const updatedTags = tags.filter((tag, index)=>index !== i);\n                                                    setTags(updatedTags);\n                                                    setFieldValue(\"keywordsEN\", updatedTags.map((tag)=>tag.text));\n                                                },\n                                                handleAddition: (tag)=>{\n                                                    setTags([\n                                                        ...tags,\n                                                        tag\n                                                    ]);\n                                                    setFieldValue(\"keywordsEN\", [\n                                                        ...tags,\n                                                        tag\n                                                    ].map((item)=>item.text));\n                                                    debounce();\n                                                },\n                                                inputFieldPosition: \"bottom\",\n                                                autocomplete: true,\n                                                allowDragDrop: false\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                                lineNumber: 578,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                            lineNumber: 577,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_22__.ErrorMessage, {\n                                            className: \"label-error\",\n                                            name: \"keywordsEN\",\n                                            component: \"div\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                            lineNumber: 611,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 574,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 573,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                        lineNumber: 571,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 541,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"inline-group\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"label-form\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_22__.Field, {\n                                    type: \"checkbox\",\n                                    name: \"publishNow\",\n                                    checked: publishNow,\n                                    onChange: (e)=>{\n                                        setPublishNow(e.target.checked);\n                                        if (e.target.checked) {\n                                            setFieldValue(\"publishDateEN\", new Date().toISOString());\n                                        }\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 623,\n                                    columnNumber: 13\n                                }, this),\n                                t(\"createArticle:publishNow\")\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 622,\n                            columnNumber: 11\n                        }, this),\n                        !publishNow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Autocomplete_FormGroup_FormLabel_Stack_TextField_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomDatePicker__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    label: t(\"createArticle:publishDate\"),\n                                    value: values.publishDateEN || new Date(),\n                                    onChange: (date)=>setFieldValue(\"publishDateEN\", date),\n                                    error: touched.publishDateEN && errors.publishDateEN\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                    lineNumber: 640,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                                lineNumber: 639,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                            lineNumber: 638,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                    lineNumber: 621,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 620,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_22__.Field, {\n                type: \"hidden\",\n                name: \"publishDateEN\",\n                value: publishNow ? new Date().toISOString() : publishDate.toISOString()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleEN.jsx\",\n                lineNumber: 652,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(AddArticleEN, \"llvZVW20fTwrlxkztVeG1u5sgoE=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _features_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_8__.useSaveFile\n    ];\n});\n_c = AddArticleEN;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AddArticleEN);\nvar _c;\n$RefreshReg$(_c, \"AddArticleEN\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/AddArticleEN.jsx\n"));

/***/ })

});