"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserResponseForCommentDTO = exports.UserInformationDTO = exports.UserDTO = void 0;
const constants_1 = require("@/utils/helpers/constants");
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
class UserDTO {
}
exports.UserDTO = UserDTO;
__decorate([
    (0, class_transformer_1.Expose)({ name: '_id' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserDTO.prototype, "id", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'firstName' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserDTO.prototype, "givenName", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'lastName' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserDTO.prototype, "familyName", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'profilePicture' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserDTO.prototype, "picture", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'email' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserDTO.prototype, "email", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'jobTitle' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserDTO.prototype, "job", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'country' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", Object)
], UserDTO.prototype, "location", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'isActive' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UserDTO.prototype, "active", void 0);
__decorate([
    (0, class_transformer_1.Exclude)({ toPlainOnly: true }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserDTO.prototype, "password", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'phone' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserDTO.prototype, "phoneNumber", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'roles' }),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], UserDTO.prototype, "roles", void 0);
__decorate([
    (0, class_transformer_1.Exclude)({ toPlainOnly: true }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserDTO.prototype, "resetPasswordToken", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'confirmAccountToken' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserDTO.prototype, "accountToken", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'isArchived' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UserDTO.prototype, "archived", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'company' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserDTO.prototype, "company", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'nationalities' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", Array)
], UserDTO.prototype, "nationalities", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'dateOfBirth' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], UserDTO.prototype, "birthDate", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'gender' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserDTO.prototype, "gender", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'addressLine' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserDTO.prototype, "address", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'candidate' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserDTO.prototype, "additionalInformation", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'createdAt' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], UserDTO.prototype, "creationDate", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Date)
], UserDTO.prototype, "updatedAt", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Number)
], UserDTO.prototype, "__v", void 0);
class UserInformationDTO {
}
exports.UserInformationDTO = UserInformationDTO;
__decorate([
    (0, class_transformer_1.Expose)({ name: '_id' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserInformationDTO.prototype, "id", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'firstName' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserInformationDTO.prototype, "givenName", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'lastName' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserInformationDTO.prototype, "familyName", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'profilePicture' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserInformationDTO.prototype, "picture", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'email' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserInformationDTO.prototype, "email", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'jobTitle' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserInformationDTO.prototype, "job", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'country' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", Object)
], UserInformationDTO.prototype, "location", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'isActive' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UserInformationDTO.prototype, "active", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", String)
], UserInformationDTO.prototype, "password", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'phone' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserInformationDTO.prototype, "phoneNumber", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Array)
], UserInformationDTO.prototype, "roles", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", String)
], UserInformationDTO.prototype, "resetPasswordToken", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", String)
], UserInformationDTO.prototype, "confirmAccountToken", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'isArchived' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UserInformationDTO.prototype, "archived", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", String)
], UserInformationDTO.prototype, "company", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'nationalities' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", Array)
], UserInformationDTO.prototype, "nationalities", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'dateOfBirth' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], UserInformationDTO.prototype, "birthDate", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'gender' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserInformationDTO.prototype, "gender", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'addressLine' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserInformationDTO.prototype, "address", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Date)
], UserInformationDTO.prototype, "shortList", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Date)
], UserInformationDTO.prototype, "createdAt", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Date)
], UserInformationDTO.prototype, "updatedAt", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Number)
], UserInformationDTO.prototype, "__v", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Object)
], UserInformationDTO.prototype, "buffer", void 0);
// getAllCommentsByArticle - comments.service
class UserResponseForCommentDTO {
}
exports.UserResponseForCommentDTO = UserResponseForCommentDTO;
__decorate([
    (0, class_transformer_1.Expose)({ name: '_id' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserResponseForCommentDTO.prototype, "id", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'firstName' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserResponseForCommentDTO.prototype, "givenName", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'lastName' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserResponseForCommentDTO.prototype, "familyName", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'email' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserResponseForCommentDTO.prototype, "emailAddress", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'profilePicture' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserResponseForCommentDTO.prototype, "picture", void 0);
//# sourceMappingURL=user.dto.js.map