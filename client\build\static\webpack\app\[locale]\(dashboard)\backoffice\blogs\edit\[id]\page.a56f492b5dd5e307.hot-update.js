"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/edit/[id]/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/EditArticle.jsx":
/*!******************************************************!*\
  !*** ./src/features/blog/components/EditArticle.jsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,Grid,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Accordion/Accordion.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,Grid,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionSummary/AccordionSummary.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,Grid,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionDetails/AccordionDetails.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,Grid,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,Grid,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormGroup/FormGroup.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,Grid,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormLabel/FormLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,Grid,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,Grid,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,Grid,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mui/icons-material/ExpandMore */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/ExpandMore.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! yup */ \"(app-pages-browser)/./node_modules/yup/index.esm.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var _user_component_updateProfile_experience_DialogModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../user/component/updateProfile/experience/DialogModal */ \"(app-pages-browser)/./src/features/user/component/updateProfile/experience/DialogModal.jsx\");\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! formik */ \"(app-pages-browser)/./node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var _components_loading_Loading__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../components/loading/Loading */ \"(app-pages-browser)/./src/components/loading/Loading.jsx\");\n/* harmony import */ var _utils_urls__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../utils/urls */ \"(app-pages-browser)/./src/utils/urls.js\");\n/* harmony import */ var _opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../opportunity/hooks/opportunity.hooks */ \"(app-pages-browser)/./src/features/opportunity/hooks/opportunity.hooks.js\");\n/* harmony import */ var _config_axios__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/config/axios */ \"(app-pages-browser)/./src/config/axios.js\");\n/* harmony import */ var _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../hooks/blog.hook */ \"(app-pages-browser)/./src/features/blog/hooks/blog.hook.js\");\n/* harmony import */ var _AddArticle__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./AddArticle */ \"(app-pages-browser)/./src/features/blog/components/AddArticle.jsx\");\n/* harmony import */ var _assets_images_icons_archiveicon_popup_svg__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/assets/images/icons/archiveicon-popup.svg */ \"(app-pages-browser)/./src/assets/images/icons/archiveicon-popup.svg\");\n/* harmony import */ var react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! react-datepicker/dist/react-datepicker.css */ \"(app-pages-browser)/./node_modules/react-datepicker/dist/react-datepicker.css\");\n/* harmony import */ var _features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/features/auth/hooks/currentUser.hooks */ \"(app-pages-browser)/./src/features/auth/hooks/currentUser.hooks.js\");\n/* harmony import */ var _components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst EditArticle = (param)=>{\n    let { articleId } = param;\n    _s();\n    const { data } = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__.useGetArticleByIdAll)(articleId);\n    const usedisarchivedArticleHook = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__.usearchivedarticle)();\n    const { data: dataEN, isLoading: isLoadingEN, isError, error } = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__.useGetArticleById)(articleId, \"en\");\n    const { data: dataFR, isLoading: isLoadingFR } = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__.useGetArticleById)(articleId, \"fr\");\n    const [isArchived, setIsArchived] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isArchivedFr, setIsArchivedFr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { user } = (0,_features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_15__[\"default\"])();\n    const [englishVersion, setEnglishVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [frenchVersion, setFrenchVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const useUpdateArticleHook = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__.useUpdateArticle)();\n    const useSaveFileHook = (0,_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_9__.useSaveFile)();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const currentYear = new Date().getFullYear();\n    const formikRefEn = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const formikRefFr = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const formikRefAll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [expanded, setExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(`panel`);\n    const [formdataEN, setFormDataEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [formdataFR, setFormDataFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const formdata = new FormData();\n    const formdatafr = new FormData();\n    const [uuidPhotoFileNameEN, setUuidPhotoFileNameEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedAction, setSelectedAction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedEnCategories, setSelectedEnCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(dataEN?.versions?.categories.map((category)=>category.id) || []);\n    const [filteredFrCategories, setFilteredFrCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [openDialog, setOpenDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openDialogfr, setOpenDialogfr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleOpenDialog = (action1)=>{\n        setSelectedAction(action1);\n        setOpenDialog(true);\n    };\n    const handleOpenDialogfr = (action1)=>{\n        setSelectedAction(action1);\n        setOpenDialogfr(true);\n    };\n    const handleCloseDialogfr = ()=>{\n        setOpenDialogfr(false);\n    };\n    const handleCloseDialog = ()=>{\n        setOpenDialog(false);\n    };\n    const useUpdateArticleAllHook = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__.useUpdateArticleAll)();\n    const [uuidPhotoFileNameFR, setUuidPhotoFileNameFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [uuidPhotoEN, setUuidPhotoEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uuidPhotoFR, setUuidPhotoFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // CTS Banner state variables\n    const [uuidCTSBannerPhotoFileNameEN, setUuidCTSBannerPhotoFileNameEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [uuidCTSBannerPhotoFileNameFR, setUuidCTSBannerPhotoFileNameFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [uuidCTSBannerPhotoEN, setUuidCTSBannerPhotoEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uuidCTSBannerPhotoFR, setUuidCTSBannerPhotoFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [formDataCTSBannerEN, setFormDataCTSBannerEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [formDataCTSBannerFR, setFormDataCTSBannerFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setEnglishVersion(dataEN ? dataEN?.versions : \"\");\n        setFrenchVersion(dataFR ? dataFR?.versions : \"\");\n        if (dataEN?.versions?.categories.length > 0 && dataEN?.versions?.categories[0] != {}) {\n            setSelectedEnCategories(dataEN?.versions?.categories.map((category)=>category.id));\n        }\n        setIsArchived(dataEN?.versions?.isArchived || false);\n        setIsArchivedFr(dataFR?.versions?.isArchived || false);\n    }, [\n        dataEN,\n        dataFR\n    ]);\n    const handleConfirmAction = ()=>{\n        if (selectedAction === \"archive\") {\n            handledisarchivearticleen(true);\n        } else if (selectedAction === \"desarchive\") {\n            handledisarchivearticleen(false);\n        }\n        setOpenDialog(false);\n    };\n    const handleConfirmActionFr = ()=>{\n        if (selectedAction === \"archive\") {\n            handlearchiveanddisarchivearticleFr(true);\n        } else if (selectedAction === \"desarchive\") {\n            handlearchiveanddisarchivearticleFr(false);\n        }\n        setOpenDialogfr(false);\n    };\n    const handleImageSelect = async function(selectedFile, language) {\n        let imageType = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"main\";\n        if (language === \"en\") {\n            let uuidPhotos = (0,uuid__WEBPACK_IMPORTED_MODULE_17__[\"default\"])().replace(/-/g, \"\");\n            if (imageType === \"ctsBanner\") {\n                setUuidCTSBannerPhotoEN(uuidPhotos);\n                const ctsBannerFormData = new FormData();\n                ctsBannerFormData.append(\"file\", selectedFile);\n                setFormDataCTSBannerEN(ctsBannerFormData);\n                const extension = selectedFile.name.split(\".\").pop();\n                setUuidCTSBannerPhotoFileNameEN(`${uuidPhotos}.${extension}`);\n            } else {\n                setUuidPhotoEN(uuidPhotos);\n                formdata.append(\"file\", selectedFile);\n                setFormDataEN(formdata);\n                const extension = selectedFile.name.split(\".\").pop();\n                setUuidPhotoFileNameEN(`${uuidPhotos}.${extension}`);\n            }\n        } else if (language === \"fr\") {\n            let uuidPhotos = (0,uuid__WEBPACK_IMPORTED_MODULE_17__[\"default\"])().replace(/-/g, \"\");\n            if (imageType === \"ctsBanner\") {\n                setUuidCTSBannerPhotoFR(uuidPhotos);\n                const ctsBannerFormData = new FormData();\n                ctsBannerFormData.append(\"file\", selectedFile);\n                setFormDataCTSBannerFR(ctsBannerFormData);\n                const extension = selectedFile.name.split(\".\").pop();\n                setUuidCTSBannerPhotoFileNameFR(`${uuidPhotos}.${extension}`);\n            } else {\n                setUuidPhotoFR(uuidPhotos);\n                formdatafr.append(\"file\", selectedFile);\n                setFormDataFR(formdatafr);\n                const extension = selectedFile.name.split(\".\").pop();\n                setUuidPhotoFileNameFR(`${uuidPhotos}.${extension}`);\n            }\n        }\n    };\n    const handleRemoveCTSBanner = (language)=>{\n        if (language === \"en\") {\n            // Reset English CTS banner data\n            setUuidCTSBannerPhotoFileNameEN(\"\");\n            setUuidCTSBannerPhotoEN(null);\n            setFormDataCTSBannerEN(null);\n            // Update form values through formik ref\n            if (formikRefEn.current) {\n                formikRefEn.current.setFieldValue(\"ctsBannerImage\", null);\n                formikRefEn.current.setFieldValue(\"ctsBannerLink\", \"\");\n            }\n        } else if (language === \"fr\") {\n            // Reset French CTS banner data\n            setUuidCTSBannerPhotoFileNameFR(\"\");\n            setUuidCTSBannerPhotoFR(null);\n            setFormDataCTSBannerFR(null);\n            // Update form values through formik ref\n            if (formikRefFr.current) {\n                formikRefFr.current.setFieldValue(\"ctsBannerImage\", null);\n                formikRefFr.current.setFieldValue(\"ctsBannerLink\", \"\");\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setEnglishVersion(dataEN ? dataEN?.versions : \"\");\n        setFrenchVersion(dataFR ? dataFR?.versions : \"\");\n        setSelectedEnCategories(dataEN?.versions?.categories.map((category)=>category.id));\n    }, [\n        dataEN,\n        dataFR\n    ]);\n    const initialValuesEn = {\n        metaTitle: englishVersion?.metaTitle,\n        metaDescription: englishVersion?.metaDescription,\n        description: englishVersion?.description,\n        visibility: englishVersion ? englishVersion.visibility : \"\",\n        category: englishVersion?.categories?.length > 0 ? englishVersion.categories?.map((category)=>category.id) : [\n            {\n                name: \"\"\n            }\n        ],\n        image: englishVersion?.image,\n        keywords: englishVersion?.keywords?.map((keyword, index)=>({\n                id: keyword.trim() || `id-${index}`,\n                text: keyword.trim()\n            })),\n        title: englishVersion?.title,\n        url: englishVersion?.url,\n        alt: englishVersion?.alt,\n        content: englishVersion?.content,\n        language: \"en\",\n        publishDate: englishVersion?.publishDate,\n        createdBy: user?.firstName + \" \" + user?.lastName,\n        highlights: englishVersion?.highlights?.map((keyword, index)=>({\n                id: keyword.trim() || `id-${index}`,\n                text: keyword.trim()\n            })),\n        faqTitle: englishVersion?.faqTitle || \"\",\n        faq: englishVersion?.faq || [],\n        ctsBannerImage: englishVersion?.ctsBanner?.image || \"\",\n        ctsBannerLink: englishVersion?.ctsBanner?.link || \"\"\n    };\n    const initialValuesAll = {\n        robotsMeta: data?.robotsMeta\n    };\n    const initialValuesFr = {\n        metaTitle: frenchVersion ? frenchVersion?.metaTitle : \"\",\n        metaDescription: frenchVersion ? frenchVersion?.metaDescription : \"\",\n        description: frenchVersion ? frenchVersion?.description : \"\",\n        visibility: frenchVersion ? frenchVersion?.visibility : \"\",\n        category: frenchVersion?.categories?.length > 0 ? frenchVersion.categories?.map((category)=>category.id) : [\n            {\n                name: \"\"\n            }\n        ],\n        image: frenchVersion ? frenchVersion?.image : \"\",\n        keywords: frenchVersion?.keywords?.map((keyword, index)=>({\n                id: keyword?.trim() || `id-${index}`,\n                text: keyword?.trim()\n            })),\n        title: frenchVersion ? frenchVersion?.title : \"\",\n        url: frenchVersion ? frenchVersion?.url : \"\",\n        alt: frenchVersion ? frenchVersion?.alt : \"\",\n        content: frenchVersion ? frenchVersion?.content : \"\",\n        language: \"fr\",\n        publishDate: frenchVersion ? frenchVersion?.publishDate : \"\",\n        createdBy: user?.firstName + \" \" + user?.lastName,\n        highlights: frenchVersion?.highlights?.map((keyword, index)=>({\n                id: keyword.trim() || `id-${index}`,\n                text: keyword.trim()\n            })),\n        faqTitle: frenchVersion?.faqTitle || \"\",\n        faq: frenchVersion?.faq || [],\n        ctsBannerImage: frenchVersion?.ctsBanner?.image || \"\",\n        ctsBannerLink: frenchVersion?.ctsBanner?.link || \"\"\n    };\n    const validationSchemaEN = yup__WEBPACK_IMPORTED_MODULE_3__.object().shape({\n        metaTitle: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n        metaDescription: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n        title: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n        image: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n        visibility: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\"))\n    });\n    const handleSaveSetting = (values)=>{\n        const data = {\n            robotsMeta: values?.robotsMeta\n        };\n        useUpdateArticleAllHook.mutate({\n            data: data,\n            id: articleId\n        }, {\n            onSuccess: ()=>{\n            // setTimeout(\n            //\n            //   (window.location.href = `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.opportunities.route}/`),\n            //   \"3000\"\n            // );\n            }\n        });\n    };\n    const handledisarchivearticleen = async ()=>{\n        const action1 = isArchived ? false : true;\n        try {\n            await usedisarchivedArticleHook.mutateAsync({\n                language: \"en\",\n                id: articleId,\n                archive: action1\n            });\n            setIsArchived(action1);\n        } catch (error) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(t(action1 ? \"article:archivedError\" : \"article:desarchivedError\"));\n            console.error(\"Erreur lors de l'archivage/d\\xe9sarchivage de l'article:\", error);\n        }\n    };\n    const handlearchiveanddisarchivearticleFr = async ()=>{\n        try {\n            await usedisarchivedArticleHook.mutateAsync({\n                language: \"fr\",\n                id: articleId,\n                archive: action\n            });\n        } catch (error) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(t(action ? \"article:archivedError\" : \"article:desarchivedError\"));\n        }\n    };\n    const handleSaveEN = async ()=>{\n        const enValues = formikRefEn.current.values;\n        const hasId = enValues.keywords.every((item)=>item.hasOwnProperty(\"id\"));\n        const highlightshasId = enValues?.highlights?.every((item)=>item.hasOwnProperty(\"id\"));\n        if (hasId) {\n            enValues.keywords = enValues.keywords.filter((item)=>item.hasOwnProperty(\"id\") && item.text !== \"\").map((item)=>item.text);\n        }\n        if (highlightshasId) {\n            enValues.highlights = enValues.keywords.filter((item)=>item.hasOwnProperty(\"id\") && item.text !== \"\").map((item)=>item.text);\n        }\n        if (enValues.category.length > 0 && typeof enValues.category[0] === \"object\") {\n            let categoryEn = [];\n            enValues.category.map((category)=>categoryEn.push(category.id));\n            enValues.category = categoryEn;\n        }\n        formikRefEn.current.submitForm();\n        const isEnFormValid = formikRefEn.current.isValid && Object.keys(formikRefEn.current.errors).length === 0;\n        if (isEnFormValid) {\n            if (uuidCTSBannerPhotoFileNameEN) {\n                enValues.ctsBanner = {\n                    ...enValues.ctsBanner,\n                    image: uuidCTSBannerPhotoFileNameEN\n                };\n            }\n            const uploadPromises = [];\n            if (uuidPhotoFileNameEN) {\n                enValues.image = uuidPhotoFileNameEN;\n                uploadPromises.push(useSaveFileHook.mutateAsync({\n                    resource: \"blogs\",\n                    folder: currentYear,\n                    filename: uuidPhotoEN,\n                    body: {\n                        formData: formdataEN,\n                        t\n                    }\n                }).then((data)=>{\n                    enValues.image = data.uuid;\n                }));\n            }\n            if (uuidCTSBannerPhotoFileNameEN) {\n                uploadPromises.push(useSaveFileHook.mutateAsync({\n                    resource: \"blogs\",\n                    folder: currentYear,\n                    filename: uuidCTSBannerPhotoEN,\n                    body: {\n                        formData: formDataCTSBannerEN,\n                        t\n                    }\n                }).then((data)=>{\n                    enValues.ctsBanner = {\n                        image: data.uuid,\n                        link: enValues.ctsBannerLink\n                    };\n                }));\n            }\n            if (uploadPromises.length > 0) {\n                Promise.all(uploadPromises).then(()=>{\n                    useUpdateArticleHook.mutate({\n                        data: enValues,\n                        language: \"en\",\n                        id: articleId\n                    });\n                });\n            } else {\n                useUpdateArticleHook.mutate({\n                    data: enValues,\n                    language: \"en\",\n                    id: articleId\n                });\n            }\n        }\n    };\n    const handleSaveFR = async ()=>{\n        const frValues = formikRefFr.current.values;\n        const hasId = frValues.keywords?.every((item)=>item.hasOwnProperty(\"id\"));\n        const highlightshasId = frValues?.highlights?.every((item)=>item.hasOwnProperty(\"id\"));\n        if (hasId) {\n            frValues.keywords = frValues.keywords.filter((item)=>item.hasOwnProperty(\"id\") && item.text !== \"\").map((item)=>item.text);\n        }\n        if (highlightshasId) {\n            frValues.highlights = frValues.keywords.filter((item)=>item?.hasOwnProperty(\"id\") && item.text !== \"\").map((item)=>item.text);\n        }\n        if (frValues.category.length > 0 && typeof frValues.category[0] === \"object\") {\n            let categoryFr = [];\n            frValues.category.map((category)=>categoryFr.push(category.id));\n            frValues.category = categoryFr;\n        }\n        formikRefFr.current.submitForm();\n        const isFrFormValid = formikRefFr.current.isValid && Object.keys(formikRefFr.current.errors).length === 0;\n        if (isFrFormValid) {\n            if (uuidCTSBannerPhotoFileNameFR) {\n                frValues.ctsBanner = {\n                    link: frValues.ctsBannerLink,\n                    image: uuidCTSBannerPhotoFileNameFR\n                };\n            }\n            const uploadPromises = [];\n            if (uuidPhotoFileNameFR) {\n                frValues.image = uuidPhotoFileNameFR;\n                uploadPromises.push(useSaveFileHook.mutateAsync({\n                    resource: \"blogs\",\n                    folder: currentYear,\n                    filename: uuidPhotoFR,\n                    body: {\n                        formData: formdataFR,\n                        t\n                    }\n                }).then((data)=>{\n                    if (data.message === \"uuid exist\") {\n                        frValues.image = data.uuid;\n                    }\n                }));\n            }\n            if (uuidCTSBannerPhotoFileNameFR) {\n                uploadPromises.push(useSaveFileHook.mutateAsync({\n                    resource: \"blogs\",\n                    folder: currentYear,\n                    filename: uuidCTSBannerPhotoFR,\n                    body: {\n                        formData: formDataCTSBannerFR,\n                        t\n                    }\n                }).then((data)=>{\n                    frValues.ctsBanner = {\n                        ...frValues.ctsBanner,\n                        image: data.uuid\n                    };\n                }));\n            }\n            if (uploadPromises.length > 0) {\n                Promise.all(uploadPromises).then(()=>{\n                    useUpdateArticleHook.mutate({\n                        data: frValues,\n                        language: \"fr\",\n                        id: articleId\n                    });\n                });\n            } else {\n                useUpdateArticleHook.mutate({\n                    data: frValues,\n                    language: \"fr\",\n                    id: articleId\n                });\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchFrenchCategories = async ()=>{\n            try {\n                const response = await _config_axios__WEBPACK_IMPORTED_MODULE_10__.axiosGetJson.get(`${_utils_urls__WEBPACK_IMPORTED_MODULE_8__.API_URLS.categories}/en/${selectedEnCategories}`);\n                const transformedCategories = response.data.map((category)=>({\n                        id: category._id,\n                        name: category.name\n                    }));\n                setFilteredFrCategories(transformedCategories);\n            } catch (error) {}\n        };\n        if (selectedEnCategories?.length > 0) {\n            fetchFrenchCategories();\n        } else {\n            setFilteredFrCategories([]);\n        }\n    }, [\n        selectedEnCategories\n    ]);\n    const handleChangeAccordion = (panel)=>(event, newExpanded)=>{\n            setExpanded(newExpanded ? panel : false);\n        };\n    if (isLoadingFR || isLoadingEN) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"content\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_loading_Loading__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                lineNumber: 562,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n            lineNumber: 561,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"heading-h2 semi-bold\",\n                children: t(\"createArticle:editArticle\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                lineNumber: 569,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"container\",\n                className: \"recent-application-pentabell\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"main-content\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"commun\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            id: \"experiences\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_18__.Formik, {\n                                    initialValues: initialValuesAll,\n                                    innerRef: formikRefAll,\n                                    children: (param)=>{\n                                        let { errors, touched, setFieldValue, values } = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_18__.Form, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    id: \"accordion\",\n                                                    expanded: expanded === `panel`,\n                                                    onChange: handleChangeAccordion(`panel`),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                                lineNumber: 584,\n                                                                columnNumber: 37\n                                                            }, void 0),\n                                                            \"aria-controls\": `panel-content`,\n                                                            id: `panel-header`,\n                                                            children: t(\"createArticle:settings\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                            lineNumber: 583,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            className: \"accordion-detail\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                container: true,\n                                                                spacing: 4,\n                                                                sx: {\n                                                                    alignItems: \"flex-end\"\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        item: true,\n                                                                        xs: 12,\n                                                                        md: 10,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                className: \"label-form\",\n                                                                                children: [\n                                                                                    t(\"createArticle:Robotsmeta\"),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                        className: \"select-pentabell\",\n                                                                                        variant: \"standard\",\n                                                                                        sx: {\n                                                                                            m: 1,\n                                                                                            minWidth: 120\n                                                                                        },\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                            value: values.robotsMeta,\n                                                                                            onChange: (event)=>{\n                                                                                                setFieldValue(\"robotsMeta\", event.target.value);\n                                                                                            },\n                                                                                            children: _utils_constants__WEBPACK_IMPORTED_MODULE_6__.RobotsMeta.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                                                    value: item,\n                                                                                                    children: item\n                                                                                                }, index, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                                                                    lineNumber: 615,\n                                                                                                    columnNumber: 39\n                                                                                                }, undefined))\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                                                            lineNumber: 605,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                                                        lineNumber: 600,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_18__.ErrorMessage, {\n                                                                                        className: \"label-error\",\n                                                                                        name: \"robotsMeta\",\n                                                                                        component: \"div\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                                                        lineNumber: 621,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                                                lineNumber: 598,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                                            lineNumber: 597,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                                        lineNumber: 596,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    \" \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        item: true,\n                                                                        xs: 12,\n                                                                        md: 2,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            text: \"Save\",\n                                                                            className: \"btn btn-filled\",\n                                                                            onClick: ()=>handleSaveSetting(values)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                                            lineNumber: 630,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                                        lineNumber: 629,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                                lineNumber: 591,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                            lineNumber: 590,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, `panel}`, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                    lineNumber: 577,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \" \"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                            lineNumber: 576,\n                                            columnNumber: 19\n                                        }, undefined);\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                    lineNumber: 574,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddArticle__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    image: englishVersion?.image,\n                                    ctsBannerImage: englishVersion?.ctsBanner?.image,\n                                    language: \"en\",\n                                    initialValues: initialValuesEn,\n                                    formRef: formikRefEn,\n                                    onImageSelect: handleImageSelect,\n                                    validationSchema: validationSchemaEN,\n                                    isEdit: true,\n                                    onCategoriesSelect: (categories)=>{\n                                        setSelectedEnCategories(categories);\n                                    },\n                                    onRemoveCTSBanner: handleRemoveCTSBanner\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                    lineNumber: 642,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"btn-container\",\n                                    children: [\n                                        \"\\xa0\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            text: t(\"global:save\"),\n                                            className: \"btn btn-filled\",\n                                            onClick: handleSaveEN\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                            lineNumber: 658,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            text: isArchived ? t(\"global:unarchive\") : t(\"global:archive\"),\n                                            className: \"btn btn-filled\",\n                                            onClick: ()=>handleOpenDialog(isArchived ? \"desarchive\" : \"archive\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                            lineNumber: 663,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                    lineNumber: 656,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_user_component_updateProfile_experience_DialogModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    open: openDialog,\n                                    message: selectedAction === \"archive\" ? t(\"messages:archiveConfirmation\") : t(\"messages:desarchiveConfirmation\"),\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_archiveicon_popup_svg__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                        lineNumber: 680,\n                                        columnNumber: 23\n                                    }, void 0),\n                                    onClose: handleCloseDialog,\n                                    onConfirm: handleConfirmAction\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                    lineNumber: 673,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                    lineNumber: 684,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    id: \"accordion\",\n                                    expanded: expanded === `panel-1`,\n                                    onChange: handleChangeAccordion(`panel-1`),\n                                    isEdit: true,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                lineNumber: 693,\n                                                columnNumber: 31\n                                            }, void 0),\n                                            \"aria-controls\": `panel-content`,\n                                            id: `panel-header`,\n                                            children: t(\"createArticle:editArticleFr\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                            lineNumber: 692,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"accordion-detail\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddArticle__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    image: frenchVersion?.image,\n                                                    ctsBannerImage: frenchVersion?.ctsBanner?.image,\n                                                    language: \"fr\",\n                                                    initialValues: initialValuesFr,\n                                                    formRef: formikRefFr,\n                                                    onImageSelect: handleImageSelect,\n                                                    validationSchema: validationSchemaEN,\n                                                    isEdit: true,\n                                                    filteredCategories: filteredFrCategories,\n                                                    onRemoveCTSBanner: handleRemoveCTSBanner\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                    lineNumber: 700,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"btn-container\",\n                                                    children: [\n                                                        \"\\xa0\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            text: t(\"global:save\"),\n                                                            className: \"btn btn-filled\",\n                                                            onClick: handleSaveFR\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                            lineNumber: 714,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            text: isArchivedFr ? t(\"global:unarchive\") : t(\"global:archive\"),\n                                                            className: \"btn btn-filled\",\n                                                            onClick: ()=>handleOpenDialogfr(isArchivedFr ? \"desarchive\" : \"archive\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                            lineNumber: 719,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                    lineNumber: 712,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_user_component_updateProfile_experience_DialogModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    open: openDialogfr,\n                                                    message: selectedAction === \"archive\" ? t(\"messages:archiveConfirmationfr\") : t(\"messages:desarchiveConfirmationfr\"),\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_archiveicon_popup_svg__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                        lineNumber: 741,\n                                                        columnNumber: 27\n                                                    }, void 0),\n                                                    onClose: handleCloseDialogfr,\n                                                    onConfirm: handleConfirmActionFr\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                    lineNumber: 734,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                            lineNumber: 699,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, `panel-1`, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                    lineNumber: 685,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                            lineNumber: 573,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                        lineNumber: 572,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                    lineNumber: 571,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                lineNumber: 570,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(EditArticle, \"dkIejSfP202WnpYQC3id0f6HnKM=\", false, function() {\n    return [\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__.useGetArticleByIdAll,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__.useGetArticleById,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__.useGetArticleById,\n        _features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__.useUpdateArticle,\n        _opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_9__.useSaveFile,\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__.useUpdateArticleAll\n    ];\n});\n_c = EditArticle;\n/* harmony default export */ __webpack_exports__[\"default\"] = (EditArticle);\nvar _c;\n$RefreshReg$(_c, \"EditArticle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/EditArticle.jsx\n"));

/***/ })

});