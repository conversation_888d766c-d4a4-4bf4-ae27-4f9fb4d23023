const PayrollContent = ({ t, data }) => {
  if (!data || !data.items) return null;

  return (
    <>
      {data.titleKey && (
        <div className="item">
          <p className="service-sub-title">{t(data.titleKey)}</p>
          {data.descriptionKey && (
            <p className="service-description paragraph">{t(data.descriptionKey)}</p>
          )}
        </div>
      )}
      <div className="payroll-tn">
        {data.items.map((item, index) => (
          <div className="payroll-tn-item" key={index}>
            {item.titleKey && <p className="title">{t(item.titleKey)}</p>}
            {item.dateKeys && (
              <p className="date">
                {item.dateKeys.map((dateKey, i) => (
                  <span key={i}>
                    {t(dateKey)}
                    {i < item.dateKeys.length - 1 && <br />}
                  </span>
                ))}
              </p>
            )}
            {item.descriptionKey && (
              <p className="paragraph">{t(item.descriptionKey)}</p>
            )}
          </div>
        ))}
      </div>
    </>
  );
};

export default PayrollContent;
