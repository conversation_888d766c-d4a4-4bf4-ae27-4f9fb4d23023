"use client";

import OpportunityItemApply from "@/features/opportunity/components/opportunityFrontOffice/OpportunityItemApply";
import { Container, Grid } from "@mui/material";
import NewApplicant from "../NewApplicant";
import ApplyLogin from "./ApplyLogin";
import useCurrentUser from "@/features/auth/hooks/currentUser.hooks";
import ApplyAuthenticated from "./ApplyAuthenticated";
import { useState } from "react";
import ApplySecondForm from "./ApplySecondForm";

export default function ApplyPage({ data, params, success, error }) {
  const { user /*, isLoading */ } = useCurrentUser(false);
  const [secondForm, setSecondForm] = useState(null);

  if (user) {
    return (
      <ApplyAuthenticated data={data} successMsg={success} errorMsg={error} />
    );
  } else {
    if (secondForm)
      return (
        <ApplySecondForm
          data={data}
          successMsg={success}
          errorMsg={error}
          secondForm={secondForm}
        />
      );
    else
      return (
        <Container className="apply-section custom-max-width">
          <Grid container spacing={2}>
            <Grid item lg={12} md={12} sm={12}>
              <OpportunityItemApply
                opportunity={data}
                language={params.locale}
              />
            </Grid>
            <Grid item lg={6} md={6} sm={12}>
              <NewApplicant
                successMsg={success}
                errorMsg={error}
                setSecondForm={setSecondForm}
              />
            </Grid>
            <Grid item lg={6} md={6} sm={12}>
              <ApplyLogin
                opportunity={data}
                language={params.locale}
                successMsg={success}
                errorMsg={error}
              />
            </Grid>
          </Grid>
        </Container>
      );
  }
}
