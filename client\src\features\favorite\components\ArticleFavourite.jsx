"use client ";
import { DataGrid } from "@mui/x-data-grid";
import { useTranslation } from "react-i18next";
import CustomButton from "@/components/ui/CustomButton";
import DialogModal from "@/features/user/component/updateProfile/experience/DialogModal";
import SvgBookmark from "@/assets/images/icons/bookmark.svg";
import { useState } from "react";
import { useMediaQuery, useTheme } from "@mui/material";
import { axiosGetJson } from "@/config/axios";
import details from "@/assets/images/viewopportunities.png";
import { websiteRoutesList } from "@/helpers/routesList";

function ArticleFavourites({
  articleFavourites,
  getFavourites,
  typeOfFavourite,
  setShortLists,
  paginationModel,
  setPaginationModel,
  totalArticles,
}) {
  const { t } = useTranslation();
  const [openDialog, setOpenDialog] = useState(false);
  const [jobsTodelete, setJobsTodelete] = useState(false);

  const truncateTitlefavorite = (title) => {
    const words = title?.split(" ");
    if (words?.length >= 3) {
      return words.slice(0, 2).join(" ") + "...";
    } else {
      return title;
    }
  };

  const handeleDeleteconfirmation = (articleId) => {
    setJobsTodelete(articleId);
    setOpenDialog(true);
  };
  const handlecanceldelete = () => {
    setOpenDialog(false);
  };
  const deleteOpportunityFromShortlist = async (articleId) => {
    try {
      await axiosGetJson.delete(`/favourite/${articleId}`, {
        data: { type: typeOfFavourite },
      });
      getFavourites.refetch();
      setShortLists(
        shortlists.filter((opportunity) => opportunity._id !== articleId)
      );
    } catch (error) {}
  };
  const handleToggleOpportunity = async () => {
    try {
      await deleteOpportunityFromShortlist(jobsTodelete);
    } catch (error) {}
    setOpenDialog(false);
  };
  const theme = useTheme();

  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const rowsArticle =
    articleFavourites?.map((item, index) => ({
      id: item._id,
      idArticle: item?.versions[0]?._id,
      title: truncateTitlefavorite(item?.versions[0]?.title),
      url: item?.versions[0]?.url,
      actions: item?._id,
    })) || [];

  const columnsJobs = [
    {
      field: "title",
      cellClassName: "datagrid-cell",
      headerClassName: "datagrid-header",
      headerName: t("favourite:title"),
      flex: 1,
    },
    {
      field: "url",
      cellClassName: "datagrid-cell",
      headerClassName: "datagrid-header",
      headerName: t("favourite:viewArticle"),
      renderCell: (params) => (
        <div id="datagrid">
          <div className="action-buttons">
            <CustomButton
              link={`/${websiteRoutesList.blog.route}/${params.value}/`}
              className={"btn btn-ghost"}
              icon={<img src={details.src} alt="detail-icon" loading="lazy" />}
            />
          </div>
        </div>
      ),
      flex: 1,
    },
    {
      field: "actions",
      cellClassName: "datagrid-cell",
      headerClassName: "datagrid-header",
      headerName: t("favourite:actions"),
      renderCell: (params) => (
        <div id="datagrid">
          <div className="action-buttons">
            <CustomButton
              icon={<SvgBookmark className="btn-filled-yellow" />}
              onClick={() => handeleDeleteconfirmation(params.row.id)}
              className={"btn btn-ghost bookmark"}
            />
          </div>
        </div>
      ),
      flex: 1,
    },
  ];
  return (
    <>
      <div style={{ height: "100%", width: "100%", padding: "20px 0px" }}>
        <DataGrid
          localeText={{ noRowsLabel: "No result found." }}
          rows={rowsArticle}
          columns={columnsJobs}
          pagination
          paginationMode="server"
          pageSizeOptions={[5, 10, 25]}
          paginationModel={paginationModel}
          onPaginationModelChange={setPaginationModel}
          rowCount={totalArticles || 0}
          columnVisibilityModel={{
            url: !isMobile,
          }}
        />
      </div>{" "}
      <DialogModal
        open={openDialog}
        message={t("messages:supprimerarticlefavoris")}
        onClose={handlecanceldelete}
        onConfirm={handleToggleOpportunity}
      />
    </>
  );
}
export default ArticleFavourites;
