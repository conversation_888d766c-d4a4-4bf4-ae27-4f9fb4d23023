"use client";
import { Container } from "@mui/material";

import avaBrooks from "../../assets/images/ai-sourcing/png/ava-brooks.webp";
import danielMitchell from "../../assets/images/ai-sourcing/png/daniel-mitchell.webp";
import eliasDeeb from "../../assets/images/ai-sourcing/png/elias-deeb.webp";
import ryanMendes from "../../assets/images/ai-sourcing/png/ryan-mendes.webp";
import sallyAnderson from "../../assets/images/ai-sourcing/png/sally-anderson.webp";

import SvgArrowLeft from "../../assets/images/icons/arrowLeft.svg";
import SvgArrowRight from "../../assets/images/icons/arrowRight.svg";
import Autoplay from "embla-carousel-autoplay";

import useEmblaCarousel from "embla-carousel-react";
import {
  NextButton,
  PrevButton,
  usePrevNextButtons,
} from "@/components/ui/emblaCarousel/EmblaCarouselArrowButtons";
import { useTranslation } from "react-i18next";

import SlideShowAiSourcing from "./SlideShowAiSourcing";

function DiscoverAiRobots() {
  const { t } = useTranslation();
  const OPTIONS = { loop: false, align: "start" };
  const slides = [
    {
      name: "Sally Anderson",
      jobTitle: t("homePage:s6:jobt1"),
      altImg: t("homePage:s6:alt1"),
      description: t("homePage:s6:descriprion1"),
      link: "https://www.linkedin.com/in/sally-anderson-969a10296/",
      img: sallyAnderson.src,
      industry: "OIL_GAS",
    },
    {
      name: "Ryan Mendes",
      jobTitle: t("homePage:s6:jobt2"),
      altImg: t("homePage:s6:alt2"),
      description: t("homePage:s6:description2"),
      link: "https://www.linkedin.com/in/ryan-mendes-08361a297/",
      img: ryanMendes.src,
      industry: "ENERGY",
    },
    {
      name: "Elias Deeb",
      jobTitle: t("homePage:s6:jobt3"),
      altImg: t("homePage:s6:alt3"),
      description: t("homePage:s6:description3"),
      link: "https://www.linkedin.com/in/elias-deeb-75905429a/",
      img: eliasDeeb.src,
      industry: "TRANSPORT",
    },
    {
      name: "daniel Mitchell",
      jobTitle: t("homePage:s6:jobt4"),
      altImg: t("homePage:s6:alt4"),
      description: t("homePage:s6:description4"),
      link: "https://www.linkedin.com/in/daniel-mitchell-4509a629a/",
      img: danielMitchell.src,
      industry: "IT_TELECOM",
    },
    {
      name: "Ava Brooks",
      jobTitle: t("homePage:s6:jobt5"),
      altImg: t("homePage:s6:alt5"),
      description: t("homePage:s6:description5"),
      link: "https://www.linkedin.com/in/ava-brooks-27457629a/",
      img: avaBrooks.src,
      industry: "Banking",
    },
  ];
  const [emblaRef, emblaApi] = useEmblaCarousel(OPTIONS, [
    Autoplay({ playOnInit: true, delay: 3000 }),
  ]);

  const {
    prevBtnDisabled,
    nextBtnDisabled,
    onPrevButtonClick,
    onNextButtonClick,
  } = usePrevNextButtons(emblaApi);

  return (
    <Container id="discover-ai-sourcing"   className="custom-max-width">
      <h2 className="heading-h1 text-center"> {t("homePage:s5:title")}</h2>
      <p className="sub-heading text-center">{t("homePage:s5:description")}</p>
      <section className="embla" id={"aisourcing__slider"}>
        <div className="embla__viewport" ref={emblaRef}>
          <div className="embla__container">
            <SlideShowAiSourcing slides={slides} t={t} />
          </div>
        </div>

        <Container className="embla__controls">
          <div className="embla__buttons">
            <PrevButton
              children={<SvgArrowLeft />}
              onClick={onPrevButtonClick}
              disabled={prevBtnDisabled}
            />
            <NextButton
              children={<SvgArrowRight />}
              onClick={onNextButtonClick}
              disabled={nextBtnDisabled}
            />
          </div>
        </Container>
      </section>
    </Container>
  );
}

export default DiscoverAiRobots;
