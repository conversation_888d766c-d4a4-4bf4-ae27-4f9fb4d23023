import { useEffect, useState } from "react";
import { Grid, Typo<PERSON>, <PERSON> } from "@mui/material";
import { axiosGetJsonSSR } from "../../../config/axios";
import useCurrentUser from "@/features/auth/hooks/currentUser.hooks";
import { useTranslation } from "react-i18next";
import SvgArrow from "@/assets/images/icons/arrow.svg";

import SvgBookmark from "@/assets/images/icons/bookmark.svg";
import CustomButton from "../../../components/ui/CustomButton";
import { getCountryImage } from "../../../utils/functions";
import {
  baseUrlFrontoffice,
  candidateRoutes,
  websiteRoutesList,
} from "../../../helpers/routesList";
import DialogModal from "@/features/user/component/updateProfile/experience/DialogModal";
import { toast } from "react-toastify";
const FavoriteOpportunitiesHomePage = () => {
  const { t, i18n } = useTranslation();
  const { user } = useCurrentUser();
  const [favourites, setFavourites] = useState([]);
  const [loading, setLoading] = useState(false);
  const [jobsTodelete, setJobsTodelete] = useState(false);

  const [pageSize, setPageSize] = useState(2);
  const [openDialog, setOpenDialog] = useState(false);

  const fetchFavorites = async () => {
    setLoading(true);
    try {
      const response = await axiosGetJsonSSR.get("/favourite", {
        params: { pageSize },
      });

      setFavourites(response.data.favourites);
    } catch (err) {
      if (process.env.NODE_ENV === "dev")
        console.error("Failed to load favorites", err);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    fetchFavorites();
  }, [pageSize, user]);
  const handeleDeleteconfirmation = (id) => {
    setJobsTodelete(id);
    setOpenDialog(true);
  };
  const handlecanceldelete = () => {
    setOpenDialog(false);
  };

  const deleteOpportunityFromShortlist = async (id) => {
    const typeOfFavourite = "opportunity";

    if (!id) {
      console.error("Invalid opportunity ID");
      return;
    }

    try {
      await axiosGetJsonSSR.delete(`/favourite/${id}`, {
        data: { type: typeOfFavourite },
      });
    } catch (error) {
      if (error.response && error.response.status === 404) {
        console.error("Article already deleted or not found");
      } else {
        console.error("Error while deleting article from favourites", error);
      }
    }
  };
  const handleToggleOpportunity = async () => {
    try {
      await deleteOpportunityFromShortlist(jobsTodelete);
      toast.success("Opportunity deleted successfully");
      fetchFavorites();
    } catch (error) {}
    setOpenDialog(false);
  };
  return (
    <div className="favorite-opportunities">
      <div className="header">
        <p variant="h4" className="heading-h3 semi-bold">
          {t("HomeDashboard:SaveOpportunities")}
        </p>
        <CustomButton
          link={`/${baseUrlFrontoffice.baseURL.route}/${candidateRoutes.favoris.route}`}
          className={"btn btn-ghost"}
          text={t("HomeDashboard:All")}
          icon={<SvgArrow />}
        />
      </div>
      {loading ? (
        <Typography>Loading...</Typography>
      ) : favourites?.length > 0 ? (
        <Grid container spacing={2} className="opportunity-list">
          {favourites.map((favoris) => (
            <Grid
              item
              key={favoris._id}
              container
              alignItems="center"
              className="opportunity-item opportunity-item-homePage"
            >
              <div className="opportunity-left">
                <Link
                  href={`/${
                    websiteRoutesList.jobLocation.route
                  }/${favoris?.country.toLowerCase()}`}
                >
                  <img
                    width={180}
                    height={140}
                    src={getCountryImage(favoris.country)}
                    className="map-home-page  "
                    alt="Live from space album cover"
                    loading="lazy"
                  />
                </Link>
              </div>
              <div className="opportunity-center">
                <CustomButton
                  text={favoris.versions?.[i18n.language]?.title}
                  className={"btn p-0 job-title article-title"}
                  link={`/${websiteRoutesList.opportunities.route}/${
                    favoris.versions?.[i18n.language]?.url
                  }`}
                />
                <Typography className="opportunity-reference">
                  Ref: {favoris.reference}
                </Typography>
              </div>
              <div className="opportunity-right">
                <CustomButton
                  icon={<SvgBookmark />}
                  onClick={() => handeleDeleteconfirmation(favoris._id)}
                  className={"btn btn-ghost bookmark"}
                />
                <CustomButton
                  className="btn  btn-homepage btn-filled"
                  text={t("HomeDashboard:ReadMore")}
                  link={`/${websiteRoutesList.opportunities.route}/${
                    favoris.versions[i18n.language]?.url
                  }`}
                />
              </div>
            </Grid>
          ))}
        </Grid>
      ) : (
        <div className="  opportunity-item  opportunity-item-homepage empty-state">
          <p className="empty-title-1">
            You haven't saved any job opportunities yet.
          </p>
          <span className="empty-title-2">
            Browse available jobs to find and save the ones that match your
            interests!
          </span>
          <div className="empty-button">
            <CustomButton
              className="btn  btn-homepage btn-filled"
              text={t("Explore")}
              link={`/${websiteRoutesList.opportunities.route}`}
            />
          </div>
        </div>
      )}
      <DialogModal
        open={openDialog}
        message={t("messages:supprimerapplicationfavoris")}
        onClose={handlecanceldelete}
        onConfirm={handleToggleOpportunity}
      />
    </div>
  );
};

export default FavoriteOpportunitiesHomePage;
