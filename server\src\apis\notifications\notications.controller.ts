import { Router, Response, NextFunction } from 'express';
import Controller from '@/utils/interfaces/controller.interface';
import isAuthenticated from '@/middlewares/authentication.middleware';
import { NotificationI } from './notification.interface';
import { NotificationService } from './notification.service';
import apiKeyMiddleware from '@/middlewares/validateApiKey.middleware';
import { UserI } from '../user/user.interfaces';
export class NotificationController implements Controller {
    public path = '/notifications';
    public router = Router();
    private notificationService = new NotificationService();
    constructor() {
        this.initialiseRoutes();
    }

    private initialiseRoutes(): void {
        this.router.get(`${this.path}`, apiKeyMiddleware, isAuthenticated, this.get);
        this.router.put(`${this.path}`, isAuthenticated, this.markAllAsRead);
        this.router.put(`${this.path}/:notificationId`, isAuthenticated, this.update);
    }

    private get = async (request: any, response: Response, next: NextFunction) => {
        try {
            const currentUser: UserI = request.user;
            const query = request.query;
            response.send(await this.notificationService.get(currentUser, query));
        } catch (error) {
            next(error);
        }
    };

    private update = async (request: any, response: Response, next: NextFunction) => {
        try {
            const { notificationId } = request.params;
            const dataToUpdate: NotificationI = request.body;
            response.status(204).send(await this.notificationService.update(notificationId, dataToUpdate));
        } catch (error) {
            next(error);
        }
    };

    private markAllAsRead = async (request: any, response: Response, next: NextFunction) => {
        try {
            const currentUser = request.user;
            response.status(204).send(await this.notificationService.markAllAsRead(currentUser));
        } catch (error) {
            next(error);
        }
    };
}
