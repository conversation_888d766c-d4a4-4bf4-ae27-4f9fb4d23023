"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const constants_1 = require("../../../utils/helpers/constants");
const applicationSchema = new mongoose_1.Schema({
    status: {
        type: String,
        enum: constants_1.ApplicationStatus,
        default: constants_1.ApplicationStatus.PENDING,
    },
    applicationDate: Date,
    note: String,
    candidate: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'Candidat',
    },
    opportunity: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'Opportunity',
    },
    resume: String,
}, {
    timestamps: true,
    toJSON: {
        transform: function (doc, ret) {
            delete ret.__v;
        },
    },
    discriminatorKey: 'type',
});
exports.default = (0, mongoose_1.model)('Application', applicationSchema);
//# sourceMappingURL=opportunity.application.model.js.map