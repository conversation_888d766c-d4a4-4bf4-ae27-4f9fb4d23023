"use client";

import {
  Check<PERSON>,
  Container,
  FormControlLabel,
  FormGroup,
  FormLabel,
  Grid,
  TextField,
} from "@mui/material";
import { ErrorMessage, Form, Formik } from "formik";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { PhoneNumberUtil } from "google-libphonenumber";
import "react-international-phone/style.css";
import * as Yup from "yup";
import SvgSuccess from "@/assets/images/icons/success.svg";
import { PhoneInput } from "react-international-phone";
import getInTouchImg from "@/assets/images/website/get-in-touch.png";
import CustomButton from "@/components/ui/CustomButton";
import { useContactForm } from "@/features/contact/hooks/Contact.hooks";
import { getInTouchValidationSchema } from "@/utils/validations";
import AlertMessage from "@/components/ui/AlertMessage";
import LazyLoadFlag from "@/components/ui/LazyLoadFlag";
import GTM from "@/components/GTM";

function GetInTouchSection() {
  const [errMsg, setErrMsg] = useState("");
  const [success, setSuccess] = useState(false);

  const { t } = useTranslation();
  const phoneUtil = PhoneNumberUtil.getInstance();

  const useContactFormHook = useContactForm(setSuccess, setErrMsg);
  const [submittedFirstName, setSubmittedFirstName] = useState("");

  const initialValues = {
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    message: "",
    acceptTerms: false,
  };

  const handleSubmit = async (values, { resetForm }) => {
    try {
      await useContactFormHook.mutateAsync({
        firstName: values.firstName,
        lastName: values.lastName,
        email: values.email,
        phone: values.phone,
        message: values.message,
        to: process.env.NEXT_PUBLIC_EMAIL_FORM_DESTINATION,
        team: "digital",
        type: "getInTouch",
      });
      setSubmittedFirstName(values.firstName);
      window.dataLayer = window.dataLayer || [];
      window.dataLayer.push({
        event: "home_page_form",
        button_id: "my_button",
      });
      resetForm();
    } catch (error) {
      console.error("Error submitting form:", error);
    }
  };

  const isPhoneValid = (phone) => {
    try {
      return phoneUtil.isValidNumber(phoneUtil.parseAndKeepRawInput(phone));
    } catch (error) {
      return false;
    }
  };
  const phoneValidationSchema = Yup.string().test(
    "is-valid-phone",
    t("validations:phoneFormat"),
    (value) => isPhoneValid(value)
  );
  const combinedValidationSchema = (t) =>
    getInTouchValidationSchema(t).shape({
      phone: phoneValidationSchema,
    });
  return (
    <Container id="get-in-touch" className="custom-max-width">
      <GTM />
      {success ? (
        <div className="section-guide">
          <div className="form-success">
            <SvgSuccess />

            <p className="sub-heading">
              {" "}
              {t("messages:thankyou")} {submittedFirstName}{" "}
              {t("messages:messagesuccess")}
            </p>
          </div>
        </div>
      ) : (
        <Grid className="container" container spacing={2}>
      
          <Grid item xs={12} sm={7}>
            <Formik
              initialValues={initialValues}
              validationSchema={() => combinedValidationSchema(t)}
              onSubmit={handleSubmit}
            >
              {({ values, handleChange, errors, setFieldValue, touched }) => (
                <Form>
                  <Grid
                    className="container"
                    container
                    rowSpacing={4}
                    columnSpacing={3}
                  >
                    <Grid item xs={12} sm={6}>
                      <FormGroup>
                        <FormLabel className="label-pentabell">
                          {t("getInTouch:firstName")}
                        </FormLabel>
                        <TextField
                          className="input-pentabell"
                          placeholder={t("getInTouch:firstName")}
                          variant="standard"
                          name="firstName"
                          value={values.firstName}
                          onChange={handleChange}
                          autoComplete="off"
                          error={!!(errors.firstName && touched.firstName)}
                        />
                      </FormGroup>
                      <ErrorMessage name="firstName">
                        {(msg) => <span className="error-span">{msg}</span>}
                      </ErrorMessage>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <FormGroup>
                        <FormLabel className="label-pentabell">
                          {t("getInTouch:lastName")}
                        </FormLabel>
                        <TextField
                          className="input-pentabell"
                          placeholder={t("getInTouch:lastName")}
                          variant="standard"
                          name="lastName"
                          value={values.lastName}
                          onChange={handleChange}
                          autoComplete="off"
                          error={!!(errors.lastName && touched.lastName)}
                        />
                      </FormGroup>
                      <ErrorMessage name="lastName">
                        {(msg) => <span className="error-span">{msg}</span>}
                      </ErrorMessage>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <FormGroup>
                        <FormLabel className="label-pentabell">
                          {t("getInTouch:email")}
                        </FormLabel>
                        <TextField
                          className="input-pentabell"
                          placeholder="<EMAIL>"
                          variant="standard"
                          name="email"
                          value={values.email}
                          onChange={handleChange}
                          autoComplete="off"
                          error={!!(errors.email && touched.email)}
                        />
                      </FormGroup>
                      <ErrorMessage name="email">
                        {(msg) => <span className="error-span">{msg}</span>}
                      </ErrorMessage>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <FormGroup>
                        <FormLabel className="label-pentabell">
                          {t("getInTouch:phone")}
                        </FormLabel>
                        <PhoneInput
                          defaultCountry="fr"
                          className="input-pentabell"
                          value={values.phone}
                          name="phone"
                          placeholder="Phone number"
                          onChange={(phoneNumber) => {
                            setFieldValue("phone", phoneNumber);
                            setErrMsg("");
                          }}
                          flagComponent={(props) => <LazyLoadFlag {...props} />}
                        />
                      </FormGroup>
                      <ErrorMessage name="phone">
                        {(msg) => <span className="error-span">{msg}</span>}
                      </ErrorMessage>
                    </Grid>
                    <Grid item xs={12} sm={12}>
                      <FormGroup>
                        <FormLabel className="label-pentabell">
                          {t("getInTouch:message")}
                        </FormLabel>
                        <TextField
                          className="input-pentabell"
                          placeholder={t("getInTouch:typeMessage")}
                          variant="standard"
                          name="message"
                          value={values.message}
                          onChange={handleChange}
                          autoComplete="off"
                          error={!!(errors.message && touched.message)}
                        />
                      </FormGroup>
                      <ErrorMessage name="message">
                        {(msg) => <span className="error-span">{msg}</span>}
                      </ErrorMessage>
                    </Grid>
                    <Grid item xs={12} sm={12}>
                      <FormGroup>
                        <FormControlLabel
                          className="checkbox-pentabell "
                          control={
                            <Checkbox
                              name="acceptTerms"
                              checked={values.acceptTerms}
                              onChange={handleChange}
                              error={
                                !!(errors.acceptTerms && touched.acceptTerms)
                              }
                            />
                          }
                          label={t(
                            "aiSourcingService:servicePageForm:formSubmissionAgreement"
                          )}
                        />
                        <ErrorMessage name="acceptTerms">
                          {(msg) => <span className="error-span">{msg}</span>}
                        </ErrorMessage>
                      </FormGroup>
                    </Grid>
                    <Grid item xs={12} sm={12}>
                      <CustomButton
                        text={t("getInTouch:submit")}
                        className={"btn btn-filled"}
                        type="submit"
                      />
                    </Grid>
                  </Grid>
                </Form>
              )}
            </Formik>
            <AlertMessage errMsg={errMsg} success={success} />
          </Grid>
          <Grid item xs={12} sm={5}>
            <div className="text-center">
              <p className="heading-h1">{t("getInTouch:getInTouch")}</p>
              <p className="sub-heading">{t("getInTouch:description")}</p>
              <img
                src={getInTouchImg.src}
                alt={t("getInTouch:altImg")}
                loading="lazy"
              />
            </div>
          </Grid>
        </Grid>
      )}
    </Container>
  );
}

export default GetInTouchSection;
