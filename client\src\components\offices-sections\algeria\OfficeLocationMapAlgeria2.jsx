
import OfficeLocationMap from "@/components/ui/OfficeLocationMap";

function OfficeLocationMapAlgeria2({ t }) {
  return (
    <OfficeLocationMap
      title={t("Algeria:officeLocation2:label")}
      subtitle={t("Algeria:officeLocation2:title")}
      address={t("Algeria:officeLocation2:address")}
      tel={t("Algeria:officeLocation2:tel1")}
      email={t("Algeria:officeLocation2:mail")}
      linkText={t("Algeria:officeLocation2:talk")}
      mapSrc="https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d27148.861492992393!2d6.04719!3d31.726610000000004!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x125cc730a32b0f71%3A0x6befb37de361c422!2sBase%2024%20F%C3%A9vrier%20Division%20Production!5e0!3m2!1sfr!2sus!4v1728630917480!5m2!1sfr!2sus"

    />
  );
}
export default OfficeLocationMapAlgeria2;
