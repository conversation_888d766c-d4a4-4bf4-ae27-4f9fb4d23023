"use client";

import CustomButton from "@/components/ui/CustomButton";
import { Grid } from "@mui/material";

function ReusableConfirmation({ icon: Icon, message, text, buttons = [] }) {
  return (
    <div>
      <div className="confirmation-dialog-icon">{Icon && <Icon />}</div>
      <p className="text-confirmation-h1">{message}</p>
      <span className="text-confirmation-second">{text}</span>

      <Grid container spacing={2} justifyContent="center">
        {buttons.map((button, index) => (
          <Grid item xs={12} sm={6} md={6} key={index}>
            <CustomButton
              text={button.text}
              className="btn btn-filled btn-submit full-width"
              type={button.type || "button"}
              onClick={button.onClick}
            />
          </Grid>
        ))}
      </Grid>
    </div>
  );
}

export default ReusableConfirmation;
