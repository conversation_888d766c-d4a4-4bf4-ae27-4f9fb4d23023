"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/glossaries/page",{

/***/ "(app-pages-browser)/./src/features/glossary/component/GlossariesListWebsite.jsx":
/*!*******************************************************************!*\
  !*** ./src/features/glossary/component/GlossariesListWebsite.jsx ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GlossaryListWebsite; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_icons_material_Search__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mui/icons-material/Search */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Search.js\");\n/* harmony import */ var _mui_icons_material_Book__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/icons-material/Book */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Book.js\");\n/* harmony import */ var _mui_icons_material_ErrorOutline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mui/icons-material/ErrorOutline */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/ErrorOutline.js\");\n/* harmony import */ var _components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst EmptyGlossaryState = (param)=>{\n    let { isEmpty, isEmptySearch, searchWord, locale, translations } = param;\n    if (isEmpty) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"empty-glossary-state\",\n            sx: {\n                textAlign: \"center\",\n                py: 8,\n                px: 4,\n                minHeight: \"400px\",\n                display: \"flex\",\n                flexDirection: \"column\",\n                alignItems: \"center\",\n                justifyContent: \"center\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Book__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        fontSize: 80,\n                        color: \"text.secondary\",\n                        mb: 3,\n                        opacity: 0.5\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    variant: \"h4\",\n                    component: \"h2\",\n                    gutterBottom: true,\n                    sx: {\n                        fontWeight: 600,\n                        color: \"text.primary\",\n                        mb: 2\n                    },\n                    children: translations?.emptyState?.title || \"No Glossary Terms Available\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    variant: \"body1\",\n                    color: \"text.secondary\",\n                    sx: {\n                        maxWidth: 600,\n                        mb: 4,\n                        lineHeight: 1.6\n                    },\n                    children: translations?.emptyState?.description || \"We're currently building our glossary. Check back soon for comprehensive definitions and explanations of industry terms.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    text: translations?.emptyState?.exploreButton || \"Explore Our Services\",\n                    link: locale === \"fr\" ? \"/fr/blog\" : \"/blog\",\n                    className: \"btn btn-filled\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (isEmptySearch) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"empty-search-state\",\n            sx: {\n                textAlign: \"center\",\n                py: 8,\n                px: 4,\n                minHeight: \"400px\",\n                display: \"flex\",\n                flexDirection: \"column\",\n                alignItems: \"center\",\n                justifyContent: \"center\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Search__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    sx: {\n                        fontSize: 80,\n                        color: \"text.secondary\",\n                        mb: 3,\n                        opacity: 0.5\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    variant: \"h4\",\n                    component: \"h2\",\n                    gutterBottom: true,\n                    sx: {\n                        fontWeight: 600,\n                        color: \"text.primary\",\n                        mb: 2\n                    },\n                    children: translations?.searchEmpty?.title || \"No Results Found\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    variant: \"body1\",\n                    color: \"text.secondary\",\n                    sx: {\n                        maxWidth: 600,\n                        mb: 2,\n                        lineHeight: 1.6\n                    },\n                    children: translations?.searchEmpty?.description || `No glossary terms found for \"${searchWord}\". Try searching with different keywords or browse all terms.`\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    sx: {\n                        mt: 3,\n                        display: \"flex\",\n                        gap: 2,\n                        flexWrap: \"wrap\",\n                        justifyContent: \"center\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            text: translations?.searchEmpty?.clearButton || \"Clear Search\",\n                            link: locale === \"fr\" ? \"/fr/glossaries\" : \"/glossaries\",\n                            className: \"btn btn-outline\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            text: translations?.searchEmpty?.browseButton || \"Browse All Terms\",\n                            link: locale === \"fr\" ? \"/fr/glossaries\" : \"/glossaries\",\n                            className: \"btn btn-filled\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 122,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n            lineNumber: 77,\n            columnNumber: 7\n        }, undefined);\n    }\n    return null;\n};\n_c = EmptyGlossaryState;\nconst ErrorGlossaryState = (param)=>{\n    let { error, locale, translations } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        className: \"error-glossary-state\",\n        sx: {\n            textAlign: \"center\",\n            py: 8,\n            px: 4,\n            minHeight: \"400px\",\n            display: \"flex\",\n            flexDirection: \"column\",\n            alignItems: \"center\",\n            justifyContent: \"center\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ErrorOutline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                sx: {\n                    fontSize: 80,\n                    color: \"error.main\",\n                    mb: 3,\n                    opacity: 0.7\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                lineNumber: 163,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                variant: \"h4\",\n                component: \"h2\",\n                gutterBottom: true,\n                sx: {\n                    fontWeight: 600,\n                    color: \"text.primary\",\n                    mb: 2\n                },\n                children: translations?.error?.title || \"Unable to Load Glossary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                lineNumber: 171,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                variant: \"body1\",\n                color: \"text.secondary\",\n                sx: {\n                    maxWidth: 600,\n                    mb: 4,\n                    lineHeight: 1.6\n                },\n                children: translations?.error?.description || \"We're experiencing technical difficulties loading the glossary. Please try again later.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                lineNumber: 183,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                severity: \"error\",\n                sx: {\n                    mb: 3,\n                    maxWidth: 500\n                },\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                lineNumber: 195,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                text: translations?.error?.retryButton || \"Try Again\",\n                onClick: ()=>window.location.reload(),\n                className: \"btn btn-filled\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                lineNumber: 198,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n        lineNumber: 150,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = ErrorGlossaryState;\nfunction GlossaryListWebsite(param) {\n    let { glossaries, locale, error, isEmpty, isEmptySearch, searchWord, translations } = param;\n    _s();\n    const letters = Object.keys(glossaries || {});\n    const [expandedLetters, setExpandedLetters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const handleToggle = (letter)=>{\n        setExpandedLetters((prev)=>({\n                ...prev,\n                [letter]: !prev[letter]\n            }));\n    };\n    const [activeHash, setActiveHash] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const updateHash = ()=>setActiveHash(window.location.hash);\n        updateHash(); // Get initial hash\n        window.addEventListener(\"hashchange\", updateHash); // Watch for changes\n        return ()=>{\n            window.removeEventListener(\"hashchange\", updateHash);\n        };\n    }, []);\n    const hasId = (id)=>activeHash === `#${id}`;\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            id: \"glossary-page\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"custom-max-width\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorGlossaryState, {\n                    error: error,\n                    locale: locale,\n                    translations: translations\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 244,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                lineNumber: 243,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n            lineNumber: 242,\n            columnNumber: 7\n        }, this);\n    }\n    if (isEmpty || isEmptySearch) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            id: \"glossary-page\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"custom-max-width\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EmptyGlossaryState, {\n                    isEmpty: isEmpty,\n                    isEmptySearch: isEmptySearch,\n                    searchWord: searchWord,\n                    locale: locale,\n                    translations: translations\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 258,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                lineNumber: 257,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n            lineNumber: 256,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"glossary-page\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            className: \"custom-max-width\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    container: true,\n                    spacing: 3,\n                    children: letters?.length > 0 && letters?.map((letter, index)=>{\n                        const letterGlossaries = glossaries[letter] || [];\n                        const isExpanded = expandedLetters[letter] || false;\n                        const displayedGlossaries = isExpanded ? letterGlossaries : letterGlossaries.slice(0, 5);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            item: true,\n                            lg: 3,\n                            md: 4,\n                            sm: 6,\n                            xs: 6,\n                            className: \"letters\",\n                            id: letter,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `letter-section ${hasId(letter) ? \"circled\" : \"\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"length\",\n                                        children: letterGlossaries.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"letter\",\n                                        children: letter\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"words\",\n                                        children: displayedGlossaries.map((glossary, glossaryIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                className: \"word\",\n                                                href: `${locale === \"fr\" ? \"/fr\" : \"\"}/glossaries/${glossary.url}`,\n                                                title: glossary.word,\n                                                children: glossary.word\n                                            }, `${glossary.url}-${glossaryIndex}`, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 25\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 21\n                                    }, this),\n                                    letterGlossaries.length > 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"glossary-button\",\n                                        onClick: ()=>handleToggle(letter),\n                                        size: \"small\",\n                                        variant: \"text\",\n                                        children: isExpanded ? translations?.showLess || \"Show less\" : translations?.showMore || \"Show more\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 23\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                lineNumber: 292,\n                                columnNumber: 19\n                            }, this)\n                        }, index, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                            lineNumber: 282,\n                            columnNumber: 17\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 273,\n                    columnNumber: 9\n                }, this),\n                letters.length > 8 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    id: \"back-to-top\",\n                    sx: {\n                        textAlign: \"center\",\n                        mt: 6\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"btn btn-filled-yellow\",\n                        onClick: ()=>window.scrollTo({\n                                top: 0,\n                                behavior: \"smooth\"\n                            }),\n                        children: translations?.backToTop || \"Back to Top\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                        lineNumber: 333,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 332,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n            lineNumber: 272,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n        lineNumber: 271,\n        columnNumber: 5\n    }, this);\n}\n_s(GlossaryListWebsite, \"HYjpPcVKTgd4Sg0CmXJgatHsZf0=\");\n_c2 = GlossaryListWebsite;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"EmptyGlossaryState\");\n$RefreshReg$(_c1, \"ErrorGlossaryState\");\n$RefreshReg$(_c2, \"GlossaryListWebsite\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/glossary/component/GlossariesListWebsite.jsx\n"));

/***/ })

});