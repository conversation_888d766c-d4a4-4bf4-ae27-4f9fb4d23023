"use client";
import React from "react";
import useEmblaCarousel from "embla-carousel-react";
import Autoplay from "embla-carousel-autoplay";
import { useTranslation } from "react-i18next";
import { Container, Link, useMediaQuery, useTheme } from "@mui/material";

import {
  NextButton,
  PrevButton,
  usePrevNextButtons,
} from "./EmblaCarouselArrowButtons";

import CustomButton from "../CustomButton";
import SvgArrowwhite from "../../../assets/images/icons/arrowwhite.svg";
import SvgArrowLeft from "../../../assets/images/icons/arrowLeft.svg";
import SvgArrowRight from "../../../assets/images/icons/arrowRight.svg";

function formatTitleWithLineBreak(title, maxCharsPerLine = 30) {
  if (!title) return "";

  const words = title.trim().split(/\s+/);
  const lines = [];
  let currentLine = "";

  for (const word of words) {
    if ((currentLine + " " + word).trim().length <= maxCharsPerLine) {
      currentLine += (currentLine ? " " : "") + word;
    } else {
      lines.push(currentLine);
      currentLine = word;
    }
  }

  if (currentLine) lines.push(currentLine);

  return lines.join("\n");
}

export default function CustomEmblaCarousel({
  slides = [],
  options,
  slideId,
  isMobileSSR,
}) {
  const { t } = useTranslation();
  const theme = useTheme();

  const isMobile = useMediaQuery(theme.breakpoints.down("sm")) || isMobileSSR;

  const [emblaRef, emblaApi] = useEmblaCarousel(options, [
    Autoplay({ playOnInit: !isMobile, delay: 3500 }),
  ]);

  const {
    prevBtnDisabled,
    nextBtnDisabled,
    onPrevButtonClick,
    onNextButtonClick,
  } = usePrevNextButtons(emblaApi);

  return (
    <section className="embla" id={slideId}>
      <div className="embla__viewport" ref={emblaRef}>
        <div className="embla__container">
          {slides.map((item, index) => (
            <div className="embla__slide" key={index}>
              <img
                alt={item.altImg}
                width={800}
                height={800}
                src={isMobile ? item.imgMobile : item.img}
                loading={index === 0 ? "eager" : "lazy"}
              />
              <Container className="slide__container custom-max-width">
                <div className="embla__slide__content">
                  {!isMobile && (
                    <p className="embla__slide__title">
                      {formatTitleWithLineBreak(item.title, 30)
                        .split("\n")
                        .map((line, i) => (
                          <React.Fragment key={i}>
                            {line}
                            <br />
                          </React.Fragment>
                        ))}
                    </p>
                  )}

                  {item.customLink ? (
                    <Link href={item.link} className="btn btn-slider">
                      {t("global:discoverBtn")}
                      <SvgArrowwhite />
                    </Link>
                  ) : item.link ? (
                    <CustomButton
                      text={item.linkBtn}
                      className="explore-btn"
                      link={item.link}
                      samePage
                      externalLink
                      icon={<SvgArrowwhite />}
                    />
                  ) : (
                    <br />
                  )}
                </div>
              </Container>
            </div>
          ))}
        </div>

        {!!slides.length && (
          <Container className="embla__controls">
            <div className="embla__buttons">
              <PrevButton
                children={<SvgArrowLeft />}
                onClick={onPrevButtonClick}
                disabled={prevBtnDisabled}
              />
              <NextButton
                children={<SvgArrowRight />}
                onClick={onNextButtonClick}
                disabled={nextBtnDisabled}
              />
            </div>
          </Container>
        )}
      </div>
    </section>
  );
}