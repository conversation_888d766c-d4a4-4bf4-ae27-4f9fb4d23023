import OfficeLocationMap from "@/components/ui/OfficeLocationMap";

function OfficeLocationMapLibya({ t }) {
  return (
    <OfficeLocationMap
      title={t("libya:officeLocation:label")}
      subtitle={t("libya:officeLocation:title")}
      address={t("libya:officeLocation:address")}
      tel={t("libya:officeLocation:tel1")}
      email={t("libya:officeLocation:mail")}
      linkText={t("libya:officeLocation:talk")}
      mapSrc="https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d107301.37762314167!2d13.060563!3d32.814312!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x13a8e94d4b4a4cc9%3A0x3e417e0f847f6762!2sInternational%20Recruitment%20%26%20Staffing%20Agency%20Libya%20%7C%20Pentabell%20Libya!5e0!3m2!1sfr!2sus!4v1728632814048!5m2!1sfr!2sus"

    />
  );
}
export default OfficeLocationMapLibya;
