import { Router, Request, Response, NextFunction } from 'express';
import Controller from '@/utils/interfaces/controller.interface';
import isAuthenticated from '@/middlewares/authentication.middleware';
import CandidatService from './candidat.service';
import validateMongoId from '@/middlewares/mongoId-validation.middleware';
import { hasRoles } from '@/middlewares/authorization.middleware';
import { Role } from '@/utils/helpers/constants';
import { MESSAGES } from '@/utils/helpers/messages';
import { CandidateI } from './candidat.interfaces';
import apiKeyMiddleware from '@/middlewares/validateApiKey.middleware';

class CandidatController implements Controller {
    public readonly path = '/candidates';
    public readonly router = Router();
    private readonly candidatService = new CandidatService();

    constructor() {
        this.initialiseRoutes();
    }

    private initialiseRoutes(): void {
        this.router.get(`${this.path}`, apiKeyMiddleware, isAuthenticated, hasRoles([Role.ADMIN]), this.getAll);
        this.router.get(`${this.path}/new`, async (req,res,next) => {
            try {
                res.send(await this.candidatService.getLatestCreatedAccount());
            } catch (error) {
                next(error);
            }
        });
        this.router.get(`${this.path}/currentCandidate`, apiKeyMiddleware, isAuthenticated, this.get);
        this.router.get(`${this.path}/byid/:id`, apiKeyMiddleware, isAuthenticated, this.getById);
        this.router.put(`${this.path}/calculate`, this.getAllCandidatesProfileCompletion);
        this.router.get(`${this.path}/Base64/:id`, apiKeyMiddleware, validateMongoId, this.getCandidateBase64);
        this.router.put(`${this.path}`, isAuthenticated, this.update);
        this.router.delete(`${this.path}`, isAuthenticated, this.delete);
        this.router.delete(`${this.path}/resume`, isAuthenticated, hasRoles([Role.CANDIDATE]), this.deleteCandidateResume);
        this.router.get(`${this.path}/calculate/:id`, this.getporcentagecandidateprofile);
    }

    public getAllCandidatesProfileCompletion = async (req: Request, res: Response): Promise<void> => {
        try {
            const results = await this.candidatService.calculateAllCandidatesProfileCompletion();
            res.status(200).json(results);
        } catch (error) {
            res.status(500).json({});
        }
    };
    private getAll = async (request: any, response: Response, next: NextFunction) => {
        try {
            const queries: any = request.query;
            const result = await this.candidatService.getAll(queries);
            response.send(result);
        } catch (error) {
            next(error);
        }
    };

    private get = async (request: any, response: Response, next: NextFunction) => {
        try {
            const id: string = request.user._id;
            response.send(await this.candidatService.get(id));
        } catch (error) {
            next(error);
        }
    };

    private getById = async (request: any, response: Response, next: NextFunction) => {
        try {
            const id: string = request.params.id;

            response.send(await this.candidatService.getbyid(id));
        } catch (error) {
            next(error);
        }
    };

    private getCandidateBase64 = async (request: any, response: Response, next: NextFunction) => {
        try {
            const id: string = request.params.id;
            response.send(await this.candidatService.getCandidateBase64(id));
        } catch (error) {
            next(error);
        }
    };

    private getporcentagecandidateprofile = async (request: Request, response: Response, next: NextFunction) => {
        try {
            const id: string = request.params.id;
            const result = await this.candidatService.calculateProfileCompletion(id);
            response.send({
                success: true,
                completionPercentage: result.completionPercentage,
            });
        } catch (error) {
            next(error);
        }
    };
    private delete = async (request: any, response: Response, next: NextFunction) => {
        try {
            const id: string = request.user._id;
            await this.candidatService.archive(id);
            response.send({
                message: MESSAGES.CANDIDATE.ARCHIVED,
            });
        } catch (error) {
            next(error);
        }
    };

    private update = async (request: any, response: Response, next: NextFunction) => {
        try {
            const candidat: CandidateI = request.body;
            const currentUser: string = request.user._id;
            const data = await this.candidatService.update(currentUser, candidat);
            response.send({
                data: data,
                message:MESSAGES.CANDIDATE_INFORMATION.INFORMATION_UPDATED,
            });
        } catch (error) {
            next(error);
        }
    };
    public deleteCandidateResume = async (request: any, response: Response, next: NextFunction) => {
        const candidateId = request.user._id;
        const { fileName } = request.body;
        try {
            await this.candidatService.deleteCandidateResume(candidateId, fileName);
            response.send({ message: MESSAGES.CANDIDATE_INFORMATION.RESUME_DELETED });
        } catch (error) {
            next(error);
        }
    };
}

export default CandidatController;
