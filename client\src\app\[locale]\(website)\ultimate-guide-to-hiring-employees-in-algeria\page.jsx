import bannerALGERIA from "@/assets/images/Algeria/Pentabell-<PERSON><PERSON>-<PERSON><PERSON><PERSON>.webp";
import BannerComponents from "@/components/pages/sites/sections/BannerComponents";
import OurPartners from "@/components/pages/sites/sections/OurPartners";
import ResponsiveRowTitleText from "@/components/ui/ResponsiveRowTitleText";
import GlobalHRServicesSection from "@/components/pages/sites/sections/GlobalHRServicesSection";
import serviceImg from "@/assets/images/services/service1.png";
import r1 from "@/assets/images/services/recrutement1.png";
import r2 from "@/assets/images/services/recrutement2.png";
import r3 from "@/assets/images/services/recrutement3.png";
import r4 from "@/assets/images/services/recrutement4.png";
import TunisiaOfficePageForm from "@/features/forms/components/TunisiaOfficePageForm";
import AlgeriaLaborData from "@/components/pages/sites/offices-sections/labor-laws/labor-data/AlgeriaLaborData";
import OfficeInfoAlgeria from "@/components/pages/sites/offices-sections/algeria/officeInfoAlgeria";
import BusinessInAlgeria from "@/components/pages/sites/offices-sections/algeria/BusinessInAlgeria";
import EORServicesAlgeria from "@/components/pages/sites/offices-sections/algeria/EORServicesAlgeria";
import OfficeLocationMapAlgeria from "@/components/pages/sites/offices-sections/algeria/OfficeLocationMapAlgeria";
import ComplexityControlSectionPanel from "@/components/pages/sites/offices-sections/ComplexityControlSectionPanel";
import OfficeLocationMapAlgeria2 from "@/components/pages/sites/offices-sections/algeria/OfficeLocationMapAlgeria2";
import initTranslations from "@/app/i18n";
import { websiteRoutesList } from "@/helpers/routesList";
import { axiosGetJsonSSR } from "@/config/axios";
import serviceimgS1 from "@/assets/images/services/service1.png";

export async function generateMetadata({ params: { locale } }) {
  const canonicalUrl = `https://www.pentabell.com/${
    locale !== "en" ? `${locale}/` : ""
  }ultimate-guide-to-hiring-employees-in-algeria/`;

  const languages = {
    fr: `https://www.pentabell.com/fr/ultimate-guide-to-hiring-employees-in-algeria/`,
    en: `https://www.pentabell.com/ultimate-guide-to-hiring-employees-in-algeria/`,
    "x-default": `https://www.pentabell.com/ultimate-guide-to-hiring-employees-in-algeria/`,
  };
  const { t } = await initTranslations(locale, [
    "Tunisia",
    "servicesByCountry",
  ]);
  try {
    const res = await axiosGetJsonSSR.get(
      `${process.env.NEXT_PUBLIC_BASE_API_URL_SSR}/seoTags/${locale}/ultimate-guide-to-hiring-employees-in-algeria`
    );
    const seoTags = res?.data?.status === 200;
    if (seoTags) {
      return {
        title: res?.data?.data?.versions[0]?.metaTitle,
        description: res?.data?.data?.versions[0]?.metaDescription,
        robots: res?.data?.data?.robotMeta,
        alternates: {
          canonical: canonicalUrl,
          languages,
        },
      };
    }
  } catch (error) {
    console.error("Error fetching SEO tags:", error);
  }

  return {
    title: t("servicesByCountry:algeria:metaTitle"),
    description: t("servicesByCountry:algeria:metaDescription"),
    alternates: {
      canonical: canonicalUrl,
      languages,
    },
    robots: "follow, index, max-snippet:-1, max-image-preview:large",
  };
}
async function hiringEmployeesAlgeriaGuide({ params: { locale } }) {
  const { t } = await initTranslations(locale, ["Tunisia", "Algeria"]);

  const SERVICES = [
    {
      id: "s1",
      title: t("Tunisia:services:dataS1:title"),
      description: t("Tunisia:services:dataS1:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.payrollServices.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: serviceImg,
      altImg: t("Tunisia:services:dataS1:altImg"),
    },
    {
      id: "s2",
      title: t("Tunisia:services:dataS2:title"),
      description: t("Tunisia:services:dataS2:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.consultingServices.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r4,
      altImg: t("Tunisia:services:dataS2:altImg"),
    },
    {
      id: "s3",
      title: t("Tunisia:services:dataS3:title"),
      description: t("Tunisia:services:dataS3:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.technicalAssistance.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r3,
      altImg: t("Tunisia:services:dataS3:altImg"),
    },
    {
      id: "s4",
      title: t("Tunisia:services:dataS4:title"),
      description: t("Tunisia:services:dataS4:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.aiSourcing.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r2,
      altImg: t("Tunisia:services:dataS4:altImg"),
    },
    {
      id: "s5",
      title: t("Tunisia:services:dataS5:title"),
      description: t("Tunisia:services:dataS5:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.directHiring.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r1,
      altImg: t("Tunisia:services:dataS5:altImg"),
    },
  ];
  return (
    <div>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Organization",
            name: t("contactUs:bureux:contacts:hydra"),
            address: {
              "@type": "PostalAddress",
              streetAddress: "Route les oliviers les cretes n°14",
              addressLocality: "Hydra, Alger",
              postalCode: "16035",
              addressCountry: "DZ",
            },
            telephone: ["+213 23 48 59 10", "+213 23 48 51 44"],
            email: "<EMAIL>",
            url:
              locale === "en"
                ? "https://www.pentabell.com/ultimate-guide-to-hiring-employees-in-algeria/"
                : `https://www.pentabell.com/${locale}/ultimate-guide-to-hiring-employees-in-algeria/`,
          }),
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Organization",
            name: t("contactUs:bureux:contacts:hassiMassoud"),
            address: {
              "@type": "PostalAddress",
              streetAddress: "Eurojapan Residence Route Nationale N°3 BP 842",
              addressLocality: "Hassi Messaoud",
              addressCountry: "DZ",
            },
            telephone: "+33 1 73 07 42 54",
            email: "<EMAIL>",
            url:
              locale === "en"
                ? "https://www.pentabell.com/ultimate-guide-to-hiring-employees-in-algeria/"
                : `https://www.pentabell.com/${locale}/ultimate-guide-to-hiring-employees-in-algeria/`,
          }),
        }}
      />
      <BannerComponents
        title={t("Algeria:title")}
        description={t("Algeria:description")}
        bannerImg={bannerALGERIA}
        height={"100vh"}
        altImg={t("Algeria:altImg")}
      />
      <OurPartners disableTxt={true} />
      <ResponsiveRowTitleText
        title={t("Algeria:intro:title")}
        paragraph={t("Algeria:intro:description")}
      />
      <OfficeInfoAlgeria t={t} />
      <BusinessInAlgeria t={t} />
      <EORServicesAlgeria t={t} />
      <OfficeLocationMapAlgeria t={t} />
      <OfficeLocationMapAlgeria2 t={t} />
      <ComplexityControlSectionPanel t={t} />
      <GlobalHRServicesSection
        title={t("Algeria:services:title")}
        SERVICES={SERVICES}
        defaultImage={serviceimgS1}
      />
      <AlgeriaLaborData />
      <TunisiaOfficePageForm country={"Algeria"} defaultCountryPhone="dz" />
    </div>
  );
}

export default hiringEmployeesAlgeriaGuide;
