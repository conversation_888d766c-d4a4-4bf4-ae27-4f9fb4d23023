"use client";

import { Container } from "@mui/material";
import { useEffect, useState } from "react";

import RightIcon from "@/assets/images/icons/2025-right.svg";
import LeftIcon from "@/assets/images/icons/2025-left.svg";

function CountdownHappyNewYear() {
  const today = new Date();
  const targetDate = new Date("2025-01-01");

  const calculateTimeLeft = () => {
    const newYear = new Date(new Date().getFullYear() + 1, 0, 1, 0, 0, 0); // Next New Year
    const now = new Date();
    const difference = newYear - now;

    if (difference <= 0) {
      return { days: 0, hours: 0, minutes: 0, seconds: 0 };
    }

    return {
      days: Math.floor(difference / (1000 * 60 * 60 * 24)),
      hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
      minutes: Math.floor((difference / (1000 * 60)) % 60),
      seconds: Math.floor((difference / 1000) % 60),
    };
  };

  const [timeLeft, setTimeLeft] = useState(calculateTimeLeft());

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  if (today < targetDate)
    return (
      <div id="new-year-countdown">
        <Container className="new-year-container">
          <div className="new-year-content">
            <RightIcon />
            <div className="countdown">
              <div className="countdown-content">
                <p className="counter">{timeLeft.days}</p>
                <p>day{timeLeft.days !== 1 ? "s" : ""}</p>
              </div>
              <div className="countdown-content">
                <p className="counter">{timeLeft.hours}</p>
                <p>hr{timeLeft.hours !== 1 ? "s" : ""}</p>
              </div>
              <div className="countdown-content">
                <p className="counter">{timeLeft.minutes}</p>
                <p>min{timeLeft.minutes !== 1 ? "s" : ""}</p>
              </div>
              <div className="countdown-content">
                <p className="counter">{timeLeft.seconds}</p>
                <p>sec{timeLeft.seconds !== 1 ? "s" : ""}</p>
              </div>
            </div>
            <LeftIcon />
          </div>
        </Container>
      </div>
    );
}

export default CountdownHappyNewYear;
