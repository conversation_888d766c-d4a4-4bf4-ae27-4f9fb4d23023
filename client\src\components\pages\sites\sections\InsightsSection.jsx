"use client";

import blogIMG from "@/assets/images/website/blog-img.png";
import SvgArrow from "@/assets/images/icons/arrow.svg";
import CustomButton from "@/components/ui/CustomButton";
import { useGetArticles } from "@/features/blog/hooks/blog.hook";
import { useTranslation } from "react-i18next";
import Link from "next/link";
import { websiteRoutesList } from "@/helpers/routesList";
import { Card, CardContent, CardMedia, CircularProgress, Container, Grid } from "@mui/material";

function InsightsSection({ language }) {
  const handleClick = (event, href) => {
    event.preventDefault();
    window.location.href = href;
  };

  const { t } = useTranslation();

  const {
    data: articlesData,
    isLoading,
    error,
  } = useGetArticles({
    language,
    isThreeLastArticles: "true",
  });

  return (
    <Container id="insights-section" className="custom-max-width">
      <h2 className="heading-h1 text-center">
        {t("payrollService:insightSection:title")}
      </h2>
      <p className="sub-heading text-center">
        {t("payrollService:insightSection:description")}
      </p>

      {isLoading ? (
        <div className="loading-spinner">
          <CircularProgress />
        </div>
      ) : error ? (
        <p className="error-message text-center">
          Error loading articles. Please try again later.
        </p>
      ) : (
        <Grid container spacing={4}>
          {articlesData?.articles?.map((article) => (
            <Grid
              className="blog-item"
              item
              xs={12}
              sm={6}
              md={4}
              key={article._id}
            >
              <Card className="card">
                {article?.category?.name && (
                  <p className="label-category">{article?.category?.name}</p>
                )}

                <CardMedia
                  className="card-image"
                  component="img"
                  image={
                    article.versions[0].image
                      ? `${process.env.NEXT_PUBLIC_BASE_API_URL}/files/${article.versions[0].image}`
                      : blogIMG.src
                  }
                  loading="lazy"
                  alt={article.versions[0]?.title}
                  onClick={() =>
                    (window.location.href = `/${websiteRoutesList.blog.route}/${article.versions[0]?.url}/`)
                  }
                />

                <CardContent className="card-content">
                  <Link
                    className="blog-title"
                    locale={language === "en" ? "en" : "fr"}
                    href={`${
                      language === "en"
                        ? `/${websiteRoutesList.blog.route}/${article.versions[0]?.url}`
                        : `/${language}/${websiteRoutesList.blog.route}/${article.versions[0]?.url}`
                    }/`}
                  >
                    {article.versions[0]?.title}
                  </Link>
                  <p className="blog-description">
                    {article.versions[0]?.metaDescription ||
                      "Lorem ipsum dolor sit amet consectetur."}
                  </p>
                </CardContent>
                <CustomButton
                  text={t("global:readMore")}
                  className={"btn btn-ghost"}
                  icon={<SvgArrow />}
                  link={`/${websiteRoutesList.blog.route}/${article.versions[0]?.url}`}
                  aHref
                />
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      <Grid className="btn-view-more">
        <CustomButton
          text={t("global:viewMore")}
          link={"/blog"}
          className={"btn btn-filled"}
          aHref
        />
      </Grid>
    </Container>
  );
}

export default InsightsSection;
