import BannerComponents from "@/components/pages/sites/sections/BannerComponents";
import banner from "@/assets/images/website/banner/Technical-Assistance-Pentabell-Services.webp";
import ResponsiveRowTitleText from "@/components/ui/ResponsiveRowTitleText";
import ServiceRow from "@/components/ui/ServiceRow";
import technicalAssistanceServiceImgS1 from "@/assets/images/services/technicalAssistanceServiceImgS1.png";
import technicalAssistanceServiceImgS2 from "@/assets/images/services/technicalAssistanceServiceImgS2.png";
import technicalAssistanceServiceImgS3 from "@/assets/images/services/technicalAssistanceServiceImgS3.png";
import HowItWorks from "@/components/pages/sites/services/HowItWorks";
import WhyPentabell from "@/components/pages/sites/services/WhyPentabell";
import AskQuestions from "@/components/pages/sites/services/AskQuestions";
import TechnicalAssServicePageForm from "@/features/forms/components/TechnicalAssServicePageForm";
import ComprehensiveSupport from "@/components/pages/sites/services/ComprehensiveSupport";
import initTranslations from "@/app/i18n";
import { axiosGetJsonSSR } from "@/config/axios";

export async function generateMetadata({ params: { locale } }) {
  const canonicalUrl = `https://www.pentabell.com/${locale !== "en" ? `${locale}/` : ""
    }hr-services/technical-assistance/`;

  const languages = {
    fr: `https://www.pentabell.com/fr/hr-services/technical-assistance/`,
    en: `https://www.pentabell.com/hr-services/technical-assistance/`,
    "x-default": `https://www.pentabell.com/hr-services/technical-assistance/`,
  };

  const { t } = await initTranslations(locale, [
    "technicalAssistanceService",
    "global",
  ]);

  try {
    const encodedSlug = encodeURIComponent("hr-services/technical-assistance");
    const res = await axiosGetJsonSSR.get(
      `${process.env.NEXT_PUBLIC_BASE_API_URL_SSR}/seoTags/${locale}/${encodedSlug}`
    );
    const seoTags = res?.data?.status === 200;
    if (seoTags) {
      return {
        title: res?.data?.data?.versions[0]?.metaTitle,
        description: res?.data?.data?.versions[0]?.metaDescription,
        robots: res?.data?.data?.robotMeta,
        alternates: {
          canonical: canonicalUrl,
          languages,
        },
      };
    }
  } catch (error) {
    console.error("Error fetching SEO tags:", error);
  }
  return {
    title: t("technicalAssistanceService:metaTitle"),
    description: t("technicalAssistanceService:metaDescription"),
    alternates: {
      canonical: canonicalUrl,
      languages,
    },
    robots: "follow, index, max-snippet:-1, max-image-preview:large",
  };
}
async function technicalAssistance({ params: { locale } }) {
  const { t } = await initTranslations(locale, [
    "technicalAssistanceService",
    "global",
  ]);

  const dataS1 = {
    label: t("technicalAssistanceService:dataS1:label"),
    title: t("technicalAssistanceService:dataS1:title"),
    paragraph: t("technicalAssistanceService:dataS1:paragraph"),
    altImg: t("technicalAssistanceService:dataS1:altImg"),
    featureImg: technicalAssistanceServiceImgS1,
  };

  const dataS2 = {
    label: t("technicalAssistanceService:dataS2:label"),
    title: t("technicalAssistanceService:dataS2:title"),
    paragraph: t("technicalAssistanceService:dataS2:paragraph"),
    altImg: t("technicalAssistanceService:dataS2:altImg"),
    featureImg: technicalAssistanceServiceImgS2,
  };

  const dataS3 = {
    label: t("technicalAssistanceService:dataS3:label"),
    title: t("technicalAssistanceService:dataS3:title"),
    paragraph: t("technicalAssistanceService:dataS3:paragraph"),
    altImg: t("technicalAssistanceService:dataS3:altImg"),
    featureImg: technicalAssistanceServiceImgS3,
  };

  const ASK_QUESTIONS = [
    {
      id: "s1",
      title: t("technicalAssistanceService:questions:QA1:question"),
      description: t("technicalAssistanceService:questions:QA1:answer"),
    },
    {
      id: "s2",
      title: t("technicalAssistanceService:questions:QA2:question"),
      description: t("technicalAssistanceService:questions:QA2:answer"),
    },
    {
      id: "s3",
      title: t("technicalAssistanceService:questions:QA3:question"),
      description: t("technicalAssistanceService:questions:QA3:answer"),
    },
    {
      id: "s4",
      title: t("technicalAssistanceService:questions:QA4:question"),
      description: t("technicalAssistanceService:questions:QA4:answer"),
    },
    {
      id: "s5",
      title: t("technicalAssistanceService:questions:QA5:question"),
      description: t("technicalAssistanceService:questions:QA5:answer"),
    },
    {
      id: "s6",
      title: t("technicalAssistanceService:questions:QA6:question"),
      description: t("technicalAssistanceService:questions:QA6:answer"),
    },
  ];

  const steps = [
    {
      title: t("technicalAssistanceService:howItWorks:steps:step1:title"),
      description: t(
        "technicalAssistanceService:howItWorks:steps:step1:description"
      ),
    },
    {
      title: t("technicalAssistanceService:howItWorks:steps:step2:title"),
      description: t(
        "technicalAssistanceService:howItWorks:steps:step2:description"
      ),
    },
    {
      title: t("technicalAssistanceService:howItWorks:steps:step3:title"),
      description: t(
        "technicalAssistanceService:howItWorks:steps:step3:description"
      ),
    },
  ];

  const reasons = [
    {
      heading: t(
        "technicalAssistanceService:whyPentabell:reasons:reason1:title"
      ),
      text: t(
        "technicalAssistanceService:whyPentabell:reasons:reason1:description"
      ),
    },
    {
      heading: t(
        "technicalAssistanceService:whyPentabell:reasons:reason2:title"
      ),
      text: t(
        "technicalAssistanceService:whyPentabell:reasons:reason2:description"
      ),
    },
    {
      heading: t(
        "technicalAssistanceService:whyPentabell:reasons:reason3:title"
      ),
      text: t(
        "technicalAssistanceService:whyPentabell:reasons:reason3:description"
      ),
    },
    {
      heading: t(
        "technicalAssistanceService:whyPentabell:reasons:reason4:title"
      ),
      text: t(
        "technicalAssistanceService:whyPentabell:reasons:reason4:description"
      ),
    },
  ];
  return (
    <div>
      <BannerComponents
        title={t("technicalAssistanceService:intro:title")}
        description={t("technicalAssistanceService:intro:description")}
        bannerImg={banner}
        height={"70vh"}
        altImg={t("technicalAssistanceService:intro:altImg")}
      />

      <ResponsiveRowTitleText
        title={t("technicalAssistanceService:overview:title")}
        paragraph={t("technicalAssistanceService:overview:paragraph1")}
        paragraph2={t("technicalAssistanceService:overview:paragraph2")}
      />

      <h2 className="heading-h1 text-center text-banking">
        {" "}
        {t("technicalAssistanceService:howWeHelp")}
      </h2>

      <ServiceRow data={dataS1} />
      <ServiceRow data={dataS2} reverse={true} darkBg={true} />
      <ServiceRow data={dataS3} />
      <ComprehensiveSupport locale={locale} />
      <HowItWorks locale={locale} steps={steps} />
      <WhyPentabell
        title1={t("technicalAssistanceService:whyPentabell:title1")}
        title2={t("technicalAssistanceService:whyPentabell:title2")}
        reasons={reasons}
      />
      <AskQuestions
        title={t("technicalAssistanceService:questions:title")}
        ASK_QUESTIONS={ASK_QUESTIONS}
      />
      {/* <InsightsSection /> */}
      <TechnicalAssServicePageForm />
    </div>
  );
}
export default technicalAssistance;
