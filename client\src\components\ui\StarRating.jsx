"use client";

import FilledStar from "../../assets/images/icons/filled-star.svg";
import EmptyStar from "../../assets/images/icons/empty-star.svg";

const StarRating = ({ rating }) => {
  const clampedRating = Math.min(Math.max(rating, 0), 5);

  return (
    <div className="star-fill">
      {[...Array(5)].map((_, index) =>
        index < clampedRating ? (
          <EmptyStar key={index} />
        ) : (
          <FilledStar key={index} />
        )
      )}
    </div>
  );
};

export default StarRating;
