"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContactController = void 0;
const express_1 = require("express");
const validateApiKey_middleware_1 = __importDefault(require("@/middlewares/validateApiKey.middleware"));
const contact_service_1 = __importDefault(require("./contact.service"));
const validateContactFormType_1 = __importDefault(require("@/middlewares/validateContactFormType"));
class ContactController {
    constructor() {
        this.path = '/contact';
        this.router = (0, express_1.Router)();
        this.contactService = new contact_service_1.default();
        this.getAllContact = async (request, response, next) => {
            try {
                const queries = request.query;
                const contacts = await this.contactService.getAllContactList(queries);
                response.status(200).send(contacts);
            }
            catch (error) {
                next(error);
            }
        };
        this.getStats = async (request, response, next) => {
            try {
                const queries = request.query;
                await this.contactService.getStats(queries);
                response.send(await this.contactService.getStats(queries));
            }
            catch (error) {
                next(error);
            }
        };
        this.fillContactForm = async (request, response, next) => {
            try {
                const contactForm = await this.contactService.fillContactFrom(request.body);
                response.status(201).send(contactForm);
            }
            catch (error) {
                next(error);
            }
        };
        this.get = async (request, response, next) => {
            try {
                const id = request.params.id;
                response.send(await this.contactService.get(id));
            }
            catch (error) {
                next(error);
            }
        };
        this.initialiseRoutes();
    }
    initialiseRoutes() {
        this.router.post(`${this.path}`, validateApiKey_middleware_1.default, validateContactFormType_1.default, this.fillContactForm);
        this.router.get(`${this.path}`, validateApiKey_middleware_1.default, this.getAllContact);
        this.router.get(`${this.path}/stats`, this.getStats);
        this.router.get(`${this.path}/:id`, validateApiKey_middleware_1.default, this.get);
    }
}
exports.ContactController = ContactController;
//# sourceMappingURL=contact.controller.js.map