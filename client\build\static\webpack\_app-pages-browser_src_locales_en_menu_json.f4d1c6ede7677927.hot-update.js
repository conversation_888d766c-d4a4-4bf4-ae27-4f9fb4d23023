"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_locales_en_menu_json",{

/***/ "(app-pages-browser)/./src/locales/en/menu.json":
/*!**********************************!*\
  !*** ./src/locales/en/menu.json ***!
  \**********************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

module.exports = /*#__PURE__*/JSON.parse('{"aboutUs":"About Us","services":"HR Services","payrollServices":"Payroll Services","consultingServices":"Consulting Services","technicalAssistance":"Technical Assistance","itTelecom":"IT Telecom","aiSourcing":"AI Sourcing","directHiring":"Direct Hiring","directHiringService":"Direct Hiring Service","hiringProcessContact":"Hiring Process Contact","opportunities":"Opportunities","oilGas":"Oil & Gas","transportation":"Transportation","pharmaceutical":"Pharmaceutical","energy":"Energy","insuranceBanking":"Banking & Insurance ","others":"Other","blog":"Blog","joinUs":"Join Us","contact":"Contact","profile":"My Profile","notification":"Notification","login":"Login","logout":"Logout","pharma":"Pharmaceutical","notifications":"Notifications","myApplications":"My Applications","myResumes":"My Resumes","favoris":"Favorites","seoSettings":"Seo Settings","statistics":"Statistics","comments":"Comments","users":"Users","events":"Events","corporateProfile":"Corporate Profile","company Profile":"company  Profile"}');

/***/ })

});