import franceImg from "@/assets/images/offices/france.png";
import switzerlandImg from "@/assets/images/offices/switzerland.png";
import { OfficesCountries } from "@/config/countries";
import LocationsList from "@/components/ui/LocationsList";

function LocationsInEurope({ t }) {
  const contacts = [
    {
      title: t("europe:locations:france:title"),
      locations: [
        t("europe:locations:france:address"),
      ],
      phones: ["+33 1 73 07 42 54"],
      email: "<EMAIL>",
      link: "#",
      img: franceImg,
      country: OfficesCountries.FRANCE,
      alt: t("europe:locations:france:alt")
    },
    {
      title: t("europe:locations:switzerland:title"),
      locations: [
        t("europe:locations:switzerland:address"),
      ],
      phones: ["+33 1 73 07 42 54"],
      email: "<EMAIL>",
      link: "#",
      img: switzerlandImg,
      country: OfficesCountries.SWITZERLAND,
      alt: t("europe:locations:switzerland:alt")
    },

  ];
  return (
    <LocationsList
      t={t}
      contacts={contacts}
      sectionSubtitleKey="europe:locations:subTitle"
      sectionTitleKey="europe:locations:title"
    />
  );
}

export default LocationsInEurope;
