"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const user_model_1 = __importDefault(require("../user.model"));
const services_1 = require("@/utils/services");
const candidat_model_1 = __importDefault(require("../../candidat/candidat.model"));
const messages_1 = require("@/utils/helpers/messages");
class AccountService {
    constructor() {
        this.User = user_model_1.default;
        this.Candidat = candidat_model_1.default;
    }
    async getCurrentUser(field) {
        const user = await this.User.findOne(field).lean();
        if (!user)
            throw new http_exception_1.default(404, messages_1.MESSAGES.USER.USER_NOT_FOUND);
        const { _id, firstName, lastName, email, profilePicture, phone, roles, candidate, password } = user;
        return {
            _id,
            firstName,
            lastName,
            email,
            password,
            profilePicture,
            phone,
            roles,
            candidate,
            passwordExist: !!password,
        };
    }
    async getAccount(field) {
        const user = await this.User.findOne(field);
        if (!user)
            throw new http_exception_1.default(404, messages_1.MESSAGES.USER.USER_NOT_FOUND);
        return user;
    }
    async update(accountData, id, file) {
        if (file)
            accountData.profileImage = file.path;
        await this.User.findByIdAndUpdate(id, accountData, { new: true });
    }
    async updatePassword(updatePasswordData, id) {
        const { currentPassword, newPassword } = updatePasswordData;
        const account = await this.getAccount({ _id: id });
        if (account.password) {
            const isMatched = await services_1.auth.comparePassword(currentPassword, account.get('password', null, { getters: false }));
            if (!isMatched)
                throw new http_exception_1.default(406, messages_1.MESSAGES.AUTH.PASSWORD_INCORRECT);
        }
        account.password = await services_1.auth.hashPassword(newPassword);
        await account.save();
    }
    async resetPassword(token, userInfo) {
        const { password, country, industry } = userInfo;
        const user = await this.getAccount({ resetPasswordToken: token });
        if (!user)
            throw new http_exception_1.default(400, messages_1.MESSAGES.AUTH.INVALID_TOKEN);
        if (password) {
            user.password = await services_1.auth.hashPassword(password);
        }
        const candidate = await this.Candidat.findOne({ user: user._id });
        if (candidate) {
            if (industry && Array.isArray(industry) && industry.length > 0 && industry[0] !== '') {
                candidate.industry = industry[0];
            }
            if (country && Array.isArray(country) && country.length > 0 && country[0] !== '') {
                candidate.country = country[0];
            }
            await candidate.save();
        }
        user.resetPasswordToken = null;
        await user.save();
    }
    async getAllUsers() {
        try {
            const users = await this.User.find({}).populate('candidat').exec();
            return users;
        }
        catch (error) {
            throw error;
        }
    }
    async getAllCandidats() {
        try {
            const candidats = await this.Candidat.find({}).populate('user').exec();
            return candidats;
        }
        catch (error) {
            throw error;
        }
    }
}
exports.default = AccountService;
//# sourceMappingURL=account.service.js.map