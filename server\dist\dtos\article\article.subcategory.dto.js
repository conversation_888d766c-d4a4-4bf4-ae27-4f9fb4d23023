"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubCategoryDTO = exports.SubCategoryVersionDTO = void 0;
const constants_1 = require("@/utils/helpers/constants");
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
class SubCategoryVersionDTO {
    constructor(subCategoryVersion = {}) {
        Object.assign(this, subCategoryVersion);
    }
}
exports.SubCategoryVersionDTO = SubCategoryVersionDTO;
__decorate([
    (0, class_transformer_1.Expose)({ name: '_id' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SubCategoryVersionDTO.prototype, "id", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'name' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SubCategoryVersionDTO.prototype, "name", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'language' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SubCategoryVersionDTO.prototype, "language", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'url' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SubCategoryVersionDTO.prototype, "slug", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'description' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SubCategoryVersionDTO.prototype, "description", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'image' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SubCategoryVersionDTO.prototype, "picture", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'createdAt' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], SubCategoryVersionDTO.prototype, "creationDate", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'updatedAt' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], SubCategoryVersionDTO.prototype, "updateDate", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Object)
], SubCategoryVersionDTO.prototype, "buffer", void 0);
class SubCategoryDTO {
    constructor(subCategory = {}) {
        Object.assign(this, subCategory);
    }
}
exports.SubCategoryDTO = SubCategoryDTO;
__decorate([
    (0, class_transformer_1.Expose)({ name: '_id' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SubCategoryDTO.prototype, "id", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'subCategoryVersions' }),
    (0, class_transformer_1.Type)(() => SubCategoryVersionDTO),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], SubCategoryDTO.prototype, "versions", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'robotsMeta' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SubCategoryDTO.prototype, "robotsMeta", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Date)
], SubCategoryDTO.prototype, "createdAt", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Date)
], SubCategoryDTO.prototype, "updatedAt", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Number)
], SubCategoryDTO.prototype, "__v", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Object)
], SubCategoryDTO.prototype, "buffer", void 0);
//# sourceMappingURL=article.subcategory.dto.js.map