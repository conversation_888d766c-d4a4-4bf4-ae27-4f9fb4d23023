import banner from "@/assets/images/France/Pentabell-France.webp";
import BannerComponents from "@/components/pages/sites/sections/BannerComponents";
import OurPartners from "@/components/pages/sites/sections/OurPartners";
import r1 from "@/assets/images/services/recrutement1.png";
import r2 from "@/assets/images/services/recrutement2.png";
import r3 from "@/assets/images/services/recrutement3.png";
import r4 from "@/assets/images/services/recrutement4.png";
import serviceImg from "@/assets/images/services/service1.png";
import TunisiaOfficePageForm from "@/features/forms/components/TunisiaOfficePageForm";
import GlobalHRServicesSection from "@/components/pages/sites/sections/GlobalHRServicesSection";
import WhychooseFrance from "@/components/pages/sites/offices-sections/france/WhychooseFrance";
import initTranslations from "@/app/i18n";
import ResponsiveRowTitleText from "@/components/ui/ResponsiveRowTitleText";
import { websiteRoutesList } from "@/helpers/routesList";
import OfficeLocationMapFrance from "@/components/pages/sites/offices-sections/france/OfficeLocationMapFrance";
import OfficeInfoFrance from "@/components/pages/sites/offices-sections/france/OfficeInfoFrance";
import BusinessInFrance from "@/components/pages/sites/offices-sections/france/BusinessInFrance";
import EORServicesFrance from "@/components/pages/sites/offices-sections/france/EORServicesFrance";
import FranceLaborData from "@/components/pages/sites/offices-sections/labor-laws/labor-data/FranceLaborData";
import { axiosGetJsonSSR } from "@/config/axios";
import serviceimgS1 from "@/assets/images/services/service1.png";

export async function generateMetadata({ params: { locale } }) {
  const canonicalUrl = `https://www.pentabell.com/${locale !== "en" ? `${locale}/` : ""
    }recruitment-agency-france/`;

  const languages = {
    fr: `https://www.pentabell.com/fr/recruitment-agency-france/`,
    en: `https://www.pentabell.com/recruitment-agency-france/`,
    "x-default": `https://www.pentabell.com/recruitment-agency-france/`,
  };
  const { t } = await initTranslations(locale, ["servicesByCountry"]);

  try {
    const res = await axiosGetJsonSSR.get(
      `${process.env.NEXT_PUBLIC_BASE_API_URL_SSR}/seoTags/${locale}/recruitment-agency-france`
    );
    const seoTags = res?.data?.status === 200;
    if (seoTags) {
      return {
        title: res?.data?.data?.versions[0]?.metaTitle,
        description: res?.data?.data?.versions[0]?.metaDescription,
        robots: res?.data?.data?.robotMeta,
        alternates: {
          canonical: canonicalUrl,
          languages,
        },
      };
    }
  } catch (error) {
    console.error("Error fetching SEO tags:", error);
  }

  return {
    title: t("servicesByCountry:france:metaTitle"),
    description: t("servicesByCountry:france:metaDescription"),
    alternates: {
      canonical: canonicalUrl,
      languages,
    },
    robots: "follow, index, max-snippet:-1, max-image-preview:large",
  };
}

async function hiringEmployeesFranceGuide({ params: { locale } }) {
  const { t } = await initTranslations(locale, ["Tunisia", "france"]);

  const SERVICES = [
    {
      id: "s1",
      title: t("Tunisia:services:dataS1:title"),
      description: t("Tunisia:services:dataS1:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.payrollServices.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: serviceImg,
      altImg: t("Tunisia:services:dataS1:altImg"),
    },
    {
      id: "s2",
      title: t("Tunisia:services:dataS2:title"),
      description: t("Tunisia:services:dataS2:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.consultingServices.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r4,
      altImg: t("Tunisia:services:dataS2:altImg"),
    },
    {
      id: "s3",
      title: t("Tunisia:services:dataS3:title"),
      description: t("Tunisia:services:dataS3:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.technicalAssistance.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r3,
      altImg: t("Tunisia:services:dataS3:altImg"),
    },
    {
      id: "s4",
      title: t("Tunisia:services:dataS4:title"),
      description: t("Tunisia:services:dataS4:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.aiSourcing.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r2,
      altImg: t("Tunisia:services:dataS4:altImg"),
    },
    {
      id: "s5",
      title: t("Tunisia:services:dataS5:title"),
      description: t("Tunisia:services:dataS5:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.directHiring.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r1,
      altImg: t("Tunisia:services:dataS5:altImg"),
    },
  ];
  return (
    <div>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Organization",
            name: t("contactUs:bureux:contacts:france"),
            address: {
              "@type": "PostalAddress",
              streetAddress:
                "Atlantic Building Montparnasse, Entrance No. 7, 3rd floor",
              addressLocality: "Paris",
              postalCode: "75015",
              addressCountry: "FR",
            },
            telephone: "+33 1 73 07 42 54",
            email: "<EMAIL>",
            url:
              locale === "en"
                ? "https://www.pentabell.com/recruitment-agency-france/"
                : `https://www.pentabell.com/${locale}/recruitment-agency-france/`,
          }),
        }}
      />
      <BannerComponents
        title={t("france:title")}
        description={t("france:description")}
        bannerImg={banner}
        height={"100vh"}
        altImg={t("france:altImg")}
      />
      <OurPartners disableTxt={true} />
      <ResponsiveRowTitleText
        title={t("france:intro:title")}
        paragraph={t("france:intro:description")}
        paragraph2={t("france:intro:description2")}
      />
      <OfficeInfoFrance t={t} />
      <BusinessInFrance t={t} />
      <EORServicesFrance t={t} />
      <WhychooseFrance t={t} />
      <OfficeLocationMapFrance t={t} />
      <GlobalHRServicesSection
        title={"Our HR services in France Payroll, Recruitment & HR Consulting"}
        SERVICES={SERVICES}
        defaultImage={serviceimgS1}
      />
      <FranceLaborData />
      <TunisiaOfficePageForm country={"France"} defaultCountryPhone="fr" />
    </div>
  );
}

export default hiringEmployeesFranceGuide;
