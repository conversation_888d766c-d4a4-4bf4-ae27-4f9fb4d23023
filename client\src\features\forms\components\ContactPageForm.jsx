"use client";
import { <PERSON><PERSON>, Autocomplete, Container } from "@mui/material";
import { Formik, Form, ErrorMessage } from "formik";
import {
  Grid,
  Checkbox,
  FormControlLabel,
  FormGroup,
  FormLabel,
  TextField,
} from "@mui/material";
import { PhoneNumberUtil } from "google-libphonenumber";
import "react-international-phone/style.css";
import * as Yup from "yup";
import { PhoneInput } from "react-international-phone";
import SvgSuccess from "@/assets/images/icons/success.svg";
import CustomButton from "@/components/ui/CustomButton";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useContactForm } from "@/features/contact/hooks/Contact.hooks";
import { contactValidationSchema } from "@/utils/validations";
import AlertMessage from "@/components/ui/AlertMessage";
import { Countries } from "@/utils/constants";
import GTM from "@/components/GTM";
import CompanyEmailValidator from "company-email-validator";
import SvgUploadIcon from "../../../assets/images/icons/uploadIcon.svg";
import { useSaveFile } from "@/features/opportunity/hooks/opportunity.hooks";
import { v4 as uuidv4 } from "uuid";
import { Industry } from "@/utils/constants";

import LazyLoadFlag from "../../../components/ui/LazyLoadFlag";

function ContactPageForm() {
  const [errMsg, setErrMsg] = useState("");
  const [success, setSuccess] = useState(false);
  const phoneUtil = PhoneNumberUtil.getInstance();
  const [isCompany, setIsCompany] = useState(false);
  const [newResumeFile, setNewResumeFile] = useState(null);
  const [errorValidationUpload, setErrorValidationUpload] = useState("");

  const { t } = useTranslation();

  const [submittedFirstName, setSubmittedFirstName] = useState("");
  const useContactFormHook = useContactForm(setSuccess, setErrMsg);

  const useSaveFileHook = useSaveFile();

  let uuidResume;
  let uuidResumeFileName;
  let formData = new FormData();

  const initialValues = {
    fullName: "",
    email: "",
    phone: "",
    youAre: "",
    subject: "",
    message: "",
    country: "",
    resume: "",
    field: "",
    acceptTerms: false,
  };

  const handleResumeChange = (e, setFieldValue) => {
    e.preventDefault();
    uuidResume = uuidv4().replace(/-/g, "");
    setErrorValidationUpload("");
    setNewResumeFile(null);
    const selectedFile = e.target.files[0];
    if (selectedFile) {
      formData.append("file", selectedFile);
      const extension = selectedFile.name.split(".").pop();
      uuidResumeFileName = `${uuidResume}.${extension}`;
      const currentYear = new Date().getFullYear();

      useSaveFileHook.mutate(
        {
          resource: "candidates",
          folder: currentYear,
          filename: uuidResume,
          body: { formData, t },
        },
        {
          onSuccess: (data) => {
            if (data.message === "uuid exist") {
              setNewResumeFile(data.uuid);
              setFieldValue("resume", data.uuid);
            } else {
              setNewResumeFile(uuidResumeFileName);
              setFieldValue("resume", uuidResumeFileName);
            }
          },
          onError: (error) => {
            if (error.response.data.status === 400) {
              setErrorValidationUpload(t("messages:requireResume"));
            } else if (error.response.data.status === 500) {
              setErrorValidationUpload("Internal Server Error");
            } else {
              setErrorValidationUpload(error.response.data.message);
            }
          },
        }
      );
    }
  };

  const handleSubmit = async (values, { resetForm }) => {
    const nonEmptyValues = {
      ...Object.fromEntries(
        Object.entries(values).filter(
          ([key, value]) =>
            key !== "acceptTerms" &&
            value !== "" &&
            value !== null &&
            value !== undefined
        )
      ),
    };

    await useContactFormHook.mutateAsync({
      ...nonEmptyValues,
      to:
        values.youAre === "Consultant"
          ? `${process.env.NEXT_PUBLIC_EMAIL_FORM_JOINUS}`
          : `${process.env.NEXT_PUBLIC_EMAIL_FORM_DESTINATION}`,
      team: "digital",
      type: "getInTouchContact",
    });
    setSubmittedFirstName(values.fullName);
    resetForm();
    setNewResumeFile(null);

    window.dataLayer = window.dataLayer || [];
    window.dataLayer.push({
      event: "Contact_form",
      button_id: "contact_form_button",
    });
  };

  const isPhoneValid = (phone) => {
    try {
      return phoneUtil.isValidNumber(phoneUtil.parseAndKeepRawInput(phone));
    } catch (error) {
      return false;
    }
  };

  const phoneValidationSchema = Yup.string().test(
    "is-valid-phone",
    t("validations:phoneFormat"),
    (value) => isPhoneValid(value)
  );

  const combinedValidationSchema = (isCompany) =>
    contactValidationSchema(t).shape({
      phone: phoneValidationSchema,
      email: Yup.string()
        .email(t("validations:invalidEmail"))
        .required(t("validations:required"))
        .test(
          "is-company-email",
          t("validations:companyEmailRequired"),
          function (value) {
            const { youAre } = this.parent;
            const isCompany = youAre === "Company";

            if (!value) return false;
            if (isCompany) {
              return CompanyEmailValidator.isCompanyEmail(value);
            }
            return true;
          }
        ),
    });

  return (
    <div id="contact-page-form">
      <GTM />
      <Container className="custom-max-width">
        {success ? (
          <div className="section-guide">
            <div className="form-success">
              <SvgSuccess />

              <p className="sub-heading">
                {" "}
                {t("messages:thankyou")} {submittedFirstName}{" "}
                {t("messages:messagesuccess")}
              </p>
            </div>
          </div>
        ) : (
          <>
            <h2 className="heading-h1">{t("getInTouch:getInTouch")}</h2>
            <p className="sub-heading">{t("getInTouch:description")}</p>
            <Formik
              initialValues={initialValues}
              validationSchema={() => combinedValidationSchema(isCompany)}
              onSubmit={handleSubmit}
            >
              {({ values, handleChange, errors, touched, setFieldValue }) => (
                <Form className="pentabell-form">
                  <Grid container rowSpacing={4} columnSpacing={3}>
                    <Grid item xs={12} sm={6}>
                      <FormGroup className="form-group ">
                        <FormLabel className="label-pentabell ">
                          {t("getInTouch:fullName")}*
                        </FormLabel>
                        <TextField
                          autoComplete="off"
                          className="input-pentabell "
                          placeholder={t("getInTouch:fullName")}
                          variant="standard"
                          type="text"
                          name="fullName"
                          value={values.fullName}
                          onChange={handleChange}
                          error={!!(errors.fullName && touched.fullName)}
                        />
                      </FormGroup>
                      <ErrorMessage name="fullName">
                        {(msg) => <span className="error-span">{msg}</span>}
                      </ErrorMessage>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <FormGroup className="form-group ">
                        <FormLabel className="label-pentabell ">
                          {t("getInTouch:email")}*
                        </FormLabel>
                        <TextField
                          autoComplete="off"
                          className="input-pentabell "
                          placeholder={t("getInTouch:email")}
                          variant="standard"
                          type="email"
                          name="email"
                          value={values.email}
                          onChange={handleChange}
                          error={!!(errors.email && touched.email)}
                        />
                      </FormGroup>
                      <ErrorMessage name="email">
                        {(msg) => <span className="error-span">{msg}</span>}
                      </ErrorMessage>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <FormGroup className="form-group ">
                        <FormLabel className="label-pentabell ">
                          {t("getInTouch:phone")}
                        </FormLabel>
                        <PhoneInput
                          defaultCountry="fr"
                          className="input-pentabell"
                          value={values.phone}
                          onChange={(phoneNumber) => {
                            setFieldValue("phone", phoneNumber);
                            setErrMsg("");
                          }}
                          flagComponent={(props) => <LazyLoadFlag {...props} />}
                        />
                      </FormGroup>
                      <ErrorMessage name="phone">
                        {(msg) => <span className="error-span">{msg}</span>}
                      </ErrorMessage>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <FormGroup className="form-group">
                        <FormLabel className="label-pentabell">
                          {t("getInTouch:youAre")}*
                        </FormLabel>
                        <Autocomplete
                          className="input-pentabell"
                          id="tags-standard"
                          options={["Consultant", "Company"]}
                          getOptionLabel={(option) => option}
                          name="youAre"
                          value={values.youAre}
                          onChange={(event, value) => {
                            setFieldValue("youAre", value);
                            setIsCompany(value === "Company");
                          }}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              className="input-pentabell multiple-select"
                              variant="standard"
                              placeholder={t(
                                "aiSourcingService:servicePageForm:chooseOne"
                              )}
                              error={!!(errors.youAre && touched.youAre)}
                            />
                          )}
                        />
                      </FormGroup>
                      <ErrorMessage name="youAre">
                        {(msg) => <span className="error-span">{msg}</span>}
                      </ErrorMessage>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <FormGroup className="form-group ">
                        <FormLabel className="label-pentabell ">
                          {t("getInTouch:subject")}
                        </FormLabel>
                        <TextField
                          autoComplete="off"
                          className="input-pentabell "
                          placeholder={t("getInTouch:subject")}
                          variant="standard"
                          type="text"
                          name="subject"
                          value={values.subject}
                          onChange={handleChange}
                          error={!!(errors.subject && touched.subject)}
                        />
                      </FormGroup>
                      <ErrorMessage name="subject">
                        {(msg) => <span className="error-span">{msg}</span>}
                      </ErrorMessage>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <FormGroup className="form-group ">
                        <FormLabel className="label-pentabell ">
                          {t("getInTouch:countryName")}
                        </FormLabel>
                        <Autocomplete
                          className="input-pentabell"
                          id="tags-standard"
                          options={Countries}
                          getOptionLabel={(option) => option}
                          name="country"
                          value={values.country}
                          onChange={(event, value) =>
                            setFieldValue("country", value)
                          }
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              className="input-pentabell multiple-select"
                              variant="standard"
                              placeholder={"Choose country"}
                              error={!!(errors.country && touched.country)}
                            />
                          )}
                        />
                      </FormGroup>
                      <ErrorMessage name="country">
                        {(msg) => <span className="error-span">{msg}</span>}
                      </ErrorMessage>
                    </Grid>
                    {values.youAre === "Consultant" && (
                      <Grid item xs={12} sm={4}>
                        <FormGroup className="form-group">
                          <FormLabel className="label-pentabell">
                            {t("consultingServices:servicePageForm:industry")}*
                          </FormLabel>
                          <Autocomplete
                            className="input-pentabell"
                            id="tags-standard"
                            options={Object.values(Industry)}
                            getOptionLabel={(option) => option}
                            name="field"
                            value={values.field}
                            onChange={(event, newValue) => {
                              setFieldValue("field", newValue);
                            }}
                            renderInput={(params) => (
                              <TextField
                                {...params}
                                className="input-pentabell multiple-select"
                                variant="standard"
                                placeholder={t(
                                  "consultingServices:servicePageForm:chooseOne"
                                )}
                                error={!!(errors.field && touched.field)}
                              />
                            )}
                          />
                        </FormGroup>
                        <ErrorMessage name="field">
                          {(msg) => <span className="error-span">{msg}</span>}
                        </ErrorMessage>
                      </Grid>
                    )}
                    <Grid
                      item
                      xs={12}
                      sm={values.youAre === "Consultant" ? 8 : 12}
                    >
                      <FormGroup className="form-group ">
                        <FormLabel className="label-pentabell ">
                          {t("getInTouch:message")}*
                        </FormLabel>
                        <TextField
                          autoComplete="off"
                          className="input-pentabell "
                          placeholder={t("getInTouch:typeMessage")}
                          variant="standard"
                          type="text"
                          name="message"
                          value={values.message}
                          onChange={handleChange}
                          error={!!(errors.message && touched.message)}
                        />
                      </FormGroup>
                      <ErrorMessage name="message">
                        {(msg) => <span className="error-span">{msg}</span>}
                      </ErrorMessage>
                    </Grid>
                    {values.youAre === "Consultant" && (
                      <Grid item xs={12} sm={12}>
                        <FormGroup className="form-group light form-section">
                          <div
                            className="custom-file-upload-wbg"
                            onClick={() =>
                              document.getElementById("file-upload").click()
                            }
                          >
                            <div>
                              <SvgUploadIcon />
                              {newResumeFile ? (
                                <>
                                  <FormLabel className="label-pentabell light">
                                    {t("joinUs:form:uploadCv")}*
                                  </FormLabel>
                                  <p className="sub-label">{newResumeFile}</p>
                                </>
                              ) : (
                                <>
                                  <FormLabel className="label-pentabell light">
                                    {t("joinUs:form:uploadCv")}
                                  </FormLabel>
                                  <p className="sub-label">
                                    {t("joinUs:form:control")}
                                  </p>
                                </>
                              )}
                              <CustomButton
                                text={"Choose a file"}
                                className={"btn btn-outlined white"}
                              />
                              {errorValidationUpload && (
                                <Alert variant="filled" severity="error">
                                  {errorValidationUpload}
                                </Alert>
                              )}
                            </div>
                            <input
                              id="file-upload"
                              type="file"
                              name="resume"
                              accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword"
                              style={{ display: "none" }}
                              onChange={(e) => {
                                handleResumeChange(e, setFieldValue);
                              }}
                              error={!!(errors.resume && touched.resume)}
                            />
                          </div>
                        </FormGroup>
                        <ErrorMessage name="resume">
                          {(msg) => (
                            <Alert variant="filled" severity="error">
                              {msg}
                            </Alert>
                          )}
                        </ErrorMessage>
                      </Grid>
                    )}

                    <Grid item xs={12} sm={8}>
                      <FormGroup>
                        <FormControlLabel
                          className="checkbox-pentabell "
                          control={
                            <Checkbox
                              name="acceptTerms"
                              checked={values.acceptTerms}
                              onChange={handleChange}
                              error={
                                !!(errors.acceptTerms && touched.acceptTerms)
                              }
                            />
                          }
                          label={t(
                            "aiSourcingService:servicePageForm:formSubmissionAgreement"
                          )}
                        />
                        <ErrorMessage name="acceptTerms">
                          {(msg) => <span className="error-span">{msg}</span>}
                        </ErrorMessage>
                      </FormGroup>
                    </Grid>
                    <Grid item xs={12} sm={4} className="flex-end">
                      <CustomButton
                        text={t("getInTouch:submit")}
                        className={"btn btn-filled btn-submit"}
                        type="submit"
                      />
                    </Grid>
                  </Grid>
                </Form>
              )}
            </Formik>
            <AlertMessage errMsg={errMsg} success={success} />
          </>
        )}
      </Container>
    </div>
  );
}

export default ContactPageForm;
