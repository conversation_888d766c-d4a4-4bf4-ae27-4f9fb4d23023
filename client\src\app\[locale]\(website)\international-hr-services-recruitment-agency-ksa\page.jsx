import banner from "@/assets/images/Saudi/Pentabell-KSA.webp";
import BannerComponents from "@/components/pages/sites/sections/BannerComponents";
import OurPartners from "@/components/pages/sites/sections/OurPartners";
import r1 from "@/assets/images/services/recrutement1.png";
import r2 from "@/assets/images/services/recrutement2.png";
import r3 from "@/assets/images/services/recrutement3.png";
import r4 from "@/assets/images/services/recrutement4.png";
import TunisiaOfficePageForm from "@/features/forms/components/TunisiaOfficePageForm";
import Overview from "@/components/pages/sites/sections/Overview";
import OfficeLocationMapSaudi from "@/components/pages/sites/offices-sections/ksa/OfficeLocationMapSaudi";
import initTranslations from "@/app/i18n";
import serviceimgS1 from "@/assets/images/services/service1.png";
import BusinessInKSA from "@/components/pages/sites/offices-sections/ksa/BusinessInKSA";
import OfficeInfoKSA from "@/components/pages/sites/offices-sections/ksa/OfficeInfoKSA";
import KSALaborData from "@/components/pages/sites/offices-sections/labor-laws/labor-data/KSALaborData";
import { websiteRoutesList } from "@/helpers/routesList";
import { axiosGetJsonSSR } from "@/config/axios";
import OurGlobalHRServices from "@/components/pages/sites/sections/OurGlobalHRServices";
import FeaturedEvents from "@/components/pages/sites/sections/FeaturedEvents";
import saudiImage from "@/assets/images/Saudi/saudiImage.png";
import SaudiIcon from "@/assets/images/icons/saudi.svg";
import SaudiBgIcon from "@/assets/images/icons/BgSaudiIcon.svg";
import PayrollIcon from "@/assets/images/services/icons/Payroll.svg";
import HRIcon from "@/assets/images/services/icons/HR.svg";
import AiIcon from "@/assets/images/services/icons/Ai.svg";
import TechassistanceIcon from "@/assets/images/services/icons/techassistance.svg";
import PayrollBGIcon from "@/assets/images/icons/moneyIcon.svg";
import HRBGIcon from "@/assets/images/icons/HrIcon.svg";
import AiBGIcon from "@/assets/images/icons/AiIcon.svg";
import TechassistanceBGIcon from "@/assets/images/icons/techAssistanceIcon.svg";
export async function generateMetadata({ params: { locale } }) {
  const canonicalUrl = `https://www.pentabell.com/${locale !== "en" ? `${locale}/` : ""
    }international-hr-services-recruitment-agency-ksa/`;

  const languages = {
    fr: `https://www.pentabell.com/fr/international-hr-services-recruitment-agency-ksa/`,
    en: `https://www.pentabell.com/international-hr-services-recruitment-agency-ksa/`,
    "x-default": `https://www.pentabell.com/international-hr-services-recruitment-agency-ksa/`,
  };

  const { t } = await initTranslations(locale, [
    "homePage",
    "servicesByCountry",
    "ksa",
  ]);
  try {
    const res = await axiosGetJsonSSR.get(
      `${process.env.NEXT_PUBLIC_BASE_API_URL_SSR}/seoTags/${locale}/international-hr-services-recruitment-agency-ksa`
    );
    const seoTags = res?.data?.status === 200;
    if (seoTags) {
      return {
        title: res?.data?.data?.versions[0]?.metaTitle,
        description: res?.data?.data?.versions[0]?.metaDescription,
        robots: res?.data?.data?.robotMeta,
        alternates: {
          canonical: canonicalUrl,
          languages,
        },
      };
    }
  } catch (error) {
    console.error("Error fetching SEO tags:", error);
  }
  return {
    title: t("servicesByCountry:ksa:metaTitle"),
    description: t("servicesByCountry:ksa:metaDescription"),
    alternates: {
      canonical: canonicalUrl,
      languages,
    },
    robots: "follow, index, max-snippet:-1, max-image-preview:large",
  };
}

async function hiringEmployeesSaudiGuide({ params: { locale } }) {
  const { t } = await initTranslations(locale, [
    "homePage",
    "servicesByCountry",
    "Tunisia",
    "ksa",
    "event",
  ]);

  const SERVICES = [
    {
      id: "s1",
      title: t("ksa:services:dataS1:title"),
      description: t("ksa:services:dataS1:description"),
      bulletPoints: [
        t("ksa:services:dataS1:puce1"),
        t("ksa:services:dataS1:puce2"),
        t("ksa:services:dataS1:puce3"),
      ],
      link: `#service-page-form`,
      linkText: t("ksa:services:dataS1:linkText"),
      img: saudiImage,
      alt: t("ksa:services:dataS1:altImg"),
      icon: <SaudiIcon />,
      bgIcon: <SaudiBgIcon className="svg-service-icon ksa" />,
    },
    {
      id: "s2",
      title: t("ksa:services:dataS2:title"),
      description: t("ksa:services:dataS2:description"),
      bulletPoints: [
        t("ksa:services:dataS2:puce1"),
        t("ksa:services:dataS2:puce2"),
        t("ksa:services:dataS2:puce3"),
        t("ksa:services:dataS2:puce4"),
      ],
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.payrollServices.route}`,
      linkText: t("ksa:services:dataS2:linkText"),
      img: r4,
      alt: t("ksa:services:dataS2:altImg"),
      icon: <PayrollIcon />,
      bgIcon: <PayrollBGIcon className="svg-service-icon ksa" />,
    },
    {
      id: "s3",
      title: t("ksa:services:dataS3:title"),
      description: t("ksa:services:dataS3:description"),
      bulletPoints: [
        t("ksa:services:dataS3:puce1"),
        t("ksa:services:dataS3:puce2"),
        t("ksa:services:dataS3:puce3"),
      ],
      link: `#service-page-form`,
      linkText: t("ksa:services:dataS3:linkText"),
      img: r3,
      alt: t("ksa:services:dataS3:altImg"),
      icon: <HRIcon />,
      bgIcon: <HRBGIcon className="svg-service-icon ksa" />,
    },
    {
      id: "s4",
      title: t("ksa:services:dataS4:title"),
      description: t("ksa:services:dataS4:description"),
      bulletPoints: [
        t("ksa:services:dataS4:puce1"),
        t("ksa:services:dataS4:puce2"),
        t("ksa:services:dataS4:puce3"),
      ],
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.aiSourcing.route}`,
      linkText: t("ksa:services:dataS4:linkText"),
      img: r2,
      alt: t("ksa:services:dataS4:altImg"),
      icon: <AiIcon />,
      bgIcon: <AiBGIcon className="svg-service-icon ksa" />,
    },
    {
      id: "s5",
      title: t("ksa:services:dataS5:title"),
      description: t("ksa:services:dataS5:description"),
      bulletPoints: [
        t("ksa:services:dataS5:puce1"),
        t("ksa:services:dataS5:puce2"),
        t("ksa:services:dataS5:puce3"),
      ],
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.technicalAssistance.route}`,
      linkText: t("ksa:services:dataS5:linkText"),
      img: r1,
      alt: t("ksa:services:dataS5:altImg"),
      icon: <TechassistanceIcon />,
      bgIcon: <TechassistanceBGIcon className="svg-service-icon ksa" />,
    },
  ];
  return (
    <div>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Organization",
            name: t("contactUs:bureux:contacts:ksa"),
            address: {
              "@type": "PostalAddress",
              streetAddress: "3530 Umar Ibn Abdul Aziz Br Rd, Az Zahra",
              addressLocality: "Riyadh",
              postalCode: "12815",
              addressCountry: "SA",
            },
            telephone: "+33 1 73 07 42 54",
            email: "<EMAIL>",
            url:
              locale === "en"
                ? "https://www.pentabell.com/international-hr-services-recruitment-agency-ksa/"
                : `https://www.pentabell.com/${locale}/international-hr-services-recruitment-agency-ksa/`,
          }),
        }}
      />
      <BannerComponents
        title={t("ksa:title")}
        description={t("ksa:description")}
        linkTitle={t("ksa:link")}
        link={`#service-page-form`}
        bannerImg={banner}
        height={"100vh"}
        altImg={t("ksa:altImg")}
        t={t}
      />
      {/* <ResponsiveRowTitleText
        title={t("ksa:intro:title")}
        paragraph={t("ksa:intro:description")}
        paragraph2={t("ksa:intro:description2")}
        /> */}
      <OfficeInfoKSA />
      <BusinessInKSA />
      <OurPartners disableTxt={true} ksa={true} />

      <OurGlobalHRServices
        title={t("ksa:services:title")}
        SERVICES={SERVICES}
        defaultImage={serviceimgS1}
        defaultService={SERVICES[0]}
        ksa={true}
      />
      <KSALaborData />
      <OfficeLocationMapSaudi />
      <Overview />
      <FeaturedEvents language={locale} />
      <TunisiaOfficePageForm
        title={t("ksa:form:title1")}
        country={"KSA"}
        defaultCountryPhone="sa"
      />
    </div>
  );
}

export default hiringEmployeesSaudiGuide;
