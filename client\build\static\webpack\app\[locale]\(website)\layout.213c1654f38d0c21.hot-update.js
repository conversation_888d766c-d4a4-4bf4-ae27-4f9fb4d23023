"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/layout",{

/***/ "(app-pages-browser)/./src/components/ui/DropdownMenu.jsx":
/*!********************************************!*\
  !*** ./src/components/ui/DropdownMenu.jsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Menu/Menu.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Fade/Fade.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _assets_images_icons_arrowDown_svg__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../assets/images/icons/arrowDown.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowDown.svg\");\n/* harmony import */ var _assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../assets/images/icons/arrowUp.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowUp.svg\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _utils_functions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/functions */ \"(app-pages-browser)/./src/utils/functions.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst DropdownMenu = (param)=>{\n    let { buttonLabel, buttonHref, menuItems, subMenu, locale, withFlag, selectedFlag } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_6__.useTranslation)();\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hoveredItem, setHoveredItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const hoverTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const open = Boolean(anchorEl);\n    const isDetailBlogPath = ()=>pathname.includes(\"/blog/\") && !pathname.includes(\"/blog/category/\") && pathname !== \"/blog/\" && pathname !== \"/fr/blog/\";\n    const handleClick = (event)=>{\n        setAnchorEl(event.currentTarget);\n    };\n    const handleClose = ()=>{\n        setAnchorEl(null);\n    };\n    const handleClick2 = (event, item)=>{\n        event.stopPropagation();\n        if (item.onClick) {\n            item.onClick();\n        } else if (item.subItems == undefined) handleClose();\n    };\n    const handleMouseEnter = (event, item, index)=>{\n        if (item.subItems) {\n            // Clear any existing timeout\n            if (hoverTimeoutRef.current) {\n                clearTimeout(hoverTimeoutRef.current);\n            }\n            setHoveredItem(index);\n            // Set the anchor element to show the submenu\n            setAnchorEl(event.currentTarget);\n        }\n    };\n    const handleMouseLeave = ()=>{\n        // Add a small delay before hiding to prevent flickering\n        hoverTimeoutRef.current = setTimeout(()=>{\n            setHoveredItem(null);\n            setAnchorEl(null);\n        }, 150);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: subMenu ? `sub-dropdown-menu` : `dropdown-menu`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: pathname.includes(buttonHref) && buttonHref !== \"\" ? \"navbar-link dropdown-toggle active\" : isDetailBlogPath() ? \"navbar-link dropdown-toggle whiteBg\" : \"navbar-link dropdown-toggle\",\n                id: \"fade-button\",\n                \"aria-controls\": open ? \"fade-menu\" : undefined,\n                \"aria-haspopup\": \"true\",\n                \"aria-expanded\": open ? \"true\" : undefined,\n                onClick: handleClick,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: pathname.includes(buttonHref) && buttonHref !== \"\" ? \"dropdown-toggle-link active\" : isDetailBlogPath() ? \"dropdown-toggle-link whiteBg\" : \"dropdown-toggle-link\",\n                        href: buttonLabel?.toLowerCase() === \"resources\" || buttonLabel?.toLowerCase() === \"ressources\" ? \"#\" : buttonHref ? (0,_utils_functions__WEBPACK_IMPORTED_MODULE_5__.generateLocalizedSlug)(locale, buttonHref).replace(\"/en/\", \"/\") : pathname.replace(\"/en/\", \"/\"),\n                        locale: locale === \"en\" ? \"en\" : \"fr\",\n                        onClick: (e)=>{\n                            if (buttonLabel?.toLowerCase() === \"resources\" || buttonLabel?.toLowerCase() === \"ressources\") {\n                                e.preventDefault();\n                                e.stopPropagation();\n                            }\n                        },\n                        sx: {\n                            ...buttonLabel?.toLowerCase() === \"resources\" || buttonLabel?.toLowerCase() === \"ressources\" ? {\n                                cursor: \"default\",\n                                textDecoration: \"none\",\n                                \"&:hover\": {\n                                    textDecoration: \"none\"\n                                }\n                            } : {}\n                        },\n                        children: withFlag ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            className: \"flag-lang\",\n                            src: selectedFlag.src,\n                            width: 26,\n                            height: 22,\n                            disabled: true,\n                            loading: \"lazy\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 125,\n                            columnNumber: 13\n                        }, undefined) : buttonLabel\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, undefined),\n                    open ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowDown_svg__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 137,\n                        columnNumber: 17\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 137,\n                        columnNumber: 36\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                id: \"fade-menu\",\n                MenuListProps: {\n                    \"aria-labelledby\": \"fade-button\"\n                },\n                anchorEl: anchorEl,\n                open: open,\n                onClose: handleClose,\n                TransitionComponent: _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                disableScrollLock: true,\n                anchorOrigin: {\n                    vertical: \"bottom\",\n                    horizontal: subMenu ? \"right\" : \"left\"\n                },\n                children: menuItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: item.subItems ? `sub-menu dropdown-item` : `dropdown-item`,\n                        onClick: (e)=>handleClick2(e, item),\n                        onMouseEnter: (e)=>handleMouseEnter(e, item, index),\n                        onMouseLeave: handleMouseLeave,\n                        children: item.subItems ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        padding: \"6px 16px\",\n                                        display: \"block\",\n                                        cursor: \"pointer\"\n                                    },\n                                    children: item?.i18nName ? t(item?.i18nName) : item.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 17\n                                }, undefined),\n                                hoveredItem === index && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DropdownMenu, {\n                                    buttonLabel: item?.i18nName ? t(item?.i18nName) : item.name,\n                                    buttonHref: item.route,\n                                    menuItems: item.subItems,\n                                    subMenu: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 165,\n                            columnNumber: 15\n                        }, undefined) : item.route ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            href: (0,_utils_functions__WEBPACK_IMPORTED_MODULE_5__.generateLocalizedSlug)(locale, item.route),\n                            locale: locale === \"en\" ? \"en\" : \"fr\",\n                            className: pathname.includes(item.route) ? \"dropdown-item-link active\" : \"dropdown-item-link\",\n                            children: [\n                                item.icon ?? item.icon,\n                                \" \",\n                                item?.i18nName ? t(item?.i18nName) : item.name\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 185,\n                            columnNumber: 15\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"dropdown-item-link\",\n                            href: \"#\",\n                            children: withFlag ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                className: \"flag-lang\",\n                                src: item.flag.src,\n                                width: 26,\n                                height: 22,\n                                loading: \"lazy\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                                lineNumber: 200,\n                                columnNumber: 19\n                            }, undefined) : item?.i18nName ? t(item?.i18nName) : item.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 198,\n                            columnNumber: 15\n                        }, undefined)\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DropdownMenu, \"36QwMemMebvJ1uXx+pPompBtN0g=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname,\n        react_i18next__WEBPACK_IMPORTED_MODULE_6__.useTranslation\n    ];\n});\n_c = DropdownMenu;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DropdownMenu);\nvar _c;\n$RefreshReg$(_c, \"DropdownMenu\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/DropdownMenu.jsx\n"));

/***/ })

});