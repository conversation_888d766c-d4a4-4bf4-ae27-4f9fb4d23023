"use server";
import initTranslations from "@/app/i18n";
import { axiosGetJsonSSR } from "@/config/axios";
import { redirect } from "next/navigation";
import { headers } from "next/headers";
import EventLeap from "../../../../../components/events/EventLeap";
import EventGitex from "../../../../../components/events/EventGitex";
import EventQHSEEXPO from "../../../../../components/events/EventQHSEEXPO";
import EventLibya from "../../../../../components/events/EventLibya";
import StrategicCommitteeMeetingCasablanca from "../../../../../components/events/StrategicCommitteeMeetingCasablanca";
import { lazy, Suspense } from "react";
import EventForumAfricaFrance from "@/components/events/EventForumAfricaFrance";
import PentabellSalesTraining from "../../../../../components/events/PentabellSalesTraining";
import EventFrancoSaudiDecarbonization from "@/components/events/EventFrancoSaudiDecarbonization";
import TeamBuildingPadel from "@/components/events/TeamBuildingPadel";
export async function generateMetadata({ params: { locale, url } }) {
  const canonicalUrl = `https://www.pentabell.com/${locale !== "en" ? `${locale}/` : ""
    }events/${url}`;

  const languages = {
    fr: `https://www.pentabell.com/fr/events/${url}`,
    en: `https://www.pentabell.com/events/${url}`,
    "x-default": `https://www.pentabell.com/events/${url}`,
  };

  const { t } = await initTranslations(locale, [
    "eventDetails",
    "eventDetailsLeap",
    "global",
    "eventDetailsLibya",
    "eventDetailsGitex",
    "ForumAfricaFrance",
    "PentabellSalestraining",
    "QHSEEXPO",
    "StrategicMeetingMo",
    "TeamBuildingPadel"

  ]);
  const metaTitle =
    url === "franco-saudi-decarbonization-days"
      ? t("eventDetails:metaTitle")
      : url === "leap-tech-conference-2025-riyadh"
        ? t("eventDetailsLeap:metaTitle")
        : url === "Libyan-French-economic-forum-2025"
          ? t("eventDetailsLibya:metaTitle")
          : url === "Gitex-africa-morocco-2025"
            ? t("eventDetailsGitex:metaTitle")
            : url === "Africa-France-forum-on-ecological-and-energy-transition-2025"
              ? t("ForumAfricaFrance:metaTitle")
              : url === "Pentabell-sales-training-and-workshop"
                ? t("PentabellSalestraining:metaTitle")
                : url === "QHSE-EXPO-2025"
                  ? t("QHSEEXPO:metaTitle")
                  : url === "strategic-committee-meeting-casablanca-2025"
                    ? t("StrategicMeetingMo:metaTitle")
                    : url === "team-building-pentabell-padel-club-sidi-bou-said"
                      ? t("TeamBuildingPadel:metaTitle")
                      : "";

  const metaDescription =
    url === "franco-saudi-decarbonization-days"
      ? t("eventDetails:metaDescription")
      : url === "leap-tech-conference-2025-riyadh"
        ? t("eventDetailsLeap:metaDescription")
        : url === "Libyan-French-economic-forum-2025"
          ? t("eventDetailsLibya:metaDescription")
          : url === "Gitex-africa-morocco-2025"
            ? t("eventDetailsGitex:metaDescription")
            : url === "Africa-France-forum-on-ecological-and-energy-transition-2025"
              ? t("eventDetailsGitex:metaDescription")
              : url === "Pentabell-sales-training-and-workshop"
                ? t("PentabellSalestraining:metaDescription")
                : url === "QHSE-EXPO-2025"
                  ? t("QHSEEXPO:metaDescription")
                  : url === "strategic-committee-meeting-casablanca-2025"
                    ? t("StrategicMeetingMo:metaDescription")
                    : url === "team-building-pentabell-padel-club-sidi-bou-said"
                      ? t("TeamBuildingPadel:metaDescription")
                      : "";

  return {
    title: metaTitle,
    description: metaDescription,
    alternates: {
      canonical: canonicalUrl,
      languages,
    },
  };
}


const page = async ({ searchParams, params }) => {
  const BannerWrapper = lazy(() => import("@/components/events/BannerWrapper"));
  const requestHeaders = headers();
  const userAgent = headers().get("user-agent") || "";
  const isMobile = /mobile/i.test(userAgent);
  const deviceType = isMobile ? "mobile" : "desktop";
  const isMobileSSR = deviceType === "mobile";
  const cookie = requestHeaders.get("cookie");

  try {
    const res = await axiosGetJsonSSR.get(
      `/events/${params.locale}/${params.url}`,
      {
        headers: {
          Cookie: cookie,
        },
      }
    );

    const eventData = res?.data?.data;

    const event =
      params.url === "franco-saudi-decarbonization-days"
        ? "decarbonization"
        : params.url === "leap-tech-conference-2025-riyadh"
          ? "leap"
          : params.url === "Libyan-French-economic-forum-2025"
            ? "libya"
            : params.url === "Gitex-africa-morocco-2025"
              ? "Gitex"
              : params.url === "Africa-France-forum-on-ecological-and-energy-transition-2025"
                ? "AfricaFranceForum"
                : params.url === "Pentabell-sales-training-and-workshop"
                  ? "PentabellSalestraining"
                  : params.url === "QHSE-EXPO-2025"
                    ? "QHSEEXPO"
                    : params.url === "strategic-committee-meeting-casablanca-2025"
                      ? "StrategicMeetingMorocco"
                      : params.url === "team-building-pentabell-padel-club-sidi-bou-said"
                        ? "TeamBuildingPadel"
                        : "";

    const { t } = await initTranslations(params.locale, [
      "eventDetails",
      "eventDetailsLeap",
      "eventDetailsLibya",
      "eventDetailsGitex",
      "ForumAfricaFrance",
      "event",
      "country",
      "PentabellSalestraining",
      "QHSEEXPO",
      "StrategicMeetingMo",
      "TeamBuildingPadel"
    ]);

    if (
      !["franco-saudi-decarbonization-days", "leap-tech-conference-2025-riyadh", "Libyan-French-economic-forum-2025", "Gitex-africa-morocco-2025", "Africa-France-forum-on-ecological-and-energy-transition-2025", "Pentabell-sales-training-and-workshop", "QHSE-EXPO-2025", "strategic-committee-meeting-casablanca-2025", "team-building-pentabell-padel-club-sidi-bou-said"].includes(params.url) &&
      !eventData
    ) {
      redirect(
        params.locale === "en" ? `/events/` : `/${params.locale}/events/`
      );
    }

    return (
      <div id="events-page">
        {eventData ? (
          <>
            <BannerWrapper
              eventData={eventData}
              language={params.locale}
              event={event}
            />
            {event === "libya" ? (
              <EventLibya event={eventData} language={params.locale} />
            ) : event === "leap" ? (
              <EventLeap event={eventData} language={params.locale} />
            ) : event === "Gitex" ? (
              <EventGitex event={eventData} language={params.locale} />
            ) : event === "AfricaFranceForum" ? (
              <EventForumAfricaFrance event={eventData} language={params.locale} />

            ) :
              event === "PentabellSalestraining" ? (
                <PentabellSalesTraining event={eventData} language={params.locale} />

              ) :
                event === "QHSEEXPO" ? (
                  <EventQHSEEXPO event={eventData} language={params.locale} />

                ) :

                  event === "StrategicMeetingMorocco" ? (
                    <StrategicCommitteeMeetingCasablanca event={eventData} language={params.locale} />

                  ) :
                    event === "TeamBuildingPadel" ? (
                      <TeamBuildingPadel event={eventData} language={params.locale} />

                    ) :

                      (
                        <EventFrancoSaudiDecarbonization event={eventData} language={params.locale} />
                      )}



          </>
        ) : params.url === "franco-saudi-decarbonization-days" ? (
          <>
            <BannerWrapper language={params.locale} event={event} />
            <EventFrancoSaudiDecarbonization language={params.locale} />
          </>
        ) : params.url === "leap-tech-conference-2025-riyadh" ? (
          <>
            <BannerWrapper language={params.locale} event={event} />
            <EventLeap language={params.locale} />
          </>
        ) : params.url === "Libyan-French-economic-forum-2025" ? (
          <>
            <BannerWrapper language={params.locale} event={event} />
            <EventLibya language={params.locale} />
          </>
        ) : params.url === "Africa-France-forum-on-ecological-and-energy-transition-2025" ? (
          <>
            <BannerWrapper language={params.locale} event={event} />
            <EventForumAfricaFrance language={params.locale} />
          </>
        ) : params.url === "QHSE-EXPO-2025" ? (
          <>
            <BannerWrapper language={params.locale} event={event} />
            <EventQHSEEXPO language={params.locale} />
          </>

        ) : params.url === "strategic-committee-meeting-casablanca-2025" ? (
          <>
            <BannerWrapper language={params.locale} event={event} />
            <StrategicCommitteeMeetingCasablanca language={params.locale} isMobileSSR={isMobileSSR} />
          </>
        ) : params.url === "team-building-pentabell-padel-club-sidi-bou-said" ? (
          <>
            <BannerWrapper language={params.locale} event={event} />
            <TeamBuildingPadel language={params.locale} isMobileSSR={isMobileSSR} />
          </>
        ) : params.url === "Gitex-africa-morocco-2025" ? (
          <>
            <BannerWrapper language={params.locale} event={event} />
            <EventGitex language={params.locale} />
          </>
        ) :
          params.url === "Pentabell-sales-training-and-workshop" ? (
            <>
              <BannerWrapper language={params.locale} event={event} />
              <PentabellSalesTraining language={params.locale} isMobileSSR={isMobileSSR} />
            </>
          ) :


            null}

      </div>
    );

  } catch (error) {
    if (
      !["franco-saudi-decarbonization-days", "leap-tech-conference-2025-riyadh", "Libyan-French-economic-forum-2025", "Gitex-africa-morocco-2025", "Africa-France-forum-on-ecological-and-energy-transition-2025", "Pentabell-sales-training-and-workshop", "QHSE-EXPO-2025", "team-building-pentabell-padel-club-sidi-bou-said"].includes(params.url)
    )
      redirect(
        params.locale === "en" ? `/events/` : `/${params.locale}/events/`
      );
  }
};

export default page;


