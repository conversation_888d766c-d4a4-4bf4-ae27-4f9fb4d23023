"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const userSettingsSchema = new mongoose_1.Schema({
    user: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
    },
    notifications: {
        newJobAlerts: {
            email: { type: Boolean, default: true },
            website: { type: Boolean, default: true },
        },
        appliedJobStatusUpdates: {
            email: { type: Boolean, default: true },
            website: { type: Boolean, default: true },
        },
        newsLetter: {
            email: { type: <PERSON>olean, default: true },
            website: { type: Boolean, default: true },
        },
    },
}, {
    timestamps: true,
});
exports.default = (0, mongoose_1.model)('UserSettings', userSettingsSchema);
//# sourceMappingURL=settings.model.js.map