import Image from "next/image";
import { Container } from "@mui/material";

import useInView from "../../features/images/hooks/view.hooks";
import {
  findIndustryClassname,
  findIndustryIcon,
  findIndustryLabel,
} from "@/utils/functions";
import CustomButton from "@/components/ui/CustomButton";

const SlideShowAiSourcing = ({ slides, t }) => {
  return (
    <>
      {slides.map((item, index) => {
        const { isInView, elementRef } = useInView();

        return (
          <div
            className={`embla__slide ${findIndustryClassname(item.industry)}`}
            key={index}
          >
            <Container
              ref={elementRef}
              className="slide__container custom-max-width"
              style={{
                backgroundImage: isInView
                  ? `linear-gradient(to bottom, rgba(35, 71, 145, 0), rgba(35, 71, 145, 0.6)), url(${item.img})`
                  : "linear-gradient(to bottom, rgba(35, 71, 145, 0), rgba(35, 71, 145, 0.6))",
                backgroundSize: "cover",
                backgroundPosition: "center",
                backgroundRepeat: "no-repeat",
                transition: "background-image 0.5s ease-in-out",
              }}
            >
              <img
                width={0}
                height={0}
                alt={item.altImg}
                src={item.img}
                style={{ display: "none" }}
                loading="lazy"
              />

              <div className="embla__slide__content">
                <p
                  className={`embla__slide__industry ${findIndustryClassname(
                    item.industry
                  )}`}
                >
                  {findIndustryIcon(item.industry)}{" "}
                  {findIndustryLabel(item.industry)}
                </p>

                <div className="top">
                  <p className="embla__slide__name">{item.name}</p>
                  <div className="bottom">
                    <CustomButton
                      text={t("homePage:s6:linkedin")}
                      className="btn btn-outlined text-white text-center no-hover"
                      link={item.link}
                      externalLink={true}
                    />
                  </div>
                </div>
              </div>
            </Container>
          </div>
        );
      })}
    </>
  );
};

export default SlideShowAiSourcing;
