"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/layout",{

/***/ "(app-pages-browser)/./src/components/ui/DropdownMenu.jsx":
/*!********************************************!*\
  !*** ./src/components/ui/DropdownMenu.jsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Menu/Menu.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Fade/Fade.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _assets_images_icons_arrowDown_svg__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../assets/images/icons/arrowDown.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowDown.svg\");\n/* harmony import */ var _assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../assets/images/icons/arrowUp.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowUp.svg\");\n/* harmony import */ var _utils_functions__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/functions */ \"(app-pages-browser)/./src/utils/functions.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst DropdownMenu = (param)=>{\n    let { buttonLabel, buttonHref, menuItems, subMenu, locale, withFlag, selectedFlag } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const open = Boolean(anchorEl);\n    const timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isDetailBlogPath = ()=>pathname.includes(\"/blog/\") && !pathname.includes(\"/blog/category/\") && pathname !== \"/blog/\" && pathname !== \"/fr/blog/\";\n    const handleClick = (event)=>{\n        setAnchorEl(event.currentTarget);\n    };\n    const handleClose = ()=>{\n        setAnchorEl(null);\n    };\n    const handleClick2 = (event, item)=>{\n        event.stopPropagation();\n        if (item.onClick) {\n            item.onClick();\n        } else if (item.subItems == undefined) handleClose();\n    };\n    const handleMouseEnter = (event)=>{\n        clearTimeout(timeoutRef.current);\n        setAnchorEl(event.currentTarget);\n    };\n    const handleMouseLeave = ()=>{\n        timeoutRef.current = setTimeout(()=>{\n            setAnchorEl(null);\n        }, 150);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: subMenu ? `sub-dropdown-menu` : `dropdown-menu`,\n        onMouseEnter: handleMouseEnter,\n        onMouseLeave: handleMouseLeave,\n        style: {\n            display: \"inline-block\",\n            position: \"relative\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: pathname.includes(buttonHref) && buttonHref !== \"\" ? \"navbar-link dropdown-toggle active\" : isDetailBlogPath() ? \"navbar-link dropdown-toggle whiteBg\" : \"navbar-link dropdown-toggle\",\n                id: \"fade-button\",\n                \"aria-controls\": open ? \"fade-menu\" : undefined,\n                \"aria-haspopup\": \"true\",\n                \"aria-expanded\": open ? \"true\" : undefined,\n                onClick: handleClick,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: pathname.includes(buttonHref) && buttonHref !== \"\" ? \"dropdown-toggle-link active\" : isDetailBlogPath() ? \"dropdown-toggle-link whiteBg\" : \"dropdown-toggle-link\",\n                        href: buttonLabel?.toLowerCase() === \"resources\" || buttonLabel?.toLowerCase() === \"ressources\" ? \"#\" : buttonHref ? (0,_utils_functions__WEBPACK_IMPORTED_MODULE_6__.generateLocalizedSlug)(locale, buttonHref).replace(\"/en/\", \"/\") : pathname.replace(\"/en/\", \"/\"),\n                        locale: locale === \"en\" ? \"en\" : \"fr\",\n                        onClick: (e)=>{\n                            if (buttonLabel?.toLowerCase() === \"resources\" || buttonLabel?.toLowerCase() === \"ressources\") {\n                                e.preventDefault();\n                                e.stopPropagation();\n                            }\n                        },\n                        sx: {\n                            ...buttonLabel?.toLowerCase() === \"resources\" || buttonLabel?.toLowerCase() === \"ressources\" ? {\n                                cursor: \"default\",\n                                textDecoration: \"none\",\n                                \"&:hover\": {\n                                    textDecoration: \"none\"\n                                }\n                            } : {}\n                        },\n                        children: withFlag ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            className: \"flag-lang\",\n                            src: selectedFlag.src,\n                            width: 26,\n                            height: 22,\n                            disabled: true,\n                            loading: \"lazy\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 119,\n                            columnNumber: 13\n                        }, undefined) : buttonLabel\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, undefined),\n                    open ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowDown_svg__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 131,\n                        columnNumber: 17\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 131,\n                        columnNumber: 36\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                id: \"fade-menu\",\n                MenuListProps: {\n                    \"aria-labelledby\": \"fade-button\",\n                    autoFocusItem: false,\n                    disableAutoFocusItem: true\n                },\n                anchorEl: anchorEl,\n                open: open,\n                onClose: handleClose,\n                TransitionComponent: _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                disableScrollLock: true,\n                anchorOrigin: {\n                    vertical: \"bottom\",\n                    horizontal: subMenu ? \"right\" : \"left\"\n                },\n                transformOrigin: {\n                    vertical: \"top\",\n                    horizontal: subMenu ? \"left\" : \"left\"\n                },\n                slotProps: {\n                    paper: {\n                        onMouseEnter: ()=>clearTimeout(timeoutRef.current),\n                        onMouseLeave: handleMouseLeave,\n                        sx: {\n                            pointerEvents: \"auto\"\n                        }\n                    }\n                },\n                children: menuItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: item.subItems ? `sub-menu dropdown-item` : `dropdown-item`,\n                        onClick: (e)=>handleClick2(e, item),\n                        onMouseEnter: ()=>clearTimeout(timeoutRef.current),\n                        onMouseLeave: item.subItems ? undefined : handleMouseLeave,\n                        sx: {\n                            \"&:hover\": {\n                                backgroundColor: item.subItems ? \"transparent !important\" : \"rgba(0, 0, 0, 0.04)\"\n                            },\n                            \"&.Mui-focusVisible\": {\n                                backgroundColor: \"transparent !important\"\n                            },\n                            \"&.Mui-selected\": {\n                                backgroundColor: \"transparent !important\"\n                            },\n                            \"&:focus\": {\n                                backgroundColor: \"transparent !important\"\n                            },\n                            \"&:first-of-type\": {\n                                backgroundColor: \"transparent !important\"\n                            }\n                        },\n                        children: item.subItems ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DropdownMenu, {\n                            buttonLabel: item?.i18nName ? t(item?.i18nName) : item.name,\n                            buttonHref: item.route,\n                            menuItems: item.subItems,\n                            subMenu: true,\n                            locale: locale\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 194,\n                            columnNumber: 15\n                        }, undefined) : item.route ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            href: (0,_utils_functions__WEBPACK_IMPORTED_MODULE_6__.generateLocalizedSlug)(locale, item.route),\n                            locale: locale === \"en\" ? \"en\" : \"fr\",\n                            className: pathname.includes(item.route) ? \"dropdown-item-link active\" : \"dropdown-item-link\",\n                            children: [\n                                item.icon ?? item.icon,\n                                \" \",\n                                item?.i18nName ? t(item?.i18nName) : item.name\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 202,\n                            columnNumber: 15\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"dropdown-item-link\",\n                            href: \"#\",\n                            children: withFlag ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                className: \"flag-lang\",\n                                src: item.flag.src,\n                                width: 26,\n                                height: 22,\n                                loading: \"lazy\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                                lineNumber: 217,\n                                columnNumber: 19\n                            }, undefined) : item?.i18nName ? t(item?.i18nName) : item.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 215,\n                            columnNumber: 15\n                        }, undefined)\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 165,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DropdownMenu, \"dkvKbdhBU6hKujyfH1UxLVv1rcc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation\n    ];\n});\n_c = DropdownMenu;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DropdownMenu);\nvar _c;\n$RefreshReg$(_c, \"DropdownMenu\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/DropdownMenu.jsx\n"));

/***/ })

});