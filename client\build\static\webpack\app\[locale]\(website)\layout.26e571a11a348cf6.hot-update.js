"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/layout",{

/***/ "(app-pages-browser)/./src/components/ui/DropdownMenu.jsx":
/*!********************************************!*\
  !*** ./src/components/ui/DropdownMenu.jsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Menu/Menu.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Fade/Fade.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _assets_images_icons_arrowDown_svg__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../assets/images/icons/arrowDown.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowDown.svg\");\n/* harmony import */ var _assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../assets/images/icons/arrowUp.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowUp.svg\");\n/* harmony import */ var _utils_functions__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/functions */ \"(app-pages-browser)/./src/utils/functions.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst DropdownMenu = (param)=>{\n    let { buttonLabel, buttonHref, menuItems, subMenu, locale, withFlag, selectedFlag } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const open = Boolean(anchorEl);\n    const timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isDetailBlogPath = ()=>pathname.includes(\"/blog/\") && !pathname.includes(\"/blog/category/\") && pathname !== \"/blog/\" && pathname !== \"/fr/blog/\";\n    const handleClick = (event)=>{\n        setAnchorEl(event.currentTarget);\n    };\n    const handleClose = ()=>{\n        setAnchorEl(null);\n    };\n    const handleClick2 = (event, item)=>{\n        event.stopPropagation();\n        if (item.onClick) {\n            item.onClick();\n        } else if (item.subItems == undefined) handleClose();\n    };\n    const handleMouseEnter = (event)=>{\n        clearTimeout(timeoutRef.current);\n        setAnchorEl(event.currentTarget);\n    };\n    const handleMouseLeave = ()=>{\n        timeoutRef.current = setTimeout(()=>{\n            setAnchorEl(null);\n        }, 150);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: subMenu ? `sub-dropdown-menu` : `dropdown-menu`,\n        onMouseEnter: handleMouseEnter,\n        onMouseLeave: handleMouseLeave,\n        style: {\n            display: \"inline-block\",\n            position: \"relative\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: pathname.includes(buttonHref) && buttonHref !== \"\" ? \"navbar-link dropdown-toggle active\" : isDetailBlogPath() ? \"navbar-link dropdown-toggle whiteBg\" : \"navbar-link dropdown-toggle\",\n                id: \"fade-button\",\n                \"aria-controls\": open ? \"fade-menu\" : undefined,\n                \"aria-haspopup\": \"true\",\n                \"aria-expanded\": open ? \"true\" : undefined,\n                onClick: handleClick,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: pathname.includes(buttonHref) && buttonHref !== \"\" ? \"dropdown-toggle-link active\" : isDetailBlogPath() ? \"dropdown-toggle-link whiteBg\" : \"dropdown-toggle-link\",\n                        href: buttonLabel?.toLowerCase() === \"resources\" || buttonLabel?.toLowerCase() === \"ressources\" ? \"#\" : buttonHref ? (0,_utils_functions__WEBPACK_IMPORTED_MODULE_6__.generateLocalizedSlug)(locale, buttonHref).replace(\"/en/\", \"/\") : pathname.replace(\"/en/\", \"/\"),\n                        locale: locale === \"en\" ? \"en\" : \"fr\",\n                        onClick: (e)=>{\n                            if (buttonLabel?.toLowerCase() === \"resources\" || buttonLabel?.toLowerCase() === \"ressources\") {\n                                e.preventDefault();\n                                e.stopPropagation();\n                            }\n                        },\n                        sx: {\n                            ...buttonLabel?.toLowerCase() === \"resources\" || buttonLabel?.toLowerCase() === \"ressources\" ? {\n                                cursor: \"default\",\n                                textDecoration: \"none\",\n                                \"&:hover\": {\n                                    textDecoration: \"none\"\n                                }\n                            } : {}\n                        },\n                        children: withFlag ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            className: \"flag-lang\",\n                            src: selectedFlag.src,\n                            width: 26,\n                            height: 22,\n                            disabled: true,\n                            loading: \"lazy\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 119,\n                            columnNumber: 13\n                        }, undefined) : buttonLabel\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, undefined),\n                    open ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowDown_svg__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 131,\n                        columnNumber: 17\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 131,\n                        columnNumber: 36\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                id: \"fade-menu\",\n                MenuListProps: {\n                    \"aria-labelledby\": \"fade-button\"\n                },\n                anchorEl: anchorEl,\n                open: open,\n                onClose: handleClose,\n                TransitionComponent: _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                disableScrollLock: true,\n                anchorOrigin: {\n                    vertical: \"bottom\",\n                    horizontal: subMenu ? \"right\" : \"left\"\n                },\n                transformOrigin: {\n                    vertical: \"top\",\n                    horizontal: subMenu ? \"left\" : \"left\"\n                },\n                slotProps: {\n                    paper: {\n                        onMouseEnter: ()=>clearTimeout(timeoutRef.current),\n                        onMouseLeave: handleMouseLeave,\n                        sx: {\n                            pointerEvents: \"auto\"\n                        }\n                    }\n                },\n                children: menuItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: item.subItems ? `sub-menu dropdown-item` : `dropdown-item`,\n                        onClick: (e)=>handleClick2(e, item),\n                        onMouseEnter: ()=>clearTimeout(timeoutRef.current),\n                        onMouseLeave: item.subItems ? undefined : handleMouseLeave,\n                        sx: {\n                            // Remove default hover background for items with submenus\n                            ...item.subItems && {\n                                \"&:hover\": {\n                                    backgroundColor: \"transparent !important\"\n                                },\n                                \"&.Mui-focusVisible\": {\n                                    backgroundColor: \"transparent !important\"\n                                },\n                                \"&.Mui-selected\": {\n                                    backgroundColor: \"transparent !important\"\n                                }\n                            }\n                        },\n                        children: item.subItems ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DropdownMenu, {\n                            buttonLabel: item?.i18nName ? t(item?.i18nName) : item.name,\n                            buttonHref: item.route,\n                            menuItems: item.subItems,\n                            subMenu: true,\n                            locale: locale\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 187,\n                            columnNumber: 15\n                        }, undefined) : item.route ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            href: (0,_utils_functions__WEBPACK_IMPORTED_MODULE_6__.generateLocalizedSlug)(locale, item.route),\n                            locale: locale === \"en\" ? \"en\" : \"fr\",\n                            className: pathname.includes(item.route) ? \"dropdown-item-link active\" : \"dropdown-item-link\",\n                            children: [\n                                item.icon ?? item.icon,\n                                \" \",\n                                item?.i18nName ? t(item?.i18nName) : item.name\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 195,\n                            columnNumber: 15\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"dropdown-item-link\",\n                            href: \"#\",\n                            children: withFlag ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                className: \"flag-lang\",\n                                src: item.flag.src,\n                                width: 26,\n                                height: 22,\n                                loading: \"lazy\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                                lineNumber: 210,\n                                columnNumber: 19\n                            }, undefined) : item?.i18nName ? t(item?.i18nName) : item.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 208,\n                            columnNumber: 15\n                        }, undefined)\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 163,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DropdownMenu, \"dkvKbdhBU6hKujyfH1UxLVv1rcc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation\n    ];\n});\n_c = DropdownMenu;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DropdownMenu);\nvar _c;\n$RefreshReg$(_c, \"DropdownMenu\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/DropdownMenu.jsx\n"));

/***/ })

});