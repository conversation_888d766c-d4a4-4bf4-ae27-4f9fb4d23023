import { Container, Grid } from "@mui/material";
import Image from "next/image";

import CustomButton from "./CustomButton";
import {
  findIndustryClassname,
  findIndustryIcon,
  findIndustryLabel,
} from "@/utils/functions";
function AiSourcingDetails({ reverse, darkBg, data }) {
  return (
    <div className={darkBg == true ? "dark-bg" : null}>
      <Container className={"ai-sourcing-details custom-max-width"}>
        <Grid
          container
          justifyContent="space-between"
          className={reverse == true ? "reverse" : null}
        >
          <Grid item xs={12} sm={12} md={8} p={0}>
            <div className="content">
              <div>
                {data.industry ? (
                  <p
                    className={`industry-label ${findIndustryClassname(
                      data.industry
                    )}`}
                  >
                    {findIndustryIcon(data.industry)}
                    {findIndustryLabel(data.industry)}
                  </p>
                ) : null}
                {data.name ? (
                  <h3 className="heading-h1 text-white">{data.name}</h3>
                ) : null}

                {data.subTitle ? (
                  <p className="sub-heading text-white">{data.subTitle}</p>
                ) : null}
              </div>

              {data.paragraph ? (
                <p className="paragraph text-white">{data.paragraph}</p>
              ) : null}

              {data.link && (
                <CustomButton
                  text={"let’s connect on LinkedIn"}
                  link={data.link}
                  className={"btn btn-outlined white"}
                  externalLink={true}
                />
              )}
            </div>
          </Grid>
          <Grid item xs={12} sm={12} md={4} p={0}>
            <div className="feauture-img">
              <div>
                <img
                  width={398}
                  height={512}
                  alt={data.altImg}
                  src={data.featureImg}
                  loading="lazy"
                />
              </div>
            </div>
          </Grid>
        </Grid>
      </Container>
    </div>
  );
}

export default AiSourcingDetails;
