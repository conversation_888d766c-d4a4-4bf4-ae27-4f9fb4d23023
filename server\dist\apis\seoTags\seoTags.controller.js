"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const authentication_middleware_1 = __importDefault(require("@/middlewares/authentication.middleware"));
const validation_middleware_1 = require("@/middlewares/validation.middleware");
const mongoId_validation_middleware_1 = __importDefault(require("@/middlewares/mongoId-validation.middleware"));
const queries_validation_middleware_1 = __importDefault(require("@/middlewares/queries-validation.middleware"));
const seoTags_validation_1 = require("./seoTags.validation");
const seoTags_service_1 = __importDefault(require("./seoTags.service"));
const validateApiKey_middleware_1 = __importDefault(require("@/middlewares/validateApiKey.middleware"));
const cache_middleware_1 = require("@/middlewares/cache.middleware");
const authorization_middleware_1 = require("@/middlewares/authorization.middleware");
const constants_1 = require("@/utils/helpers/constants");
class SeoTagsController {
    constructor() {
        this.path = '/seoTags';
        this.router = (0, express_1.Router)();
        this.seoTagsService = new seoTags_service_1.default();
        this.getSeoTags = async (request, response, next) => {
            try {
                const queries = request.query;
                const result = await this.seoTagsService.getAll(queries);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.getSeoTagBySlug = async (request, response, next) => {
            try {
                const slug = request.params.slug;
                const language = request.params.language;
                const result = await this.seoTagsService.getSeoTagBySlug(slug, language);
                response.send({ data: result, status: 200 });
            }
            catch (error) {
                next(error);
            }
        };
        this.getSeoTagById = async (request, response, next) => {
            try {
                const id = request.params.id;
                const language = request.params.language;
                const result = await this.seoTagsService.getSeoTagById(id);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.create = async (request, response, next) => {
            try {
                const data = request.body;
                const result = await this.seoTagsService.create(data);
                response.status(201).send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.update = async (request, response, next) => {
            try {
                const id = request.params.id;
                const data = request.body;
                const language = request.params.language;
                const result = await this.seoTagsService.update(id, data);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.initialiseRoutes();
    }
    initialiseRoutes() {
        this.router.get(`${this.path}`, validateApiKey_middleware_1.default, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), queries_validation_middleware_1.default, cache_middleware_1.validateCache, this.getSeoTags);
        this.router.get(`${this.path}/:id`, validateApiKey_middleware_1.default, queries_validation_middleware_1.default, cache_middleware_1.validateCache, this.getSeoTagById);
        this.router.get(`${this.path}/:language/:slug*`, validateApiKey_middleware_1.default, queries_validation_middleware_1.default, cache_middleware_1.validateCache, this.getSeoTagBySlug);
        this.router.post(`${this.path}`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), (0, validation_middleware_1.validationMiddleware)(seoTags_validation_1.createSeoTagsSchema), cache_middleware_1.invalidateCache, this.create);
        this.router.put(`${this.path}/:id`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), mongoId_validation_middleware_1.default, (0, validation_middleware_1.validationMiddleware)(seoTags_validation_1.createSeoTagsSchema), cache_middleware_1.invalidateCache, this.update);
    }
}
exports.default = SeoTagsController;
//# sourceMappingURL=seoTags.controller.js.map