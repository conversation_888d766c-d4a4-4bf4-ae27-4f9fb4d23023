"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const constants_1 = require("@/utils/helpers/constants");
const opportunityversionsShema = new mongoose_1.Schema({
    metaTitle: String,
    metaDescription: String,
    alt: String,
    language: {
        type: String,
        enum: constants_1.Language,
    },
    title: {
        type: String,
        required: true,
    },
    jobDescription: {
        type: String,
        required: true,
    },
    shareOnSocialMedia: { type: Boolean, default: false },
    createdBy: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
    },
    url: String,
    opportunityType: {
        type: String,
        enum: constants_1.OpportunityType,
    },
    isArchived: {
        type: Boolean,
        default: false,
    },
    visibility: {
        type: String,
        enum: constants_1.Visibility,
        default: constants_1.Visibility.Draft,
    },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now },
});
const opportunitySchema = new mongoose_1.Schema({
    versions: { type: Map, of: opportunityversionsShema, required: true },
    opportunityType: { type: String, enum: constants_1.OpportunityType },
    isPublished: {
        type: Boolean,
        default: false,
    },
    status: {
        type: String,
        enum: constants_1.JobStatus,
        default: constants_1.JobStatus.ACTIVE,
    },
    dateOfExpiration: Date,
    dateOfRequisition: Date,
    industry: {
        type: String,
        enum: constants_1.Industry,
    },
    urgent: {
        type: Boolean,
        default: false,
    },
    publishDate: { type: Date },
    country: {
        type: String,
    },
    reference: {
        type: String,
        required: true,
    },
    contractType: {
        type: String,
    },
    minExperience: { type: Number, required: true },
    maxExperience: { type: Number, required: true },
}, {
    timestamps: true,
    toJSON: {
        transform: function (doc, ret) {
            delete ret.__v;
        },
    },
    discriminatorKey: 'type',
});
opportunitySchema.index({
    'versions.fr.title': 'text',
    'versions.en.title': 'text',
    'versions.fr.jobDescription': 'text',
    'versions.en.jobDescription': 'text',
}, {
    weights: {
        'versions.fr.title': 25,
        'versions.en.title': 25,
        'versions.fr.jobDescription': 10,
        'versions.en.jobDescription': 10,
    },
    name: 'OpportunityTextIndex',
});
exports.default = (0, mongoose_1.model)('Opportunity', opportunitySchema);
//# sourceMappingURL=opportunity.model.js.map