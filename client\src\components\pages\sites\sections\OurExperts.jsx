"use client";
import { Container, useMediaQuery, useTheme } from "@mui/material";

import user4 from "../../assets/images/sourcing-team/user4.jpg";

import useEmblaCarousel from "embla-carousel-react";
import CustomButton from "@/components/ui/CustomButton";
import {
  findCountryFlag,
  findCountryLabel,
  findIndustryClassname,
} from "@/utils/functions";
import {
  NextButton,
  PrevButton,
  usePrevNextButtons,
} from "@/components/ui/emblaCarousel/EmblaCarouselArrowButtons";
import { TeamCountries } from "@/config/countries";
import Image from "next/image";
import SvgArrowRight from "../../assets/images/icons/arrowRight2.svg";
import SvgArrow from "../../assets/images/icons/arrow.svg";

import SvgArrowLeft from "../../assets/images/icons/arrowLeft2.svg";

function OurExperts() {
  const OPTIONS = { loop: false, align: "start" };
  const slides = [
    {
      name: "Ameni Mhadhbi",
      country: TeamCountries.TUNISIA,
      link: "/services",
      img: user4.src,
    },
    {
      name: "Ameni Mhadhbi 2",
      country: TeamCountries.TUNISIA,
      link: "/services",
      img: user4.src,
    },
    {
      name: "Ameni mhadhbi 3",
      country: TeamCountries.TUNISIA,
      link: "/services",
      img: user4.src,
    },
    {
      name: "Ameni Mhadhbi",
      country: TeamCountries.TUNISIA,
      link: "/services",
      img: user4.src,
    },
    {
      name: "Ameni Mhadhbi 2",
      country: TeamCountries.TUNISIA,
      link: "/services",
      img: user4.src,
    },
    {
      name: "Ameni mhadhbi 3",
      country: TeamCountries.TUNISIA,
      link: "/services",
      img: user4.src,
    },
    {
      name: "Ameni Mhadhbi",
      country: TeamCountries.TUNISIA,
      link: "/services",
      img: user4.src,
    },
    {
      name: "Ameni Mhadhbi 2",
      country: TeamCountries.TUNISIA,
      link: "/services",
      img: user4.src,
    },
    {
      name: "Ameni mhadhbi 3",
      country: TeamCountries.TUNISIA,
      link: "/services",
      img: user4.src,
    },
  ];

  const [emblaRef, emblaApi] = useEmblaCarousel(OPTIONS);

  const {
    prevBtnDisabled,
    nextBtnDisabled,
    onPrevButtonClick,
    onNextButtonClick,
  } = usePrevNextButtons(emblaApi);

  const theme = useTheme();

  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  return (
    <Container id="expert-sourcing"  className="custom-max-width">
      <section className="embla" id={"aisourcing__slider"}>
        <div className="top-section">
          <div className="heading">
            <p className="heading-h1 text-white">Discover our Ai Sourcing</p>
            <p className="sub-heading text-white">
              Roary member of on cam has ens peab the success of our dients,
              Leam more about our experts and their capabilities.
            </p>
          </div>
          {!isMobile && (
            <Container className="embla__controls mobile-btn custom-max-width">
              <div className="embla__buttons">
                <PrevButton
                  children={<SvgArrowLeft />}
                  onClick={onPrevButtonClick}
                  disabled={prevBtnDisabled}
                />
                <NextButton
                  children={<SvgArrowRight />}
                  onClick={onNextButtonClick}
                  disabled={nextBtnDisabled}
                />
              </div>
            </Container>
          )}
        </div>
        <div className="embla__viewport" ref={emblaRef}>
          <div className="embla__container">
            {slides.map((item, index) => (
              <div
                className={`embla__slide ${findIndustryClassname(
                  item.industry
                )}`}
                key={index}
              >
                <Container className="slide__container custom-max-width">
                  <div className="embla__slide__content">
                    <div>
                      <p className={`country-flag`}>
                        <img
                          width={22}
                          height={14}
                          src={findCountryFlag(item.country).src}
                          alt={findCountryLabel(item.country)}
                        />
                        {findCountryLabel(item.country)}
                      </p>
                      <img
                        width={228}
                        className="team-pic"
                        height={228}
                        src={item.img}
                        alt="sourcing pentabell"
                        loading="lazy"
                      />
                    </div>

                    <div className="top">
                      <p className="embla__slide__name">{item.name}</p>
                      <p className="embla__slide__jobTitle">{item.jobTitle}</p>
                      <div className="bottom">
                        <p className="embla__slide__description">
                          {item.description}
                        </p>
                        <CustomButton
                          text={"Contact"}
                          className={"btn yellow text-left"}
                          icon={<SvgArrow />}
                          link={item.link}
                        />
                      </div>
                    </div>
                  </div>
                </Container>
              </div>
            ))}
          </div>
        </div>
        {isMobile && (
          <Container className="embla__controls mobile-btn custom-max-width">
            <div className="embla__buttons">
              <PrevButton
                children={<SvgArrowLeft />}
                onClick={onPrevButtonClick}
                disabled={prevBtnDisabled}
              />
              <NextButton
                children={<SvgArrowRight />}
                onClick={onNextButtonClick}
                disabled={nextBtnDisabled}
              />
            </div>
          </Container>
        )}
      </section>
    </Container>
  );
}

export default OurExperts;
