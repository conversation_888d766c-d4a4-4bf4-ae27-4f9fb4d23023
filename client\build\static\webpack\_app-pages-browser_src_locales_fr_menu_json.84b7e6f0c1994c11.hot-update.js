"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_locales_fr_menu_json",{

/***/ "(app-pages-browser)/./src/locales/fr/menu.json":
/*!**********************************!*\
  !*** ./src/locales/fr/menu.json ***!
  \**********************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

module.exports = /*#__PURE__*/JSON.parse('{"aboutUs":"A propos","services":"Services RH","payrollServices":"Portage Salarial","consultingServices":"Consulting RH","technicalAssistance":"Assistance technique","itTelecom":"IT Télécom","aiSourcing":"Sourcing IA","directHiring":"Recrutement direct","directHiringService":"Service de recrutement direct","hiringProcessContact":"Contact pour le processus de recrutement","opportunities":"Opportunités","oilGas":"Pétrole et gaz","transportation":"Transport","pharmaceutical":"Pharmaceutique","energy":"Energie","insuranceBanking":"Banque et assurance","others":"Autres","blog":"Blog","joinUs":"Rejoignez-nous","contact":"Contact","profile":"Mon Profil","notification":"Notification","login":"Connexion","logout":"Déconnexion","pharma":"Pharmaceutique","notifications":"Notifications","myApplications":"Mes Candidatures","myResumes":"Mes Résumés","favoris":"Favoris","seoSettings":"Paramètres SEO","statistics":"Statistiques","comments":"Commentaires","users":"Utilisateurs","events":"Evénements","corporateProfile":"Corporate Profile","companyProfile":"Company Profile"}');

/***/ })

});