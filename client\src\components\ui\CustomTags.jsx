import React from "react";
import { FormLabel } from "@mui/material";
import ReactTags from "react-tag-input";
import { ErrorMessage } from "formik";

const CustomTags = ({
  label,
  name,
  tags = [],
  setTags,
  setFieldValue,
  errors,
  touched,
  delimiters = [188, 13],
  inputFieldPosition = "bottom", 
  ...props 
}) => {

  const handleDelete = (i) => {
    const updatedTags = tags.filter((tag, index) => index !== i);
    setTags(updatedTags);
    setFieldValue(name, updatedTags.map((tag) => tag.text));
  };

  const handleAddition = (tag) => {
    const updatedTags = [...tags, tag];
    setTags(updatedTags);
    setFieldValue(name, updatedTags.map((item) => item.text));
  };

  return (
    <FormLabel className="label-form">
      {label}
      <div id="tags">
        <ReactTags
          tags={tags}
          className={
            "input-pentabell" +
            (errors && touched ? " is-invalid" : "")
          }
          delimiters={delimiters}
          handleDelete={handleDelete}
          handleAddition={handleAddition}
          inputFieldPosition={inputFieldPosition}
          autocomplete
          allowDragDrop={false}
          {...props} 
        />
      </div>
      <ErrorMessage
        className="label-error"
        name={name}
        component="div"
      />
    </FormLabel>
  );
};

export default CustomTags;
