"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const authentication_middleware_1 = __importDefault(require("@/middlewares/authentication.middleware"));
const validation_middleware_1 = require("@/middlewares/validation.middleware");
const mongoId_validation_middleware_1 = __importDefault(require("@/middlewares/mongoId-validation.middleware"));
const queries_validation_middleware_1 = __importDefault(require("@/middlewares/queries-validation.middleware"));
const opportunity_validation_1 = require("../opportunity.validation");
const seoOpportunity_service_1 = __importDefault(require("../service/seoOpportunity.service"));
const validateApiKey_middleware_1 = __importDefault(require("@/middlewares/validateApiKey.middleware"));
const cache_middleware_1 = require("@/middlewares/cache.middleware");
class SeoOpportunityController {
    constructor() {
        this.path = '/seoOpportunity';
        this.router = (0, express_1.Router)();
        this.seoOpportunityService = new seoOpportunity_service_1.default();
        this.getSeoOpportunity = async (request, response, next) => {
            try {
                const result = await this.seoOpportunityService.get();
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.create = async (request, response, next) => {
            try {
                const data = request.body;
                const result = await this.seoOpportunityService.create(data);
                response.status(201).send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.update = async (request, response, next) => {
            try {
                const id = request.params.id;
                const data = request.body;
                const result = await this.seoOpportunityService.update(id, data);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.initialiseRoutes();
    }
    initialiseRoutes() {
        this.router.get(`${this.path}`, validateApiKey_middleware_1.default, queries_validation_middleware_1.default, cache_middleware_1.validateCache, this.getSeoOpportunity);
        this.router.post(`${this.path}`, authentication_middleware_1.default, (0, validation_middleware_1.validationMiddleware)(opportunity_validation_1.createSeoOpportunitySchema), cache_middleware_1.invalidateCache, this.create);
        this.router.put(`${this.path}/:id`, authentication_middleware_1.default, mongoId_validation_middleware_1.default, (0, validation_middleware_1.validationMiddleware)(opportunity_validation_1.createSeoOpportunitySchema), cache_middleware_1.invalidateCache, this.update);
    }
}
exports.default = SeoOpportunityController;
//# sourceMappingURL=seoOpportunity.controller.js.map