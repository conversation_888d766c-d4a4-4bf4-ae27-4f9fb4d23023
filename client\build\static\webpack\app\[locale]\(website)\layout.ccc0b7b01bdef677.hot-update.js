"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/layout",{

/***/ "(app-pages-browser)/./src/components/ui/DropdownMenu.jsx":
/*!********************************************!*\
  !*** ./src/components/ui/DropdownMenu.jsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Menu/Menu.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Fade/Fade.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _assets_images_icons_arrowDown_svg__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../assets/images/icons/arrowDown.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowDown.svg\");\n/* harmony import */ var _assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../assets/images/icons/arrowUp.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowUp.svg\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _utils_functions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/functions */ \"(app-pages-browser)/./src/utils/functions.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst DropdownMenu = (param)=>{\n    let { buttonLabel, buttonHref, menuItems, subMenu, locale, withFlag, selectedFlag } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_6__.useTranslation)();\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hoveredSubMenu, setHoveredSubMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const open = Boolean(anchorEl);\n    const isDetailBlogPath = ()=>pathname.includes(\"/blog/\") && !pathname.includes(\"/blog/category/\") && pathname !== \"/blog/\" && pathname !== \"/fr/blog/\";\n    const handleClick = (event)=>{\n        setAnchorEl(event.currentTarget);\n    };\n    const handleClose = ()=>{\n        setAnchorEl(null);\n    };\n    const handleClick2 = (event, item)=>{\n        event.stopPropagation(); // Prevent event bubbling\n        if (item.onClick) {\n            item.onClick(); // Call the onClick handler if provided\n        } else {\n            if (item.subItems == undefined) handleClose();\n        }\n    };\n    const handleMouseEnter = (item, index)=>{\n        if (item.subItems) {\n            setHoveredSubMenu(index);\n        }\n    };\n    const handleMouseLeave = ()=>{\n        setHoveredSubMenu(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: subMenu ? `sub-dropdown-menu` : `dropdown-menu`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: pathname.includes(buttonHref) && buttonHref !== \"\" ? \"navbar-link dropdown-toggle active\" : isDetailBlogPath() ? \"navbar-link dropdown-toggle whiteBg\" : \"navbar-link dropdown-toggle\",\n                id: \"fade-button\",\n                \"aria-controls\": open ? \"fade-menu\" : undefined,\n                \"aria-haspopup\": \"true\",\n                \"aria-expanded\": open ? \"true\" : undefined,\n                onClick: handleClick,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: pathname.includes(buttonHref) && buttonHref !== \"\" ? \"dropdown-toggle-link active\" : isDetailBlogPath() ? \"dropdown-toggle-link whiteBg\" : \"dropdown-toggle-link\",\n                        href: // Disable href when buttonLabel is \"resources\"\n                        buttonLabel?.toLowerCase() === \"resources\" || buttonLabel?.toLowerCase() === \"ressources\" ? \"#\" : buttonHref ? (0,_utils_functions__WEBPACK_IMPORTED_MODULE_5__.generateLocalizedSlug)(locale, buttonHref).replace(\"/en/\", \"/\") : pathname.replace(\"/en/\", \"/\"),\n                        locale: locale === \"en\" ? \"en\" : \"fr\",\n                        onClick: (e)=>{\n                            // Prevent navigation when buttonLabel is \"resources\"\n                            if (buttonLabel?.toLowerCase() === \"resources\" || buttonLabel?.toLowerCase() === \"ressources\") {\n                                e.preventDefault();\n                                e.stopPropagation();\n                            }\n                        },\n                        sx: {\n                            // Ensure resources text has the same styling as other menu items\n                            ...buttonLabel?.toLowerCase() === \"resources\" || buttonLabel?.toLowerCase() === \"ressources\" ? {\n                                cursor: \"default\",\n                                textDecoration: \"none\",\n                                \"&:hover\": {\n                                    textDecoration: \"none\"\n                                }\n                            } : {}\n                        },\n                        children: withFlag ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            className: \"flag-lang\",\n                            src: selectedFlag.src,\n                            width: 26,\n                            height: 22,\n                            disabled: true,\n                            loading: \"lazy\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 117,\n                            columnNumber: 13\n                        }, undefined) : buttonLabel\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, undefined),\n                    open ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowDown_svg__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 129,\n                        columnNumber: 17\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 129,\n                        columnNumber: 36\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                id: \"fade-menu\",\n                MenuListProps: {\n                    \"aria-labelledby\": \"fade-button\"\n                },\n                anchorEl: anchorEl,\n                open: open,\n                onClose: handleClose,\n                TransitionComponent: _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                disableScrollLock: true,\n                anchorOrigin: {\n                    vertical: \"bottom\",\n                    horizontal: subMenu ? \"right\" : \"left\"\n                },\n                children: menuItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: item.subItems ? `sub-menu dropdown-item` : `dropdown-item`,\n                        onClick: (e)=>handleClick2(e, item),\n                        onMouseEnter: ()=>handleMouseEnter(item, index),\n                        onMouseLeave: handleMouseLeave,\n                        children: item.subItems ? // If the item has a subMenu, render another DropdownMenu\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DropdownMenu, {\n                            buttonLabel: item?.i18nName ? t(item?.i18nName) : item.name,\n                            buttonHref: item.route,\n                            menuItems: item.subItems,\n                            subMenu: true,\n                            forceOpen: hoveredSubMenu === index\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 158,\n                            columnNumber: 15\n                        }, undefined) : item.route ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            href: (0,_utils_functions__WEBPACK_IMPORTED_MODULE_5__.generateLocalizedSlug)(locale, item.route),\n                            locale: locale === \"en\" ? \"en\" : \"fr\",\n                            className: pathname.includes(item.route) ? \"dropdown-item-link active\" : \"dropdown-item-link\",\n                            children: [\n                                item.icon ?? item.icon,\n                                \" \",\n                                item?.i18nName ? t(item?.i18nName) : item.name\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 167,\n                            columnNumber: 15\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"dropdown-item-link\",\n                            href: \"#\",\n                            children: withFlag ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                className: \"flag-lang\",\n                                src: item.flag.src,\n                                width: 26,\n                                height: 22,\n                                loading: \"lazy\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                                lineNumber: 182,\n                                columnNumber: 19\n                            }, undefined) : item?.i18nName ? t(item?.i18nName) : item.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 180,\n                            columnNumber: 15\n                        }, undefined)\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DropdownMenu, \"hhDT75lNM0Q7wfhfst8M1N+TPHY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname,\n        react_i18next__WEBPACK_IMPORTED_MODULE_6__.useTranslation\n    ];\n});\n_c = DropdownMenu;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DropdownMenu);\nvar _c;\n$RefreshReg$(_c, \"DropdownMenu\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/DropdownMenu.jsx\n"));

/***/ })

});