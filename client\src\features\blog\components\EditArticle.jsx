"use client";
import { useEffect, useRef, useState } from "react";
import { v4 as uuidv4 } from "uuid";
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  FormControl,
  Select,
  MenuItem,
  FormLabel,
  FormGroup,
  Grid,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { useTranslation } from "react-i18next";
import * as Yup from "yup";
import { toast } from "react-toastify";
import DialogModal from "../../user/component/updateProfile/experience/DialogModal";
import { Formik, Form, ErrorMessage } from "formik";
import { RobotsMeta } from "../../../utils/constants";
import Loading from "../../../components/loading/Loading";
import { API_URLS } from "../../../utils/urls";
import { useSaveFile } from "../../opportunity/hooks/opportunity.hooks";
import { axiosGetJson } from "@/config/axios";
import {
  usearchivedarticle,
  useGetArticleById,
  useGetArticleByIdAll,
  useUpdateArticle,
  useUpdateArticleAll,
} from "../hooks/blog.hook";
import AddArticle from "./AddArticle";
import Svgarchivepopup from "@/assets/images/icons/archiveicon-popup.svg";

import "react-datepicker/dist/react-datepicker.css";
import useCurrentUser from "@/features/auth/hooks/currentUser.hooks";
import CustomButton from "@/components/ui/CustomButton";

const EditArticle = ({ articleId }) => {
  const { data } = useGetArticleByIdAll(articleId);
  const usedisarchivedArticleHook = usearchivedarticle();
  const {
    data: dataEN,
    isLoading: isLoadingEN,
    isError,
    error,
  } = useGetArticleById(articleId, "en");

  const { data: dataFR, isLoading: isLoadingFR } = useGetArticleById(
    articleId,
    "fr"
  );
  const [isArchived, setIsArchived] = useState(false);
  const [isArchivedFr, setIsArchivedFr] = useState(false);
  const { user } = useCurrentUser();
  const [englishVersion, setEnglishVersion] = useState({});
  const [frenchVersion, setFrenchVersion] = useState({});

  const useUpdateArticleHook = useUpdateArticle();
  const useSaveFileHook = useSaveFile();
  const { t } = useTranslation();
  const currentYear = new Date().getFullYear();
  const formikRefEn = useRef(null);
  const formikRefFr = useRef(null);
  const formikRefAll = useRef(null);
  const [expanded, setExpanded] = useState(`panel`);
  const [formdataEN, setFormDataEN] = useState();
  const [formdataFR, setFormDataFR] = useState();
  const formdata = new FormData();
  const formdatafr = new FormData();
  const [uuidPhotoFileNameEN, setUuidPhotoFileNameEN] = useState("");
  const [selectedAction, setSelectedAction] = useState(null);
  const [selectedEnCategories, setSelectedEnCategories] = useState(
    dataEN?.versions?.categories.map((category) => category.id) || []
  );
  const [filteredFrCategories, setFilteredFrCategories] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [openDialogfr, setOpenDialogfr] = useState(false);
  const handleOpenDialog = (action) => {
    setSelectedAction(action);
    setOpenDialog(true);
  };

  const handleOpenDialogfr = (action) => {
    setSelectedAction(action);
    setOpenDialogfr(true);
  };
  const handleCloseDialogfr = () => {
    setOpenDialogfr(false);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };
  const useUpdateArticleAllHook = useUpdateArticleAll();
  const [uuidPhotoFileNameFR, setUuidPhotoFileNameFR] = useState("");
  const [uuidPhotoEN, setUuidPhotoEN] = useState(null);
  const [uuidPhotoFR, setUuidPhotoFR] = useState(null);
  useEffect(() => {
    setEnglishVersion(dataEN ? dataEN?.versions : "");
    setFrenchVersion(dataFR ? dataFR?.versions : "");
    if (
      dataEN?.versions?.categories.length > 0 &&
      dataEN?.versions?.categories[0] != {}
    ) {
      setSelectedEnCategories(
        dataEN?.versions?.categories.map((category) => category.id)
      );
    }
    setIsArchived(dataEN?.versions?.isArchived || false);
    setIsArchivedFr(dataFR?.versions?.isArchived || false);
  }, [dataEN, dataFR]);

  const handleConfirmAction = () => {
    if (selectedAction === "archive") {
      handledisarchivearticleen(true);
    } else if (selectedAction === "desarchive") {
      handledisarchivearticleen(false);
    }
    setOpenDialog(false);
  };

  const handleConfirmActionFr = () => {
    if (selectedAction === "archive") {
      handlearchiveanddisarchivearticleFr(true);
    } else if (selectedAction === "desarchive") {
      handlearchiveanddisarchivearticleFr(false);
    }
    setOpenDialogfr(false);
  };

  const handleImageSelect = async (selectedFile, language) => {
    if (language === "en") {
      let uuidPhotos = uuidv4().replace(/-/g, "");
      setUuidPhotoEN(uuidPhotos);
      formdata.append("file", selectedFile);
      setFormDataEN(formdata);
      const extension = selectedFile.name.split(".").pop();
      setUuidPhotoFileNameEN(`${uuidPhotos}.${extension}`);
    } else if (language === "fr") {
      let uuidPhotos = uuidv4().replace(/-/g, "");
      setUuidPhotoFR(uuidPhotos);
      formdatafr.append("file", selectedFile);
      setFormDataFR(formdatafr);
      const extension = selectedFile.name.split(".").pop();
      setUuidPhotoFileNameFR(`${uuidPhotos}.${extension}`);
    }
  };

  useEffect(() => {
    setEnglishVersion(dataEN ? dataEN?.versions : "");
    setFrenchVersion(dataFR ? dataFR?.versions : "");
    setSelectedEnCategories(
      dataEN?.versions?.categories.map((category) => category.id)
    );
  }, [dataEN, dataFR]);

  const initialValuesEn = {
    metaTitle: englishVersion?.metaTitle,
    metaDescription: englishVersion?.metaDescription,
    description: englishVersion?.description,
    visibility: englishVersion ? englishVersion.visibility : "",
    category:
      englishVersion?.categories?.length > 0
        ? englishVersion.categories?.map((category) => category.id)
        : [{ name: "" }],
    image: englishVersion?.image,
    keywords: englishVersion?.keywords?.map((keyword, index) => ({
      id: keyword.trim() || `id-${index}`,
      text: keyword.trim(),
    })),
    title: englishVersion?.title,
    url: englishVersion?.url,
    alt: englishVersion?.alt,
    content: englishVersion?.content,
    language: "en",
    publishDate: englishVersion?.publishDate,
    createdBy: user?.firstName + " " + user?.lastName,
    highlights: englishVersion?.highlights?.map((keyword, index) => ({
      id: keyword.trim() || `id-${index}`,
      text: keyword.trim(),
    })),
    faqTitle: englishVersion?.faqTitle || "",
    faq: englishVersion?.faq || [],
  };

  const initialValuesAll = {
    robotsMeta: data?.robotsMeta,
  };

  const initialValuesFr = {
    metaTitle: frenchVersion ? frenchVersion?.metaTitle : "",
    metaDescription: frenchVersion ? frenchVersion?.metaDescription : "",
    description: frenchVersion ? frenchVersion?.description : "",
    visibility: frenchVersion ? frenchVersion?.visibility : "",
    category:
      frenchVersion?.categories?.length > 0
        ? frenchVersion.categories?.map((category) => category.id)
        : [{ name: "" }],
    image: frenchVersion ? frenchVersion?.image : "",
    keywords: frenchVersion?.keywords?.map((keyword, index) => ({
      id: keyword?.trim() || `id-${index}`,
      text: keyword?.trim(),
    })),
    title: frenchVersion ? frenchVersion?.title : "",
    url: frenchVersion ? frenchVersion?.url : "",
    alt: frenchVersion ? frenchVersion?.alt : "",
    content: frenchVersion ? frenchVersion?.content : "",
    language: "fr",
    publishDate: frenchVersion ? frenchVersion?.publishDate : "",
    createdBy: user?.firstName + " " + user?.lastName,
    highlights: frenchVersion?.highlights?.map((keyword, index) => ({
      id: keyword.trim() || `id-${index}`,
      text: keyword.trim(),
    })),
    faqTitle: frenchVersion?.faqTitle || "",
    faq: frenchVersion?.faq || [],
  };

  const validationSchemaEN = Yup.object().shape({
    metaTitle: Yup.string().required(t("validations:emptyField")),
    metaDescription: Yup.string().required(t("validations:emptyField")),
    title: Yup.string().required(t("validations:emptyField")),
    image: Yup.string().required(t("validations:emptyField")),
    visibility: Yup.string().required(t("validations:emptyField")),
  });

  const handleSaveSetting = (values) => {
    const data = {
      robotsMeta: values?.robotsMeta,
    };
    useUpdateArticleAllHook.mutate(
      {
        data: data,
        id: articleId,
      },
      {
        onSuccess: () => {
          // setTimeout(
          //
          //   (window.location.href = `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.opportunities.route}/`),
          //   "3000"
          // );
        },
      }
    );
  };

  const handledisarchivearticleen = async () => {
    const action = isArchived ? false : true;
    try {
      await usedisarchivedArticleHook.mutateAsync({
        language: "en",
        id: articleId,
        archive: action,
      });

      setIsArchived(action);
    } catch (error) {
      toast.error(
        t(action ? "article:archivedError" : "article:desarchivedError")
      );
      console.error(
        "Erreur lors de l'archivage/désarchivage de l'article:",
        error
      );
    }
  };

  const handlearchiveanddisarchivearticleFr = async () => {
    try {
      await usedisarchivedArticleHook.mutateAsync({
        language: "fr",
        id: articleId,
        archive: action,
      });
    } catch (error) {
      toast.error(
        t(action ? "article:archivedError" : "article:desarchivedError")
      );
    }
  };

  const handleSaveEN = async () => {
    const enValues = formikRefEn.current.values;
    const hasId = enValues.keywords.every((item) => item.hasOwnProperty("id"));

    const highlightshasId = enValues?.highlights?.every((item) =>
      item.hasOwnProperty("id")
    );
    if (hasId) {
      enValues.keywords = enValues.keywords
        .filter((item) => item.hasOwnProperty("id") && item.text !== "")
        .map((item) => item.text);
    }
    if (highlightshasId) {
      enValues.highlights = enValues.keywords
        .filter((item) => item.hasOwnProperty("id") && item.text !== "")
        .map((item) => item.text);
    }
    if (
      enValues.category.length > 0 &&
      typeof enValues.category[0] === "object"
    ) {
      let categoryEn = [];
      enValues.category.map((category) => categoryEn.push(category.id));

      enValues.category = categoryEn;
    }

    formikRefEn.current.submitForm();
    const isEnFormValid =
      formikRefEn.current.isValid &&
      Object.keys(formikRefEn.current.errors).length === 0;

    if (isEnFormValid) {
      if (uuidPhotoFileNameEN) {
        enValues.image = uuidPhotoFileNameEN;
        useSaveFileHook.mutate(
          {
            resource: "blogs",
            folder: currentYear,
            filename: uuidPhotoEN,
            body: { formData: formdataEN, t },
          },
          {
            onSuccess: (data) => {
              enValues.image = data.uuid;
              useUpdateArticleHook.mutate({
                data: enValues,
                language: "en",
                id: articleId,
              });
            },
          }
        );
      } else {
        useUpdateArticleHook.mutate({
          data: enValues,
          language: "en",
          id: articleId,
        });
      }
    }
  };

  const handleSaveFR = async () => {
    const frValues = formikRefFr.current.values;
    const hasId = frValues.keywords?.every((item) => item.hasOwnProperty("id"));
    const highlightshasId = frValues?.highlights?.every((item) =>
      item.hasOwnProperty("id")
    );
    if (hasId) {
      frValues.keywords = frValues.keywords
        .filter((item) => item.hasOwnProperty("id") && item.text !== "")
        .map((item) => item.text);
    }
    if (highlightshasId) {
      frValues.highlights = frValues.keywords
        .filter((item) => item?.hasOwnProperty("id") && item.text !== "")
        .map((item) => item.text);
    }
    if (
      frValues.category.length > 0 &&
      typeof frValues.category[0] === "object"
    ) {
      let categoryFr = [];
      frValues.category.map((category) => categoryFr.push(category.id));
      frValues.category = categoryFr;
    }
    formikRefFr.current.submitForm();

    const isFrFormValid =
      formikRefFr.current.isValid &&
      Object.keys(formikRefFr.current.errors).length === 0;

    if (isFrFormValid) {
      if (uuidPhotoFileNameFR) {
        frValues.image = uuidPhotoFileNameFR;
        useSaveFileHook.mutate(
          {
            resource: "categories",
            folder: currentYear,
            filename: uuidPhotoFR,
            body: { formData: formdataFR, t },
          },
          {
            onSuccess: (data) => {
              if (data.message === "uuid exist") {
                frValues.image = data.uuid;
              }
              useUpdateArticleHook.mutate({
                data: frValues,
                language: "fr",
                id: articleId,
              });
            },
          }
        );
      } else {
        useUpdateArticleHook.mutate({
          data: frValues,
          language: "fr",
          id: articleId,
        });
      }
    }
  };

  useEffect(() => {
    const fetchFrenchCategories = async () => {
      try {
        const response = await axiosGetJson.get(
          `${API_URLS.categories}/en/${selectedEnCategories}`
        );

        const transformedCategories = response.data.map((category) => ({
          id: category._id,
          name: category.name,
        }));

        setFilteredFrCategories(transformedCategories);
      } catch (error) {}
    };
    if (selectedEnCategories?.length > 0) {
      fetchFrenchCategories();
    } else {
      setFilteredFrCategories([]);
    }
  }, [selectedEnCategories]);

  const handleChangeAccordion = (panel) => (event, newExpanded) => {
    setExpanded(newExpanded ? panel : false);
  };

  if (isLoadingFR || isLoadingEN) {
    return (
      <div className="content">
        <Loading />
      </div>
    );
  }

  return (
    <>
      <p className="heading-h2 semi-bold">{t("createArticle:editArticle")}</p>
      <div id="container" className="recent-application-pentabell">
        <div className="main-content">
          <div className="commun">
            <div id="experiences">
              <Formik initialValues={initialValuesAll} innerRef={formikRefAll}>
                {({ errors, touched, setFieldValue, values }) => (
                  <Form>
                    <Accordion
                      key={`panel}`}
                      id="accordion"
                      expanded={expanded === `panel`}
                      onChange={handleChangeAccordion(`panel`)}
                    >
                      <AccordionSummary
                        expandIcon={<ExpandMoreIcon />}
                        aria-controls={`panel-content`}
                        id={`panel-header`}
                      >
                        {t("createArticle:settings")}
                      </AccordionSummary>
                      <AccordionDetails className="accordion-detail">
                        <Grid
                          container
                          spacing={4}
                          sx={{ alignItems: "flex-end" }}
                        >
                          <Grid item xs={12} md={10}>
                            <FormGroup>
                              <FormLabel className="label-form">
                                {t("createArticle:Robotsmeta")}
                                <FormControl
                                  className="select-pentabell"
                                  variant="standard"
                                  sx={{ m: 1, minWidth: 120 }}
                                >
                                  <Select
                                    value={values.robotsMeta}
                                    onChange={(event) => {
                                      setFieldValue(
                                        "robotsMeta",
                                        event.target.value
                                      );
                                    }}
                                  >
                                    {RobotsMeta.map((item, index) => (
                                      <MenuItem key={index} value={item}>
                                        {item}
                                      </MenuItem>
                                    ))}
                                  </Select>
                                </FormControl>
                                <ErrorMessage
                                  className="label-error"
                                  name="robotsMeta"
                                  component="div"
                                />
                              </FormLabel>
                            </FormGroup>
                          </Grid>{" "}
                          <Grid item xs={12} md={2}>
                            <CustomButton
                              text={"Save"}
                              className={"btn btn-filled"}
                              onClick={() => handleSaveSetting(values)}
                            />
                          </Grid>
                        </Grid>
                      </AccordionDetails>
                    </Accordion>{" "}
                  </Form>
                )}
              </Formik>
              <AddArticle
                image={englishVersion?.image}
                language="en"
                initialValues={initialValuesEn}
                formRef={formikRefEn}
                onImageSelect={handleImageSelect}
                validationSchema={validationSchemaEN}
                isEdit={true}
                onCategoriesSelect={(categories) => {
                  setSelectedEnCategories(categories);
                }}
              />
              <div className="btn-container">
                &nbsp;
                <CustomButton
                  text={t("global:save")}
                  className={"btn btn-filled"}
                  onClick={handleSaveEN}
                />
                <CustomButton
                  text={
                    isArchived ? t("global:unarchive") : t("global:archive")
                  }
                  className={"btn btn-filled"}
                  onClick={() =>
                    handleOpenDialog(isArchived ? "desarchive" : "archive")
                  }
                />
              </div>
              <DialogModal
                open={openDialog}
                message={
                  selectedAction === "archive"
                    ? t("messages:archiveConfirmation")
                    : t("messages:desarchiveConfirmation")
                }
                icon={<Svgarchivepopup />}
                onClose={handleCloseDialog}
                onConfirm={handleConfirmAction}
              />
              <br />
              <Accordion
                key={`panel-1`}
                id="accordion"
                expanded={expanded === `panel-1`}
                onChange={handleChangeAccordion(`panel-1`)}
                isEdit={true}
              >
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon />}
                  aria-controls={`panel-content`}
                  id={`panel-header`}
                >
                  {t("createArticle:editArticleFr")}
                </AccordionSummary>
                <AccordionDetails className="accordion-detail">
                  <AddArticle
                    image={frenchVersion?.image}
                    language="fr"
                    initialValues={initialValuesFr}
                    formRef={formikRefFr}
                    onImageSelect={handleImageSelect}
                    validationSchema={validationSchemaEN}
                    isEdit={true}
                    filteredCategories={filteredFrCategories}
                  />
                  <div className="btn-container">
                    &nbsp;
                    <CustomButton
                      text={t("global:save")}
                      className={"btn btn-filled"}
                      onClick={handleSaveFR}
                    />
                    <CustomButton
                      text={
                        isArchivedFr
                          ? t("global:unarchive")
                          : t("global:archive")
                      }
                      className={"btn btn-filled"}
                      onClick={() =>
                        handleOpenDialogfr(
                          isArchivedFr ? "desarchive" : "archive"
                        )
                      }
                    />
                  </div>

                  <DialogModal
                    open={openDialogfr}
                    message={
                      selectedAction === "archive"
                        ? t("messages:archiveConfirmationfr")
                        : t("messages:desarchiveConfirmationfr")
                    }
                    icon={<Svgarchivepopup />}
                    onClose={handleCloseDialogfr}
                    onConfirm={handleConfirmActionFr}
                  />
                </AccordionDetails>
              </Accordion>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default EditArticle;
