"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const recruiter_shortlist_service_1 = __importDefault(require("../services/recruiter.shortlist.service"));
const authentication_middleware_1 = __importDefault(require("@/middlewares/authentication.middleware"));
const mongoId_validation_middleware_1 = __importDefault(require("@/middlewares/mongoId-validation.middleware"));
const authorization_middleware_1 = require("@/middlewares/authorization.middleware");
const constants_1 = require("@/utils/helpers/constants");
const messages_1 = require("@/utils/helpers/messages");
const validateApiKey_middleware_1 = __importDefault(require("@/middlewares/validateApiKey.middleware"));
class RecruiterShortlistController {
    constructor() {
        this.path = '/users/favourite';
        this.router = (0, express_1.Router)();
        this.shortlistService = new recruiter_shortlist_service_1.default();
        this.getAll = async (request, response, next) => {
            try {
                const queries = request.query;
                const id = request.user._id;
                const result = await this.shortlistService.getAll(queries, id);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.delete = async (request, response, next) => {
            try {
                const candidateId = request.params.id;
                const recruiterId = request.user._id;
                await this.shortlistService.deleteFromShortlist(recruiterId, candidateId);
                response.send({
                    message: messages_1.MESSAGES.RECRUITER_SHORTLIST.CANDIDATE_DELETED,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.addCandidateToShortlist = async (request, response, next) => {
            try {
                const candidateId = request.params.id;
                const id = request.user._id;
                const result = await this.shortlistService.addCandidateToShortlist(id, candidateId);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.initialiseRoutes();
    }
    initialiseRoutes() {
        this.router.get(`${this.path}`, validateApiKey_middleware_1.default, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.RECRUITER, constants_1.Role.ADMIN]), this.getAll);
        this.router.delete(`${this.path}/:id`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.RECRUITER]), mongoId_validation_middleware_1.default, this.delete);
        this.router.put(`${this.path}/:id`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.RECRUITER]), mongoId_validation_middleware_1.default, this.addCandidateToShortlist);
    }
}
exports.default = RecruiterShortlistController;
//# sourceMappingURL=recruiter.shortlist.controller.js.map