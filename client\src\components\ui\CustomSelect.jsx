import {
  Select,
  MenuItem,
  FormLabel,
  Checkbox,
  ListItemText,
} from "@mui/material";

export default function CustomSelect({
  label,
  name,
  value,
  onChange,
  selected,
  options,
  error,
  multiple = false,
  getOptionLabel = (item) => item,
  getOptionValue = (item) => item,
}) {
  return (
    <div className="form-group">
      {label && <FormLabel className="label-form">{label}</FormLabel>}
      <Select
        variant="standard"
        name={name}
        selected={selected}
        multiple={multiple}
        value={value}
        onChange={onChange}
        className={`select-pentabell ${error ? "is-invalid" : ""}`}
        renderValue={(selected) =>
          multiple
            ? selected
              .map((val) => {
                const match = options.find((opt) => getOptionValue(opt) === val);
                return match ? getOptionLabel(match) : val;
              })
              .join(", ")
            : getOptionLabel(selected)
        }
      >
        {options.map((item) => {
          const itemValue = getOptionValue(item);
          return (
            <MenuItem key={itemValue} value={itemValue}>
              {multiple && (
                <Checkbox checked={value.indexOf(itemValue) > -1} />
              )}
              <ListItemText primary={getOptionLabel(item)} />
            </MenuItem>
          );
        })}
      </Select>
      {error && <div className="label-error">{error}</div>}
    </div>
  );
}
