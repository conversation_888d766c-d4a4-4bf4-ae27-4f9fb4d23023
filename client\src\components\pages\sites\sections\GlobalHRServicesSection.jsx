"use client";
import {
  Container,
  Grid,
  AccordionSummary,
  AccordionDetails,
  Accordion,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import SvgArrow from "@/assets/images/icons/arrow.svg";
import { useState } from "react";
import CustomButton from "@/components/ui/CustomButton";

function GlobalHRServicesSection({ SERVICES, subTitle, title, defaultImage }) {
  const [expanded, setExpanded] = useState("s1");

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const handleChange = (panel) => (event, isExpanded) => {
    setExpanded(isExpanded ? panel : false);
  };
  const getServiceImageById = (id) => {
    const service = SERVICES.find((service) => service.id === id);

    return service
      ? { img: service.img.src, alt: service.altImg }
      : { img: defaultImage, alt: "Pentabell service" };
  };

  return (
    <div id="global-hr-services">
      <Container className="custom-max-width">
        <Grid className="container" container spacing={2}>
          <Grid item xs={12} sm={12}>
            {subTitle && (
              <p className="sub-heading text-yellow text-center">{subTitle}</p>
            )}

            <h2 className="heading-h1 text-white text-center">{title}</h2>
          </Grid>

          <Grid item xs={12} sm={6}>
            {SERVICES.map((item, index) => {
              return (
                <Accordion
                  key={index}
                  elevation={0}
                  expanded={expanded === item.id}
                  className="services-accordion"
                  disableGutters={true}
                  onChange={handleChange(item.id)}
                >
                  <AccordionSummary
                    aria-controls="panel1bh-content"
                    id="panel1bh-header"
                    className="services-accordion-header"
                  >
                    <p className="service-title">{item.title}</p>
                  </AccordionSummary>
                  <AccordionDetails elevation={0}>
                    <p className="service-description paragraph">
                      {item.description}
                    </p>
                    <CustomButton
                      text={item.linkText}
                      className={"btn btn-ghost text-yellow"}
                      icon={<SvgArrow />}
                      link={item.link}
                    />
                    {isMobile && (
                      <div className="service-image">
                        <div
                          style={{ backgroundImage: `url(${item.img.src})` }}
                        />
                        <img
                          width={0}
                          height={0}
                          alt={item.altImg}
                          style={{ display: "none" }}
                          loading="lazy"
                        />
                      </div>
                    )}
                  </AccordionDetails>
                </Accordion>
              );
            })}
          </Grid>
          <Grid
            item
            xs={false}
            sx={{ display: { xs: "none", sm: "block" } }}
            sm={6}
          >
            <div className="service-image">
              <div
                style={{
                  backgroundImage: `url(${getServiceImageById(expanded).img})`,
                }}
              />
              <img
                width={0}
                height={0}
                alt={getServiceImageById(expanded).alt}
                style={{ display: "none" }}
                loading="lazy"
              />
            </div>
          </Grid>
        </Grid>
      </Container>
    </div>
  );
}

export default GlobalHRServicesSection;
