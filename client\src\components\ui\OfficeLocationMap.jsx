import { Container, Grid, <PERSON> } from "@mui/material";
import SvglocationPin from "@/assets/images/icons/locationPin.svg";
import SvgcallUs from "@/assets/images/icons/callUs.svg";
import Svgemail from "@/assets/images/icons/email.svg";

function OfficeLocationMap({
    title,
    subtitle,
    address,
    tel,
    email,
    linkText,
    mapSrc,
    linkHref = "#service-page-form",
}) {
    return (
        <Container id="office-location-map" className="custom-max-width">
            <Grid className="container" justifyContent="space-between" container spacing={0}>
                <Grid item xs={12} sm={6}>
                    <div className="content">
                        <p className="heading-h2 text-white">{title}</p>
                        <p className="sub-heading text-white">{subtitle}</p>
                        <div>
                            <p className="paragraph text-white">
                                <span><SvglocationPin /></span> {address}
                            </p>
                            <p className="paragraph text-white">
                                <span><SvgcallUs /></span> {tel}
                            </p>
                            <p className="paragraph text-white">
                                <span><Svgemail /></span> {email}
                            </p>
                        </div>
                        <Link href={linkHref} className="btn btn-outlined white">
                            {linkText}
                        </Link>
                    </div>
                </Grid>
                <Grid item xs={12} sm={6}>
                    <div className="map-frame">
                        <iframe
                            src={mapSrc}
                            allowfullscreen=""
                            priority
                            loading="lazy"
                            referrerPolicy="no-referrer-when-downgrade"
                        ></iframe>
                    </div>
                </Grid>
            </Grid>
        </Container>
    );
}

export default OfficeLocationMap;