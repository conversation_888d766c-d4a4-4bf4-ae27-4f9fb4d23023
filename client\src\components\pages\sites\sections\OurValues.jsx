"use client";
import { Container } from "@mui/material";
import { useTranslation } from "react-i18next";

function OurValues() {
  const { t } = useTranslation();

  return (
    <Container id="our-values"  className="custom-max-width">
      <h2 className="heading-h1">{t("aboutUs:values:title")}</h2>
      <div className="values">
        <div className="value-item banking-insurance">
          <h3 className="sub-heading text-white">
            {t("aboutUs:values:integrity:title")}
          </h3>
          <p className="paragraph text-white">
            {t("aboutUs:values:integrity:description")}
          </p>
        </div>
        <div className="value-item it-telecom">
          <h3 className="sub-heading text-white">
            {t("aboutUs:values:collaboration:title")}
          </h3>
          <p className="paragraph text-white">
            {t("aboutUs:values:collaboration:description")}
          </p>
        </div>
        <div className="value-item oil-gaz">
          <h3 className="sub-heading text-white">
            {t("aboutUs:values:innovation:title")}
          </h3>
          <p className="paragraph text-white">
            {t("aboutUs:values:innovation:description")}
          </p>
        </div>
        <div className="value-item energy">
          <h3 className="sub-heading text-white">
            {t("aboutUs:values:diversity:title")}
          </h3>
          <p className="paragraph text-white">
            {t("aboutUs:values:diversity:description")}
          </p>
        </div>
        <div className="value-item transport">
          <h3 className="sub-heading text-white">
            {t("aboutUs:values:valueCreation:title")}
          </h3>
          <p className="paragraph text-white">
            {t("aboutUs:values:valueCreation:description")}
          </p>
        </div>
      </div>
    </Container>
  );
}

export default OurValues;
