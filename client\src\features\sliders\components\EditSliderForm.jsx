"use client";

import { useState, useRef } from "react";
import { Formik, ErrorMessage, Form } from "formik";
import { useTranslation } from "react-i18next";

import upload from "@/assets/images/add.png";
import { API_URLS } from "../../../utils/urls";

import { Visibility } from "../../../utils/constants";
import "react-datepicker/dist/react-datepicker.css";
import { FormGroup, FormLabel, MenuItem, Select } from "@mui/material";
import CustomTextInput from "@/components/ui/CustomTextInput";

const EditSliderForm = ({
  language,
  initialValues,
  formRef,
  onImageSelect,
  validationSchema,
  img,
  imgMobile,
}) => {
  const [selectedMobileImage, setSelectedMobileImage] = useState(null);
  const { t } = useTranslation();
  const imageInputRef = useRef(null);
  const mobileImageInputRef = useRef(null);
  const [selectedImage, setSelectedImage] = useState(null);

  const handlePhotoChange = async (imageType) => {
    let selectedFile;
    if (imageType === "mobile") {
      selectedFile = mobileImageInputRef.current.files[0];
      setSelectedMobileImage(selectedFile);
    } else {
      selectedFile = imageInputRef.current.files[0];
      setSelectedImage(selectedFile);
    }

    if (selectedFile) {
      onImageSelect(selectedFile, language, imageType);
    }
  };

  return (
    <div className="commun">
      <div id="experiences">
        <div id="form">
          <Formik
            initialValues={initialValues}
            validationSchema={validationSchema}
            onSubmit={() => {}}
            innerRef={formRef}
            enableReinitialize="true"
          >
            {({ errors, touched, setFieldValue, values, validateForm }) => (
              <Form>
                <div className="inline-group">
                  <div>
                    {" "}
                    <FormGroup>
                      <CustomTextInput
                        label={t("createArticle:title")}
                        name="title"
                        value={values.title}
                        onChange={(e) => {
                          const title = e.target.value;
                          setFieldValue("title", title);
                        }}
                      />
                    </FormGroup>
                  </div>
                </div>
                <div className="inline-group">
                  <div>
                    {" "}
                    <FormGroup>
                      <CustomTextInput
                        label={t("createArticle:alt")}
                        name="altImg"
                        value={values.altImg}
                        onChange={(e) => {
                          const altImg = e.target.value;
                          setFieldValue("altImg", altImg);
                        }}
                      />
                    </FormGroup>
                  </div>
                  <div>
                    {" "}
                    <FormGroup>
                      <FormLabel className="label-form">
                        {t("createArticle:visibility")}

                        <Select
                          className="select-pentabell"
                          variant="standard"
                          value={Visibility.filter(
                            (option) => values.visibility === option
                          )}
                          selected={values?.visibility}
                          onChange={(event) => {
                            setFieldValue("visibility", event.target.value);
                          }}
                        >
                          {Visibility.map((item, index) => (
                            <MenuItem key={index} value={item}>
                              {item}
                            </MenuItem>
                          ))}
                        </Select>

                        <ErrorMessage
                          className="label-error"
                          name="visibilityEN"
                          component="div"
                        />
                      </FormLabel>
                    </FormGroup>
                  </div>
                </div>

                <div className="inline-group">
                  <div>
                    {" "}
                    <FormGroup>
                      <CustomTextInput
                        label={t("createArticle:link")}
                        name="link"
                        value={values.link}
                        onChange={(e) => {
                          const link = e.target.value;
                          setFieldValue("link", link);
                        }}
                      />
                    </FormGroup>
                  </div>
                </div>
                <div className="inline-group">
                  <div>
                    {" "}
                    <FormGroup>
                      <CustomTextInput
                        label={t("Text Button")}
                        name="linkBtn"
                        value={values.linkBtn}
                        onChange={(e) => {
                          const linkBtn = e.target.value;
                          setFieldValue("linkBtn", linkBtn);
                        }}
                      />
                    </FormGroup>
                  </div>
                </div>

                <div className="inline-group">
                  <div>
                    <FormGroup>
                      <FormLabel className="label-form">
                        {t("createArticle:featuredImage")}
                        <div className="upload-container">
                          <label
                            htmlFor={`image-upload-${language}`}
                            className="file-labels"
                          >
                            <input
                              type="file"
                              id={`image-upload-${language}`}
                              name="img"
                              accept=".png, .jpg, .jpeg  ,.webp"
                              ref={imageInputRef}
                              onChange={(e) => {
                                setFieldValue("img", e.target.files[0]);
                                handlePhotoChange();
                              }}
                              className={
                                "file-input" +
                                (errors.img && touched.img ? " is-invalid" : "")
                              }
                            />
                            <div className="upload-area">
                              <div>
                                <div
                                  className="icon-pic"
                                  style={{
                                    backgroundImage: `url("${
                                      selectedImage
                                        ? URL.createObjectURL(selectedImage)
                                        : img
                                        ? `${process.env.NEXT_PUBLIC_BASE_API_URL}${API_URLS.files}/${img}`
                                        : upload.src
                                    }")`,
                                    backgroundSize: "cover",
                                    backgroundRepeat: "no-repeat",
                                    backgroundPosition: "center",
                                    //backgroundColor: "#40bd3921",
                                  }}
                                ></div>
                              </div>
                              <div>
                                <p className="upload-text">
                                  {t("createArticle:addFeatImg")}
                                </p>
                                <p className="upload-description">
                                  {t("createArticle:clickBox")}
                                </p>
                              </div>
                            </div>
                            <ErrorMessage
                              name="image"
                              component="div"
                              className="invalid-feedback error"
                            />
                          </label>
                        </div>
                      </FormLabel>
                    </FormGroup>
                  </div>
                </div>
                <div className="inline-group">
                  <div>
                    <FormGroup>
                      <FormLabel className="label-form">
                        {t("createArticle:featuredImage")}
                        <div className="upload-container">
                          <label
                            htmlFor={`imgMobile-upload-${language}`}
                            className="file-labels"
                          >
                            <input
                              type="file"
                              id={`imgMobile-upload-${language}`}
                              name="imgMobile"
                              accept=".png, .jpg, .jpeg ,.webp"
                              ref={mobileImageInputRef}
                              onChange={(e) => {
                                setFieldValue("imgMobile", e.target.files[0]);
                                handlePhotoChange("mobile");
                              }}
                              className={
                                "file-input" +
                                (errors.imgMobile && touched.imgMobile
                                  ? " is-invalid"
                                  : "")
                              }
                            />
                            <div className="upload-area">
                              <div>
                                <div
                                  className="icon-pic"
                                  style={{
                                    backgroundImage: `url("${
                                      selectedMobileImage
                                        ? URL.createObjectURL(
                                            selectedMobileImage
                                          )
                                        : imgMobile
                                        ? `${process.env.NEXT_PUBLIC_BASE_API_URL}/files/${imgMobile}`
                                        : upload.src
                                    }")`,
                                    backgroundSize: "cover",
                                    backgroundRepeat: "no-repeat",
                                    backgroundPosition: "center",
                                    //backgroundColor: "#40bd3921",
                                  }}
                                ></div>
                              </div>
                              <div>
                                <p className="upload-text">
                                  {t("sliders:addimgmobile")}
                                </p>
                                <p className="upload-description">
                                  {t("createArticle:clickBox")}
                                </p>
                              </div>
                            </div>
                            <ErrorMessage
                              name="image"
                              component="div"
                              className="invalid-feedback error"
                            />
                          </label>
                        </div>
                      </FormLabel>
                    </FormGroup>
                  </div>
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </div>
    </div>
  );
};

export default EditSliderForm;
