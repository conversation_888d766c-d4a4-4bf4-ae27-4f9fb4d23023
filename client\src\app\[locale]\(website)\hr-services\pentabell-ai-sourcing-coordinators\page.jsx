import BannerComponents from "@/components/pages/sites/sections/BannerComponents";
import banner from "@/assets/images/website/banner/Service-AI-Sourcing-Management-Pentabell-Services.webp";
import ResponsiveRowTitleText from "@/components/ui/ResponsiveRowTitleText";
import ServiceRow from "@/components/ui/ServiceRow";
import aiSourcingServiceImgS1 from "../../../../../assets/images/services/aiSourcingServiceImgS1.png";

import avaBrooks from "@/assets/images/ai-sourcing/png/ava-brooks.webp";
import daniel<PERSON>itchell from "@/assets/images/ai-sourcing/png/daniel-mitchell.webp";
import eliasDeeb from "@/assets/images/ai-sourcing/png/elias-deeb.webp";
import ryanMendes from "@/assets/images/ai-sourcing/png/ryan-mendes.webp";
import sallyAnderson from "@/assets/images/ai-sourcing/png/sally-anderson.webp";
import WhyPentabell from "@/components/pages/sites/services/WhyPentabell";
import AskQuestions from "@/components/pages/sites/services/AskQuestions";
import AiSourcingServicePageForm from "@/features/forms/components/AiSourcingServicePageForm";
import AiSourcingDetails from "@/components/ui/AiSourcingDetails";
import initTranslations from "@/app/i18n";
import { axiosGetJsonSSR } from "@/config/axios";

export async function generateMetadata({ params: { locale } }) {
  const canonicalUrl = `https://www.pentabell.com/${locale !== "en" ? `${locale}/` : ""
    }hr-services/pentabell-ai-sourcing-coordinators/`;

  const languages = {
    fr: `https://www.pentabell.com/fr/hr-services/pentabell-ai-sourcing-coordinators/`,
    en: `https://www.pentabell.com/hr-services/pentabell-ai-sourcing-coordinators/`,
    "x-default": `https://www.pentabell.com/hr-services/pentabell-ai-sourcing-coordinators/`,
  };

  const { t } = await initTranslations(locale, [
    "aiSourcingService",
    "global",
    "homePage",
  ]);

  try {
    const encodedSlug = encodeURIComponent(
      "hr-services/pentabell-ai-sourcing-coordinators"
    );
    const res = await axiosGetJsonSSR.get(
      `${process.env.NEXT_PUBLIC_BASE_API_URL_SSR}/seoTags/${locale}/${encodedSlug}`
    );
    const seoTags = res?.data?.status === 200;
    if (seoTags) {
      return {
        title: res?.data?.data?.versions[0]?.metaTitle,
        description: res?.data?.data?.versions[0]?.metaDescription,
        robots: res?.data?.data?.robotMeta,
        alternates: {
          canonical: canonicalUrl,
          languages,
        },
      };
    }
  } catch (error) {
    console.error("Error fetching SEO tags:", error);
  }
  return {
    title: t("aiSourcingService:metaTitle"),
    description: t("aiSourcingService:metaDescription"),
    alternates: {
      canonical: canonicalUrl,
      languages,
    },
    robots: "follow, index, max-snippet:-1, max-image-preview:large",
  };
}
async function pentabellAiSourcing({ params: { locale } }) {
  const { t, resources } = await initTranslations(locale, [
    "aiSourcingService",
    "global",
    "homePage",
  ]);
  const dataS1 = {
    featureImg: aiSourcingServiceImgS1,
    altImg: t("technicalAssistanceService:dataS1:altImg"),
    itemsWithParagraph: [
      {
        title: t("aiSourcingService:dataS1:title"),
        paragraph: t("aiSourcingService:dataS1:paragraph"),
      },
      {
        title: t("aiSourcingService:dataS2:title"),
        paragraph: t("aiSourcingService:dataS2:paragraph"),
      },
      {
        title: t("aiSourcingService:dataS3:title"),
        paragraph: t("aiSourcingService:dataS3:paragraph"),
      },
    ],
  };

  const ASK_QUESTIONS = [
    {
      id: "s1",
      title: t("aiSourcingService:questions:QA1:question"),
      description: t("aiSourcingService:questions:QA1:answer"),
    },
    {
      id: "s2",
      title: t("aiSourcingService:questions:QA2:question"),
      description: t("aiSourcingService:questions:QA2:answer"),
    },
    {
      id: "s3",
      title: t("aiSourcingService:questions:QA3:question"),
      description: t("aiSourcingService:questions:QA3:answer"),
    },
  ];
  const dataSouringRyan = {
    industry: t("aiSourcingService:dataSouringRyan:industry"),
    name: t("aiSourcingService:dataSouringRyan:name"),
    subTitle: t("aiSourcingService:dataSouringRyan:subTitle"),
    featureImg: ryanMendes.src,
    altImg: t("homePage:s6:alt2"),
    paragraph: t("aiSourcingService:dataSouringRyan:paragraph"),
    link: "https://www.linkedin.com/in/ryan-mendes-08361a297/",
  };

  const dataSouringSally = {
    industry: t("aiSourcingService:dataSouringSally:industry"),
    name: t("aiSourcingService:dataSouringSally:name"),
    subTitle: t("aiSourcingService:dataSouringSally:subTitle"),
    featureImg: sallyAnderson.src,
    altImg: t("homePage:s6:alt1"),
    paragraph: t("aiSourcingService:dataSouringSally:paragraph"),
    link: "https://www.linkedin.com/in/sally-anderson-969a10296/",
  };

  const dataSouringDaniel = {
    industry: t("aiSourcingService:dataSouringDaniel:industry"),
    name: t("aiSourcingService:dataSouringDaniel:name"),
    subTitle: t("aiSourcingService:dataSouringDaniel:subTitle"),
    featureImg: danielMitchell.src,
    altImg: t("homePage:s6:alt4"),
    paragraph: t("aiSourcingService:dataSouringDaniel:paragraph"),
    link: "https://www.linkedin.com/in/daniel-mitchell-4509a629a/",
  };

  const dataSouringElyes = {
    industry: t("aiSourcingService:dataSouringElias:industry"),
    name: t("aiSourcingService:dataSouringElias:name"),
    subTitle: t("aiSourcingService:dataSouringElias:subTitle"),
    featureImg: eliasDeeb.src,
    altImg: t("homePage:s6:alt3"),
    paragraph: t("aiSourcingService:dataSouringElias:paragraph"),
    link: "https://www.linkedin.com/in/elias-deeb-75905429a/",
  };

  const dataSouringAva = {
    industry: t("aiSourcingService:dataSouringAva:industry"),
    name: t("aiSourcingService:dataSouringAva:name"),
    subTitle: t("aiSourcingService:dataSouringAva:subTitle"),
    featureImg: avaBrooks.src,
    altImg: t("homePage:s6:alt5"),
    paragraph: t("aiSourcingService:dataSouringAva:paragraph"),
    link: "https://www.linkedin.com/in/ava-brooks-27457629a/",
  };
  const reasons = [
    {
      heading: t("aiSourcingService:whyPentabell:reasons:reason1:title"),
      text: t("aiSourcingService:whyPentabell:reasons:reason1:description"),
    },
    {
      heading: t("aiSourcingService:whyPentabell:reasons:reason2:title"),
      text: t("aiSourcingService:whyPentabell:reasons:reason2:description"),
    },
    {
      heading: t("aiSourcingService:whyPentabell:reasons:reason3:title"),
      text: t("aiSourcingService:whyPentabell:reasons:reason3:description"),
    },
    {
      heading: t("aiSourcingService:whyPentabell:reasons:reason4:title"),
      text: t("aiSourcingService:whyPentabell:reasons:reason4:description"),
    },
  ];
  return (
    <div>
      <BannerComponents
        title={t("aiSourcingService:intro:title")}
        description={t("aiSourcingService:intro:description")}
        bannerImg={banner}
        height={"70vh"}
        altImg={t("aiSourcingService:intro:altImg")}
      />

      <ResponsiveRowTitleText
        title={t("aiSourcingService:overview:title")}
        paragraph={t("aiSourcingService:overview:description1")}
        paragraph2={t("aiSourcingService:overview:description2")}
      />

      <p className="heading-h1 text-center text-banking">
        {t("aiSourcingService:howWeHelp")}
      </p>

      <ServiceRow data={dataS1} />
      <AiSourcingDetails data={dataSouringRyan} reverse={true} darkBg={true} />
      <AiSourcingDetails data={dataSouringSally} />
      <AiSourcingDetails
        data={dataSouringDaniel}
        reverse={true}
        darkBg={true}
      />
      <AiSourcingDetails data={dataSouringElyes} />
      <AiSourcingDetails data={dataSouringAva} reverse={true} darkBg={true} />

      <WhyPentabell
        title1={t("aiSourcingService:whyPentabell:title1")}
        title2={t("aiSourcingService:whyPentabell:title2")}
        reasons={reasons}
      />
      <AskQuestions
        title={t("aiSourcingService:questions:title")}
        ASK_QUESTIONS={ASK_QUESTIONS}
      />
      {/* <InsightsSection /> */}
      <AiSourcingServicePageForm />
    </div>
  );
}
export default pentabellAiSourcing;
