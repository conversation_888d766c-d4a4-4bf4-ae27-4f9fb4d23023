import initTranslations from "@/app/i18n";
import ComplexityControlSection from "@/components/ui/ComplexityControlSection";

async function ApproachToDirectHire({ locale }) {
  const { t, resources } = await initTranslations(locale, [
    "directHiringService",
    "global",
  ]);

  const items = [
    {
      icon: "",
      title: t("DirectHiringService:approachToDirectHiring:needAssessment:title"),
      description: t("DirectHiringService:approachToDirectHiring:needAssessment:description"),
    },
    {
      icon: "",
      title: t("DirectHiringService:approachToDirectHiring:soucingAndScreening:title"),
      description: t("DirectHiringService:approachToDirectHiring:soucingAndScreening:description"),
    },
    {
      icon: "",
      title: t("DirectHiringService:approachToDirectHiring:interviewing:title"),
      description: t("DirectHiringService:approachToDirectHiring:interviewing:description"),
    },
    {
      icon: "",
      title: t("DirectHiringService:approachToDirectHiring:presentingTopTalent:title"),
      description: t("DirectHiringService:approachToDirectHiring:presentingTopTalent:description"),
    },

    {
      icon: "",
      title: t("DirectHiringService:approachToDirectHiring:clientCandidateInterviews:title"),
      description: t("DirectHiringService:approachToDirectHiring:clientCandidateInterviews:description"),
    },
    {
      icon: "",
      title: t("DirectHiringService:approachToDirectHiring:confirmingPick:title"),
      description: t("DirectHiringService:approachToDirectHiring:confirmingPick:description"),
    },
  ];

  return (
    <ComplexityControlSection
      title1={t("DirectHiringService:approachToDirectHiring:title")}
      title2=""
      description={t("DirectHiringService:approachToDirectHiring:description")}
      items={items}
    />
  );
}

export default ApproachToDirectHire;
