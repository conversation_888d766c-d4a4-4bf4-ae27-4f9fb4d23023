"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.extractEmails = void 0;
exports.getCategoryUrlByIdVersion = getCategoryUrlByIdVersion;
exports.getCategoryGuideUrlByIdVersion = getCategoryGuideUrlByIdVersion;
exports.getServiceGuideUrlByIdVersion = getServiceGuideUrlByIdVersion;
exports.verifyRole = verifyRole;
exports.validateEmailFormat = validateEmailFormat;
exports.formatDate = formatDate;
exports.mapNotifications = mapNotifications;
exports.getConnectedUser = getConnectedUser;
exports.computeChecksumFromBuffer = computeChecksumFromBuffer;
exports.sendAlert = sendAlert;
exports.computeChecksumFromStream = computeChecksumFromStream;
exports.consoleLog = consoleLog;
exports.delay = delay;
exports.parseFrenchDate = parseFrenchDate;
exports.validate = validate;
exports.extractTextFromFile = extractTextFromFile;
exports.calculateChecksum = calculateChecksum;
exports.downloadGoogleProfilePhoto = downloadGoogleProfilePhoto;
exports.verifyRoles = verifyRoles;
exports.extractTextFromScannedPDF = extractTextFromScannedPDF;
exports.formatJobDetails = formatJobDetails;
exports.generateJobDescription = generateJobDescription;
exports.translateJobTitle = translateJobTitle;
exports.getClientMetadata = getClientMetadata;
const user_model_1 = __importDefault(require("@/apis/user/user.model"));
const socket_1 = require("../config/socket");
const ws_1 = __importDefault(require("ws"));
const geoip_lite_1 = __importDefault(require("geoip-lite"));
const settings_model_1 = __importDefault(require("@/apis/settings/settings.model"));
const alert_model_1 = __importDefault(require("@/apis/alert/alert.model"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const crypto_1 = __importDefault(require("crypto"));
const article_category_model_1 = __importDefault(require("@/apis/article/category/article.category.model"));
const validator_1 = __importDefault(require("validator"));
const word_extractor_1 = __importDefault(require("word-extractor"));
const pdf_parse_1 = __importDefault(require("pdf-parse"));
const mammoth_1 = __importDefault(require("mammoth"));
const fs_1 = __importDefault(require("fs"));
const https_1 = __importDefault(require("https"));
const uuid_1 = require("uuid");
const path_1 = __importDefault(require("path"));
const files_service_1 = __importDefault(require("@/apis/storage/files.service"));
const axios_1 = __importDefault(require("axios"));
const form_data_1 = __importDefault(require("form-data"));
const guide_category_model_1 = __importDefault(require("@/apis/guide/categoryguide/guide.category.model"));
const messages_1 = require("./messages");
const http_exception_1 = __importDefault(require("../exceptions/http.exception"));
const User = user_model_1.default;
const Alert = alert_model_1.default;
const Category = article_category_model_1.default;
const Settings = settings_model_1.default;
const MAX_RETRIES = 5;
const RETRY_DELAY_MS = 2000;
async function getCategoryUrlByIdVersion(id) {
    try {
        const category = await Category.findOne({ 'versionscategory._id': id }).exec();
        if (category) {
            const version = category.versionscategory.find((v) => v._id.toString() === id.toString());
            return version ? version : null;
        }
        return null;
    }
    catch (error) {
        console.error('Error fetching category URL:', error);
        return null;
    }
}
async function getCategoryGuideUrlByIdVersion(id) {
    try {
        const category = await guide_category_model_1.default.findOne({ 'categoryguide._id': id }).exec();
        if (category) {
            const version = category.categoryguide.find((v) => v._id.toString() === id.toString());
            return version ? version : null;
        }
        return null;
    }
    catch (error) {
        console.error('Error fetching category URL:', error);
        return null;
    }
}
async function getServiceGuideUrlByIdVersion(id) {
    try {
        const category = await Category.findOne({ 'versionscategory._id': id }).exec();
        if (category) {
            const version = category.versionscategory.find((v) => v._id.toString() === id.toString());
            return version ? version : null;
        }
        return null;
    }
    catch (error) {
        console.error('Error fetching category URL:', error);
        return null;
    }
}
function verifyRole(role, currentUser) {
    return currentUser.roles.indexOf(role) === -1 ? false : true;
}
function validateEmailFormat(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        throw new http_exception_1.default(400, messages_1.MESSAGES.USER.EMAIL_INVALID);
    }
}
function formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}
async function mapNotifications(notifications) {
    const mappedNotifications = [];
    for (const notification of notifications) {
        const user = await User.findById(notification.sender);
        mappedNotifications.push({
            _id: notification._id,
            image: user ? `${process.env.STORAGE_URL}/${user.profilePicture}` : undefined,
            message: notification.message,
            detailPage: notification.link,
            receivedTime: notification.createdAt,
            isRead: notification.isRead,
            sender: user ? `${user?.firstName} ${user?.lastName?.toUpperCase()}` : null,
            type: notification.type,
            link: notification.link,
        });
    }
    return mappedNotifications;
}
async function getConnectedUser(refreshToken) {
    if (!refreshToken)
        return 'Guest User';
    try {
        const payload = jsonwebtoken_1.default.verify(refreshToken, String(process.env.REFRESH_TOKEN_PRIVATE_KEY));
        if (!payload || !payload._id)
            return 'Guest User';
        const connectedUser = await User.findById(payload._id);
        return connectedUser?.firstName ? `${connectedUser.firstName} ${connectedUser.lastName?.toUpperCase()}` : 'Guest User';
    }
    catch (err) {
        if (err.name === 'TokenExpiredError') {
            return 'Guest User';
        }
        console.error('Token verification failed:', err);
        return 'Guest User';
    }
}
function computeChecksumFromBuffer(buffer, algorithm = 'sha256') {
    const hash = crypto_1.default.createHash(algorithm);
    hash.update(buffer);
    return hash.digest('hex');
}
async function sendAlert(opportunite) {
    const alerts = await Alert.find({
        isActive: true,
        industry: opportunite.industry,
        country: String(opportunite.country),
    });
    if (!alerts || alerts.length === 0) {
        console.log('No active alerts found for this opportunity.');
        return;
    }
    await Promise.all(alerts.map(async (alert) => {
        const userSettings = await Settings.findOne({ user: alert.createdBy }).lean();
        if (userSettings?.notifications?.newJobAlerts?.website === true) {
            console.log(`Sending job alert to user: ${alert.createdBy}`);
            const message = 'New job opportunity';
            const description = `${opportunite.versions['en'].title} in ${String(opportunite.country)},${String(opportunite.industry)}`;
            const link = `/opportunities/${opportunite.versions['en'].url}`;
            await (0, socket_1.sendNotification)({
                receiver: alert.createdBy,
                type: 'jobalert',
                message,
                description,
                link,
            });
        }
        else {
            console.log(`User ${alert.createdBy} has disabled website job alerts.`);
        }
    }));
}
function computeChecksumFromStream(stream) {
    return new Promise((resolve, reject) => {
        const hash = crypto_1.default.createHash('sha256');
        stream.on('data', (data) => {
            hash.update(data);
        });
        stream.on('end', () => {
            const checksum = hash.digest('hex');
            resolve(checksum);
        });
        stream.on('error', reject);
    });
}
function consoleLog(...args) {
    if (process.env.NODE_ENV !== 'prod') {
        console.log(args.toString().replace(',', ' '));
    }
}
function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}
function parseFrenchDate(frenchDateStr, timeStr) {
    const frenchToEnglishMonths = {
        janvier: 'January',
        février: 'February',
        mars: 'March',
        avril: 'April',
        mai: 'May',
        juin: 'June',
        juillet: 'July',
        août: 'August',
        septembre: 'September',
        octobre: 'October',
        novembre: 'November',
        décembre: 'December',
    };
    const englishDateStr = frenchDateStr.replace(/janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre/, (match) => frenchToEnglishMonths[match.toLowerCase()]);
    const combinedDateTimeStr = `${englishDateStr} ${timeStr}`;
    return new Date(combinedDateTimeStr);
}
function validate(input) {
    if (input.length === 0)
        return false;
    if (input.includes('INTERNAL REQUISITION FORM'))
        return false;
    const keywordsFr = ['formation', 'expériences', 'expérience', 'compétences', 'compétence', 'résumé', 'contact', 'objectif', 'projets académique'];
    const keywordsEn = ['education', 'experiences', 'experience', 'skills', 'skill', 'summary', 'contact', 'objective', 'academic projects'];
    const allKeywords = [...keywordsFr, ...keywordsEn];
    const pattern = new RegExp(allKeywords.join('|'), 'i');
    return pattern.test(input);
}
const extractEmails = (text) => {
    const words = text.split(/\s+/);
    return words.filter(word => validator_1.default.isEmail(word));
};
exports.extractEmails = extractEmails;
async function extractTextFromFile(contentType, file) {
    if (contentType === 'application/pdf') {
        const data = await (0, pdf_parse_1.default)(file);
        let flatText = data.text;
        flatText = flatText.replace(/\s+/g, ' ').trim();
        if (flatText.length === 0) {
            flatText = await extractTextFromScannedPDF(file);
            flatText = flatText.replace(/\s+/g, ' ').trim();
        }
        return flatText;
    }
    else if (contentType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
        const textResult = await mammoth_1.default.extractRawText({ buffer: file });
        return textResult.value;
    }
    else if (contentType === 'application/msword') {
        const extractor = new word_extractor_1.default();
        const doc = await extractor.extract(file);
        return doc.getBody();
    }
    else
        return;
}
function calculateChecksum(filePathOrUrl) {
    return new Promise((resolve, reject) => {
        const hash = crypto_1.default.createHash('sha256');
        if (filePathOrUrl.startsWith('http://') || filePathOrUrl.startsWith('https://')) {
            https_1.default
                .get(filePathOrUrl, response => {
                if (response.statusCode !== 200) {
                    return reject(new Error(`Failed to fetch resource. Status code: ${response.statusCode}`));
                }
                response.on('data', chunk => hash.update(chunk));
                response.on('end', () => resolve(hash.digest('hex')));
                response.on('error', err => reject(err));
            })
                .on('error', err => reject(err));
        }
        else {
            const fileStream = fs_1.default.createReadStream(filePathOrUrl);
            fileStream.on('data', chunk => hash.update(chunk));
            fileStream.on('end', () => resolve(hash.digest('hex')));
            fileStream.on('error', err => reject(err));
        }
    });
}
async function downloadGoogleProfilePhoto(imageUrl, foundUser) {
    const folderPath = path_1.default.join(__dirname, `../../../uploads`, 'users', new Date().getFullYear().toString());
    if (!fs_1.default.existsSync(folderPath))
        fs_1.default.mkdirSync(folderPath, { recursive: true });
    const uuid = (0, uuid_1.v4)().replace(/-/g, '');
    const filesService = new files_service_1.default();
    const fileName = `${uuid}.jpg`;
    const filePath = path_1.default.join(folderPath, fileName);
    const urlParts = imageUrl.split('/');
    const originalName = decodeURIComponent(urlParts[urlParts.length - 1]);
    https_1.default
        .get(imageUrl, async (response) => {
        if (response.statusCode === 200) {
            const checksum = await calculateChecksum(imageUrl);
            const existingFile = await filesService.getFileByChecksum(checksum);
            if (existingFile) {
                await user_model_1.default.findByIdAndUpdate(foundUser._id, { profilePicture: existingFile.fileName });
            }
            else {
                const file = fs_1.default.createWriteStream(filePath);
                const fileType = response.headers['content-type'];
                response.pipe(file);
                file.on('finish', async () => {
                    file.close();
                    const fileSize = fs_1.default.statSync(filePath).size;
                    const fileData = {
                        resource: 'users',
                        folder: new Date().getFullYear().toString(),
                        uuid,
                        fileName,
                        fileType,
                        originalName,
                        fileSize,
                        checksum,
                    };
                    await filesService.createFile(fileData);
                    console.log(`Profile photo saved as: ${filePath}`);
                });
                await user_model_1.default.findByIdAndUpdate(foundUser._id, { profilePicture: fileName });
            }
        }
        else {
            console.error(`Failed to download profile photo. Status code: ${response.statusCode}`);
        }
    })
        .on('error', (err) => {
        fs_1.default.unlink(filePath, () => { });
        console.error(`Error downloading image: ${err.message}`);
    });
}
function verifyRoles(userRoles, requiredRoles) {
    return requiredRoles.every(role => userRoles.includes(role));
}
async function extractTextFromScannedPDF(dataBuffer) {
    const formData = new form_data_1.default();
    formData.append('file', dataBuffer, {
        filename: 'test.pdf',
        contentType: 'multipart/form-data',
    });
    const response = await axios_1.default.post(process.env.EXTRACT_TEXT_FROM_PDF, formData, {
        headers: {
            ...formData?.getHeaders(),
        },
    });
    return response?.data?.text;
}
function formatJobDetails(details, language = 'en') {
    if (!details)
        return '';
    const createSection = (title, content) => `<strong>${title}:</strong><br>${content}<br><br>`;
    const createList = (items) => items?.map(item => `&mdash; ${item}<br>`).join('') || '';
    let html = '';
    html += createSection(language === 'fr' ? "Aperçu de l'entreprise" : 'Company overview', details?.company_overview || 'N/A');
    html += createSection('Position', details?.job_overview?.position || 'N/A');
    html += createSection(language === 'fr' ? 'Description du poste' : 'Summary', details?.job_overview?.summary || 'N/A');
    html += `<strong>${language === 'fr' ? 'Responsabilités' : 'Responsibilities'}:</strong><br>${createList(details?.responsibilities)}<br>`;
    html += `<strong>${language === 'fr' ? 'Exigences du candidat:' : 'Requirements:'}</strong><br>
    &mdash; ${details?.candidate_requirements?.education || 'N/A'}<br>
    &mdash; ${details?.candidate_requirements?.experience || 'N/A'}<br>${createList(details?.candidate_requirements?.skills)}`;
    return html;
}
async function generateJobDescription(industry, jobTitle, minExperience, jobDescription, location, language = 'en', attempt = 1) {
    console.log({
        industry,
        jobTitle,
        minExperience,
        jobDescription,
        location,
        language,
    });
    return new Promise((resolve, reject) => {
        const socket = new ws_1.default(process.env.JOB_DESCRIPTION_GENERATOR);
        const messageBuffer = [];
        let dataReceived = false;
        let timeout;
        const startInactivityTimer = () => {
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                socket.close();
            }, 15000);
        };
        socket.on('open', () => {
            socket.send(JSON.stringify({
                input: {
                    industry,
                    job_title: jobTitle,
                    jobDescription,
                    location,
                    language,
                    number_responsibilities: 5,
                    number_requirements: 5,
                    min_experience: minExperience,
                },
            }));
            startInactivityTimer();
        });
        socket.on('message', (data) => {
            try {
                messageBuffer.push(JSON.parse(data.toString('utf-8')));
                dataReceived = true;
                startInactivityTimer();
            }
            catch (error) {
                clearTimeout(timeout);
                socket.close();
                reject(new Error('Invalid WebSocket message ' + error));
            }
        });
        socket.on('close', async () => {
            clearTimeout(timeout);
            if (dataReceived && messageBuffer.length) {
                const finalData = messageBuffer.reduce((acc, part) => ({ ...acc, ...part }), {});
                resolve(formatJobDetails(finalData, language));
            }
            else {
                if (attempt < MAX_RETRIES) {
                    console.warn(`Retrying WebSocket (Attempt ${attempt + 1})...`);
                    await delay(RETRY_DELAY_MS);
                    resolve(generateJobDescription(industry, jobTitle, minExperience, jobDescription, location, language, attempt + 1));
                }
                else {
                    reject(new Error('No data received after maximum retries'));
                }
            }
        });
        socket.on('error', error => {
            clearTimeout(timeout);
            reject(new Error(`WebSocket error: ${error.message}`));
        });
    });
}
async function translateJobTitle(job_title, target_language) {
    try {
        const response = await axios_1.default.post(process.env.JOB_TITLE_TRANSLATOR, {
            job_title,
            target_language,
        });
        return response.data.translated_title;
    }
    catch (err) {
        console.error(err);
        return null;
    }
}
function getClientMetadata(request) {
    const ip = request.headers['x-forwarded-for']?.split(',')[0]?.trim() || request.socket.remoteAddress || request.ip || 'Unknown';
    const userAgent = request.headers['user-agent'] || 'Unknown';
    const geo = geoip_lite_1.default.lookup(ip);
    const result = {
        ip,
        userAgent,
        country: geo?.country || 'Unknown',
    };
    if (geo?.city)
        result.city = geo.city;
    if (geo?.region)
        result.region = geo.region;
    return result;
}
//# sourceMappingURL=functions.js.map