"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/add/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/AddArticleFroala.jsx":
/*!***********************************************************!*\
  !*** ./src/features/blog/components/AddArticleFroala.jsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Accordion/Accordion.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionSummary/AccordionSummary.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionDetails/AccordionDetails.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormGroup/FormGroup.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormLabel/FormLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mui/icons-material/ExpandMore */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/ExpandMore.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! yup */ \"(app-pages-browser)/./node_modules/yup/index.esm.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! formik */ \"(app-pages-browser)/./node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var _utils_urls__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../utils/urls */ \"(app-pages-browser)/./src/utils/urls.js\");\n/* harmony import */ var _AddArticleEN__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./AddArticleEN */ \"(app-pages-browser)/./src/features/blog/components/AddArticleEN.jsx\");\n/* harmony import */ var _AddArticleFR__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./AddArticleFR */ \"(app-pages-browser)/./src/features/blog/components/AddArticleFR.jsx\");\n/* harmony import */ var _opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../opportunity/hooks/opportunity.hooks */ \"(app-pages-browser)/./src/features/opportunity/hooks/opportunity.hooks.js\");\n/* harmony import */ var _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../hooks/blog.hook */ \"(app-pages-browser)/./src/features/blog/hooks/blog.hook.js\");\n/* harmony import */ var react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-datepicker/dist/react-datepicker.css */ \"(app-pages-browser)/./node_modules/react-datepicker/dist/react-datepicker.css\");\n/* harmony import */ var _config_axios__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/config/axios */ \"(app-pages-browser)/./src/config/axios.js\");\n/* harmony import */ var _features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/features/auth/hooks/currentUser.hooks */ \"(app-pages-browser)/./src/features/auth/hooks/currentUser.hooks.js\");\n/* harmony import */ var _components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! lodash/debounce */ \"(app-pages-browser)/./node_modules/lodash/debounce.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_15__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AddArticle = ()=>{\n    _s();\n    const { user } = (0,_features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_13__[\"default\"])();\n    const savedArticle = localStorage.getItem(\"savedArticle\");\n    const useUpdateAutoSaveHook = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useUpdateAutoSave)();\n    const useCreateArticleHook = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useCreateArticle)();\n    const useCreateAutoSaveHook = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useCreateAutoSave)();\n    const useSaveFileHook = (0,_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_9__.useSaveFile)();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const currentYear = new Date().getFullYear();\n    const formikRefAll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [expanded, setExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(`panel`);\n    const [formdataEN, setFormDataEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new FormData());\n    const [formdataFR, setFormDataFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new FormData());\n    const [uuidPhotoFileNameEN, setUuidPhotoFileNameEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [uuidPhotoFileNameFR, setUuidPhotoFileNameFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedLanguages, setSelectedLanguages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        en: true,\n        fr: false\n    });\n    const [categoriesEN, setCategoriesEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categoriesFR, setCategoriesFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredCategoriesEN, setFilteredCategoriesEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredCategoriesFR, setFilteredCategoriesFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCategoriesEN, setSelectedCategoriesEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCategoriesFR, setSelectedCategoriesFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const getCategoriesEN = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories)(\"en\");\n    const getCategoriesFR = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories)(\"fr\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (getCategoriesEN.data?.categories) {\n            const transformedCategories = getCategoriesEN.data.categories.map((category)=>({\n                    id: category.versionscategory[0]?.id,\n                    name: category.versionscategory[0]?.name\n                }));\n            setCategoriesEN(transformedCategories);\n        }\n    }, [\n        getCategoriesEN.data\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (getCategoriesFR.data?.categories) {\n            const transformedCategories = getCategoriesFR.data.categories.map((category)=>({\n                    id: category?.versionscategory[0]?.id,\n                    name: category.versionscategory[0]?.name\n                }));\n            setCategoriesFR(transformedCategories);\n        }\n    }, [\n        getCategoriesFR.data\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedCategoriesEN.length > 0) {\n            fetchTranslatedCategories(selectedCategoriesEN, \"en\");\n        } else if (selectedCategoriesFR.length > 0) {\n            fetchTranslatedCategories(selectedCategoriesFR, \"fr\");\n        }\n    }, [\n        selectedCategoriesEN,\n        selectedCategoriesFR\n    ]);\n    const fetchTranslatedCategories = async (selectedCategories, language)=>{\n        try {\n            const response = await _config_axios__WEBPACK_IMPORTED_MODULE_12__.axiosGetJson.get(`${_utils_urls__WEBPACK_IMPORTED_MODULE_6__.API_URLS.categories}/${language}/${selectedCategories}`);\n            if (language === \"en\") {\n                const transformedCategories = response.data.map((category)=>({\n                        id: category._id,\n                        name: category.name\n                    }));\n                setFilteredCategoriesFR(transformedCategories);\n            } else {\n                const transformedCategories = response.data.map((category)=>({\n                        id: category._id,\n                        name: category.name\n                    }));\n                setFilteredCategoriesEN(transformedCategories);\n            }\n        } catch (error) {\n            console.error(\"Error fetching translated categories:\", error);\n        }\n    };\n    const handleImageSelect = async (selectedFile, language)=>{\n        if (language === \"en\") {\n            const uuidPhotos = (0,uuid__WEBPACK_IMPORTED_MODULE_16__[\"default\"])().replace(/-/g, \"\");\n            const newFormData = new FormData();\n            newFormData.append(\"file\", selectedFile);\n            setFormDataEN(newFormData);\n            const extension = selectedFile.name.split(\".\").pop();\n            setUuidPhotoFileNameEN(`${uuidPhotos}.${extension}`);\n        } else if (language === \"fr\") {\n            const uuidPhotos = (0,uuid__WEBPACK_IMPORTED_MODULE_16__[\"default\"])().replace(/-/g, \"\");\n            const newFormData = new FormData();\n            newFormData.append(\"file\", selectedFile);\n            setFormDataFR(newFormData);\n            const extension = selectedFile.name.split(\".\").pop();\n            setUuidPhotoFileNameFR(`${uuidPhotos}.${extension}`);\n        }\n    };\n    // Helper function to safely parse localStorage items\n    const getLocalStorageItem = (key)=>{\n        try {\n            const item = localStorage.getItem(key);\n            return item ? JSON.parse(item) : \"\";\n        } catch (error) {\n            console.warn(`Error parsing localStorage item \"${key}\":`, error);\n            return \"\";\n        }\n    };\n    const titleEN = getLocalStorageItem(\"title\");\n    const metaTitleEN = getLocalStorageItem(\"metatitle\");\n    const metaDescriptionEN = getLocalStorageItem(\"metaDescription\");\n    const contentEN = getLocalStorageItem(\"content\");\n    const contentFR = getLocalStorageItem(\"contentfr\");\n    const titleFR = getLocalStorageItem(\"titlefr\");\n    const metaDescriptionFR = getLocalStorageItem(\"metaDescriptionfr\");\n    const metaTitleFR = getLocalStorageItem(\"metatitlefr\");\n    const initialValues = {\n        robotsMeta: \"index\",\n        metaTitleEN: metaTitleEN,\n        metaDescriptionEN: metaDescriptionEN,\n        descriptionEN: \"\",\n        visibilityEN: \"\",\n        categoryEN: [],\n        imageEN: null,\n        keywordsEN: \"\",\n        titleEN: titleEN,\n        urlEN: \"\",\n        altEN: \"\",\n        contentEN: contentEN,\n        highlightsEN: [],\n        publishDateEN: \"\",\n        faqTitleEN: \"\",\n        faqEN: [],\n        titleFR: titleFR,\n        metaTitleFR: metaTitleFR,\n        metaDescriptionFR: metaDescriptionFR,\n        descriptionFR: \"\",\n        visibilityFR: \"\",\n        categoryFR: [],\n        imageFR: null,\n        keywordsFR: \"\",\n        urlFR: \"\",\n        altFR: \"\",\n        contentFR: contentFR,\n        publishDateFR: \"\",\n        highlightsFR: [],\n        faqTitleFR: \"\",\n        faqFR: []\n    };\n    const validationSchema = yup__WEBPACK_IMPORTED_MODULE_3__.object().shape({\n        ...selectedLanguages.en && {\n            metaTitleEN: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            metaDescriptionEN: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            titleEN: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            category: yup__WEBPACK_IMPORTED_MODULE_3__.array().min(1, t(\"validations:minCategory\")),\n            visibilityEN: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\"))\n        },\n        ...selectedLanguages.fr && {\n            metaTitleFR: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            metaDescriptionFR: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            titleFR: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n            visibilityFR: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\"))\n        }\n    });\n    const uploadFile = (filename, formData, lang)=>{\n        return new Promise((resolve)=>{\n            useSaveFileHook.mutate({\n                resource: \"blogs\",\n                folder: currentYear,\n                filename,\n                body: {\n                    formData,\n                    t\n                }\n            }, {\n                onSuccess: (data)=>{\n                    if (data.message === \"uuid exist\") {\n                        resolve({\n                            lang,\n                            uuid: data.uuid\n                        });\n                    } else {\n                        resolve({\n                            lang,\n                            uuid: data.uuid\n                        });\n                    }\n                },\n                onError: (error)=>{\n                    console.error(`Error uploading ${lang} image:`, error);\n                    resolve({\n                        lang,\n                        uuid: null\n                    });\n                }\n            });\n        });\n    };\n    const clearLocalStorage = ()=>{\n        const keysToRemove = [\n            \"title\",\n            \"content\",\n            \"titlefr\",\n            \"contentfr\",\n            \"metaDescription\",\n            \"metaDescriptionfr\",\n            \"metatitle\",\n            \"metatitlefr\",\n            \"savedArticle\"\n        ];\n        keysToRemove.forEach((key)=>{\n            try {\n                localStorage.removeItem(key);\n            } catch (error) {\n                console.warn(`Error removing localStorage item \"${key}\":`, error);\n            }\n        });\n    };\n    const handleSubmit = async (values)=>{\n        const data = {\n            robotsMeta: values.robotsMeta,\n            versions: []\n        };\n        if (!selectedLanguages.en && !selectedLanguages.fr) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please select at least one version!\");\n            return;\n        }\n        try {\n            let resultEN, resultFR;\n            // Validate required files\n            if (selectedLanguages.en && (!uuidPhotoFileNameEN || !formdataEN)) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please select an image for the English version!\");\n                return;\n            }\n            if (selectedLanguages.fr && (!uuidPhotoFileNameFR || !formdataFR)) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please select an image for the French version!\");\n                return;\n            }\n            if (selectedLanguages.en) {\n                resultEN = await uploadFile(uuidPhotoFileNameEN, formdataEN, \"English\");\n            }\n            if (selectedLanguages.fr) {\n                resultFR = await uploadFile(uuidPhotoFileNameFR, formdataFR, \"French\");\n            }\n            if (selectedLanguages.en && resultEN.uuid) {\n                data.versions.push({\n                    language: \"en\",\n                    metaTitle: values.metaTitleEN,\n                    title: values.titleEN,\n                    metaDescription: values.metaDescriptionEN,\n                    description: values.descriptionEN,\n                    url: values.urlEN,\n                    visibility: values.visibilityEN,\n                    publishDate: values.publishDateEN,\n                    content: values.contentEN,\n                    alt: values.altEN,\n                    keywords: values.keywordsEN,\n                    highlights: values.highlightsEN,\n                    image: resultEN.uuid,\n                    category: values.categoryEN === \"\" ? [] : values.categoryEN,\n                    createdBy: user?.firstName + \" \" + user?.lastName,\n                    faqTitle: values.faqTitleEN || \"\",\n                    faq: values.faqEN || []\n                });\n            }\n            if (selectedLanguages.fr && resultFR.uuid) {\n                data.versions.push({\n                    language: \"fr\",\n                    metaTitle: values.metaTitleFR,\n                    title: values.titleFR,\n                    metaDescription: values.metaDescriptionFR,\n                    url: values.urlFR,\n                    visibility: values.visibilityFR,\n                    description: values.descriptionFR,\n                    publishDate: values.publishDateFR,\n                    content: values.contentFR,\n                    alt: values.altFR,\n                    keywords: values.keywordsFR,\n                    highlights: values.highlightsFR,\n                    image: resultFR.uuid,\n                    category: values.categoryFR === \"\" ? [] : values.categoryFR,\n                    createdBy: user?.firstName + \" \" + user?.lastName,\n                    faqTitle: values.faqTitleFR || \"\",\n                    faq: values.faqFR || []\n                });\n            }\n            if (data.versions.length > 0) {\n                if (isSavedArticle !== \"\") {\n                    useUpdateAutoSaveHook.mutate({\n                        data: data,\n                        id: isSavedArticle\n                    }, {\n                        onSuccess: ()=>{\n                            clearLocalStorage();\n                        //window.location.href = `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.blogs.route}/`;\n                        }\n                    });\n                } else {\n                    useCreateArticleHook.mutate({\n                        data\n                    }, {\n                        onSuccess: ()=>{\n                            clearLocalStorage();\n                        // window.location.href = `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.blogs.route}/`;\n                        }\n                    });\n                }\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"No valid image uploads found. Article not created.\");\n            }\n        } catch (error) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"An error occurred while processing uploads.\");\n        }\n    };\n    const handleChangeAccordion = (panel)=>(_, newExpanded)=>{\n            setExpanded(newExpanded ? panel : false);\n        };\n    const handleCategoriesENSelect = (selectedCategories)=>{\n        setSelectedCategoriesEN(selectedCategories);\n    };\n    const handleCategoriesFRSelect = (selectedCategories)=>{\n        setSelectedCategoriesFR(selectedCategories);\n    };\n    const [isSavedArticle, setIsSavedArticle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(savedArticle ? savedArticle : \"\");\n    const isSavedArticleRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(isSavedArticle);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        isSavedArticleRef.current = isSavedArticle;\n    }, [\n        isSavedArticle\n    ]);\n    const autosave = async ()=>{\n        try {\n            const values = formikRefAll.current?.values;\n            if (!values) return;\n            const data = {\n                robotsMeta: values?.robotsMeta,\n                versions: []\n            };\n            if (selectedLanguages.en) {\n                data.versions.push({\n                    language: \"en\",\n                    metaTitle: values.metaTitleEN,\n                    title: values.titleEN,\n                    metaDescription: values.metaDescriptionEN,\n                    url: values.urlEN,\n                    visibility: \"Draft\",\n                    publishDate: values.publishDateEN,\n                    content: values.contentEN,\n                    alt: values.altEN,\n                    keywords: values.keywordsEN,\n                    category: values.categoryEN,\n                    highlights: values.highlightsEN,\n                    faqTitle: values.faqTitleEN || \"\",\n                    faq: values.faqEN || []\n                });\n            }\n            if (selectedLanguages.fr) {\n                data.versions.push({\n                    language: \"fr\",\n                    metaTitle: values.metaTitleFR,\n                    title: values.titleFR,\n                    metaDescription: values.metaDescriptionFR,\n                    url: values.urlFR,\n                    visibility: \"Draft\",\n                    publishDate: values.publishDateFR,\n                    content: values.contentFR,\n                    alt: values.altFR,\n                    keywords: values.keywordsFR,\n                    category: values.categoryFR,\n                    highlights: values.highlightsFR,\n                    faqTitle: values.faqTitleFR || \"\",\n                    faq: values.faqFR || []\n                });\n            }\n            if (isSavedArticleRef.current != \"\") {\n                useUpdateAutoSaveHook.mutate({\n                    data: data,\n                    id: isSavedArticleRef.current\n                });\n            } else {\n                if (data.versions.length > 0) {\n                    useCreateAutoSaveHook.mutate({\n                        data\n                    }, {\n                        onSuccess: (data)=>{\n                            setIsSavedArticle(data.articleId);\n                            localStorage.setItem(\"savedArticle\", data.articleId);\n                        }\n                    });\n                }\n            }\n        } catch (error) {\n            console.warn(\"Auto-save failed:\", error);\n        }\n    };\n    // Optimized debounce timing: 2 minutes instead of 2 minutes (120000ms)\n    const handleChange = lodash_debounce__WEBPACK_IMPORTED_MODULE_15___default()(autosave, 30000); // 30 seconds for better UX\n    const handleClear = ()=>{\n        formikRefAll.current?.resetForm();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"heading-h2 semi-bold\",\n                children: t(\"createArticle:addArticle\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                lineNumber: 473,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"container\",\n                className: \"recent-application-pentabell\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `main-content`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"commun\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"label-form\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: selectedLanguages.en,\n                                                onChange: ()=>setSelectedLanguages((prev)=>({\n                                                            ...prev,\n                                                            en: !prev.en\n                                                        }))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"English\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"label-form\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: selectedLanguages.fr,\n                                                onChange: ()=>setSelectedLanguages((prev)=>({\n                                                            ...prev,\n                                                            fr: !prev.fr\n                                                        }))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"French\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                        lineNumber: 492,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                lineNumber: 478,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                id: \"experiences\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    id: \"form\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.Formik, {\n                                        initialValues: initialValues,\n                                        validationSchema: validationSchema,\n                                        innerRef: formikRefAll,\n                                        onSubmit: handleSubmit,\n                                        className: \"formik-form\",\n                                        children: (param)=>{\n                                            let { errors, touched, setFieldValue, values } = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.Form, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        id: \"accordion\",\n                                                        disableGutters: true,\n                                                        expanded: expanded === `panel`,\n                                                        onChange: handleChangeAccordion(`panel`),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                    lineNumber: 525,\n                                                                    columnNumber: 39\n                                                                }, void 0),\n                                                                \"aria-controls\": `panel-content`,\n                                                                id: `panel-header`,\n                                                                children: t(\"createArticle:settings\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                lineNumber: 524,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"accordion-detail\",\n                                                                elevation: 0,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"inline-group\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                    className: \"label-form\",\n                                                                                    children: [\n                                                                                        \"Robots meta\",\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                            className: \"select-pentabell\",\n                                                                                            variant: \"standard\",\n                                                                                            sx: {\n                                                                                                m: 1,\n                                                                                                minWidth: 120\n                                                                                            },\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                                value: _utils_constants__WEBPACK_IMPORTED_MODULE_5__.RobotsMeta.filter((option)=>values.robotsMeta === option),\n                                                                                                selected: values.robotsMeta,\n                                                                                                onChange: (event)=>{\n                                                                                                    setFieldValue(\"robotsMeta\", event.target.value);\n                                                                                                },\n                                                                                                children: _utils_constants__WEBPACK_IMPORTED_MODULE_5__.RobotsMeta.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                                        value: item,\n                                                                                                        children: item\n                                                                                                    }, item, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                                        lineNumber: 558,\n                                                                                                        columnNumber: 41\n                                                                                                    }, undefined))\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                                lineNumber: 545,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                            lineNumber: 540,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.ErrorMessage, {\n                                                                                            className: \"label-error\",\n                                                                                            name: \"robotsMeta\",\n                                                                                            component: \"div\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                            lineNumber: 564,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                    lineNumber: 538,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                lineNumber: 537,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_17__.ErrorMessage, {\n                                                                                name: \"robotsMeta\",\n                                                                                component: \"div\",\n                                                                                className: \"label-error\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                                lineNumber: 572,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                        lineNumber: 536,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                    lineNumber: 535,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                lineNumber: 531,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, `panel`, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    selectedLanguages.en && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddArticleEN__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        errors: errors,\n                                                        touched: touched,\n                                                        setFieldValue: setFieldValue,\n                                                        values: values,\n                                                        onImageSelect: handleImageSelect,\n                                                        debounce: handleChange,\n                                                        categories: categoriesEN,\n                                                        filteredCategories: filteredCategoriesEN,\n                                                        onCategoriesSelect: handleCategoriesENSelect\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                        lineNumber: 582,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    selectedLanguages.fr && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddArticleFR__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        errors: errors,\n                                                        touched: touched,\n                                                        setFieldValue: setFieldValue,\n                                                        values: values,\n                                                        onImageSelect: handleImageSelect,\n                                                        categories: categoriesFR,\n                                                        filteredCategories: filteredCategoriesFR,\n                                                        onCategoriesSelect: handleCategoriesFRSelect,\n                                                        debounce: handleChange\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                        lineNumber: 595,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"btn-container\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                type: \"button\",\n                                                                text: \"Clear\",\n                                                                className: \"btn btn-filled\",\n                                                                onClick: handleClear\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                lineNumber: 608,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                type: \"submit\",\n                                                                text: \"Save\",\n                                                                className: \"btn btn-filled\",\n                                                                onClick: ()=>{}\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                                lineNumber: 614,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                        lineNumber: 607,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 21\n                                            }, undefined);\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                    lineNumber: 507,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                                lineNumber: 506,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                        lineNumber: 477,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                    lineNumber: 476,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\AddArticleFroala.jsx\",\n                lineNumber: 475,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(AddArticle, \"B+cNPaZHMPFNJqb0o7Rak/frCrI=\", false, function() {\n    return [\n        _features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useUpdateAutoSave,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useCreateArticle,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useCreateAutoSave,\n        _opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_9__.useSaveFile,\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_10__.useGetCategories\n    ];\n});\n_c = AddArticle;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AddArticle);\nvar _c;\n$RefreshReg$(_c, \"AddArticle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/AddArticleFroala.jsx\n"));

/***/ })

});