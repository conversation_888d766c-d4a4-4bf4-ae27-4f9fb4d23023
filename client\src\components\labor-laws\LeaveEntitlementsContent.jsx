import SvgCallendar from "@/assets/images/icons/yellow/yellowCalendar.svg";

const LeaveEntitlementsContent = ({ t, section }) => {
    if (!section || !section.data) return null;

    const {
        description,
        subTitle,
        subDescription,
        leaves = [],
        annualLeave,
        maternityLeave,
        paternityLeave,
        sickLeave,
        paidTimeOff,
        parentalLeave,
        bereavementLeave,
        pilgrimageleave,
        marriageLeave,
        casualLeave
    } = section.data;

    const renderList = (title, description) => (
        <div className="item">
            <p className="service-sub-title">{t(title)}</p>
            <p className="service-description paragraph">
                {Array.isArray(description)
                    ? description.map((desc, i) => <p key={i}>{t(desc)}</p>)
                    : <p>{t(description)}</p>}
            </p>
        </div>
    );



    const renderTextBlock = (content, className) => {
        if (!content) return null;
        return Array.isArray(content) ? (
            content.map((line, i) => (
                <p key={i} className={className}>
                    {t(line)}
                </p>
            ))
        ) : (
            <p className={className}>{t(content)}</p>
        );
    };

    return (
        <>
            <div id="labor-tn-laws" className="item">
                {renderTextBlock(description, "service-description paragraph")}
                {renderTextBlock(subTitle, "service-sub-title")}
                {renderTextBlock(subDescription, "service-description paragraph")}

                <div className="holidays-dates">
                    {leaves.map((leave, index) => (
                        <div className="item" key={index}>
                            <p className="title">
                                <SvgCallendar />
                                {t(leave.date)}
                            </p>
                            <p className="paragraph">{t(leave.title)}</p>
                        </div>
                    ))}
                </div>
            </div>

            {annualLeave && renderList(annualLeave.title, annualLeave.description)}
            {paidTimeOff && renderList(paidTimeOff.title, paidTimeOff.description)}
            {maternityLeave && renderList(maternityLeave.title, maternityLeave.description)}
            {paternityLeave && renderList(paternityLeave.title, paternityLeave.description)}
            {sickLeave && renderList(sickLeave.title, sickLeave.description)}
            {casualLeave && renderList(casualLeave.title, casualLeave.description)}
            {marriageLeave && renderList(marriageLeave.title, marriageLeave.description)}
            {bereavementLeave && renderList(bereavementLeave.title, bereavementLeave.description)}
            {parentalLeave && renderList(parentalLeave.title, parentalLeave.description)}
            {pilgrimageleave && renderList(pilgrimageleave.title, pilgrimageleave.description)}
        </>
    );
};

export default LeaveEntitlementsContent;
