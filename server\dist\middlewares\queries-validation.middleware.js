"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const validateQueries = async (request, response, next) => {
    const queries = request.query;
    const { pageNumber, pageSize } = queries;
    if (pageNumber || pageSize) {
        if (Number(pageNumber) < 1 || Number(pageSize) < 1)
            return response.status(406).end();
    }
    next();
};
exports.default = validateQueries;
//# sourceMappingURL=queries-validation.middleware.js.map