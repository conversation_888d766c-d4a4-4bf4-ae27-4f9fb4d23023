"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.scheduleCronJob = scheduleCronJob;
const node_cron_1 = __importDefault(require("node-cron"));
const article_service_1 = __importDefault(require("@/apis/article/article.service"));
const articleService = new article_service_1.default();
function scheduleCronJob() {
    node_cron_1.default.schedule('0 9 * * *', async () => {
        await articleService.updateBlogSitemap();
    });
    node_cron_1.default.schedule('0 0 * * *', articleService.checkAndUpdateVisibility, {
        scheduled: true,
        timezone: 'Africa/Tunis',
    });
}
//# sourceMappingURL=cron-job.js.map