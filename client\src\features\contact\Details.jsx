"use client";

import { useTranslation } from "react-i18next";
import {
  Button,
  Grid,

  Typography,

} from "@mui/material";

import { formatDate, formatResumeName } from "@/utils/functions";


import { API_URLS } from "@/utils/urls";
import { useGetContactById } from "./hooks/Contact.hooks";
import { LabelContactFields, TypeContactLabels } from "@/utils/constants";

const Details = ({ contactId }) => {
  const { t } = useTranslation();
  const { data: contactData, isLoading, error } = useGetContactById(contactId);
  const getCleanedContactData = (data) => {
    if (!data) return {};

    return Object.entries(data).reduce((acc, [key, value]) => {
      if (
        ["_id", "updatedAt", "__v", "to", "team", "message"].includes(key) ||
        value === null ||
        value === undefined ||
        value === ""
      ) {
        return acc;
      }
      if (key === "createdAt") {
        acc[key] = formatDate(value);
      } else {
        acc[key] = value;
      }

      return acc;
    }, {});
  };


  const cleanedContactData = getCleanedContactData(contactData);

  return (
    <div id="container" className="recent-application-pentabell">
      <div className={`main-content`}>
        
        <Typography variant="h4" className="heading-h2 semi-bold">
          {t("contact:contactDetails")} :{" "}
          {`( ${TypeContactLabels[contactData?.type]} )`}
        </Typography>
        <Grid container spacing={2} sx={{ marginTop: "10px", padding: "20px" }}>
          {Object.entries(cleanedContactData).map(([label, content]) => (
            <Grid item xs={12} sm={6} key={label}>
              <Typography variant="subtitle1" className="label-form">
                {LabelContactFields[label]}
              </Typography>
              {label === "resume" ? (
                <Typography variant="body1" color="textSecondary">
                  <Button
                    className=""
                    onClick={() =>
                      window.open(
                        `${process.env.NEXT_PUBLIC_BASE_API_URL}${API_URLS.files}/${content}`,
                        "_blank"
                      )
                    }
                  >
                    {" "}
                    {formatResumeName(
                      content,
                      contactData?.fullName
                        ? contactData?.fullName
                        : contactData?.firstName + contactData?.lastName
                    )}
                  </Button>
                </Typography>
              ) : label === "type" ? (
                <Typography variant="body1" color="textSecondary">
                  {TypeContactLabels[content]}
                </Typography>
              ) : (
                <Typography variant="body1" color="textSecondary">
                  {typeof content === "boolean"
                    ? content
                      ? "Yes"
                      : "No"
                    : content || ""}
                </Typography>
              )}
            </Grid>
          ))}
          <Grid item xs={12} sm={12} key={"message"}>
            <Typography variant="subtitle1" className="label-form">
              {"Message"}
            </Typography>
            <Typography variant="body1" color="textSecondary">
              {contactData?.message}
            </Typography>
          </Grid>
        </Grid>
  
      </div>{" "}
    </div>
  );
};

export default Details;
