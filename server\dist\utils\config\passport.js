"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const passport_1 = __importDefault(require("passport"));
const passport_google_oauth2_1 = require("passport-google-oauth2");
const passport_microsoft_1 = require("passport-microsoft");
const passport_linkedin_oauth2_1 = require("passport-linkedin-oauth2");
const user_model_1 = __importDefault(require("@/apis/user/user.model"));
const constants_1 = require("../helpers/constants");
const candidat_model_1 = __importDefault(require("@/apis/candidat/candidat.model"));
const alert_model_1 = __importDefault(require("@/apis/alert/alert.model"));
const socket_1 = require("./socket");
const settings_model_1 = __importDefault(require("../../apis/settings/settings.model"));
const login_records_model_1 = __importDefault(require("../../apis/auth/login-records/login-records.model"));
const functions_1 = require("../helpers/functions");
const http_exception_1 = __importDefault(require("../exceptions/http.exception"));
const messages_1 = require("../helpers/messages");
const User = user_model_1.default;
const Candidate = candidat_model_1.default;
const LoginRecord = login_records_model_1.default;
passport_1.default.use(new passport_google_oauth2_1.Strategy({
    clientID: process.env.GOOGLE_CLIENT_ID,
    clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    callbackURL: process.env.GOOGLE_CALLBACK_URL,
    passReqToCallback: true,
}, async function (request, accessToken, refreshToken, profile, done) {
    try {
        const { isSignup } = JSON.parse(request.query.state);
        let foundUser = await User.findOne({ email: new RegExp(`^${profile.emails[0].value}$`, 'i') });
        if (isSignup && foundUser)
            return done(new Error('User already exists. Please log in.'));
        if (!isSignup && !foundUser)
            return done(new Error('User does not exist. Please sign up.'));
        if (foundUser === null) {
            return done(new Error('User not found in the database.'));
        }
        if (foundUser.isArchived === true) {
            throw new http_exception_1.default(403, messages_1.MESSAGES.AUTH.ARCHIVED);
        }
        if (!foundUser) {
            foundUser = await User.create({
                firstName: profile.given_name,
                lastName: profile.family_name,
                email: profile.email,
                isActive: true,
                roles: [constants_1.Role.CANDIDATE],
            });
            if (profile?.picture || profile?.photos[0]?.value)
                await (0, functions_1.downloadGoogleProfilePhoto)(profile?.picture || profile?.photos[0]?.value, foundUser);
            const createdCandidate = await Candidate.create({
                user: foundUser._id,
                emails: [profile.email],
                profilePercentage: 20,
            });
            const defaultUserSettings = {
                user: foundUser._id,
                notifications: {
                    newJobAlerts: {
                        email: true,
                        website: true,
                    },
                    appliedJobStatusUpdates: {
                        email: true,
                        website: true,
                    },
                    newsLetter: {
                        email: true,
                        website: true,
                    },
                },
            };
            await settings_model_1.default.create(defaultUserSettings);
            await LoginRecord.create({ user: foundUser._id, type: 'register' });
            const alertData = {
                isActive: true,
            };
            const createdAlert = await alert_model_1.default.create({
                ...alertData,
                createdBy: foundUser._id,
            });
            await user_model_1.default.findByIdAndUpdate(foundUser._id, { alerts: createdAlert._id, candidate: createdCandidate._id });
            const notificationMessage = 'Welcome to Pentabell, we are happy to see you here!';
            await (0, socket_1.sendNotification)({
                receiver: foundUser._id,
                sender: null,
                message: notificationMessage,
                link: `${process.env.LOGIN_LINK}`,
            });
        }
        return done(null, foundUser);
    }
    catch (error) {
        console.error(error);
        return done(error);
    }
}));
passport_1.default.use(new passport_microsoft_1.Strategy({
    clientID: process.env.MICROSOFT_CLIENT_ID,
    clientSecret: process.env.MICROSOFT_CLIENT_SECRET,
    callbackURL: process.env.MICROSOFT_CALLBACK_URL,
    scope: ['user.read'],
    tenant: 'common',
    authorizationURL: process.env.MICROSOFT_AUTHORIZATION_URL,
    tokenURL: process.env.MICROSOFT_TOKEN_URL,
    passReqToCallback: true,
}, async function (request, accessToken, refreshToken, profile, done) {
    try {
        const { isSignup } = JSON.parse(request.query.state);
        let foundUser = await User.findOne({
            email: profile._json.mail ? new RegExp(`^${profile._json.mail}$`, 'i') : new RegExp(`^${profile._json.userPrincipalName}$`, 'i'),
        });
        if (isSignup && foundUser)
            return done(new Error('User already exists. Please log in.'));
        if (!isSignup && !foundUser)
            return done(new Error('User does not exist. Please sign up.'));
        if (foundUser === null) {
            return done(new Error('User not found in the database.'));
        }
        if (foundUser.isArchived === true) {
            throw new http_exception_1.default(403, messages_1.MESSAGES.AUTH.ARCHIVED);
        }
        if (!foundUser) {
            foundUser = await User.create({
                firstName: profile.name.givenName,
                lastName: profile.name.familyName,
                email: profile._json.mail || profile._json.userPrincipalName,
                isActive: true,
                roles: [constants_1.Role.CANDIDATE],
                phone: profile._json.mobilePhone === null ? undefined : profile._json.mobilePhone,
            });
            const createdCandidate = await Candidate.create({
                user: foundUser._id,
                emails: profile._json.mail ? [profile._json.mail] : profile._json.userPrincipalName ? [profile._json.userPrincipalName] : [],
                profilePercentage: 20,
            });
            const defaultUserSettings = {
                user: foundUser._id,
                notifications: {
                    newJobAlerts: {
                        email: true,
                        website: true,
                    },
                    appliedJobStatusUpdates: {
                        email: true,
                        website: true,
                    },
                    newsLetter: {
                        email: true,
                        website: true,
                    },
                },
            };
            await settings_model_1.default.create(defaultUserSettings);
            await LoginRecord.create({ user: foundUser._id, type: 'register' });
            const alertData = {
                isActive: true,
            };
            const createdAlert = await alert_model_1.default.create({
                ...alertData,
                createdBy: foundUser._id,
            });
            await user_model_1.default.findByIdAndUpdate(foundUser._id, { alerts: createdAlert._id, candidate: createdCandidate._id });
            const notificationMessage = 'Welcome to Pentabell, we are happy to see you here!';
            await (0, socket_1.sendNotification)({
                receiver: foundUser._id,
                sender: null,
                message: notificationMessage,
                link: `${process.env.LOGIN_LINK}`,
            });
        }
        return done(null, foundUser);
    }
    catch (error) {
        console.error(error);
        return done(error);
    }
}));
passport_1.default.use(new passport_linkedin_oauth2_1.Strategy({
    clientID: process.env.LINKEDIN_CLIENT_ID,
    clientSecret: process.env.LINKEDIN_CLIENT_SECRET,
    callbackURL: process.env.LINKEDIN_CALLBACK_URL,
    scope: ['r_emailaddress', 'r_liteprofile'],
}, function (token, tokenSecret, profile, done) {
    process.nextTick(function () {
        console.log(profile);
        return done(null, profile);
    });
}));
passport_1.default.serializeUser((user, done) => {
    done(null, user.id);
});
passport_1.default.deserializeUser(async (id, done) => {
    try {
        const user = await User.findById(id);
        done(null, user);
    }
    catch (error) {
        done(error);
    }
});
exports.default = passport_1.default;
//# sourceMappingURL=passport.js.map