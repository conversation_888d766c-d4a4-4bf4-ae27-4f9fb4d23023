import { useState } from "react";
import { useTranslation } from "react-i18next";
import {
  Grid,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Tabs,
  Tab,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { useGetCandidatById } from "../hooks/Candidatures.hooks";
import { useRouter } from "next/navigation";
import { formatDate, formatResumeName } from "@/utils/functions";
import { API_URLS } from "@/utils/urls";

import upload from "@/assets/images/profilePic.png";
import TabPanel from "@/components/ui/TabPanel";
import ApplicationsCandidat from "../../application/component/ApplicationsCandidat";

const CandidateInfos = ({ IdCandidat }) => {
  const { t } = useTranslation();
  const router = useRouter();
  const [selectedImage, setSelectedImage] = useState(null);
  const [expanded, setExpanded] = useState(`panel0`);
  const [tabValue, setTabValue] = useState(0);

  const { data, isLoading, isError, error } = useGetCandidatById(IdCandidat);

  if (isLoading) {
    return <Typography>Loading...</Typography>;
  }

  if (isError) {
    return <Typography color="error">Error: {error.message}</Typography>;
  }

  if (!data) {
    return <Typography>No candidate data found.</Typography>;
  }

  const {
    user,
    skills,
    industries,
    industry,
    experiences,
    certifications,
    educations,
  } = data;

  const handleChange = (panel) => (event, newExpanded) => {
    setExpanded(newExpanded ? panel : false);
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  return (
    <>
      {" "}
      <Tabs
        value={tabValue}
        onChange={handleTabChange}
        aria-label="Candidate Information  Tabs "
        centered
      >
        <Tab className="label-form" label={t("personalInformation:title")} />
        <Tab
          className="label-form"
          label={t("candidate:professionalInformation")}
        />
        <Tab className="label-form" label={t("candidate:Applications")} />
      </Tabs>
      <div id="container" className="recent-application-pentabell">
        <div className="main-content">
          <TabPanel value={tabValue} index={0}>
            <Grid
              container
              spacing={2}
              sx={{ marginTop: "10px", padding: "20px" }}
            >
              <div className="commun profile-infos">
                <div className="inline-group uploads">
                  <div className="upload-container">
                    <label htmlFor="image-upload">
                      <div className="upload-area">
                        <div className="profile-container">
                          <div
                            className="icon-pic"
                            style={{
                              backgroundImage: `url("${
                                selectedImage || user?.profilePicture
                                  ? `${process.env.NEXT_PUBLIC_BASE_API_URL}${API_URLS.files}/${user?.profilePicture}`
                                  : upload?.src
                              }")`,
                              backgroundSize: "cover",
                              backgroundRepeat: "no-repeat",
                              backgroundPosition: "center",
                            }}
                          ></div>
                          <div>
                            <p className="label">{t("profilePicture")}</p>
                          </div>
                        </div>
                      </div>
                    </label>
                  </div>
                </div>
              </div>
              <Grid item xs={12} sm={6}></Grid>

              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1" className="label-form">
                  {t("application:email")}
                </Typography>
                <Typography variant="body1" color="textSecondary">
                  {user?.email}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1" className="label-form">
                  {t("application:phone")}
                </Typography>
                <Typography variant="body1" color="textSecondary">
                  {user?.phone}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1" className="label-form">
                  {t("application:fullName")}
                </Typography>
                <Typography variant="body1" color="textSecondary">
                  {user?.firstName} {user?.lastName}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1" className="label-form">
                  {t("application:jobTitle")}
                </Typography>
                <Typography variant="body1" color="textSecondary">
                  {user?.jobTitle}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1" className="label-form">
                  {t("application:Country")}
                </Typography>
                <Typography variant="body1" color="textSecondary">
                  {user?.country}
                </Typography>
              </Grid>

                        <Grid item xs={12} sm={6}>
                            <Typography variant="subtitle1" className="label-form">
                                {t("application:Nationalities")}
                            </Typography>
                            <Typography variant="body1" color="textSecondary">
                                {user?.nationalities?.join(", ")}
                            </Typography>
                        </Grid>

              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1" className="label-form">
                  {t("application:dateOfBirth")}
                </Typography>
                <Typography variant="body1" color="textSecondary">
                  {user?.dateOfBirth ? formatDate(user?.dateOfBirth) : "N/A"}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1" className="label-form">
                  {t("application:active")}
                </Typography>
                <Typography variant="body1" color="textSecondary">
                  {user?.isActive ? "Yes" : "No"}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1" className="label-form">
                  {t("application:archived")}
                </Typography>
                <Typography variant="body1" color="textSecondary">
                  {user?.isArchived ? "Yes" : "No"}
                </Typography>
              </Grid>
            </Grid>
          </TabPanel>

          <TabPanel value={tabValue} index={1}>
            <Grid
              container
              spacing={2}
              sx={{ marginTop: "10px", padding: "20px" }}
            >
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1" className="label-form">
                  {t("application:skills")}
                </Typography>
                <Typography variant="body1" color="textSecondary">
                  {skills.join(", ")}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1" className="label-form">
                  {t("application:industrie")}
                </Typography>
                <Typography variant="body1" color="textSecondary">
                  {industry}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1" className="label-form">
                  {t("application:Industries")}
                </Typography>
                <Typography variant="body1" color="textSecondary">
                  {industries.join(", ")}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}></Grid>

              <Grid item xs={12} sm={6}>
                <div id="experiences" className="commun">
                  <Typography variant="subtitle1" className="label-form">
                    {t("candidate:experiences")}
                  </Typography>
                  {experiences && experiences?.length > 0 ? (
                    experiences?.map((experience, index) => (
                      <Accordion
                        id="accordion"
                        key={experience._id}
                        expanded={expanded === index}
                        onChange={handleChange(index)}
                      >
                        <AccordionSummary
                          expandIcon={<ExpandMoreIcon />}
                          aria-controls="panel-content"
                          id="panel-header"
                        >
                          {`Experience ${index + 1}`}
                        </AccordionSummary>
                        <AccordionDetails className="accordion-detail">
                          <table className="table-sm custom-table">
                            <tbody>
                              <tr className="spacement">
                                <td className="titleTable">
                                  {t("experience:Name")}
                                </td>
                                <td className="titleTable">
                                  {t("experience:company")}
                                </td>
                                <td className="titleTable">
                                  {t("experience:contractType")}
                                </td>
                              </tr>
                              <tr>
                                <td>{experience.title}</td>
                                <td>{experience.company}</td>
                                <td>{experience.contractType}</td>
                              </tr>
                            </tbody>
                          </table>
                        </AccordionDetails>
                      </Accordion>
                    ))
                  ) : (
                    <Typography variant="body1">
                      {t("application:noExperiences")}
                    </Typography>
                  )}
                </div>
              </Grid>
              <Grid item xs={12} sm={6}>
                <div id="experiences" className="commun">
                  <Typography variant="subtitle1" className="label-form">
                    {t("candidate:certfications")}
                  </Typography>
                  {certifications && certifications?.length > 0 ? (
                    certifications?.map((certification, index) => (
                      <Accordion
                        id="accordion"
                        key={certification._id}
                        expanded={expanded === `certification-${index}`}
                        onChange={handleChange(`certification-${index}`)}
                      >
                        <AccordionSummary
                          expandIcon={<ExpandMoreIcon />}
                          id="panel-header"
                        >
                          {`Certification ${index + 1}`}
                        </AccordionSummary>
                        <AccordionDetails className="accordion-detail">
                          <table className="table-sm custom-table">
                            <tbody>
                              <tr>
                                <td className="titleTable">
                                  {t("certification:Name")}
                                </td>
                              </tr>
                              <tr className="spacement">
                                <td>{certification.title}</td>
                              </tr>
                            </tbody>
                          </table>
                          <br />
                          <table className="table-sm custom-table">
                            <tbody>
                              <tr>
                                <td className="titleTable">
                                  {t("certification:academy")}
                                </td>
                                <td className="titleTable">
                                  {t("certification:startdate")}
                                </td>
                                <td className="titleTable">
                                  {t("certification:enddate")}
                                </td>
                              </tr>
                              <tr>
                                <td>{certification.academy}</td>
                                <td>{formatDate(certification.startDate)}</td>
                                <td>{formatDate(certification.endDate)}</td>
                              </tr>
                            </tbody>
                          </table>
                        </AccordionDetails>
                      </Accordion>
                    ))
                  ) : (
                    <Typography variant="body1" color="textSecondary">
                      {t("certification:noCertifications")}
                    </Typography>
                  )}
                </div>
              </Grid>
              <Grid item xs={12} sm={6}>
                <div id="experiences" className="commun">
                  <Typography variant="subtitle1" className="label-form">
                    {t("candidate:educations")}
                  </Typography>
                  {educations && educations?.length > 0 ? (
                    educations?.map((education, index) => (
                      <Accordion
                        id="accordion"
                        key={education?._id}
                        expanded={expanded === `education-${index}`}
                        onChange={handleChange(`education-${index}`)}
                      >
                        <AccordionSummary
                          expandIcon={<ExpandMoreIcon />}
                          aria-controls="panel-content"
                          id="panel-header"
                        >
                          {`Education ${index + 1}`}
                        </AccordionSummary>
                        <AccordionDetails className="accordion-detail">
                          <table className="table-sm custom-table">
                            <tbody>
                              <tr>
                                <td className="titleTable">
                                  {" "}
                                  {t("education:degree")}{" "}
                                </td>
                                <td className="titleTable">
                                  {" "}
                                  {t("education:university")}{" "}
                                </td>
                                <td className="titleTable">
                                  {" "}
                                  {t("education:fieldofstudy")}{" "}
                                </td>
                              </tr>
                              <tr className="spacement">
                                <td>{education.degree}</td>
                                <td>{education.university}</td>
                                <td>{education.fieldOfStudy}</td>
                              </tr>
                            </tbody>
                          </table>
                          <br />
                          <table class=" table-sm custom-table">
                            <tbody>
                              <tr>
                                <td className="titleTable">
                                  {" "}
                                  {t("education:startdate")}{" "}
                                </td>
                                <td className="titleTable">
                                  {t("education:enddate")}{" "}
                                </td>
                                <td></td>
                              </tr>
                              <tr>
                                <td>{formatDate(education.startDate)}</td>
                                <td>{formatDate(education.endDate)}</td>
                              </tr>
                            </tbody>
                          </table>
                        </AccordionDetails>
                      </Accordion>
                    ))
                  ) : (
                    <Typography variant="body1" color="textSecondary">
                      {t("education:noEducations")}
                    </Typography>
                  )}
                </div>
              </Grid>
            </Grid>
          </TabPanel>
          <TabPanel value={tabValue} index={2}>
            <ApplicationsCandidat IdCandidat={IdCandidat} />
          </TabPanel>
        </div>
      </div>
    </>
  );
};

export default CandidateInfos;
