"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const constants_1 = require("@/utils/helpers/constants");
const guideVersionShema = new mongoose_1.Schema({
    language: { type: String, enum: constants_1.Language },
    title: { type: String },
    keywords: { type: [String] },
    metaTitle: { type: String, default: '' },
    metaDescription: { type: String, default: '' },
    url: { type: String },
    alt: { type: String, default: '' },
    image: { type: String },
    file: { type: String },
    content: { type: String },
    visibility: {
        type: String,
        enum: constants_1.Visibility,
        default: constants_1.Visibility.Draft,
    },
    category: [{ type: mongoose_1.Types.ObjectId, ref: 'CategoryGuide' }],
    service: [{ type: mongoose_1.Types.ObjectId, ref: 'Category' }],
    publishDate: { type: Date },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now },
    highlights: { type: [String] },
    description: { type: String },
    isArchived: { type: Boolean, default: false },
    guideList: {
        itemTitle: { type: String },
        itemDescription: { type: String },
        subtitles: [
            {
                title: { type: String },
                description: { type: String },
            },
        ],
    },
});
const guideSchema = new mongoose_1.Schema({
    versionsguide: { type: [guideVersionShema] },
    robotsMeta: {
        type: String,
        enum: constants_1.robotsMeta,
        default: constants_1.robotsMeta.index,
    },
    isArticle: { type: Boolean },
    createdBy: { type: mongoose_1.Types.ObjectId, ref: 'User' },
    cible: {
        type: String,
        enum: constants_1.Cible,
        default: constants_1.Cible.client,
    },
    totalDownload: { type: Number, default: 0 },
    mailBanner: { type: String },
    isArchived: { type: Boolean, default: false },
}, {
    timestamps: true,
    toJSON: {
        transform: function (doc, ret) {
            delete ret.__v;
        },
    },
});
exports.default = (0, mongoose_1.model)('Guide', guideSchema);
//# sourceMappingURL=guide.model.js.map