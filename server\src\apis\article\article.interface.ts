import { Language, Visibility, robotsMeta } from '@/utils/helpers/constants';
import mongoose from 'mongoose';

export interface FaqItemI {
    question: string;
    answer: string;
}

export interface CTSBannerI {
    image: string;
    link: string;
}

export interface ArticleVersion {
    language: Language;
    title: string;
    image: string;
    keywords: string[];
    metaTitle: string;
    metaDescription: string;
    url: string;
    _id: any;
    category: mongoose.Types.ObjectId[];
    alt?: string;
    visibility: Visibility;
    publishDate: Date;
    shareOnSocialMedia: boolean;
    isArchived: boolean;
    content: string;
    canonical: string;
    createdAt: Date;
    updatedAt: Date;
    highlights: string[];
    description: string;
    faqTitle: string;
    faq: FaqItemI[];
    ctsBanner: CTSBannerI;
}

export interface ArticleI {
    _id: string;
    versions: ArticleVersion[];
    title: string;
    url: string;
    image: string;
    visibility: Visibility;
    totalCommentaires: number;
    publishDate?: Date;
    tags: string[];
    category: mongoose.Types.ObjectId[];
    subcategory: string | null;
    alt: string;
    isArchived: boolean;
    language: Language;
    shareOnSocialMedia: boolean;
    robotsMeta: robotsMeta;
    createdAt: Date;
    createdBy: any;
    updatedAt: Date;
}

export { Language, Visibility, robotsMeta };
