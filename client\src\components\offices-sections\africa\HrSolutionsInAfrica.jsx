import HrSolutionsSection from "@/components/ui/HrSolutionsSection";

function HrSolutionsInAfrica({ t }) {
  const sections = [];
  let i = 1;
  while (t(`africa:whyPentabell:s${i}:title`, { defaultValue: "" })) {
    sections.push({
      title: t(`africa:whyPentabell:s${i}:title`),
      description: t(`africa:whyPentabell:s${i}:description`)
    });
    i++;
  }

  return (
    <HrSolutionsSection
      title={t("africa:whyPentabell:title")}
      items={sections}
    />
  );
}

export default HrSolutionsInAfrica;
