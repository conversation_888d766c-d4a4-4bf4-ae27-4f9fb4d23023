
import { useTranslation } from "react-i18next";

import StatusCard from "@/components/ui/charts/StatusCard";
import job from "@/assets/images/applicationsdashboard.png";
import expiredIcon from "@/assets/images/rejectedicon.png";
import pendingIcon from "@/assets/images/pendingIcon.png";
import activeIcon from "@/assets/images/activedashboard.png";

const ApplicationsStatusChart = ({ accepted, pending, rejected, totalApplications }) => {
  const { t } = useTranslation();

  return (
    <div className="stats-container">
      <StatusCard
        iconSrc={job.src}
        iconAlt="job"
        title={t("statisticsApp:myApplications")}
        value={totalApplications}
        cardClassName="stats-job"
      />

      <StatusCard
        iconSrc={activeIcon.src}
        iconAlt="accepted"
        iconClassName="stats-icon2"
        title={t("statisticsApp:accepted")}
        value={accepted}
        cardClassName="stats-active"
      />

      <StatusCard
        iconSrc={expiredIcon.src}
        iconAlt="rejected"
        iconClassName="stats-icon-rejected"
        title={t("statisticsApp:rejected")}
        value={rejected}
        cardClassName="stats-expired"
      />

      <StatusCard
        iconSrc={pendingIcon.src}
        iconAlt="pending"
        iconClassName="stats-icon1"
        title={t("statisticsApp:pending")}
        value={pending}
        cardClassName="stats-views"
      />
    </div>
  );
};

export default ApplicationsStatusChart;
