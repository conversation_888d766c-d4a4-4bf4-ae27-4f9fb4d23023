"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommentDTO = void 0;
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const user_dto_1 = require("../user/user.dto");
class CommentDTO {
    constructor(comment) {
        Object.assign(this, comment);
    }
}
exports.CommentDTO = CommentDTO;
__decorate([
    (0, class_transformer_1.Expose)({ name: '_id' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CommentDTO.prototype, "id", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'email' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CommentDTO.prototype, "email", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'user' }),
    (0, class_transformer_1.Type)(() => user_dto_1.UserResponseForCommentDTO),
    __metadata("design:type", user_dto_1.UserResponseForCommentDTO)
], CommentDTO.prototype, "user", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'firstName' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CommentDTO.prototype, "name", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'comment' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CommentDTO.prototype, "content", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'approved' }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CommentDTO.prototype, "approved", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'responses' }),
    (0, class_transformer_1.Type)(() => CommentDTO),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], CommentDTO.prototype, "responses", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'badge' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CommentDTO.prototype, "badge", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Number)
], CommentDTO.prototype, "updatedAt", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Number)
], CommentDTO.prototype, "article", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Number)
], CommentDTO.prototype, "__v", void 0);
//# sourceMappingURL=commentaires.dto.js.map