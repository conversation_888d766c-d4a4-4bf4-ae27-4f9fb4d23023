"use client";

import { Grid, useMediaQuery, useTheme } from "@mui/material";
import { useState } from "react";
import SvgArrow from "@/assets/images/icons/arrow.svg";
import Link from "next/link";

function OurGlobalHRServices({ SERVICES, title, defaultService, ksa }) {
  const [serviceItem, setServiceItem] = useState(defaultService);

  const theme = useTheme();

  const isWeb = useMediaQuery(theme.breakpoints.down("lg"));

  const getServiceById = (id) => {
    const service = SERVICES.find((service) => service.id === id);
    setServiceItem(service);
  };

  return (
    <div id="global-hr-services-website" className={ksa ? "ksa" : ""}>
      <Grid item xs={12} sm={12}>
        <h2 className="heading-h1 text-white text-center">{title}</h2>
      </Grid>
      <div className={`services-list ${ksa === true ? "ksa" : ""}`}>
        {SERVICES.map((item) => {
          return (
            <button
              className={
                serviceItem?.id === item.id
                  ? "service-item-yellow"
                  : "service-item"
              }
              key={item.id}
              onClick={() => getServiceById(item.id)}
            >
              {item.icon}
              <p className="service-title">{item.title}</p>
            </button>
          );
        })}
      </div>
      {serviceItem && (
        <>
          {!isWeb && serviceItem.bgIcon}
          <div className="service">
            <div className="service-content">
              {serviceItem.bulletPoints ? (
                <>
                  <p className="service-subtitle">{serviceItem.description}</p>
                  <p className="service-content-title">{serviceItem.title}</p>

                  <ul className="list-arrow">
                    {serviceItem.bulletPoints.map((bullet) => (
                      <li className="item-arrow">{bullet}</li>
                    ))}
                  </ul>
                </>
              ) : (
                <>
                  {" "}
                  <p className="service-content-title">{serviceItem.title}</p>
                  <p className="service-description">
                    {serviceItem.description}
                  </p>
                </>
              )}
              <Link
                href={serviceItem.link}
                style={{ textDecoration: "none", cursor: "pointer" }}
                className={"btn btn-ghost text-yellow"}
              >
                {serviceItem.linkText}
                <SvgArrow />
              </Link>
              {/* <CustomButton
                text={serviceItem.linkText}
                className={"btn btn-ghost text-yellow"}
                icon={<SvgArrow />}
                link={serviceItem.link}
              /> */}
            </div>
            <div className="service-content-image">
              <div
                style={{
                  backgroundImage: `url(${serviceItem.img.src})`,
                }}
              />
              <img
                width={0}
                height={0}
                alt={serviceItem.alt}
                style={{ display: "none" }}
                loading="lazy"
              />
            </div>
          </div>
        </>
      )}
    </div>
  );
}

export default OurGlobalHRServices;
