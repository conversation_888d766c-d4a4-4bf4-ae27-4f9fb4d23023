import blogIMG4 from "@/assets/images/website/banner/HR-Management-Services-Pentabell.webp";
import blogIMG3 from "@/assets/images/website/banner/HR-Services-Pentabell.webp";
import blogIMG3Mobile from "@/assets/images/website/banner/mobile-banner/HR-Services-Pentabell.webp";
import blogIMG4Mobile from "@/assets/images/website/banner/mobile-banner/HR-Management-Services-Pentabell.webp";
import { websiteRoutesList } from "@/helpers/routesList";
import initTranslations from "@/app/i18n";
import CustomEmblaCarousel from "@/components/ui/emblaCarousel/CustomEmblaCarousel";

export default async function HomeSlider({ locale, isMobileSSR }) {
  const { t } = await initTranslations(locale, ["homePage"]);

  const staticSliders = [
    {
      title: t("homePage:banner:banner4"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.directHiring.route}`,
      img: blogIMG4.src,
      altImg: t("homePage:banner:alt4"),
      imgMobile: blogIMG4Mobile.src,
    },
    {
      title: t("homePage:banner:banner3"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.payrollServices.route}`,
      img: blogIMG3.src,
      altImg: t("homePage:banner:alt3"),
      imgMobile: blogIMG3Mobile.src,
    },
  ];

  let sliders = [];
  let isStatic = false;

  try {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_BASE_API_URL}/sliders?visibility=Public&language=${locale}&isArchived=false`
    );
    const data = await res.json();

    const filtered = data.sliders
      .filter((slider) => slider.existingLanguages.includes(locale))
      .map((slider) => slider.versionslide[0]);

    if (filtered.length === 0) {
      sliders = staticSliders;
      isStatic = true;
    } else {
      sliders = filtered;
    }
  } catch (error) {
    console.error("Error fetching sliders:", error);
    sliders = staticSliders;
    isStatic = true;
  }

  const slides = sliders.map(
    ({ title, link, linkBtn, img, altImg, imgMobile }) => ({
      title,
      link,
      linkBtn,
      img: isStatic
        ? img
        : `${process.env.NEXT_PUBLIC_BASE_API_URL}/files/${img}`,
      altImg,
      imgMobile: isStatic
        ? imgMobile
        : `${process.env.NEXT_PUBLIC_BASE_API_URL}/files/${imgMobile}`,
    })
  );

  return (
    <CustomEmblaCarousel
      isMobileSSR={isMobileSSR}
      slides={slides}
      options={{ loop: true }}
      slideId="home__slider"
    />
  );
}
