"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationController = void 0;
const express_1 = require("express");
const authentication_middleware_1 = __importDefault(require("@/middlewares/authentication.middleware"));
const notification_service_1 = require("./notification.service");
const validateApiKey_middleware_1 = __importDefault(require("@/middlewares/validateApiKey.middleware"));
class NotificationController {
    constructor() {
        this.path = '/notifications';
        this.router = (0, express_1.Router)();
        this.notificationService = new notification_service_1.NotificationService();
        this.get = async (request, response, next) => {
            try {
                const currentUser = request.user;
                const query = request.query;
                response.send(await this.notificationService.get(currentUser, query));
            }
            catch (error) {
                next(error);
            }
        };
        this.update = async (request, response, next) => {
            try {
                const { notificationId } = request.params;
                const dataToUpdate = request.body;
                response.status(204).send(await this.notificationService.update(notificationId, dataToUpdate));
            }
            catch (error) {
                next(error);
            }
        };
        this.markAllAsRead = async (request, response, next) => {
            try {
                const currentUser = request.user;
                response.status(204).send(await this.notificationService.markAllAsRead(currentUser));
            }
            catch (error) {
                next(error);
            }
        };
        this.initialiseRoutes();
    }
    initialiseRoutes() {
        this.router.get(`${this.path}`, validateApiKey_middleware_1.default, authentication_middleware_1.default, this.get);
        this.router.put(`${this.path}`, authentication_middleware_1.default, this.markAllAsRead);
        this.router.put(`${this.path}/:notificationId`, authentication_middleware_1.default, this.update);
    }
}
exports.NotificationController = NotificationController;
//# sourceMappingURL=notications.controller.js.map