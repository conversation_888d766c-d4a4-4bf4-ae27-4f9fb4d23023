"use client";
import { Container, useMediaQuery } from "@mui/material";
import { useTheme } from "@mui/material";
import { useTranslation } from "react-i18next";
import dynamic from "next/dynamic";

import { OFFICES_COUNTRIES_LIST, OFFICES_ZONE_LIST } from "@/config/countries";

import OurGlobalHRServices from "@/components/pages/sites/sections/OurGlobalHRServices";

// import MpSvgComponent from "./MpSvgComponent";
import CustomButton from "@/components/ui/CustomButton";
import OfficesListForContactPage from "./OfficesListForContactPage";
import Link from "next/link";
import { websiteRoutesList } from "@/helpers/routesList";

const MpSvgComponent = dynamic(() => import("./MpSvgComponent"), {
  ssr: false
});

function GlobalMap({ locale, SERVICES, title, defaultService }) {
  const { t } = useTranslation();

  const colors = [
    "oil-gaz",
    "energy",
    "banking-insurance",
    "transport",
    "it-telecom",
  ];

  let lastColor = "";

  function getRandomColor() {
    let randomColor;
    do {
      randomColor = colors[Math.floor(Math.random() * colors.length)];
    } while (randomColor === lastColor);
    lastColor = randomColor;
    return randomColor;
  }

  const handleRemoveAll = () => {
    const elements = document.querySelectorAll(".box-info");
    elements.forEach((element) => element.classList.remove("selected"));
    const AllSelectedPin = document.querySelectorAll(".pin");
    AllSelectedPin.forEach((element) => element.classList.remove("selected"));
  };

  const handleClick = (countryId, idPin) => {
    // Remove 'selected' class from all elements with class 'box-info'

    const elements = document.querySelectorAll(".box-info");
    const AllSelectedPin = document.querySelectorAll(".pin");
    AllSelectedPin.forEach((element) => element.classList.remove("selected"));

    elements.forEach((element) => element.classList.remove("selected"));

    // Add 'selected' class to the specific element by ID

    // Find the parent element by id
    const parentElement = document.getElementById(idPin);

    // Remove 'selected' class from all child elements with class 'pin'
    const allPins = parentElement.querySelectorAll(".pin");
    allPins.forEach((pin) => pin.classList.remove("selected"));

    // Find the child element with class 'pin' within the parent element
    const pinElement = parentElement.querySelector(".pin");

    // Add the class 'selected' to the child element
    if (pinElement) {
      pinElement.classList.add("selected");
      // add random class for color pin
      // Add a random class for color pin
      const randomColor = getRandomColor();
      pinElement.classList.add(randomColor); // Add random color class

      document.getElementById(countryId).classList.add("selected");
    }
  };

  const theme = useTheme();

  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  if (isMobile)
    return (
      <OfficesListForContactPage
        title={t("homePage:s2:title")}
        subtitle={t("homePage:s2:description")}
      >
        <OurGlobalHRServices
          SERVICES={SERVICES}
          title={title}
          defaultService={defaultService}
        />
      </OfficesListForContactPage>
    );
  else
    return (
      <div id="global-map">
        <Container className="custom-max-width">
          <h2 className="heading-h1 text-center text-white">
            {t("homePage:s2:title")}
          </h2>
          <p className="sub-heading text-center">
            {t("homePage:s2:description")}
          </p>
          <div id="btns-zone">
            {OFFICES_ZONE_LIST.map((item, index) => {
              return (
                <CustomButton
                  key={item.id}
                  id={index}
                  text={t(item.label)}
                  link={item.link}
                  className={"btn btn-zone"}
                />
              );
            })}
            <Link
              href={`/${websiteRoutesList.contact.route}/#contact-page-form`}
              className={"btn btn-zone"}
            >
              {t("contactUs:bureux:restOfTheWold")}
            </Link>
          </div>
          <div id="btns-country">
            {OFFICES_COUNTRIES_LIST.map((item, index) => {
              return !isMobile ? (
                <CustomButton
                  key={index}
                  text={t(item.label)}
                  onClick={() => {
                    locale === "fr"
                      ? handleClick(item.idFr, item.idPin)
                      : handleClick(item.id, item.idPin);
                  }}
                  className={"btn btn-country"}
                />
              ) : (
                <CustomButton
                  text={t(item.label)}
                  link={item.link}
                  className={"btn btn-country"}
                />
              );
            })}
          </div>
        </Container>
        <MpSvgComponent handleClick={handleClick} locale={locale} />
        <img
          width={0}
          height={0}
          alt={t("homePage:s2:map")}
          style={{ display: "none" }}
          loading="lazy"
        />
        <OurGlobalHRServices
          SERVICES={SERVICES}
          title={title}
          defaultService={defaultService}
        />
      </div>
    );
}

export default GlobalMap;
