"use client"; // si nécessaire, uniquement une fois en haut du fichier

import { Container } from "@mui/material";
import { useTranslation } from "react-i18next";
import useEmblaCarousel from "embla-carousel-react";
import Autoplay from "embla-carousel-autoplay";

// Partner logos (unique & propre)
import abbLogo from "../../../../../public/images/partners/logo-abb.webp";
import nokia<PERSON>ogo from "../../../../../public/images/partners/logo-nokia.webp";
import totalLogo from "../../../../../public/images/partners/logo-Total.webp";
import navalLogo from "../../../../../public/images/partners/logo-navalgrp.webp";
import alcatelLucent from "../../../../../public/images/partners/logo-Alcatel.webp";
import zte from "../../../../../public/images/partners/logo-ZTE.webp";
import john<PERSON>ock<PERSON>ll from "../../../../../public/images/partners/logo-johncockerill.webp";
import proximus from "../../../../../public/images/partners/logo-proximus.webp";
import visa from "../../../../../public/images/partners/logo-visa.webp";
import ARCELOR from "../../../../../public/images/partners/logo-arcelor.webp";
import TRACTEBEL from "../../../../../public/images/partners/logo-tractebel.webp";
import Samsung from "../../../../../public/images/partners/logo-samsung.webp";
import Weir from "../../../../../public/images/partners/logo-weir.webp";
import Butachimie from "../../../../../public/images/partners/logo-butachimie.webp";
import EGIS from "../../../../../public/images/partners/logo-egis.webp";
import systra from "../../../../../public/images/partners/logo-systra.webp";
import Equinix from "../../../../../public/images/partners/logo-equinix.webp";
import Lilly from "../../../../../public/images/partners/logo-lilly.webp";
import Saipem from "../../../../../public/images/partners/logo-saipem.webp";
import NEC from "../../../../../public/images/partners/logo-nec.webp";
import ColasRail from "../../../../../public/images/partners/logo-colas.webp";
import Omv from "../../../../../public/images/partners/logo-omv.webp";
import Alstom from "../../../../../public/images/partners/logo-Alstom.webp";
import ge from "../../../../../public/images/partners/logo-ge.webp";
import tecnimont from "../../../../../public/images/partners/logo-tecnimont.webp";
import siemens from "../../../../../public/images/partners/logo-siemens.webp";
import geocean from "../../../../../public/images/partners/logo-geocean.webp";
import lFB from "../../../../../public/images/partners/logo-LFB.webp";
import ericsson from "../../../../../public/images/partners/logo-Ericsson.webp";
import motorola from "../../../../../public/images/partners/logo-motorola.webp";
import jesa from "../../../../../public/images/partners/logo-jesa.webp";
import metsooutotec from "../../../../../public/images/partners/logo-metsooutotec.webp";


import PartnerSlide from "./PartnersSlideShow";

function OurPartners({ disableTxt, ksa }) {
  const { t } = useTranslation();

  const OPTIONS = { loop: true, align: "start" };

  const slides = [
    { label: "ABB", logo: abbLogo },
    { label: "Nokia", logo: nokiaLogo },
    { label: "Total", logo: totalLogo },
    { label: "Naval", logo: navalLogo },
    { label: "AlcatelLucent", logo: alcatelLucent },
    { label: "ZTE", logo: zte },
    { label: "JohnCockerill", logo: johnCockerill },
    { label: "Proximus", logo: proximus },
    { label: "ARCELOR", logo: ARCELOR },
    { label: "TRACTEBEL", logo: TRACTEBEL },
    { label: "Samsung", logo: Samsung },
    { label: "Butachimie", logo: Butachimie },
    { label: "EGIS", logo: EGIS },
    { label: "Systra", logo: systra },
    { label: "Equinix", logo: Equinix },
    { label: "ColasRail", logo: ColasRail },
    { label: "Omv", logo: Omv },
    { label: "Alstom", logo: Alstom },
    { label: "LFB", logo: lFB },
    { label: "Tecnimont", logo: tecnimont },
    { label: "Motorola", logo: motorola },
    { label: "Ericsson", logo: ericsson },
    { label: "Siemens", logo: siemens },
    { label: "Geocean", logo: geocean },
    { label: "Visa", logo: visa },
    { label: "NEC", logo: NEC },
    { label: "Weir", logo: Weir },
    { label: "GE", logo: ge },
    { label: "Lilly", logo: Lilly },
    { label: "Saipem", logo: Saipem },
    { label: "MetsoOutotec", logo: metsooutotec },
    { label: "Jesa", logo: jesa },
  ];

  const [emblaRef] = useEmblaCarousel(OPTIONS, [
    Autoplay({ playOnInit: true, delay: 1000 }),
  ]);

  return (
    <Container
      id="our-partners"
      className={ksa ? `custom-max-width ksa` : `custom-max-width`}
    >
      <section className="embla" id={"partners__slider"}>
        {!disableTxt && (
          <div className="heading">
            <h2 className="heading-h1 text-center">
              {t("aboutUs:partners:title")}
            </h2>
            <p className="sub-heading text-center">
              {t("aboutUs:partners:description")}
            </p>
          </div>
        )}

        <div className="embla__viewport" ref={emblaRef}>
          <div className="embla__container">
            {slides.map((item, index) => (
              <PartnerSlide key={index} item={item} t={t} />
            ))}
          </div>
        </div>
      </section>
    </Container>
  );
}

export default OurPartners;
