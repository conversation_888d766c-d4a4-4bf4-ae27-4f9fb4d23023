import { Schema, model, Types } from 'mongoose';
import { CategoryI, CategoryVersion } from './article.category.interfaces';
import { Language, robotsMeta } from '@/utils/helpers/constants';

const categoryVersionSchema = new Schema<CategoryVersion>({
    language: { type: String, enum: Language, required: true },
    name: { type: String },
    url: { type: String, unique: true },
    description: { type: String },
    image: { type: String, default: null },
    canonical: { type: String },
    articles: [{ type: Types.ObjectId, ref: 'Article' }],
    guides: [{ type: Types.ObjectId, ref: 'Guide' }],
    metaTitle: { type: String, default: '' },
    metaDescription: { type: String, default: '' },
    createdAt: { type: Date, required: true, default: Date.now },
    updatedAt: { type: Date, required: true, default: Date.now },
});

const categorySchema = new Schema<CategoryI>(
    {
        versions: { Map, of: categoryVersionSchema, required: true },
        robotsMeta: {
            type: String,
            enum: robotsMeta,
            default: robotsMeta.index,
        },
    },

    {
        timestamps: true,
        toJSON: {
            transform: function (doc, ret) {
                delete ret.__v;
            },
        },
    },
);

export default model<CategoryI>('Category', categorySchema);
