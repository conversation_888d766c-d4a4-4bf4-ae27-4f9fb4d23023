"use client";
import { DataGrid } from "@mui/x-data-grid";
import { useTranslation } from "react-i18next";
import CustomButton from "@/components/ui/CustomButton";
import { useEffect, useState } from "react";
import { useGetCandidatures } from "../hooks/Candidatures.hooks";
import Svgpreview from "@/assets/images/icons/preview-icon.svg";
import { findIndustryColoredIcon, formatDate } from "@/utils/functions";
import Svgedit from "@/assets/images/icons/edit-icon.svg";
import {
  Grid,
  InputBase,
  MenuItem,
  Select,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import { Status } from "@/utils/constants";
import { adminRoutes, baseUrlBackoffice } from "@/helpers/routesList";
import moment from "moment";
import CustomFilters from "@/components/ui/CustomFilters";
function CandidaturesList({ opportunityId }) {
  const [searchQuery, setSearchQuery] = useState();
  const [applicationDateFrom, setApplicationDateFrom] = useState(null);
  const [applicationDateTo, setApplicationDateTo] = useState(null);
  const [status, setStatus] = useState("");
  const [jobTitle, setJobTitle] = useState();
  const [paginationModel, setPaginationModel] = useState({
    page: 0,
    pageSize: 10,
  });
  const [search, setSearch] = useState(false);
  const isOpen = true;

  const { t, i18n } = useTranslation();

  const getCandidatures = useGetCandidatures({
    pageSize: paginationModel.pageSize,
    pageNumber: paginationModel.page + 1,
    paginated: true,
    searchQuery,
    jobTitle,
    applicationDateFrom,
    applicationDateTo,
    status,
    opportunity: opportunityId ? opportunityId : "",
  });
  const theme = useTheme();

  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  useEffect(() => {
    getCandidatures.refetch();
  }, [search, paginationModel, opportunityId]);

  const rowsContacts =
    getCandidatures?.data?.applications?.map((item) => ({
      id: item._id,
      opportunityTitle:
        item.opportunity?.versions[i18n.language]?.title ?? "N/A",

      candidateEmails: item.candidate?.user?.email || "N/A",
      candidateName:
        item.candidate?.user?.firstName && item.candidate?.user?.lastName
          ? `${item.candidate.user.firstName} ${item.candidate.user.lastName}`
          : "N/A",
      country: item.opportunity?.country || "N/A",
      applicationDate: item.applicationDate
        ? formatDate(item.applicationDate)
        : "N/A",
      status: item.status || "N/A",
      actions: item._id,
    })) || [];
  const handleActionChange = (e, id) => {
    const action = e.target.value;
    if (action === "edit") {
      handleEdit(id);
    } else if (action === "archive") {
      handleDeleteOpportiniteConfirmation(id);
    }
  };
  const handleSearch = () => {
    setSearch(!search);
  };

  const handleReset = () => {
    setSearchQuery("");
    setApplicationDateFrom(null);
    setApplicationDateTo(null);
    setJobTitle("");
    setStatus("");
    setPaginationModel({ page: 0, pageSize: 10 });
    setSearch(!search);
  };
  const truncateTitle = (title) => {
    const words = title?.split(" ");
    if (words?.length > 3) {
      return words.slice(0, 1).join(" ") + "...";
    } else {
      return title;
    }
  };
  const filters = [
    {
      label: t("application:searchByNote"),
      type: "text",
      value: searchQuery,
      onChange: (e) => setSearchQuery(e.target.value),
    },
    {
      label: t("application:searchByJobTitle"),
      type: "text",
      value: jobTitle,
      onChange: (e) => setJobTitle(e.target.value),
    },
    {
      label: t("application:from"),
      type: "date",
      value: applicationDateFrom,
      onChange: setApplicationDateFrom,
    },
    {
      label: t("application:to"),
      type: "date",
      value: applicationDateTo,
      onChange: setApplicationDateTo,
    },
    {
      label: t("application:status"),
      type: "select",
      value: status,
      options: Status,
      onChange: (e, value) => setStatus(value),
    },
  ];
  const columnsContacts = [
    {
      field: "opportunityTitle",
      cellClassName: "datagrid-cell",
      headerClassName: "datagrid-header",
      headerName: t("application:job"),
      flex: 1,
      renderCell: (params) => {
        const item = getCandidatures?.data?.applications?.find(
          (app) => app._id === params.id
        );

        const opportunityTitle =
          item?.opportunity?.versions[i18n.language]?.title || "N/A";

        const industryIcon = findIndustryColoredIcon(
          item?.opportunity?.industry
        );

        return (
          <div className="job-title-cell">
            {industryIcon && (
              <span className="industry-icon">{industryIcon}</span>
            )}
            <span>{truncateTitle(opportunityTitle)}</span>
          </div>
        );
      },
    },

    {
      field: "candidateName",
      cellClassName: "datagrid-cell",
      headerClassName: "datagrid-header",
      headerName: t("application:namecandidat"),

      flex: 1,
    },

    {
      field: "applicationDate",
      cellClassName: "datagrid-cell",
      renderCell: (params) => moment(params.value).format("DD-MM-YYYY"),
      headerName: t("application:applicationDate"),
      flex: 1,
    },
    {
      field: "status",
      cellClassName: "datagrid-cell",
      headerClassName: "datagrid-header",
      headerName: t("application:status"),
      flex: 1,
    },
    {
      field: "actions",
      headerName: t("candidatures:actions"),
      headerClassName: "datagrid-header",
      cellClassName: "datagrid-cell",
      flex: 1,
      renderCell: (params) => {
        const id = params.row.id;
        return (
          <Select
            value=""
            onChange={(e) => handleActionChange(e, id)}
            displayEmpty
            input={<InputBase />}
            style={{ width: "100%" }}
            renderValue={() => t("candidatures:actions")}
          >
            <MenuItem value="detail">
              <CustomButton
                text={t("global:detail")}
                icon={<Svgpreview />}
                leftIcon={true}
                link={`/${baseUrlBackoffice.baseURL.route}/${adminRoutes.applications.route}/${adminRoutes.detail.route}/${id}/`}
                className="btn btn-ghost edit-blog"
              />
            </MenuItem>

            <MenuItem value="update">
              <CustomButton
                text={t("global:update")}
                icon={<Svgedit />}
                leftIcon={true}
                link={`/${baseUrlBackoffice.baseURL.route}/${adminRoutes.applications.route}/${adminRoutes.edit.route}/${id}/`}
                className="btn btn-ghost edit-blog"
              />
            </MenuItem>
          </Select>
        );
      },
    },
  ];

  return (
    <>
      {opportunityId ? (
        <p className="heading-h2 semi-bold">
          {t("application:applicationByOpportunity")}{" "}
          <span className="opportunities-nbr">
            {getCandidatures?.data?.totalApplications}
          </span>
        </p>
      ) : (
        <p className="heading-h2 semi-bold">
          {t("application:candidaturelist")}{" "}
          <span className="opportunities-nbr">
            {getCandidatures?.data?.totalApplications}
          </span>
        </p>
      )}

      <div id="container" className="recent-application-pentabell">
        <div className={`main-content ${isOpen ? "open" : "closed"}`}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <CustomFilters
                filters={filters}
                onSearch={handleSearch}
                onReset={handleReset}
                searchLabel={t("search")}
              />
            </Grid>
            <Grid item xs={12}>
              <div style={{ height: "100%", width: "100%" }}>
                <DataGrid
                  localeText={{ noRowsLabel: "No result found." }}
                  rows={rowsContacts}
                  columns={columnsContacts}
                  pagination
                  paginationMode="server"
                  pageSizeOptions={[5, 10, 25]}
                  paginationModel={paginationModel}
                  onPaginationModelChange={setPaginationModel}
                  rowCount={getCandidatures?.data?.totalApplications || 0}
                  className="pentabell-table"
                  columnVisibilityModel={{
                    applicationDate: !isMobile,
                    status: !isMobile,
                    opportunityTitle: !isMobile,
                  }}
                />
              </div>
            </Grid>
          </Grid>
        </div>
      </div>
    </>
  );
}

export default CandidaturesList;
