import OfficeLocationMap from "@/components/ui/OfficeLocationMap";

function OfficeLocationMapIraq({ t }) {
  return (
    <OfficeLocationMap
      title={t("iraq:officeLocation:label")}
      subtitle={t("iraq:officeLocation:title")}
      address={t("iraq:officeLocation:address")}
      tel={t("iraq:officeLocation:tel1")}
      email={t("iraq:officeLocation:mail")}
      linkText={t("iraq:officeLocation:talk")}
      mapSrc="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3333.7592717068956!2d44.35170359999999!3d33.3251084!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x15577f37b47d89e7%3A0xde2529cde4b27911!2sInternational%20Recruitment%20%26%20Staffing%20Agency%20Iraq%20%7C%20Pentabell%20Iraq!5e0!3m2!1sfr!2stn!4v1737969238678!5m2!1sfr!2stn"
    
    />
  );
}
export default OfficeLocationMapIraq;
