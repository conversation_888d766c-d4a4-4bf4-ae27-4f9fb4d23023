"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createSeoOpportunitySchema = exports.updateApplicationSchema = exports.createApplicationSchema = exports.updateOpportunitySchema = exports.createOpportunitySchema = void 0;
const Joi = __importStar(require("joi"));
const constants_1 = require("@/utils/helpers/constants");
const notAfterToday = (value, helpers) => {
    const currentDate = new Date();
    const providedDate = new Date(value);
    if (providedDate > currentDate) {
        return helpers.message({ custom: 'Date of requisition: cannot be set in the future.' });
    }
    return value;
};
const createSeoOpportunitySchema = Joi.object({
    metaTitle: Joi.string().min(3).required(),
    metaDescription: Joi.string().min(3).required(),
    robotMeta: Joi.string().min(3).required(),
});
exports.createSeoOpportunitySchema = createSeoOpportunitySchema;
const createOpportunitySchema = Joi.object({
    title: Joi.string().min(3).required(),
    jobDescription: Joi.string().min(10).required(),
    dateOfExpiration: Joi.string().required(),
    dateOfRequisition: Joi.string().custom(notAfterToday, 'not after today'),
    country: Joi.valid(...Object.values(constants_1.Countries)).required(),
    industry: Joi.valid(...Object.values(constants_1.Industry)).required(),
    genre: Joi.valid(...Object.values(constants_1.Genre)).required(),
    createdBy: Joi.string(),
    isPublished: Joi.boolean(),
}).options({ abortEarly: false });
exports.createOpportunitySchema = createOpportunitySchema;
const updateOpportunitySchema = Joi.object({
    title: Joi.string().min(3),
    jobDescription: Joi.string().min(10),
    dateOfExpiration: Joi.string(),
    dateOfRequisition: Joi.string().custom(notAfterToday, 'not after today'),
    country: Joi.valid(...Object.values(constants_1.Countries)),
    industry: Joi.valid(...Object.values(constants_1.Industry)),
    gender: Joi.valid(...Object.values(constants_1.Gender)),
    createdBy: Joi.string(),
    isPublished: Joi.boolean(),
    versions: Joi.object(),
}).options({ abortEarly: false });
exports.updateOpportunitySchema = updateOpportunitySchema;
const createApplicationSchema = Joi.object({
    status: Joi.valid(...Object.values(constants_1.ApplicationStatus)),
    applicationDate: Joi.string(),
    candidate: Joi.string(),
    opportunity: Joi.string(),
    note: Joi.string(),
    resume: Joi.string(),
}).options({ abortEarly: false });
exports.createApplicationSchema = createApplicationSchema;
const updateApplicationSchema = Joi.object({
    status: Joi.valid(...Object.values(constants_1.ApplicationStatus)),
});
exports.updateApplicationSchema = updateApplicationSchema;
//# sourceMappingURL=opportunity.validation.js.map