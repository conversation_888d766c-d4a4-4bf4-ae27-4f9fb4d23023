"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PopulatedOpportunityApplicationDTO = exports.OpportunityApplicationDTO = exports.ApplicationDTO = void 0;
const constants_1 = require("@/utils/helpers/constants");
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const opportunity_dto_1 = require("./opportunity.dto");
class User {
    constructor(user = {}) {
        Object.assign(this, user);
    }
}
__decorate([
    (0, class_transformer_1.Expose)({ name: '_id' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], User.prototype, "id", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'firstName' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], User.prototype, "firstName", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'lastName' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], User.prototype, "lastName", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'email' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], User.prototype, "email", void 0);
class ApplicationDTO {
    constructor(application = {}) {
        Object.assign(this, application);
    }
}
exports.ApplicationDTO = ApplicationDTO;
__decorate([
    (0, class_transformer_1.Expose)({ name: '_id' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApplicationDTO.prototype, "id", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'status' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApplicationDTO.prototype, "state", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'applicationDate' }),
    __metadata("design:type", String)
], ApplicationDTO.prototype, "creationDate", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'note' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApplicationDTO.prototype, "message", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'resume' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApplicationDTO.prototype, "attachment", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Date)
], ApplicationDTO.prototype, "createdAt", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Date)
], ApplicationDTO.prototype, "updatedAt", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Number)
], ApplicationDTO.prototype, "__v", void 0);
class OpportunityApplicationDTO extends ApplicationDTO {
    constructor(application = {}) {
        super();
        Object.assign(this, application);
    }
}
exports.OpportunityApplicationDTO = OpportunityApplicationDTO;
__decorate([
    (0, class_transformer_1.Expose)({ name: 'opportunity' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", opportunity_dto_1.OpportunityDTO)
], OpportunityApplicationDTO.prototype, "jobOffer", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'candidate' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", User)
], OpportunityApplicationDTO.prototype, "applicant", void 0);
class PopulatedOpportunityApplicationDTO extends ApplicationDTO {
    constructor(application = {}) {
        super();
        Object.assign(this, application);
    }
}
exports.PopulatedOpportunityApplicationDTO = PopulatedOpportunityApplicationDTO;
__decorate([
    (0, class_transformer_1.Expose)({ name: 'opportunity' }),
    (0, class_transformer_1.Type)(() => opportunity_dto_1.OpportunityDTO),
    __metadata("design:type", opportunity_dto_1.OpportunityDTO)
], PopulatedOpportunityApplicationDTO.prototype, "jobOffer", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'candidate' }),
    (0, class_transformer_1.Type)(() => User),
    __metadata("design:type", User)
], PopulatedOpportunityApplicationDTO.prototype, "applicant", void 0);
//# sourceMappingURL=opportunity.application.dto.js.map