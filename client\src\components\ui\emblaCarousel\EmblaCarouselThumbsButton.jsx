import Image from "next/image";

export const Thumb = (props) => {
  const { selected, index, onClick, teampic } = props;

  return (
    <div
      className={"embla-thumbs__slide".concat(
        selected ? " embla-thumbs__slide--selected" : ""
      )}
    >
      <button
        onClick={onClick}
        type="button"
        className="embla-thumbs__slide__number"
      >
        <img width={197} height={148} src={teampic.src} alt="Carousel" loading="lazy"/>
      </button>
    </div>
  );
};
