"use client";
import {
  Container,
  Grid,
  AccordionS<PERSON>mary,
  AccordionDetails,
  Accordion,
} from "@mui/material";
import { useState } from "react";
import SvgexpandIcon from "@/assets/images/icons/arrowUp.svg";
import { useTranslation } from "react-i18next";

function AskQuestions({ ASK_QUESTIONS, title }) {
  const { t } = useTranslation();
  const [expanded, setExpanded] = useState(null);

  const handleChange = (panel) => (event, isExpanded) => {
    setExpanded(isExpanded ? panel : false);
  };
  return (
    <div id="ask-questions-section">
      <Container className="custom-max-width">
        <h2 className="heading-h1">{title}</h2>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "FAQPage",
              mainEntity: [
                ASK_QUESTIONS.map((askedQuestion) => ({
                  "@type": "Question",
                  name: askedQuestion.title,
                  acceptedAnswer: {
                    "@type": "Answer",
                    text: askedQuestion.description,
                  },
                })),
              ],
            }),
          }}
        />
        <Grid className="container" container columnSpacing={3} rowSpacing={2}>
          {ASK_QUESTIONS.map((item, index) => {
            return (
              <Grid item xs={12} sm={6} key={index}>
                <Accordion
                  elevation={0}
                  expanded={expanded === item.id}
                  className="services-accordion"
                  disableGutters={true}
                  onChange={handleChange(item.id)}
                >
                  <AccordionSummary
                    aria-controls="panel1bh-content"
                    id="panel1bh-header"
                    className="services-accordion-header"
                    expandIcon={<SvgexpandIcon />}
                  >
                    <h3 className="service-title">{item.title}</h3>
                  </AccordionSummary>
                  <AccordionDetails elevation={0}>
                    <p className="service-description paragraph">
                      {item.description}
                    </p>
                  </AccordionDetails>
                </Accordion>
              </Grid>
            );
          })}
        </Grid>
      </Container>
    </div>
  );
}

export default AskQuestions;
