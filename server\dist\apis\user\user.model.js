"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const constants_1 = require("@/utils/helpers/constants");
const userSchema = new mongoose_1.Schema({
    firstName: {
        type: String,
    },
    lastName: {
        type: String,
    },
    profilePicture: {
        type: String,
        default: '',
    },
    email: {
        type: String,
        unique: true,
    },
    jobTitle: {
        type: String,
    },
    country: {
        type: String,
        index: true,
        enum: constants_1.Countries,
    },
    exist: { type: Boolean, default: false },
    isActive: {
        type: Boolean,
        default: false,
    },
    password: {
        type: String,
    },
    phone: {
        type: String,
        default: '',
    },
    roles: [
        {
            type: String,
            enum: constants_1.Role,
            default: [constants_1.Role.CANDIDATE],
        },
    ],
    resetPasswordToken: {
        type: String,
        default: '',
        get: () => undefined,
    },
    confirmAccountToken: {
        type: String,
        default: '',
        length: 40,
    },
    isArchived: {
        type: Boolean,
        default: false,
    },
    company: {
        type: String,
        minlength: 3,
    },
    nationalities: {
        type: [{ type: String }],
    },
    dateOfBirth: { type: Date },
    gender: {
        type: String,
        enum: constants_1.Gender,
    },
    addressLine: {
        type: String,
    },
    alerts: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'Alert',
    },
    candidate: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'Candidat',
    },
    seoDetails: {
        type: String,
    },
    lastLogin: Date,
}, {
    timestamps: true,
    toJSON: {
        transform: function (doc, ret) {
            delete ret.password;
            delete ret.type;
            delete ret.resetPasswordToken;
            delete ret.confirmAccountToken;
            delete ret.__v;
        },
    },
    discriminatorKey: 'type',
});
exports.default = (0, mongoose_1.model)('User', userSchema);
//# sourceMappingURL=user.model.js.map