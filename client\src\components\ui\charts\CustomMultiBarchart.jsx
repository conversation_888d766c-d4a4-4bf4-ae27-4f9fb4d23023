import React from "react";
import { <PERSON><PERSON><PERSON> } from "@mui/x-charts/BarChart";
import NodataSVG from "@/assets/images/icons/noDataIcon.svg";
import { useTranslation } from "react-i18next";

const CustomMultiBarChart = ({ chart, chartSettings }) => {
  const id = React.useId();
  const clipPathId = `${id}-clip-path`;
  const hasData = chart.dataset?.length > 0;
  const { t } = useTranslation();

  function valueFormatter(value) {
    switch (value) {
      case "resumes":
        return t("statsDash:uploadedResumes");
      case "register":
        return t("statsDash:newAccounts");
      case "opportunities":
        return t("statsDash:newOpportunities");
      case "articles":
        return t("statsDash:newArticles");
      case "contacts":
        return t("statsDash:newContacts");
      case "newsletters":
        return t("statsDash:newslettersSubscriptions");
      case "applications":
        return t("statsDash:applications");
      case "login":
        return t("statsDash:logins");
      default:
        return value.charAt(0).toUpperCase() + value.slice(1);
    }
  }
  return (
    <>
      {hasData ? (
        <BarChart
          dataset={chart?.dataset}
          xAxis={[
            {
              scaleType: "band",
              dataKey: "month",
              categoryGapRatio: 0.2,
              barGapRatio: 0.5,
            },
          ]}
          slotProps={{
            legend: { hidden: true },
          }}
          series={chart?.dataKey?.map((key) => ({
            dataKey: key,
            label: valueFormatter(key),
          }))}
          barGap={10}
          barCategoryGap={20}
          {...chartSettings}
          colors={chart.color}
          borderRadius={50}
        />
      ) : (
        <NodataSVG />
        // <img src={Nodata.src}  />
      )}

      {/* <ResponsiveChartContainer
        dataset={chart?.dataset}
        series={chart?.dataKey?.map((key) => ({
          type: "bar",
          dataKey: key,
          label: valueFormatter(key),
        }))}
        xAxis={[{ scaleType: "band", dataKey: "month" }]}
        barGap={10}
        barCategoryGap={20}
        {...chartSettings}
        colors={chart.color}
      >
        <ChartsClipPath id={clipPathId} />
        <g clipPath={`url(#${clipPathId})`}>
          <BarPlot borderRadius={50} />
        </g>
        <ChartsXAxis />
        <ChartsYAxis />

        <CustomItemTooltip />
      </ResponsiveChartContainer> */}
    </>
  );
};

export default CustomMultiBarChart;
