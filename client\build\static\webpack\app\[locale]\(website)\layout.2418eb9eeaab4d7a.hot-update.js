"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/layout",{

/***/ "(app-pages-browser)/./src/components/ui/DropdownMenu.jsx":
/*!********************************************!*\
  !*** ./src/components/ui/DropdownMenu.jsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Menu/Menu.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Fade/Fade.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _assets_images_icons_arrowDown_svg__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../assets/images/icons/arrowDown.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowDown.svg\");\n/* harmony import */ var _assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../assets/images/icons/arrowUp.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowUp.svg\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _utils_functions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/functions */ \"(app-pages-browser)/./src/utils/functions.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst DropdownMenu = (param)=>{\n    let { buttonLabel, buttonHref, menuItems, subMenu, locale, withFlag, selectedFlag, forceOpen = false } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    const buttonRef = useRef(null);\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_6__.useTranslation)();\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hoveredSubMenu, setHoveredSubMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const open = Boolean(anchorEl) || forceOpen;\n    // Handle forceOpen for hover functionality\n    useEffect(()=>{\n        if (forceOpen && buttonRef.current && !anchorEl) {\n            setAnchorEl(buttonRef.current);\n        } else if (!forceOpen && anchorEl && subMenu) {\n            setAnchorEl(null);\n        }\n    }, [\n        forceOpen,\n        anchorEl,\n        subMenu\n    ]);\n    const isDetailBlogPath = ()=>pathname.includes(\"/blog/\") && !pathname.includes(\"/blog/category/\") && pathname !== \"/blog/\" && pathname !== \"/fr/blog/\";\n    const handleClick = (event)=>{\n        if (!forceOpen) {\n            setAnchorEl(event.currentTarget);\n        }\n    };\n    const handleClose = ()=>{\n        setAnchorEl(null);\n    };\n    const handleClick2 = (event, item)=>{\n        event.stopPropagation(); // Prevent event bubbling\n        if (item.onClick) {\n            item.onClick(); // Call the onClick handler if provided\n        } else {\n            if (item.subItems == undefined) handleClose();\n        }\n    };\n    const handleMouseEnter = (item, index)=>{\n        if (item.subItems) {\n            setHoveredSubMenu(index);\n        }\n    };\n    const handleMouseLeave = ()=>{\n        setHoveredSubMenu(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: subMenu ? `sub-dropdown-menu` : `dropdown-menu`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                ref: buttonRef,\n                className: pathname.includes(buttonHref) && buttonHref !== \"\" ? \"navbar-link dropdown-toggle active\" : isDetailBlogPath() ? \"navbar-link dropdown-toggle whiteBg\" : \"navbar-link dropdown-toggle\",\n                id: \"fade-button\",\n                \"aria-controls\": open ? \"fade-menu\" : undefined,\n                \"aria-haspopup\": \"true\",\n                \"aria-expanded\": open ? \"true\" : undefined,\n                onClick: handleClick,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: pathname.includes(buttonHref) && buttonHref !== \"\" ? \"dropdown-toggle-link active\" : isDetailBlogPath() ? \"dropdown-toggle-link whiteBg\" : \"dropdown-toggle-link\",\n                        href: // Disable href when buttonLabel is \"resources\"\n                        buttonLabel?.toLowerCase() === \"resources\" || buttonLabel?.toLowerCase() === \"ressources\" ? \"#\" : buttonHref ? (0,_utils_functions__WEBPACK_IMPORTED_MODULE_5__.generateLocalizedSlug)(locale, buttonHref).replace(\"/en/\", \"/\") : pathname.replace(\"/en/\", \"/\"),\n                        locale: locale === \"en\" ? \"en\" : \"fr\",\n                        onClick: (e)=>{\n                            // Prevent navigation when buttonLabel is \"resources\"\n                            if (buttonLabel?.toLowerCase() === \"resources\" || buttonLabel?.toLowerCase() === \"ressources\") {\n                                e.preventDefault();\n                                e.stopPropagation();\n                            }\n                        },\n                        sx: {\n                            // Ensure resources text has the same styling as other menu items\n                            ...buttonLabel?.toLowerCase() === \"resources\" || buttonLabel?.toLowerCase() === \"ressources\" ? {\n                                cursor: \"default\",\n                                textDecoration: \"none\",\n                                \"&:hover\": {\n                                    textDecoration: \"none\"\n                                }\n                            } : {}\n                        },\n                        children: withFlag ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            className: \"flag-lang\",\n                            src: selectedFlag.src,\n                            width: 26,\n                            height: 22,\n                            disabled: true,\n                            loading: \"lazy\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 131,\n                            columnNumber: 13\n                        }, undefined) : buttonLabel\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, undefined),\n                    open ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowDown_svg__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 143,\n                        columnNumber: 17\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 143,\n                        columnNumber: 36\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                id: \"fade-menu\",\n                MenuListProps: {\n                    \"aria-labelledby\": \"fade-button\"\n                },\n                anchorEl: anchorEl,\n                open: open,\n                onClose: handleClose,\n                TransitionComponent: _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                disableScrollLock: true,\n                anchorOrigin: {\n                    vertical: \"bottom\",\n                    horizontal: subMenu ? \"right\" : \"left\"\n                },\n                children: menuItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: item.subItems ? `sub-menu dropdown-item` : `dropdown-item`,\n                        onClick: (e)=>handleClick2(e, item),\n                        onMouseEnter: ()=>handleMouseEnter(item, index),\n                        onMouseLeave: handleMouseLeave,\n                        children: item.subItems ? // If the item has a subMenu, render another DropdownMenu\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DropdownMenu, {\n                            buttonLabel: item?.i18nName ? t(item?.i18nName) : item.name,\n                            buttonHref: item.route,\n                            menuItems: item.subItems,\n                            subMenu: true,\n                            forceOpen: hoveredSubMenu === index\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 172,\n                            columnNumber: 15\n                        }, undefined) : item.route ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            href: (0,_utils_functions__WEBPACK_IMPORTED_MODULE_5__.generateLocalizedSlug)(locale, item.route),\n                            locale: locale === \"en\" ? \"en\" : \"fr\",\n                            className: pathname.includes(item.route) ? \"dropdown-item-link active\" : \"dropdown-item-link\",\n                            children: [\n                                item.icon ?? item.icon,\n                                \" \",\n                                item?.i18nName ? t(item?.i18nName) : item.name\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 181,\n                            columnNumber: 15\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"dropdown-item-link\",\n                            href: \"#\",\n                            children: withFlag ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                className: \"flag-lang\",\n                                src: item.flag.src,\n                                width: 26,\n                                height: 22,\n                                loading: \"lazy\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                                lineNumber: 196,\n                                columnNumber: 19\n                            }, undefined) : item?.i18nName ? t(item?.i18nName) : item.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 194,\n                            columnNumber: 15\n                        }, undefined)\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DropdownMenu, \"lxglQQ+J5oKa9HopvhsHr1LgQUg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname,\n        react_i18next__WEBPACK_IMPORTED_MODULE_6__.useTranslation\n    ];\n});\n_c = DropdownMenu;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DropdownMenu);\nvar _c;\n$RefreshReg$(_c, \"DropdownMenu\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/DropdownMenu.jsx\n"));

/***/ })

});