"use client";
import { Container, Grid } from "@mui/material";

import { useTranslation } from "react-i18next";

function PentabellByNumbers({ locale }) {
  const { t } = useTranslation();

  return (
    <Container className="custom-max-width">
      <Grid className="container" container spacing={2}>
        <Grid item xs={12} sm={12}>
          <h2 className="heading-h2">
            {t("homePage:introSection:numbers")} <br />
            <span className="sub-heading update-span">
              {t("homePage:introSection:update")}{" "}
              {t("homePage:introSection:date")}
            </span>
          </h2>
        </Grid>
        <Grid item xs={12} sm={12} className="numbers">
          <div className="stats-container-pentabell">
            <div className="stat-box purple">
              <span className="corner top-left"></span>
              <span className="corner top-right"></span>
              <span className="corner bottom-left"></span>
              <span className="corner bottom-right"></span>
              <p className="numbers-pentabell">15K+</p>
              <p>{t("homePage:introSection:hiredExperts")}</p>
            </div>
            <div className="stat-box red">
              <span className="corner top-left"></span>
              <span className="corner top-right"></span>
              <span className="corner bottom-left"></span>
              <span className="corner bottom-right"></span>
              <p className="numbers-pentabell">17K+</p>
              <p>{t("homePage:introSection:performedMissions")}</p>
            </div>
            <div className="stat-box yellow">
              <span className="corner top-left"></span>
              <span className="corner top-right"></span>
              <span className="corner bottom-left"></span>
              <span className="corner bottom-right"></span>
              <p className="numbers-pentabell">61</p>
              <p>{t("homePage:introSection:nationalities")}</p>
            </div>
            <div className="stat-box green">
              <span className="corner top-left"></span>
              <span className="corner top-right"></span>
              <span className="corner bottom-left"></span>
              <span className="corner bottom-right"></span>
              <p className="numbers-pentabell">187</p>
              <p>{t("homePage:introSection:satisfiedClients")}</p>
            </div>
            <div className="stat-box blue">
              <span className="corner top-left"></span>
              <span className="corner top-right"></span>
              <span className="corner bottom-left"></span>
              <span className="corner bottom-right"></span>
              <p className="numbers-pentabell">265K+</p>
              <p>{t("homePage:introSection:cvs")}</p>
            </div>
          </div>
        </Grid>
      </Grid>
    </Container>
  );
}

export default PentabellByNumbers;
