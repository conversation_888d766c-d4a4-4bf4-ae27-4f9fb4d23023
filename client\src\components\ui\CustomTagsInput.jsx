import { WithContext as ReactTags } from "react-tag-input";

export default function CustomTagsInput({ tags, setTags, placeholder, error }) {
  const handleDelete = (i) => setTags(tags.filter((_, idx) => idx !== i));
  const handleAddition = (tag) => setTags([...tags, tag]);
  return (
    <div id="tags" className={`input-pentabell ${error ? "is-invalid" : ""}`}>
      <ReactTags
        tags={tags}
        handleDelete={handleDelete}
        handleAddition={handleAddition}
        inputFieldPosition="bottom"
        autocomplete
        allowDragDrop={false}
        placeholder={placeholder}
      />
      {error && <div className="label-error">{error}</div>}
    </div>
  );
}
