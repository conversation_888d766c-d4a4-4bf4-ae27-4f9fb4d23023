import { FormLabel } from "@mui/material";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs from "dayjs";

export default function CustomDatePicker({ label, value, onChange, error }) {
  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <div className="form-group">
        <FormLabel className="label-form">{label}</FormLabel>
        <DatePicker
          format="DD/MM/YYYY"
          value={dayjs(value)}
          onChange={(date) => onChange(dayjs(date).format("YYYY-MM-DD"))}
          className={`input-date ${error ? "is-invalid" : ""}`}
        />
        {error && <div className="label-error">{error}</div>}
      </div>
    </LocalizationProvider>
  );
}