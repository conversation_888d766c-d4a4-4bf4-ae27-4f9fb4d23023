"use client";;
import { Container, Grid } from "@mui/material";
import CustomButton from "@/components/ui/CustomButton";
import { useTranslation } from "react-i18next";
import { websiteRoutesList } from "@/helpers/routesList";

function OurCultureAndLocation({locale}) {
  const { t } = useTranslation();
  return (
    <Container id="our-culture-location" className="custom-max-width">
      <Grid className="container" container columnSpacing={0}>
        <Grid item xs={12} sm={5}>
          <div className="culture-section">
            <p className="heading-h2 text-white">
              {t("joinUs:ourCulture:title")}
            </p>
            <p className="paragraph text-white">
              {t("joinUs:ourCulture:description")}
            </p>
            <CustomButton
              text={t("joinUs:ourCulture:readMore")}
              link={`/${websiteRoutesList.aboutUs.route}`}
              className={"btn btn-outlined white"}
              aHref
            />
          </div>
        </Grid>
        <Grid item xs={12} sm={5}>
          <div className="location-section">
            <p className="heading-h2 text-white">
              {t("joinUs:ourLocation:title")}
            </p>
            <p className="paragraph text-white">
              {t("joinUs:ourLocation:description")}
            </p>
            <CustomButton
              text={t("joinUs:ourLocation:readMore")}
              link={`/${websiteRoutesList.contact.route}#our-location-section`}
              className={"btn btn-outlined white"}
              aHref
            />
          </div>
        </Grid>
      </Grid>
    </Container>
  );
}

export default OurCultureAndLocation;
