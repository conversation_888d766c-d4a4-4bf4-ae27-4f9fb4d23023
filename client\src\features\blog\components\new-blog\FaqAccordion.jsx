import { useState } from "react";
import { Typography, Box, Collapse, IconButton } from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import RemoveIcon from "@mui/icons-material/Remove";

export default function FaqAccordion({ title, items }) {
  const [openIndex, setOpenIndex] = useState(null);

  const toggle = (index) => {
    setOpenIndex((prev) => (prev === index ? null : index));
  };

  return (
    <Box id="faq-container">
      <Typography variant="h2" className="faq-title">
        {title}
      </Typography>
      {items.map((item, index) => {
        const isOpen = openIndex === index;
        return (
          <Box key={index} className="faq-item">
            <Box className="faq-question" onClick={() => toggle(index)}>
              <Typography variant="body1" className="faq-question-text">
                <span className="faq-number">Q{index + 1}</span> {item.question}
              </Typography>
              <IconButton size="small" className="faq-icon">
                {isOpen ? <RemoveIcon /> : <AddIcon />}
              </IconButton>
            </Box>
            <Collapse in={isOpen} timeout="auto" unmountOnExit>
              <Box className="faq-answer">{item.answer}</Box>
            </Collapse>
          </Box>
        );
      })}
    </Box>
  );
}
