import { useState, useRef, useEffect } from "react";
import "react-international-phone/style.css";
const LazyLoadFlag = ({ src, alt }) => {
  const [isVisible, setIsVisible] = useState(false);
  const imgRef = useRef();

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.unobserve(entry.target);
        }
      },
      { threshold: 0.1 }
    );

    if (imgRef.current) observer.observe(imgRef.current);

    return () => observer.disconnect();
  }, []);

  return (
    <img
      ref={imgRef}
      src={isVisible ? src : undefined}
      data-src={src}
      alt={alt}
      loading="lazy"
      style={{ opacity: isVisible ? 1 : 0.5, transition: "opacity 0.3s" }}
    />
  );
};

export default LazyLoadFlag;
