"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/glossaries/page",{

/***/ "(app-pages-browser)/./src/components/sections/GlossaryBanner.jsx":
/*!****************************************************!*\
  !*** ./src/components/sections/GlossaryBanner.jsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Container_Input_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Container,Input,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Container_Input_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Container,Input,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Input/Input.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Container_Input_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Container,Input,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _assets_images_icons_BookIcon_svg__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/assets/images/icons/BookIcon.svg */ \"(app-pages-browser)/./src/assets/images/icons/BookIcon.svg\");\n/* harmony import */ var _assets_images_icons_searchIcon_svg__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/assets/images/icons/searchIcon.svg */ \"(app-pages-browser)/./src/assets/images/icons/searchIcon.svg\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction GlossaryBanner(param) {\n    let { bannerImg, height, altImg, letters = [], searchWord = \"\", hasContent = true } = param;\n    _s();\n    const { t } = useTranslation();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [keyword, setKeyword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(searchWord || searchParams?.get(\"word\") || \"\");\n    const handleSearchChange = (e)=>{\n        setKeyword(e.target.value);\n    };\n    const handleSearchClick = ()=>{\n        const params = new URLSearchParams();\n        if (keyword.trim()) {\n            params.set(\"word\", keyword.trim());\n        }\n        const queryString = params.toString();\n        router.push(`${pathname}${queryString ? `?${queryString}` : \"\"}`);\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\") {\n            handleSearchClick();\n        }\n    };\n    const handleClearSearch = ()=>{\n        setKeyword(\"\");\n        router.push(pathname);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"glossary-banner\",\n        className: \"center-banner\",\n        style: {\n            backgroundImage: `url(${bannerImg.src})`,\n            height: height\n        },\n        children: [\n            altImg && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                width: 0,\n                height: 0,\n                alt: altImg,\n                src: \"\",\n                style: {\n                    display: \"none\"\n                },\n                loading: \"lazy\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                lineNumber: 61,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_Input_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"top-section custom-max-width\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_BookIcon_svg__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"heading-h1 text-white page-title\",\n                        children: t(\"glossary:banner:title\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"glossary-search\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_Input_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"glossary-search-input\",\n                                type: \"text\",\n                                placeholder: \"What are you looking for ?\",\n                                onChange: handleSearchChange,\n                                onKeyDown: handleKeyDown,\n                                value: keyword\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_Input_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"btn-search\",\n                                onClick: handleSearchClick,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_searchIcon_svg__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this),\n                    hasContent && letters?.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"letters\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"letter selected\",\n                                children: \"#\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                                lineNumber: 91,\n                                columnNumber: 13\n                            }, this),\n                            letters.map((letter, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: `#${letter}`,\n                                    className: \"letter\",\n                                    children: letter\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 15\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, this),\n                    !hasContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"letters-empty\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                color: \"white\",\n                                opacity: 0.8,\n                                fontSize: \"14px\",\n                                marginTop: \"20px\"\n                            },\n                            children: searchWord ? `No results found for \"${searchWord}\"` : \"No glossary terms available\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                            lineNumber: 102,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n_s(GlossaryBanner, \"XcJDQxtYUQazr3yog0GQznqGJAQ=\", true, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = GlossaryBanner;\n/* harmony default export */ __webpack_exports__[\"default\"] = (GlossaryBanner);\nvar _c;\n$RefreshReg$(_c, \"GlossaryBanner\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/GlossaryBanner.jsx\n"));

/***/ })

});