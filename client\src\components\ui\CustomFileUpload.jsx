export default function CustomFileUpload({ label, onFileSelect, previewUrl, error }) {
  return (
    <div className="upload-container">
      <label className="file-labels">
        <input
          type="file"
          accept=".png, .jpg, .jpeg, .webp"
          onChange={onFileSelect}
          className={`file-input ${error ? "is-invalid" : ""}`}
        />
        <div className="upload-area">
          <div
            className="icon-pic"
            style={{
              backgroundImage: `url("${previewUrl}")`,
              backgroundSize: "cover",
              backgroundPosition: "center",
            }}
          />
          <p className="upload-text">{label}</p>
        </div>
      </label>
      {error && <div className="label-error">{error}</div>}
    </div>
  );
}
