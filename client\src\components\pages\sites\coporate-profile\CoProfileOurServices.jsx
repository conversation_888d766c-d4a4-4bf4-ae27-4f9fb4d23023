"use client";
import { Container, Grid } from "@mui/material";
import services from "@/assets/images/website/coporate-profile/services.png";
import yellowShape2 from "@/assets/images/website/coporate-profile/yellow-shape2.png";
import Link from "next/link";
import { websiteRoutesList } from "@/helpers/routesList";

function CoProfileOurServices() {
  return (
    <div id="coporate-profile-services">
      <Container className="custom-max-width white-bg">
        <Grid container>
          <Grid item xs={12} sm={12}>
            <h2 className="heading-h2 text-white bold text-center">
              How We’re Helping Our Clients
            </h2>
          </Grid>
          <Grid item xs={12} sm={12}>
            <Grid container className="nowrap-section">
              <Grid item xs={12} sm={12} md={4} className="relative-section">
                <img
                  className="fixed-element"
                  height={"100%"}
                  width={"auto"}
                  src={yellowShape2.src}
                  loading="lazy"
                />
                <img
                  width={"100%"}
                  height={"auto"}
                  alt={`Pentabell HR Services`}
                  title={`Pentabell HR Services`}
                  src={services.src}
                  loading="lazy"
                />
              </Grid>
              <Grid item xs={12} sm={12} md={9} className="contents">
                <Grid container columnSpacing={2}>
                  <Grid item xs={12} sm={6}>
                    <h3 className="sub-heading text-yellow semi-bold mb-0">
                      <Link className="internal-link yellow" href={`/${websiteRoutesList.services.route}/${websiteRoutesList.payrollServices.route}`}>
                      Global Payroll</Link>
                    </h3>
                    <p className="paragraph text-white">
                      We streamline global payroll operations with accurate, compliant, and timely payroll for businesses across countries. We automate the application of local tax laws, currency conversions, and reporting requirements to ensure that payroll operation runs seamlessly.
                    </p>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <h3 className="sub-heading text-yellow semi-bold mb-0">
                      
                      <Link className="internal-link yellow" href={`/${websiteRoutesList.services.route}/${websiteRoutesList.consultingServices.route}`}>
                      HR Consultancy</Link>
                      
                    </h3>
                    <p className="paragraph text-white ">
                      Our Human Resource Consulting services are a combination of strategic insights, industry expertise, and data-oriented approaches to improve workforce performance. We work with organizations to create and implement strategies for hiring, managing, and developing employees that support their objectives.
                    </p>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <h3 className="sub-heading text-yellow semi-bold mb-0">
                      
                      <Link className="internal-link yellow" href={`/${websiteRoutesList.services.route}/${websiteRoutesList.technicalAssistance.route}`}>
                      Technical Assistance</Link>
                      
                    </h3>
                    <p className="paragraph text-white ">
                      When technical challenges happen, they can disrupt your projects. We provide on-site support and project management assistance to keep things running smoothly. We help you with process improvements and risk reduction to keep your projects on track and moving successfully.
                    </p>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <h3 className="sub-heading text-yellow semi-bold mb-0">
                      
                      <Link className="internal-link yellow" href={`/${websiteRoutesList.services.route}/${websiteRoutesList.directHiring.route}`}>
                      Direct Hire</Link>
                      
                    </h3>
                    <p className="paragraph text-white ">
                      Pentabell connects organizations with highly qualified experts using global recruitment expertise and our in-house assessment tools. We handle the entire hiring process—sourcing, evaluating, and onboarding potential candidates—to match technical skills, cultural fit, and organizational needs.
                    </p>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <h3 className="sub-heading text-yellow semi-bold mb-0">
                      
                      <Link className="internal-link yellow" href={`/${websiteRoutesList.services.route}/${websiteRoutesList.aiSourcing.route}`}>
                      AI sourcing</Link>
                      
                    </h3>
                    <p className="paragraph text-white">
                      Pentabell uses AI technology to accelerate the hiring process. Our AI-driven algorithms analyze datasets to identify candidates with the right skills and experiences. We leverage our filtering and recommendation system to shortlist the best candidates for your job offer.
                    </p>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Container>
    </div>
  );
}

export default CoProfileOurServices;
