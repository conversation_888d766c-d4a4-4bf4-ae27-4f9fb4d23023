import { redirect } from "next/navigation";
import { axiosGetJsonSSR } from "@/config/axios";
import BannerComponents from "@/components/pages/sites/sections/BannerComponents";
import initTranslations from "@/app/i18n";
import blogBanner from "../../../../../../assets/images/website/banner/blogBanner.webp";
import GuideList from "../../../../../../features/guides/components/GuideList";


export async function generateMetadata({ params }) {
  const language = params.locale;
  const url = params?.category;
  let languages = {};
  const { t } = await initTranslations(language, ["aboutUs", "listArticle"]);

  let canonicalUrl = `https://www.pentabell.com/${
    language !== "en" ? `${language}/` : ""
  }guide/category/${url}/`;

  try {
    const res = await axiosGetJsonSSR.get(
      `/guidecategory/${language}/guide/${url}`
    );

    const oppositeLanguageUrl = res?.data?.categoryguide[0].url;
    const oppositeLanguageName = res?.data?.categoryguide[0].name;

    if (language === "fr") {
      languages.fr = `https://www.pentabell.com/fr/blog/category/${url}/`;

      if (oppositeLanguageUrl) {
        languages.en = `https://www.pentabell.com/blog/category/${oppositeLanguageUrl}/`;
        languages[
          "x-default"
        ] = `https://www.pentabell.com/blog/category/${oppositeLanguageUrl}/`;
      }
    }

    if (language === "en") {
      languages.en = `https://www.pentabell.com/blog/category/${url}/`;
      languages[
        "x-default"
      ] = `https://www.pentabell.com/blog/category/${url}/`;

      if (oppositeLanguageUrl) {
        languages.fr = `https://www.pentabell.com/fr/blog/category/${oppositeLanguageUrl}/`;
      }
    }
    return {
      title: `${t(
          "listArticle:metaTitleArticle" 
        )} ${oppositeLanguageName}
          ${t("listArticle:metaTitleArticle2")}`, 
          description: `${oppositeLanguageName} ${t(
            "listArticle:metaDescriptionArticle" 
          )} ${oppositeLanguageName}
            ${t("listArticle:metaDescriptionArticle2")}`, 
            alternates: {
              canonical: canonicalUrl,
              languages,
            },
    };
 
  } catch (error) {
    return {
      title: "Error",
      description: "An error occurred while fetching the article",
    };
  }


}

export default async function Page({ searchParams, params }) {
  const { t } = await initTranslations(params.locale, ["aboutUs", "global"]);

  const urlCategory = params?.category;

  try {
    const res = await axiosGetJsonSSR.get(
      `/guidecategory/${params.locale}/guide/category/${urlCategory}`,
      {
        params: {
          pageSize: 6,
          pageNumber: searchParams?.pageNumber || 1,
        },
      }
    );

    const articles = res?.data;
    // if (articles?.articles?.length > 0)
    // if (status === 404)
    //   redirect(params.locale === "en" ? `/blog` : `/${params.locale}/blog`);

    return (
      <>
        <BannerComponents
          bannerImg={blogBanner}
          height={"70vh"}
          
          description={t("global:getInspired")}
          title={"Pentabell Blog"}
        />
        <GuideList
          data={articles}
          language={params.locale}
          searchParams={searchParams}
          isCategory
        />
      </>
    );
    // else redirect(params.locale === "en" ? `/blog` : `/${params.locale}/blog`);
  } catch (error) {
    redirect(params.locale === "en" ? `/blog` : `/${params.locale}/blog`);
  }
}
