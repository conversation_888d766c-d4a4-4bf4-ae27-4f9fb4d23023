"use client ";
import { DataGrid } from "@mui/x-data-grid";
import { useTranslation } from "react-i18next";
import CustomButton from "@/components/ui/CustomButton";
import { useEffect, useState } from "react";
import { useGetContacts } from "../hooks/Contact.hooks";
import {
  Grid,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import { TypeContacts } from "@/utils/constants";
import { TypeContactLabels } from "@/utils/constants";
import Svgpreview from "@/assets/images/icons/preview-icon.svg";
import { adminRoutes, baseUrlBackoffice } from "../../../helpers/routesList";
import CustomFilters from "@/components/ui/CustomFilters";

function ListContacts() {
  const [type, setType] = useState("");
  const [keyword, setKeyword] = useState("");
  const [email, setEmail] = useState("");
  const [createdAt, setCreatedAt] = useState(null);
  const [sortOrder, setSortOrder] = useState("desc");
  const theme = useTheme();

  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const [paginationModel, setPaginationModel] = useState({
    page: 0,
    pageSize: 10,
  });
  const [search, setSearch] = useState(false);

  const { t } = useTranslation();

  const getContacts = useGetContacts({
    pageSize: paginationModel.pageSize,
    pageNumber: paginationModel.page + 1,
    paginated: true,
    keyword,
    sortOrder,
    createdAt,
    type,
    email,
  });

  useEffect(() => {
    getContacts.refetch();
  }, [search, paginationModel]);

  const rowsContacts =
    getContacts?.data?.contacts?.map((item, index) => ({
      id: item._id,
      fullName: item.fullName
        ? item.fullName
        : item.firstName + " " + item.lastName || "N/A",
      email: item?.email || "N/A",
      type: TypeContactLabels[item.type] || "N/A",
      actions: item?._id,
      userId: item?.userId,
    })) || [];
  const resetSearch = () => {
    setKeyword("");
    setEmail("");
    setType("");
    setSortOrder("");
    setCreatedAt(null);
    setPaginationModel({ page: 0, pageSize: 10 });
    setSearch(!search);
  };
  const handleCreatedAtChange = (date) => {
    setCreatedAt(date);
  };
  const handleSearchChange = (e) => {
    setKeyword(e.target.value);
  };

  const handleSearch = () => {
    setSearch(!search);
  };


  const columnsContacts = [
    {
      field: "fullName",
      cellClassName: "datagrid-cell",
      headerClassName: "datagrid-header",
      headerName: t("contact:fullName"),
      renderCell: (params) =>
        params.row.userId === null ? (
          <span>{params.value}</span>
        ) : (
          <CustomButton
            text={params.value}
            link={`/${baseUrlBackoffice.baseURL.route}/${adminRoutes.user.route}/${adminRoutes.detail.route}/${params.row.userId}/`}
            className="btn btn-ghost edit-blog"
          />
        ),
      flex: 1,
    },
    {
      field: "email",
      cellClassName: "datagrid-cell",
      headerClassName: "datagrid-header",
      headerName: t("contact:email"),
      renderCell: (params) => (
        <a href={`mailto:${params.value}`}>{params.value}</a>
      ),
      flex: 1,
    },
    {
      field: "type",
      cellClassName: "datagrid-cell",
      headerClassName: "datagrid-header",
      headerName: t("contact:type"),
      flex: 1,
    },
    {
      field: "actions",
      cellClassName: "datagrid-cell",
      headerClassName: "datagrid-header",
      headerName: t("contact:actions"),
      renderCell: (params) => (
        <div id="datagrid">
          <div className="action-buttons">
            <CustomButton
              text={t("contact:detail")}
              icon={<Svgpreview />}
              leftIcon={true}
              link={`/${baseUrlBackoffice.baseURL.route}/${adminRoutes.contacts.route}/${adminRoutes.detail.route}/${params.row.id}/`}
              className="btn btn-ghost edit-blog"
            />
          </div>
        </div>
      ),
      flex: 1,
    },
  ];

  const filters = [
    {
      label: t("Search"),
      type: "text",
      value: keyword,
      placeholder: "Search by message...",
      onChange: (e) => setKeyword(e.target.value),
    },
    {
      label: t("           Search by Name or Email"),
      type: "text",
      value: email,
      onChange: (e) => setEmail(e.target.value),
    },
    {
      label: t("Type"),
      type: "select",
      value: type,
      options: TypeContacts,
      onChange: (e, value) => setType(value),
    },
    {
      type: "select",
      label: t("global:sort"),
      value: sortOrder
        ? {
          value: sortOrder,
          label: t(sortOrder === "desc" ? "global:newest" : "global:oldest"),
        }
        : null,
      onChange: (_, val) => setSortOrder(val?.value || ""),
      options: [
        { value: "desc", label: t("global:newest") },
        { value: "asc", label: t("global:oldest") },
      ],
      condition: true,
    },
    {
      label: t("  Created At"),
      type: "date",
      value: createdAt,
      onChange: setCreatedAt,
    },

  ];
  return (
    <>
      <p className="heading-h2 semi-bold">
        {t("contact:listOfContacts")}{" "}
        <span className="opportunities-nbr">
          {getContacts?.data?.totalContacts}
        </span>
      </p>

      <div id="container" className="recent-application-pentabell">
        <div className={`main-content`}>
          <div>
            <Grid item xs={12}>
              <CustomFilters
                filters={filters}
                onSearch={handleSearch}
                onReset={resetSearch}
                searchLabel={t("global:search")}
              />
            </Grid>
          </div>
          <Grid item xs={12}>
            <div style={{ height: "100%", width: "100%", padding: "20px 0px" }}>
              <DataGrid
                localeText={{ noRowsLabel: "No result found." }}
                rows={rowsContacts}
                columns={columnsContacts}
                pagination
                autoHeight
                paginationMode="server"
                pageSizeOptions={[5, 10, 25]}
                paginationModel={paginationModel}
                onPaginationModelChange={setPaginationModel}
                rowCount={getContacts?.data?.totalContacts || 0}
                columnVisibilityModel={{
                  type: !isMobile,
                  createdAt: !isMobile,
                  email: !isMobile,
                }}
              />
            </div>{" "}
          </Grid>{" "}
        </div>
      </div>
    </>
  );
}
export default ListContacts;
