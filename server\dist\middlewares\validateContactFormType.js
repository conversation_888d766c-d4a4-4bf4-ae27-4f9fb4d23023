"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const constants_1 = require("@/utils/helpers/constants");
const contact_validation_1 = require("@/apis/contact/contact.validation");
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
let allowedFields = [];
let validationSchema = {};
const validateContactFormType = async (request, response, next) => {
    if (!request.body.type)
        return next(new http_exception_1.default(400, 'Type form is missing'));
    switch (request.body.type) {
        case 'getInTouch':
            allowedFields = constants_1.GetInTouchForm;
            validationSchema = contact_validation_1.getInTouchFormSchema;
            break;
        case 'getInTouchContact':
            allowedFields = constants_1.GetInTouchFormContact;
            validationSchema = contact_validation_1.getInTouchFormContactSchema;
            break;
        case 'payrollService':
            allowedFields = constants_1.PayrollServiceForm;
            validationSchema = contact_validation_1.payrollServiceFormSchema;
            break;
        case 'mainService':
            allowedFields = constants_1.mainServiceForm;
            validationSchema = contact_validation_1.mainServiceFormSchema;
            break;
        case 'technicalAssistanceService':
            allowedFields = constants_1.technicalAssistanceContact;
            validationSchema = contact_validation_1.technicalAssistanceContactSchema;
            break;
        case 'consultingService':
            allowedFields = constants_1.ConsultingServiceForm;
            validationSchema = contact_validation_1.technicalAssistanceContactSchema;
            break;
        case 'aiSourcingService':
            allowedFields = constants_1.ConsultingServiceForm;
            validationSchema = contact_validation_1.payrollServiceFormSchema;
            break;
        case 'directHiringService':
            allowedFields = constants_1.ConsultingServiceForm;
            validationSchema = contact_validation_1.DirectHiringSchema;
            break;
        case 'joinUs':
            allowedFields = constants_1.JoinUsForm;
            validationSchema = contact_validation_1.JoinUsFormContactSchema;
            break;
        case 'countryContact':
            allowedFields = constants_1.countryContact;
            validationSchema = contact_validation_1.CountryContactSchema;
            break;
        default:
            break;
    }
    const invalidFields = Object.keys(request.body).filter(field => !allowedFields.includes(field));
    if (invalidFields.length > 0) {
        console.log('Invalid field(s) :', invalidFields);
        return next(new http_exception_1.default(400, 'Invalid field(s) in request'));
    }
    const validationOptions = {
        abortEarly: false,
        allowUnknown: true,
        stripUnknown: true,
    };
    try {
        const value = await validationSchema.validateAsync(request.body, validationOptions);
        request.body = value;
        next();
    }
    catch (error) {
        const errors = [];
        error.details.forEach((error) => {
            errors.push(error.message);
        });
        response.status(500).send(errors);
    }
};
exports.default = validateContactFormType;
//# sourceMappingURL=validateContactFormType.js.map