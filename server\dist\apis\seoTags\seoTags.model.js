"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const constants_1 = require("@/utils/helpers/constants");
const seoVersionSchema = new mongoose_1.Schema({
    language: { type: String, enum: constants_1.Language, },
    metaTitle: {
        type: String,
        required: true,
        minlength: 3,
    },
    metaDescription: {
        type: String,
        required: true,
    },
    canonical: {
        type: String,
    },
});
const seoTagsSchema = new mongoose_1.Schema({
    slug: {
        type: String,
        required: true,
        minlength: 3,
    },
    versions: { type: [seoVersionSchema], required: true },
    robotMeta: {
        type: String,
        required: true,
    },
}, {
    timestamps: true,
});
exports.default = (0, mongoose_1.model)('SeoTags', seoTagsSchema);
//# sourceMappingURL=seoTags.model.js.map