"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/edit/[id]/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/EditArticle.jsx":
/*!******************************************************!*\
  !*** ./src/features/blog/components/EditArticle.jsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,Grid,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Accordion/Accordion.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,Grid,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionSummary/AccordionSummary.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,Grid,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AccordionDetails/AccordionDetails.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,Grid,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,Grid,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormGroup/FormGroup.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,Grid,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormLabel/FormLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,Grid,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,Grid,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Accordion,AccordionDetails,AccordionSummary,FormControl,FormGroup,FormLabel,Grid,MenuItem,Select!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mui/icons-material/ExpandMore */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/ExpandMore.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! yup */ \"(app-pages-browser)/./node_modules/yup/index.esm.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var _user_component_updateProfile_experience_DialogModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../user/component/updateProfile/experience/DialogModal */ \"(app-pages-browser)/./src/features/user/component/updateProfile/experience/DialogModal.jsx\");\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! formik */ \"(app-pages-browser)/./node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../utils/constants */ \"(app-pages-browser)/./src/utils/constants.js\");\n/* harmony import */ var _components_loading_Loading__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../components/loading/Loading */ \"(app-pages-browser)/./src/components/loading/Loading.jsx\");\n/* harmony import */ var _utils_urls__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../utils/urls */ \"(app-pages-browser)/./src/utils/urls.js\");\n/* harmony import */ var _opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../opportunity/hooks/opportunity.hooks */ \"(app-pages-browser)/./src/features/opportunity/hooks/opportunity.hooks.js\");\n/* harmony import */ var _config_axios__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/config/axios */ \"(app-pages-browser)/./src/config/axios.js\");\n/* harmony import */ var _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../hooks/blog.hook */ \"(app-pages-browser)/./src/features/blog/hooks/blog.hook.js\");\n/* harmony import */ var _AddArticle__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./AddArticle */ \"(app-pages-browser)/./src/features/blog/components/AddArticle.jsx\");\n/* harmony import */ var _assets_images_icons_archiveicon_popup_svg__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/assets/images/icons/archiveicon-popup.svg */ \"(app-pages-browser)/./src/assets/images/icons/archiveicon-popup.svg\");\n/* harmony import */ var react_datepicker_dist_react_datepicker_css__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! react-datepicker/dist/react-datepicker.css */ \"(app-pages-browser)/./node_modules/react-datepicker/dist/react-datepicker.css\");\n/* harmony import */ var _features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/features/auth/hooks/currentUser.hooks */ \"(app-pages-browser)/./src/features/auth/hooks/currentUser.hooks.js\");\n/* harmony import */ var _components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst EditArticle = (param)=>{\n    let { articleId } = param;\n    _s();\n    const { data } = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__.useGetArticleByIdAll)(articleId);\n    const usedisarchivedArticleHook = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__.usearchivedarticle)();\n    const { data: dataEN, isLoading: isLoadingEN, isError, error } = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__.useGetArticleById)(articleId, \"en\");\n    const { data: dataFR, isLoading: isLoadingFR } = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__.useGetArticleById)(articleId, \"fr\");\n    const [isArchived, setIsArchived] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isArchivedFr, setIsArchivedFr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { user } = (0,_features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_15__[\"default\"])();\n    const [englishVersion, setEnglishVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [frenchVersion, setFrenchVersion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const useUpdateArticleHook = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__.useUpdateArticle)();\n    const useSaveFileHook = (0,_opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_9__.useSaveFile)();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const currentYear = new Date().getFullYear();\n    const formikRefEn = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const formikRefFr = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const formikRefAll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [expanded, setExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(`panel`);\n    const [formdataEN, setFormDataEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [formdataFR, setFormDataFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const formdata = new FormData();\n    const formdatafr = new FormData();\n    const [uuidPhotoFileNameEN, setUuidPhotoFileNameEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedAction, setSelectedAction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedEnCategories, setSelectedEnCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(dataEN?.versions?.categories.map((category)=>category.id) || []);\n    const [filteredFrCategories, setFilteredFrCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [openDialog, setOpenDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openDialogfr, setOpenDialogfr] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleOpenDialog = (action1)=>{\n        setSelectedAction(action1);\n        setOpenDialog(true);\n    };\n    const handleOpenDialogfr = (action1)=>{\n        setSelectedAction(action1);\n        setOpenDialogfr(true);\n    };\n    const handleCloseDialogfr = ()=>{\n        setOpenDialogfr(false);\n    };\n    const handleCloseDialog = ()=>{\n        setOpenDialog(false);\n    };\n    const useUpdateArticleAllHook = (0,_hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__.useUpdateArticleAll)();\n    const [uuidPhotoFileNameFR, setUuidPhotoFileNameFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [uuidPhotoEN, setUuidPhotoEN] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uuidPhotoFR, setUuidPhotoFR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setEnglishVersion(dataEN ? dataEN?.versions : \"\");\n        setFrenchVersion(dataFR ? dataFR?.versions : \"\");\n        if (dataEN?.versions?.categories.length > 0 && dataEN?.versions?.categories[0] != {}) {\n            setSelectedEnCategories(dataEN?.versions?.categories.map((category)=>category.id));\n        }\n        setIsArchived(dataEN?.versions?.isArchived || false);\n        setIsArchivedFr(dataFR?.versions?.isArchived || false);\n    }, [\n        dataEN,\n        dataFR\n    ]);\n    const handleConfirmAction = ()=>{\n        if (selectedAction === \"archive\") {\n            handledisarchivearticleen(true);\n        } else if (selectedAction === \"desarchive\") {\n            handledisarchivearticleen(false);\n        }\n        setOpenDialog(false);\n    };\n    const handleConfirmActionFr = ()=>{\n        if (selectedAction === \"archive\") {\n            handlearchiveanddisarchivearticleFr(true);\n        } else if (selectedAction === \"desarchive\") {\n            handlearchiveanddisarchivearticleFr(false);\n        }\n        setOpenDialogfr(false);\n    };\n    const handleImageSelect = async (selectedFile, language)=>{\n        if (language === \"en\") {\n            let uuidPhotos = (0,uuid__WEBPACK_IMPORTED_MODULE_17__[\"default\"])().replace(/-/g, \"\");\n            setUuidPhotoEN(uuidPhotos);\n            formdata.append(\"file\", selectedFile);\n            setFormDataEN(formdata);\n            const extension = selectedFile.name.split(\".\").pop();\n            setUuidPhotoFileNameEN(`${uuidPhotos}.${extension}`);\n        } else if (language === \"fr\") {\n            let uuidPhotos = (0,uuid__WEBPACK_IMPORTED_MODULE_17__[\"default\"])().replace(/-/g, \"\");\n            setUuidPhotoFR(uuidPhotos);\n            formdatafr.append(\"file\", selectedFile);\n            setFormDataFR(formdatafr);\n            const extension = selectedFile.name.split(\".\").pop();\n            setUuidPhotoFileNameFR(`${uuidPhotos}.${extension}`);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setEnglishVersion(dataEN ? dataEN?.versions : \"\");\n        setFrenchVersion(dataFR ? dataFR?.versions : \"\");\n        setSelectedEnCategories(dataEN?.versions?.categories.map((category)=>category.id));\n    }, [\n        dataEN,\n        dataFR\n    ]);\n    const initialValuesEn = {\n        metaTitle: englishVersion?.metaTitle,\n        metaDescription: englishVersion?.metaDescription,\n        description: englishVersion?.description,\n        visibility: englishVersion ? englishVersion.visibility : \"\",\n        category: englishVersion?.categories?.length > 0 ? englishVersion.categories?.map((category)=>category.id) : [\n            {\n                name: \"\"\n            }\n        ],\n        image: englishVersion?.image,\n        keywords: englishVersion?.keywords?.map((keyword, index)=>({\n                id: keyword.trim() || `id-${index}`,\n                text: keyword.trim()\n            })),\n        title: englishVersion?.title,\n        url: englishVersion?.url,\n        alt: englishVersion?.alt,\n        content: englishVersion?.content,\n        language: \"en\",\n        publishDate: englishVersion?.publishDate,\n        createdBy: user?.firstName + \" \" + user?.lastName,\n        highlights: englishVersion?.highlights?.map((keyword, index)=>({\n                id: keyword.trim() || `id-${index}`,\n                text: keyword.trim()\n            })),\n        faqTitle: englishVersion?.faqTitle || \"\",\n        faq: englishVersion?.faq || [],\n        ctsBannerImage: englishVersion?.ctsBanner?.image || \"\",\n        ctsBannerLink: englishVersion?.ctsBanner?.link || \"\"\n    };\n    const initialValuesAll = {\n        robotsMeta: data?.robotsMeta\n    };\n    const initialValuesFr = {\n        metaTitle: frenchVersion ? frenchVersion?.metaTitle : \"\",\n        metaDescription: frenchVersion ? frenchVersion?.metaDescription : \"\",\n        description: frenchVersion ? frenchVersion?.description : \"\",\n        visibility: frenchVersion ? frenchVersion?.visibility : \"\",\n        category: frenchVersion?.categories?.length > 0 ? frenchVersion.categories?.map((category)=>category.id) : [\n            {\n                name: \"\"\n            }\n        ],\n        image: frenchVersion ? frenchVersion?.image : \"\",\n        keywords: frenchVersion?.keywords?.map((keyword, index)=>({\n                id: keyword?.trim() || `id-${index}`,\n                text: keyword?.trim()\n            })),\n        title: frenchVersion ? frenchVersion?.title : \"\",\n        url: frenchVersion ? frenchVersion?.url : \"\",\n        alt: frenchVersion ? frenchVersion?.alt : \"\",\n        content: frenchVersion ? frenchVersion?.content : \"\",\n        language: \"fr\",\n        publishDate: frenchVersion ? frenchVersion?.publishDate : \"\",\n        createdBy: user?.firstName + \" \" + user?.lastName,\n        highlights: frenchVersion?.highlights?.map((keyword, index)=>({\n                id: keyword.trim() || `id-${index}`,\n                text: keyword.trim()\n            })),\n        faqTitle: frenchVersion?.faqTitle || \"\",\n        faq: frenchVersion?.faq || [],\n        ctsBannerImage: frenchVersion?.ctsBanner?.image || \"\",\n        ctsBannerLink: frenchVersion?.ctsBanner?.link || \"\"\n    };\n    const validationSchemaEN = yup__WEBPACK_IMPORTED_MODULE_3__.object().shape({\n        metaTitle: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n        metaDescription: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n        title: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n        image: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\")),\n        visibility: yup__WEBPACK_IMPORTED_MODULE_3__.string().required(t(\"validations:emptyField\"))\n    });\n    const handleSaveSetting = (values)=>{\n        const data = {\n            robotsMeta: values?.robotsMeta\n        };\n        useUpdateArticleAllHook.mutate({\n            data: data,\n            id: articleId\n        }, {\n            onSuccess: ()=>{\n            // setTimeout(\n            //\n            //   (window.location.href = `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.opportunities.route}/`),\n            //   \"3000\"\n            // );\n            }\n        });\n    };\n    const handledisarchivearticleen = async ()=>{\n        const action1 = isArchived ? false : true;\n        try {\n            await usedisarchivedArticleHook.mutateAsync({\n                language: \"en\",\n                id: articleId,\n                archive: action1\n            });\n            setIsArchived(action1);\n        } catch (error) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(t(action1 ? \"article:archivedError\" : \"article:desarchivedError\"));\n            console.error(\"Erreur lors de l'archivage/d\\xe9sarchivage de l'article:\", error);\n        }\n    };\n    const handlearchiveanddisarchivearticleFr = async ()=>{\n        try {\n            await usedisarchivedArticleHook.mutateAsync({\n                language: \"fr\",\n                id: articleId,\n                archive: action\n            });\n        } catch (error) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(t(action ? \"article:archivedError\" : \"article:desarchivedError\"));\n        }\n    };\n    const handleSaveEN = async ()=>{\n        const enValues = formikRefEn.current.values;\n        const hasId = enValues.keywords.every((item)=>item.hasOwnProperty(\"id\"));\n        const highlightshasId = enValues?.highlights?.every((item)=>item.hasOwnProperty(\"id\"));\n        if (hasId) {\n            enValues.keywords = enValues.keywords.filter((item)=>item.hasOwnProperty(\"id\") && item.text !== \"\").map((item)=>item.text);\n        }\n        if (highlightshasId) {\n            enValues.highlights = enValues.keywords.filter((item)=>item.hasOwnProperty(\"id\") && item.text !== \"\").map((item)=>item.text);\n        }\n        if (enValues.category.length > 0 && typeof enValues.category[0] === \"object\") {\n            let categoryEn = [];\n            enValues.category.map((category)=>categoryEn.push(category.id));\n            enValues.category = categoryEn;\n        }\n        formikRefEn.current.submitForm();\n        const isEnFormValid = formikRefEn.current.isValid && Object.keys(formikRefEn.current.errors).length === 0;\n        if (isEnFormValid) {\n            if (uuidPhotoFileNameEN) {\n                enValues.image = uuidPhotoFileNameEN;\n                useSaveFileHook.mutate({\n                    resource: \"blogs\",\n                    folder: currentYear,\n                    filename: uuidPhotoEN,\n                    body: {\n                        formData: formdataEN,\n                        t\n                    }\n                }, {\n                    onSuccess: (data)=>{\n                        enValues.image = data.uuid;\n                        useUpdateArticleHook.mutate({\n                            data: enValues,\n                            language: \"en\",\n                            id: articleId\n                        });\n                    }\n                });\n            } else {\n                useUpdateArticleHook.mutate({\n                    data: enValues,\n                    language: \"en\",\n                    id: articleId\n                });\n            }\n        }\n    };\n    const handleSaveFR = async ()=>{\n        const frValues = formikRefFr.current.values;\n        const hasId = frValues.keywords?.every((item)=>item.hasOwnProperty(\"id\"));\n        const highlightshasId = frValues?.highlights?.every((item)=>item.hasOwnProperty(\"id\"));\n        if (hasId) {\n            frValues.keywords = frValues.keywords.filter((item)=>item.hasOwnProperty(\"id\") && item.text !== \"\").map((item)=>item.text);\n        }\n        if (highlightshasId) {\n            frValues.highlights = frValues.keywords.filter((item)=>item?.hasOwnProperty(\"id\") && item.text !== \"\").map((item)=>item.text);\n        }\n        if (frValues.category.length > 0 && typeof frValues.category[0] === \"object\") {\n            let categoryFr = [];\n            frValues.category.map((category)=>categoryFr.push(category.id));\n            frValues.category = categoryFr;\n        }\n        formikRefFr.current.submitForm();\n        const isFrFormValid = formikRefFr.current.isValid && Object.keys(formikRefFr.current.errors).length === 0;\n        if (isFrFormValid) {\n            if (uuidPhotoFileNameFR) {\n                frValues.image = uuidPhotoFileNameFR;\n                useSaveFileHook.mutate({\n                    resource: \"categories\",\n                    folder: currentYear,\n                    filename: uuidPhotoFR,\n                    body: {\n                        formData: formdataFR,\n                        t\n                    }\n                }, {\n                    onSuccess: (data)=>{\n                        if (data.message === \"uuid exist\") {\n                            frValues.image = data.uuid;\n                        }\n                        useUpdateArticleHook.mutate({\n                            data: frValues,\n                            language: \"fr\",\n                            id: articleId\n                        });\n                    }\n                });\n            } else {\n                useUpdateArticleHook.mutate({\n                    data: frValues,\n                    language: \"fr\",\n                    id: articleId\n                });\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchFrenchCategories = async ()=>{\n            try {\n                const response = await _config_axios__WEBPACK_IMPORTED_MODULE_10__.axiosGetJson.get(`${_utils_urls__WEBPACK_IMPORTED_MODULE_8__.API_URLS.categories}/en/${selectedEnCategories}`);\n                const transformedCategories = response.data.map((category)=>({\n                        id: category._id,\n                        name: category.name\n                    }));\n                setFilteredFrCategories(transformedCategories);\n            } catch (error) {}\n        };\n        if (selectedEnCategories?.length > 0) {\n            fetchFrenchCategories();\n        } else {\n            setFilteredFrCategories([]);\n        }\n    }, [\n        selectedEnCategories\n    ]);\n    const handleChangeAccordion = (panel)=>(event, newExpanded)=>{\n            setExpanded(newExpanded ? panel : false);\n        };\n    if (isLoadingFR || isLoadingEN) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"content\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_loading_Loading__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                lineNumber: 444,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n            lineNumber: 443,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"heading-h2 semi-bold\",\n                children: t(\"createArticle:editArticle\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                lineNumber: 451,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"container\",\n                className: \"recent-application-pentabell\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"main-content\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"commun\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            id: \"experiences\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_18__.Formik, {\n                                    initialValues: initialValuesAll,\n                                    innerRef: formikRefAll,\n                                    children: (param)=>{\n                                        let { errors, touched, setFieldValue, values } = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_18__.Form, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    id: \"accordion\",\n                                                    expanded: expanded === `panel`,\n                                                    onChange: handleChangeAccordion(`panel`),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                                lineNumber: 466,\n                                                                columnNumber: 37\n                                                            }, void 0),\n                                                            \"aria-controls\": `panel-content`,\n                                                            id: `panel-header`,\n                                                            children: t(\"createArticle:settings\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                            lineNumber: 465,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            className: \"accordion-detail\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                container: true,\n                                                                spacing: 4,\n                                                                sx: {\n                                                                    alignItems: \"flex-end\"\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        item: true,\n                                                                        xs: 12,\n                                                                        md: 10,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                className: \"label-form\",\n                                                                                children: [\n                                                                                    t(\"createArticle:Robotsmeta\"),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                        className: \"select-pentabell\",\n                                                                                        variant: \"standard\",\n                                                                                        sx: {\n                                                                                            m: 1,\n                                                                                            minWidth: 120\n                                                                                        },\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                            value: values.robotsMeta,\n                                                                                            onChange: (event)=>{\n                                                                                                setFieldValue(\"robotsMeta\", event.target.value);\n                                                                                            },\n                                                                                            children: _utils_constants__WEBPACK_IMPORTED_MODULE_6__.RobotsMeta.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                                                    value: item,\n                                                                                                    children: item\n                                                                                                }, index, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                                                                    lineNumber: 497,\n                                                                                                    columnNumber: 39\n                                                                                                }, undefined))\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                                                            lineNumber: 487,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                                                        lineNumber: 482,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_18__.ErrorMessage, {\n                                                                                        className: \"label-error\",\n                                                                                        name: \"robotsMeta\",\n                                                                                        component: \"div\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                                                        lineNumber: 503,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                                                lineNumber: 480,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                                            lineNumber: 479,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                                        lineNumber: 478,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    \" \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        item: true,\n                                                                        xs: 12,\n                                                                        md: 2,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            text: \"Save\",\n                                                                            className: \"btn btn-filled\",\n                                                                            onClick: ()=>handleSaveSetting(values)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                                            lineNumber: 512,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                                        lineNumber: 511,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                                lineNumber: 473,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                            lineNumber: 472,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, `panel}`, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \" \"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                            lineNumber: 458,\n                                            columnNumber: 19\n                                        }, undefined);\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                    lineNumber: 456,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddArticle__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    image: englishVersion?.image,\n                                    language: \"en\",\n                                    initialValues: initialValuesEn,\n                                    formRef: formikRefEn,\n                                    onImageSelect: handleImageSelect,\n                                    validationSchema: validationSchemaEN,\n                                    isEdit: true,\n                                    onCategoriesSelect: (categories)=>{\n                                        setSelectedEnCategories(categories);\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                    lineNumber: 524,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"btn-container\",\n                                    children: [\n                                        \"\\xa0\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            text: t(\"global:save\"),\n                                            className: \"btn btn-filled\",\n                                            onClick: handleSaveEN\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                            lineNumber: 538,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            text: isArchived ? t(\"global:unarchive\") : t(\"global:archive\"),\n                                            className: \"btn btn-filled\",\n                                            onClick: ()=>handleOpenDialog(isArchived ? \"desarchive\" : \"archive\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                    lineNumber: 536,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_user_component_updateProfile_experience_DialogModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    open: openDialog,\n                                    message: selectedAction === \"archive\" ? t(\"messages:archiveConfirmation\") : t(\"messages:desarchiveConfirmation\"),\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_archiveicon_popup_svg__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                        lineNumber: 560,\n                                        columnNumber: 23\n                                    }, void 0),\n                                    onClose: handleCloseDialog,\n                                    onConfirm: handleConfirmAction\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                    lineNumber: 553,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                    lineNumber: 564,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    id: \"accordion\",\n                                    expanded: expanded === `panel-1`,\n                                    onChange: handleChangeAccordion(`panel-1`),\n                                    isEdit: true,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            expandIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ExpandMore__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                lineNumber: 573,\n                                                columnNumber: 31\n                                            }, void 0),\n                                            \"aria-controls\": `panel-content`,\n                                            id: `panel-header`,\n                                            children: t(\"createArticle:editArticleFr\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                            lineNumber: 572,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Accordion_AccordionDetails_AccordionSummary_FormControl_FormGroup_FormLabel_Grid_MenuItem_Select_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"accordion-detail\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddArticle__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    image: frenchVersion?.image,\n                                                    language: \"fr\",\n                                                    initialValues: initialValuesFr,\n                                                    formRef: formikRefFr,\n                                                    onImageSelect: handleImageSelect,\n                                                    validationSchema: validationSchemaEN,\n                                                    isEdit: true,\n                                                    filteredCategories: filteredFrCategories\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                    lineNumber: 580,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"btn-container\",\n                                                    children: [\n                                                        \"\\xa0\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            text: t(\"global:save\"),\n                                                            className: \"btn btn-filled\",\n                                                            onClick: handleSaveFR\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                            lineNumber: 592,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            text: isArchivedFr ? t(\"global:unarchive\") : t(\"global:archive\"),\n                                                            className: \"btn btn-filled\",\n                                                            onClick: ()=>handleOpenDialogfr(isArchivedFr ? \"desarchive\" : \"archive\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                            lineNumber: 597,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                    lineNumber: 590,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_user_component_updateProfile_experience_DialogModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    open: openDialogfr,\n                                                    message: selectedAction === \"archive\" ? t(\"messages:archiveConfirmationfr\") : t(\"messages:desarchiveConfirmationfr\"),\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_archiveicon_popup_svg__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                        lineNumber: 619,\n                                                        columnNumber: 27\n                                                    }, void 0),\n                                                    onClose: handleCloseDialogfr,\n                                                    onConfirm: handleConfirmActionFr\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                                    lineNumber: 612,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                            lineNumber: 579,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, `panel-1`, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                                    lineNumber: 565,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                            lineNumber: 455,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                        lineNumber: 454,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                    lineNumber: 453,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\EditArticle.jsx\",\n                lineNumber: 452,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(EditArticle, \"vOs6f+0R7ZX8SlB03lQ59mjcpAc=\", false, function() {\n    return [\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__.useGetArticleByIdAll,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__.useGetArticleById,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__.useGetArticleById,\n        _features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__.useUpdateArticle,\n        _opportunity_hooks_opportunity_hooks__WEBPACK_IMPORTED_MODULE_9__.useSaveFile,\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _hooks_blog_hook__WEBPACK_IMPORTED_MODULE_11__.useUpdateArticleAll\n    ];\n});\n_c = EditArticle;\n/* harmony default export */ __webpack_exports__[\"default\"] = (EditArticle);\nvar _c;\n$RefreshReg$(_c, \"EditArticle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/EditArticle.jsx\n"));

/***/ })

});