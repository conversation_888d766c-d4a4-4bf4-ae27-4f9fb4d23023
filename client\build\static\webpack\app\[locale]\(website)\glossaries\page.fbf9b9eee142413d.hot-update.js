"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/glossaries/page",{

/***/ "(app-pages-browser)/./src/features/glossary/component/GlossariesListWebsite.jsx":
/*!*******************************************************************!*\
  !*** ./src/features/glossary/component/GlossariesListWebsite.jsx ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GlossaryListWebsite; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Container,Grid,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_icons_material_Search__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mui/icons-material/Search */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Search.js\");\n/* harmony import */ var _mui_icons_material_Book__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/icons-material/Book */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Book.js\");\n/* harmony import */ var _mui_icons_material_ErrorOutline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mui/icons-material/ErrorOutline */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/ErrorOutline.js\");\n/* harmony import */ var _components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst EmptyGlossaryState = (param)=>{\n    let { isEmpty, isEmptySearch, searchWord, locale, translations } = param;\n    if (isEmpty) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"empty-glossary-state\",\n            sx: {\n                textAlign: \"center\",\n                py: 8,\n                px: 4,\n                minHeight: \"400px\",\n                display: \"flex\",\n                flexDirection: \"column\",\n                alignItems: \"center\",\n                justifyContent: \"center\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Book__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        fontSize: 80,\n                        color: \"text.secondary\",\n                        mb: 3,\n                        opacity: 0.5\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    variant: \"h4\",\n                    component: \"h2\",\n                    gutterBottom: true,\n                    sx: {\n                        fontWeight: 600,\n                        color: \"text.primary\",\n                        mb: 2\n                    },\n                    children: translations?.emptyState?.title || \"No Glossary Terms Available\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    variant: \"body1\",\n                    color: \"text.secondary\",\n                    sx: {\n                        maxWidth: 600,\n                        mb: 4,\n                        lineHeight: 1.6\n                    },\n                    children: translations?.emptyState?.description || \"We're currently building our glossary. Check back soon for comprehensive definitions and explanations of industry terms.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    text: translations?.emptyState?.exploreButton || \"Explore Our Services\",\n                    link: locale === \"fr\" ? \"/fr/blog\" : \"/blog\",\n                    className: \"btn btn-filled\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (isEmptySearch) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"empty-search-state\",\n            sx: {\n                textAlign: \"center\",\n                py: 8,\n                px: 4,\n                minHeight: \"400px\",\n                display: \"flex\",\n                flexDirection: \"column\",\n                alignItems: \"center\",\n                justifyContent: \"center\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Search__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    sx: {\n                        fontSize: 80,\n                        color: \"text.secondary\",\n                        mb: 3,\n                        opacity: 0.5\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    variant: \"h4\",\n                    component: \"h2\",\n                    gutterBottom: true,\n                    sx: {\n                        fontWeight: 600,\n                        color: \"text.primary\",\n                        mb: 2\n                    },\n                    children: translations?.searchEmpty?.title || \"No Results Found\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    variant: \"body1\",\n                    color: \"text.secondary\",\n                    sx: {\n                        maxWidth: 600,\n                        mb: 2,\n                        lineHeight: 1.6\n                    },\n                    children: translations?.searchEmpty?.description || `No glossary terms found for \"${searchWord}\". Try searching with different keywords or browse all terms.`\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    sx: {\n                        mt: 3,\n                        display: \"flex\",\n                        gap: 2,\n                        flexWrap: \"wrap\",\n                        justifyContent: \"center\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            text: translations?.searchEmpty?.clearButton || \"Clear Search\",\n                            link: locale === \"fr\" ? \"/fr/glossaries\" : \"/glossaries\",\n                            className: \"btn btn-outline\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            text: translations?.searchEmpty?.browseButton || \"Browse All Terms\",\n                            link: locale === \"fr\" ? \"/fr/glossaries\" : \"/glossaries\",\n                            className: \"btn btn-filled\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 122,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n            lineNumber: 77,\n            columnNumber: 7\n        }, undefined);\n    }\n    return null;\n};\n_c = EmptyGlossaryState;\nconst ErrorGlossaryState = (param)=>{\n    let { error, locale, translations } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        className: \"error-glossary-state\",\n        sx: {\n            textAlign: \"center\",\n            py: 8,\n            px: 4,\n            minHeight: \"400px\",\n            display: \"flex\",\n            flexDirection: \"column\",\n            alignItems: \"center\",\n            justifyContent: \"center\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ErrorOutline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                sx: {\n                    fontSize: 80,\n                    color: \"error.main\",\n                    mb: 3,\n                    opacity: 0.7\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                lineNumber: 163,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                variant: \"h4\",\n                component: \"h2\",\n                gutterBottom: true,\n                sx: {\n                    fontWeight: 600,\n                    color: \"text.primary\",\n                    mb: 2\n                },\n                children: translations?.error?.title || \"Unable to Load Glossary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                lineNumber: 171,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                variant: \"body1\",\n                color: \"text.secondary\",\n                sx: {\n                    maxWidth: 600,\n                    mb: 4,\n                    lineHeight: 1.6\n                },\n                children: translations?.error?.description || \"We're experiencing technical difficulties loading the glossary. Please try again later.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                lineNumber: 183,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                severity: \"error\",\n                sx: {\n                    mb: 3,\n                    maxWidth: 500\n                },\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                lineNumber: 195,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                text: translations?.error?.retryButton || \"Try Again\",\n                onClick: ()=>window.location.reload(),\n                className: \"btn btn-filled\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                lineNumber: 198,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n        lineNumber: 150,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = ErrorGlossaryState;\nfunction GlossaryListWebsite(param) {\n    let { glossaries, locale, error, isEmpty, isEmptySearch, searchWord, translations } = param;\n    _s();\n    const letters = Object.keys(glossaries || {});\n    const [expandedLetters, setExpandedLetters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const handleToggle = (letter)=>{\n        setExpandedLetters((prev)=>({\n                ...prev,\n                [letter]: !prev[letter]\n            }));\n    };\n    const hasId = (id)=>{\n        const hash = window.location.hash;\n        return hash === `#${id}`;\n    };\n    useEffect(()=>{\n        const hash = window.location.hash; // e.g., \"#A\"\n        if (hash === \"#A\") {\n            console.log(\"Hash is #A\");\n            // Optional: Scroll to the element with ID \"A\"\n            const element = document.getElementById(\"A\");\n            if (element) {\n                element.scrollIntoView({\n                    behavior: \"smooth\"\n                });\n            }\n        }\n    }, []);\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            id: \"glossary-page\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"custom-max-width\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorGlossaryState, {\n                    error: error,\n                    locale: locale,\n                    translations: translations\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 247,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                lineNumber: 246,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n            lineNumber: 245,\n            columnNumber: 7\n        }, this);\n    }\n    if (isEmpty || isEmptySearch) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            id: \"glossary-page\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"custom-max-width\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EmptyGlossaryState, {\n                    isEmpty: isEmpty,\n                    isEmptySearch: isEmptySearch,\n                    searchWord: searchWord,\n                    locale: locale,\n                    translations: translations\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 261,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                lineNumber: 260,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n            lineNumber: 259,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"glossary-page\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            className: \"custom-max-width\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    container: true,\n                    spacing: 3,\n                    children: letters?.length > 0 && letters?.map((letter, index)=>{\n                        const letterGlossaries = glossaries[letter] || [];\n                        const isExpanded = expandedLetters[letter] || false;\n                        const displayedGlossaries = isExpanded ? letterGlossaries : letterGlossaries.slice(0, 5);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            item: true,\n                            lg: 3,\n                            md: 4,\n                            sm: 6,\n                            xs: 6,\n                            className: \"letters\",\n                            id: letter,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `letter-section ${hasId(letter) ? \"circled\" : \"\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"length\",\n                                        children: letterGlossaries.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"letter\",\n                                        children: letter\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"words\",\n                                        children: displayedGlossaries.map((glossary, glossaryIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                className: \"word\",\n                                                href: `${locale === \"fr\" ? \"/fr\" : \"\"}/glossaries/${glossary.url}`,\n                                                title: glossary.word,\n                                                children: glossary.word\n                                            }, `${glossary.url}-${glossaryIndex}`, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 25\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 21\n                                    }, this),\n                                    letterGlossaries.length > 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"glossary-button\",\n                                        onClick: ()=>handleToggle(letter),\n                                        size: \"small\",\n                                        variant: \"text\",\n                                        children: isExpanded ? translations?.showLess || \"Show less\" : translations?.showMore || \"Show more\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 23\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                lineNumber: 295,\n                                columnNumber: 19\n                            }, this)\n                        }, index, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                            lineNumber: 285,\n                            columnNumber: 17\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 276,\n                    columnNumber: 9\n                }, this),\n                letters.length > 8 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    id: \"back-to-top\",\n                    sx: {\n                        textAlign: \"center\",\n                        mt: 6\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Container_Grid_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"btn btn-filled-yellow\",\n                        onClick: ()=>window.scrollTo({\n                                top: 0,\n                                behavior: \"smooth\"\n                            }),\n                        children: translations?.backToTop || \"Back to Top\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                        lineNumber: 336,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                    lineNumber: 335,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n            lineNumber: 275,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n        lineNumber: 274,\n        columnNumber: 5\n    }, this);\n}\n_s(GlossaryListWebsite, \"j5IWYoeXU+broLETSQxmivBGwBM=\");\n_c2 = GlossaryListWebsite;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"EmptyGlossaryState\");\n$RefreshReg$(_c1, \"ErrorGlossaryState\");\n$RefreshReg$(_c2, \"GlossaryListWebsite\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/glossary/component/GlossariesListWebsite.jsx\n"));

/***/ })

});