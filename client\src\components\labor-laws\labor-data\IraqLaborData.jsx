"use client";
import LaborLaws from "../LaborLaws";
import { createList } from '../../../utils/functions';
export const LABOR_KEYS = {
    workingHours: {
        title: "iraq:IraqLabor:workingHours:title",
    },
    employmentContracts: {
        title: "iraq:IraqLabor:employmentContracts:title",
    },
};
export const iraqLaborData = {
    title: "iraq:IraqLabor:title",
    sections: [
        {
            id: 1,
            title: LABOR_KEYS.workingHours.title,
            subsections: [
                {
                    title: "iraq:IraqLabor:workingHours:title1",
                    description: "iraq:IraqLabor:workingHours:description1",
                },
                {
                    title: "iraq:IraqLabor:workingHours:title2",
                    description: "iraq:IraqLabor:workingHours:description2",
                },
            ],
        },
        {
            id: 2,
            title: LABOR_KEYS.employmentContracts.title,
            subsections: [
                {
                    description: "iraq:IraqLabor:employmentContracts:description"
                },
                {
                    title: "iraq:IraqLabor:employmentContracts:title1",
                    list: createList(["iraq:IraqLabor:employmentContracts:data1", "iraq:IraqLabor:employmentContracts:data2"]),

                },
                { description: 'iraq:IraqLabor:employmentContracts:ps' }
            ],
        },
        {
            id: 3,
            title: "iraq:IraqLabor:payroll:title",
            type: "payroll",
            titleKey: "iraq:IraqLabor:payroll:description",

            items: [
                {
                    titleKey: "iraq:IraqLabor:payroll:fiscalYear:title",
                    dateKeys: ["iraq:IraqLabor:payroll:fiscalYear:date1", "iraq:IraqLabor:payroll:fiscalYear:date2"],
                    descriptionKey: "iraq:IraqLabor:payroll:fiscalYear:description",
                },
                {
                    titleKey: "iraq:IraqLabor:payroll:payrollCycle:title",
                    dateKeys: ["iraq:IraqLabor:payroll:payrollCycle:date"],
                    descriptionKey: "iraq:IraqLabor:payroll:payrollCycle:description",
                },
                {
                    titleKey: "iraq:IraqLabor:payroll:minimumWage:title",
                    dateKeys: ["iraq:IraqLabor:payroll:minimumWage:wage", "iraq:IraqLabor:payroll:minimumWage:date"],
                    descriptionKey: "iraq:IraqLabor:payroll:minimumWage:description",
                },
                {
                    titleKey: "iraq:IraqLabor:payroll:payrollManagement:title",
                    dateKeys: ["iraq:IraqLabor:payroll:payrollManagement:date1", "iraq:IraqLabor:payroll:payrollManagement:date2"],
                    descriptionKey: "iraq:IraqLabor:payroll:payrollManagement:description",
                },


            ],
        },
        {
            id: 4,
            title: "iraq:IraqLabor:termination:title",

            subsections: [
                { description: "iraq:IraqLabor:termination:description" },
                {
                    title: "iraq:IraqLabor:termination:title1",
                    description: "iraq:IraqLabor:termination:description1",
                },

                {
                    list: createList(["iraq:IraqLabor:termination:data1", "iraq:IraqLabor:termination:data2", "iraq:IraqLabor:termination:data3", "iraq:IraqLabor:termination:data4", "iraq:IraqLabor:termination:data5"]),
                },
                {
                    title: "iraq:IraqLabor:termination:title2",
                    description: "iraq:IraqLabor:termination:description2",
                },
                {
                    title: "iraq:IraqLabor:termination:title3",
                    description: "iraq:IraqLabor:termination:description3",
                },
                { description: "iraq:IraqLabor:termination:description4", list: createList(["iraq:IraqLabor:termination:item1", "iraq:IraqLabor:termination:item2"]), },
                { description: "iraq:IraqLabor:termination:description5" },

            ],
        },
        {
            id: 5,
            title: "iraq:IraqLabor:leaveEntitlements:title",
            type: "leaveEntitlements",
            data: {
                description: "iraq:IraqLabor:leaveEntitlements:description1",
                description: "iraq:IraqLabor:leaveEntitlements:description2",
                title: "iraq:IraqLabor:leaveEntitlements:subTitle",
                leaves: [
                    { date: "iraq:IraqLabor:leaveEntitlements:leaves:dataS1:date", title: "iraq:IraqLabor:leaveEntitlements:leaves:dataS1:title" },
                    { date: "iraq:IraqLabor:leaveEntitlements:leaves:dataS3:date", title: "iraq:IraqLabor:leaveEntitlements:leaves:dataS3:title" },
                    { date: "iraq:IraqLabor:leaveEntitlements:leaves:dataS4:date", title: "iraq:IraqLabor:leaveEntitlements:leaves:dataS4:title" },
                    { date: "iraq:IraqLabor:leaveEntitlements:leaves:dataS5:date", title: "iraq:IraqLabor:leaveEntitlements:leaves:dataS5:title" },
                    { date: "iraq:IraqLabor:leaveEntitlements:leaves:dataS6:date", title: "iraq:IraqLabor:leaveEntitlements:leaves:dataS6:title" },
                    { date: "iraq:IraqLabor:leaveEntitlements:leaves:dataS7:date", title: "iraq:IraqLabor:leaveEntitlements:leaves:dataS7:title" },
                    { date: "iraq:IraqLabor:leaveEntitlements:leaves:dataS8:date", title: "iraq:IraqLabor:leaveEntitlements:leaves:dataS8:title" },
                    { date: "iraq:IraqLabor:leaveEntitlements:leaves:dataS9:date", title: "iraq:IraqLabor:leaveEntitlements:leaves:dataS9:title" },
                    { date: "iraq:IraqLabor:leaveEntitlements:leaves:dataS10:date", title: "iraq:IraqLabor:leaveEntitlements:leaves:dataS10:title" },
                    { date: "iraq:IraqLabor:leaveEntitlements:leaves:dataS11:date", title: "iraq:IraqLabor:leaveEntitlements:leaves:dataS11:title" },
                    { date: "iraq:IraqLabor:leaveEntitlements:leaves:dataS12:date", title: "iraq:IraqLabor:leaveEntitlements:leaves:dataS12:title" },
                    { date: "iraq:IraqLabor:leaveEntitlements:leaves:dataS13:date", title: "iraq:IraqLabor:leaveEntitlements:leaves:dataS13:title" },
                ],
                maternityLeave: {
                    title: "iraq:IraqLabor:leaveEntitlements:leaves:maternityLeave:title",
                    description: ["iraq:IraqLabor:leaveEntitlements:leaves:maternityLeave:description1"],
                },
                marriageLeave: {
                    title: "iraq:IraqLabor:leaveEntitlements:leaves:marriageLeave:title",
                    description: ["iraq:IraqLabor:leaveEntitlements:leaves:marriageLeave:description1"],
                },
                bereavementLeave: {
                    title: "iraq:IraqLabor:leaveEntitlements:leaves:bereavementLeave:title",
                    description: ["iraq:IraqLabor:leaveEntitlements:leaves:bereavementLeave:description1"],
                },
                paternityLeave: {
                    title: "iraq:IraqLabor:leaveEntitlements:leaves:paternityLeave:title",
                    description: "iraq:IraqLabor:leaveEntitlements:leaves:paternityLeave:description1",
                },
                sickLeave: {
                    title: "iraq:IraqLabor:leaveEntitlements:leaves:sickLeave:title",
                    description: "iraq:IraqLabor:leaveEntitlements:leaves:sickLeave:description1",
                },
            },
        },
        {
            id: 6,
            title: "iraq:IraqLabor:tax:title",
            subsections: [

                { description: "iraq:IraqLabor:tax:description" },
                {
                    title: "iraq:IraqLabor:tax:title1",
                    description: "iraq:IraqLabor:tax:description1"

                },
                {
                    list: createList(["iraq:IraqLabor:tax:data01", "iraq:IraqLabor:tax:data03", "iraq:IraqLabor:tax:data04"]),
                },
                {
                    title: "iraq:IraqLabor:tax:title2",
                    list: createList(["iraq:IraqLabor:tax:data11", "iraq:IraqLabor:tax:data12", "iraq:IraqLabor:tax:data13"]),
                },
            ],
        },
        {
            id: 7,
            title: "iraq:IraqLabor:visa:title",
            subsections: [
                { description: "iraq:IraqLabor:visa:description1", title: "iraq:IraqLabor:visa:title1" },

                { title: "iraq:IraqLabor:visa:title2", description: "iraq:IraqLabor:visa:description2" },
                {
                    description: "iraq:IraqLabor:visa:description21",
                },
                {
                    description: "iraq:IraqLabor:visa:description22",
                },
                {
                    description: "iraq:IraqLabor:visa:description23",
                },
                {
                    description: "iraq:IraqLabor:visa:description24",
                },

                {
                    title: "iraq:IraqLabor:visa:title3",
                    list: createList(["iraq:IraqLabor:visa:data1", "iraq:IraqLabor:visa:data2", "iraq:IraqLabor:visa:data3"]),
                },
                {
                    title: "iraq:IraqLabor:visa:title4",
                    list: createList(["iraq:IraqLabor:visa:data01", "iraq:IraqLabor:visa:data02", "iraq:IraqLabor:visa:data03", "iraq:IraqLabor:visa:data04"]),
                },
                {
                    title: "iraq:IraqLabor:visa:title5",
                    description: "iraq:IraqLabor:visa:data11"
                }

            ],
        },
    ],
};


export default function IraqLaborData() {
    return <LaborLaws data={iraqLaborData} />;
}
