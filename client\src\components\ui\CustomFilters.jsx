
"use client";
import {
  Grid,
  FormGroup,
  FormLabel,
  TextField,
  InputAdornment,
  Autocomplete,
} from "@mui/material";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DemoContainer } from "@mui/x-date-pickers/internals/demo";
import SvgSearchIcon from "@/assets/images/icons/searchIcon.svg";
import SvgRefreshIcon from "@/assets/images/icons/refreshIcon.svg";
import CustomButton from "./CustomButton";
const CustomFilters = ({ filters, onSearch, onReset, searchLabel = "Search" }) => {
  return (
    <Grid id="filter" container spacing={2} justifyContent="flex-start" alignItems="center">
      {filters
        .filter((f) => f.condition !== false)
        .map((filter, index) => (
          <Grid item xs={12} sm={4} key={index}>
            <FormGroup className="form-group">
              <FormLabel className="label-pentabell">{filter.label}</FormLabel>

              {filter.type === "text" && (
                <TextField
                  className="input-pentabell"
                  autoComplete="off"
                  variant="standard"
                  value={filter.value}
                  onChange={filter.onChange}
                  placeholder={filter.placeholder || filter.label}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SvgSearchIcon />
                      </InputAdornment>
                    ),
                  }}
                />
              )}

              {filter.type === "date" && (
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                  <DemoContainer components={["DatePicker"]}>
                    <DatePicker
                      value={filter.value}
                      onChange={filter.onChange}
                      className="input-date"
                      placeholder={filter.placeholder || filter.label}
                    />
                  </DemoContainer>
                </LocalizationProvider>
              )}

              {filter.type === "select" && (
                <Autocomplete
                  className="input-pentabell"
                  options={filter.options || []}
                  getOptionLabel={(option) =>
                    typeof option === "string" ? option : option.label
                  }
                  isOptionEqualToValue={(option, value) =>
                    typeof option === "string" || typeof value === "string"
                      ? option === value
                      : option?.value === value?.value
                  }
                  value={filter.value}
                  onChange={filter.onChange}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      className="input-pentabell multiple-select"
                      variant="standard"
                    />
                  )}
                />
              )}


            </FormGroup>
          </Grid>
        ))}

       <Grid item xs={12} sm={4} className="btns-filter dashboard">
        <CustomButton
          icon={<SvgRefreshIcon />}
          className="btn btn-outlined btn-refresh"
          onClick={onReset}
        />
        <CustomButton
          text={searchLabel}
          className="btn btn-filled"
          onClick={onSearch}
        />
      </Grid>
    </Grid>
  );
};

export default CustomFilters;
