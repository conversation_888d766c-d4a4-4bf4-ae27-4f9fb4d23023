"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetCategoriesByLanguageDTO = exports.CategoryDTO = exports.CategoryVersionDTO = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const article_dto_1 = require("./article.dto");
class CategoryVersionDTO {
    constructor(category) {
        Object.assign(this, category);
    }
}
exports.CategoryVersionDTO = CategoryVersionDTO;
__decorate([
    (0, class_transformer_1.Expose)({ name: '_id' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CategoryVersionDTO.prototype, "id", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'language' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CategoryVersionDTO.prototype, "language", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'name' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CategoryVersionDTO.prototype, "name", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'url' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CategoryVersionDTO.prototype, "slug", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'description' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CategoryVersionDTO.prototype, "description", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'image' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CategoryVersionDTO.prototype, "picture", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'canonical' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CategoryVersionDTO.prototype, "canonical", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'articles' }),
    (0, class_transformer_1.Type)(() => article_dto_1.ArticleDTO),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], CategoryVersionDTO.prototype, "articles", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'metaTitle' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CategoryVersionDTO.prototype, "metaTitle", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'metaDescriptio' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CategoryVersionDTO.prototype, "metaDescriptio", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], CategoryVersionDTO.prototype, "updatedAt", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Number)
], CategoryVersionDTO.prototype, "__v", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Date)
], CategoryVersionDTO.prototype, "createdAt", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Object)
], CategoryVersionDTO.prototype, "buffer", void 0);
class CategoryDTO {
    constructor(category) {
        Object.assign(this, category);
    }
}
exports.CategoryDTO = CategoryDTO;
__decorate([
    (0, class_transformer_1.Expose)({ name: '_id' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CategoryDTO.prototype, "id", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'versionscategory' }),
    (0, class_transformer_1.Type)(() => CategoryVersionDTO),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], CategoryDTO.prototype, "versions", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'robotsMeta' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CategoryDTO.prototype, "robotsMeta", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'createdAt' }),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], CategoryDTO.prototype, "creationDate", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Date)
], CategoryDTO.prototype, "updatedAt", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Number)
], CategoryDTO.prototype, "__v", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Object)
], CategoryDTO.prototype, "buffer", void 0);
class GetCategoriesByLanguageDTO {
    constructor(x = {}) {
        Object.assign(this, x);
    }
}
exports.GetCategoriesByLanguageDTO = GetCategoriesByLanguageDTO;
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", String)
], GetCategoriesByLanguageDTO.prototype, "_id", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'versionscategory' }),
    (0, class_transformer_1.Type)(() => versionscategoryDTO),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], GetCategoriesByLanguageDTO.prototype, "versions", void 0);
__decorate([
    (0, class_transformer_1.Exclude)(),
    __metadata("design:type", Object)
], GetCategoriesByLanguageDTO.prototype, "buffer", void 0);
class versionscategoryDTO {
    constructor(x = {}) {
        Object.assign(this, x);
    }
}
__decorate([
    (0, class_transformer_1.Expose)({ name: 'name' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], versionscategoryDTO.prototype, "name", void 0);
__decorate([
    (0, class_transformer_1.Expose)({ name: 'id' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], versionscategoryDTO.prototype, "id", void 0);
//# sourceMappingURL=article.category.dto.js.map