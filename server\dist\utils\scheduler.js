"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.scheduleGetLatestCreatedAccount = exports.scheduleImportOpportunityFromHunter = void 0;
const node_cron_1 = __importDefault(require("node-cron"));
const logger_1 = require("./logger");
const scheduleImportOpportunityFromHunter = (opportunityService) => {
    node_cron_1.default.schedule('0 * * * *', async () => {
        logger_1.logger.info(`Cron Job [Import Opportunities From Hunter] Started At ${new Date()}`);
        await opportunityService.importOpportunitiesFromHunter();
    });
};
exports.scheduleImportOpportunityFromHunter = scheduleImportOpportunityFromHunter;
const scheduleGetLatestCreatedAccount = (candidateService) => {
    node_cron_1.default.schedule('0 8 * * *', async () => {
        await candidateService.getLatestCreatedAccount();
    });
};
exports.scheduleGetLatestCreatedAccount = scheduleGetLatestCreatedAccount;
//# sourceMappingURL=scheduler.js.map