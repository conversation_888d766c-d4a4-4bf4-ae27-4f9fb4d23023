import { Grid } from "@mui/material";
import Link from "next/link";
import SvgFacebook from "@/assets/images/icons/facebook.svg";
import SvgInstagram from "@/assets/images/icons/instagram.svg";
import SvgLinkedin from "@/assets/images/icons/linkedin.svg";
import SvgX from "@/assets/images/icons/x.svg";
import SvgYoutube from "@/assets/images/icons/Youtube.svg";
export default function SocialMediaLinks() {
  return (
    <Grid id="social-media-links" item xs={12} sm={4} className="bottom-links">
      <Link target="_blank" aria-label="Facebook" href={"https://www.facebook.com/Pentabell"} className="link">
        <SvgFacebook />
      </Link>
      <Link target="_blank" aria-label="YouTube" href={"https://www.youtube.com/channel/UCPzmQxP1jVhBSM2-uSSrPvA"} className="link">
        <SvgYoutube />
      </Link>
      <Link target="_blank" aria-label="X" href={"https://twitter.com/pentabell_group#"} className="link">
        <SvgX />
      </Link>
      <Link target="_blank" aria-label="Instagram" href={"https://www.instagram.com/pentabell/?hl=en"} className="link">
        <SvgInstagram />
      </Link>
      <Link target="_blank" aria-label="LinkedIn" href={"https://www.linkedin.com/company/1859573/"} className="link">
        <SvgLinkedin />
      </Link>
    </Grid>
  );
}
