"use client";

import { ErrorMessage, Field } from "formik";
import { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { v4 as uuidv4 } from "uuid";
import CustomTextInput from "@/components/ui/CustomTextInput";
import { Visibility } from "../../../utils/constants";
import "react-datepicker/dist/react-datepicker.css";
import { slug } from "@feelinglovelynow/slug";
import { API_URLS } from "../../../utils/urls";
import upload from "@/assets/images/add.png";
import "suneditor/dist/css/suneditor.min.css";
import { WithContext as ReactTags } from "react-tag-input";
import CustomSunEditor from "@/components/ui/CustomSunEditor";
import FaqSection from "./FaqSection";

import {
  Autocomplete,
  FormGroup,
  FormLabel,
  MenuItem,
  Select,
  Stack,
  TextField,
} from "@mui/material";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { DemoContainer } from "@mui/x-date-pickers/internals/demo";
import dayjs from "dayjs";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { useSaveFile } from "@/features/opportunity/hooks/opportunity.hooks";
import useCurrentUser from "@/features/auth/hooks/currentUser.hooks";
import CustomSelect from "@/components/ui/CustomSelect";
import DocumentImporter from "./DocumentImporter";

function AddArticleFR({
  errors,
  touched,
  setFieldValue,
  values,
  onImageSelect,
  categories,
  filteredCategories,
  onCategoriesSelect,
  debounce,
}) {
  const handlePaste = (event, cleanData, maxCharCount) => {
    let html = cleanData;

    // Correction des balises non fermées
    html = html.replace(/<strong>(.*?)$/g, "<strong>$1</strong>");

    return html;
  };
  const KeyCodes = {
    comma: 188,
    enter: 13,
  };
  const delimiters = [KeyCodes.comma, KeyCodes.enter];
  const [tags, setTags] = useState([]);
  const [highlights, setHighlights] = useState([]);

  const { user } = useCurrentUser();
  const { t } = useTranslation();
  const [publishNow, setPublishNow] = useState(false);
  const [publishDate, setPublishDate] = useState(new Date());
  const imageInputRef = useRef(null);
  const [selectedImage, setSelectedImage] = useState(null);
  const language = "fr";

  const handlePhotoChange = async () => {
    const selectedFile = imageInputRef.current.files[0];
    setSelectedImage(imageInputRef.current.files[0]);

    if (selectedFile) {
      onImageSelect(selectedFile, language);
    }
  };

  const useSaveFileHook = useSaveFile();

  const handlePhotoBlogChange = async (file, info, core, uploadHandler) => {
    if (file instanceof HTMLImageElement) {
      const src = file.src;

      if (src.startsWith("data:image")) {
        const base64Data = src.split(",")[1];
        const contentType = src.match(/data:(.*?);base64/)[1];
        const byteCharacters = atob(base64Data);
        const byteNumbers = new Array(byteCharacters.length)
          .fill(0)
          .map((_, i) => byteCharacters.charCodeAt(i));
        const byteArray = new Uint8Array(byteNumbers);

        const blob = new Blob([byteArray], { type: contentType });
        const fileName = `image_${Date.now()}.${contentType.split("/")[1]}`;
        const selectedFile = new File([blob], fileName, { type: contentType });

        await uploadFile(selectedFile, uploadHandler, core, file);
      } else {
        fetch(src)
          .then((response) => response.blob())
          .then((blob) => {
            const contentType = blob.type;
            const fileName = `image_${Date.now()}.${contentType.split("/")[1]}`;
            const selectedFile = new File([blob], fileName, {
              type: contentType,
            });

            uploadFile(selectedFile, uploadHandler, core, file);
          })
          .catch((error) =>
            console.error("Error converting image URL to Blob:", error)
          );
      }
    } else {
      console.error("File is not an HTMLImageElement.");
    }
  };

  const uploadFile = (selectedFile, uploadHandler, core, originalImage) => {
    let uuidPhoto;
    uuidPhoto = uuidv4().replace(/-/g, "");

    const formData = new FormData();
    formData.append("file", selectedFile);

    const extension = selectedFile.name.split(".").pop();
    const currentYear = new Date().getFullYear();

    useSaveFileHook.mutate(
      {
        resource: "blogs",
        folder: currentYear.toString(),
        filename: uuidPhoto,
        body: { formData, t },
      },
      {
        onSuccess: (dataUUID) => {
          const uuidPhotoFileName =
            dataUUID.message === "uuid exist"
              ? dataUUID.uuid
              : `${uuidPhoto}.${extension}`;

          const imageUrl = `${process.env.NEXT_PUBLIC_BASE_API_URL}/files/${uuidPhotoFileName}`;

          originalImage.src = imageUrl;

          uploadHandler({
            result: [
              {
                id: uuidPhotoFileName,
                url: imageUrl,
              },
            ],
          });
        },
        onError: (error) => {
          console.error("Error uploading file:", error);
        },
      }
    );
  };
  const [titlefr, setTitlefr] = useState(() => {
    const savedTitlefr = localStorage.getItem("titlefr");
    return savedTitlefr ? JSON.parse(savedTitlefr) : "";
  });
  const [metatitlefr, setMetatitlefr] = useState(() => {
    const savedMetatitlefr = localStorage.getItem("metatitlefr");
    return savedMetatitlefr ? JSON.parse(savedMetatitlefr) : "";
  });
  const [metaDescriptionfr, setMetaDescriptionfr] = useState(() => {
    const savedMetadescriptionfr = localStorage.getItem("metaDescriptionfr");
    return savedMetadescriptionfr ? JSON.parse(savedMetadescriptionfr) : "";
  });

  const [contentfr, setContentfr] = useState(() => {
    const savedContentfr = localStorage.getItem("contentfr");
    return savedContentfr ? JSON.parse(savedContentfr) : "";
  });

  const handleEditorChange = (newContentfr) => {
    setContentfr(newContentfr);
    setFieldValue("contentFR", newContentfr);
  };

  const handleContentExtracted = (extractedContent) => {
    setContentfr(extractedContent);
    setFieldValue("contentFR", extractedContent);
    localStorage.setItem("contentfr", JSON.stringify(extractedContent));
    debounce();
  };

  const handleMetadataExtracted = (metadata) => {
    if (metadata.title && !values.titleFR) {
      setFieldValue("titleFR", metadata.title);
      setTitlefr(metadata.title);
      const url = slug(metadata.title);
      setFieldValue("urlFR", url);
    }

    if (metadata.description && !values.descriptionFR) {
      setFieldValue("descriptionFR", metadata.description);
    }

    if (metadata.keywords && metadata.keywords.length > 0) {
      const keywordTags = metadata.keywords.map((keyword, index) => ({
        id: `extracted-${index}`,
        text: keyword,
      }));

      const existingKeywords = values.keywordsFR || [];
      const mergedKeywords = [...existingKeywords, ...keywordTags];
      setFieldValue(
        "keywordsFR",
        mergedKeywords.map((tag) => tag.text)
      );

      // Update the local tags state to show in ReactTags component
      const existingTags = tags || [];
      const mergedTags = [...existingTags, ...keywordTags];
      setTags(mergedTags);
    }

    debounce();
  };

  useEffect(() => {
    if (titlefr) {
      localStorage.setItem("titlefr", JSON.stringify(titlefr));
    }
    if (contentfr) {
      localStorage.setItem("contentfr", JSON.stringify(contentfr));
    }
    if (metatitlefr) {
      localStorage.setItem("metatitlefr", JSON.stringify(metatitlefr));
    }
    if (metaDescriptionfr) {
      localStorage.setItem(
        "metaDescriptionfr",
        JSON.stringify(metaDescriptionfr)
      );
    }
  }, [titlefr, contentfr, metatitlefr, metaDescriptionfr]);

  return (
    <>
      <p className="label-pentabell">Add article French : </p>
      <div className="inline-group">
        <div>
          <FormGroup>
            <CustomTextInput
              label={t("createArticle:title")}
              name="titleFR"
              value={titlefr || values.titleFR}
              onChange={(e) => {
                const titleFR = e.target.value;
                setFieldValue("titleFR", titleFR);
                setTitlefr(titleFR);
                const url = slug(titleFR);
                setFieldValue("urlFR", url);
              }}
            />
          </FormGroup>
        </div>
        <div>
          <FormGroup>
            <FormLabel className="label-form">
              {t("createArticle:categories")}
              <Stack>
                <Autocomplete
                  multiple
                  className="input-pentabell"
                  id="tags-standard"
                  options={
                    filteredCategories.length > 0
                      ? filteredCategories
                      : categories
                  }
                  getOptionLabel={(option) => option.name}
                  selected={
                    values.categoryFR.length > 0
                      ? (filteredCategories.length > 0
                          ? filteredCategories
                          : categories
                        ).filter((category) =>
                          values.categoryFR.includes(category.id)
                        )
                      : []
                  }
                  onChange={(event, selectedOptions) => {
                    const categoryIds = selectedOptions.map(
                      (category) => category.id
                    );
                    setFieldValue("categoryFR", categoryIds);
                    onCategoriesSelect(categoryIds);
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      className="input-pentabell  multiple-select"
                      variant="standard"
                    />
                  )}
                />
              </Stack>
            </FormLabel>
          </FormGroup>
          {touched.categoryFR && errors.categoryFR && (
            <div className="error">{errors.categoryFR}</div>
          )}
        </div>
      </div>
      <div className="inline-group">
        <div>
          <FormGroup>
            <CustomTextInput
              label={t("Description")}
              name="descriptionFR"
              value={values.descriptionFR}
              onChange={(e) => {
                const descriptionFR = e.target.value;
                setFieldValue("descriptionFR", descriptionFR);
              }}
              multiline={true}
              rows={3}

            />
          </FormGroup>
        </div>
      </div>
      <div className="inline-group">
        <div>
          <FormGroup>
            <FormLabel className="label-form">
              Highlights
              <div id="tags">
                <ReactTags
                  tags={highlights}
                  className={
                    "input-pentabell" +
                    (errors.highlightsFR && touched.highlightsFR
                      ? " is-invalid"
                      : "")
                  }
                  delimiters={delimiters}
                  handleDelete={(i) => {
                    const updatedTags = highlights.filter(
                      (tag, index) => index !== i
                    );
                    setHighlights(updatedTags);
                    setFieldValue(
                      "highlightsFR",
                      updatedTags.map((tag) => tag.text)
                    );
                  }}
                  handleAddition={(tag) => {
                    setHighlights([...highlights, tag]);
                    setFieldValue(
                      "highlightsFR",
                      [...highlights, tag].map((item) => item.text)
                    );
                  }}
                  inputFieldPosition="bottom"
                  autocomplete
                  allowDragDrop={false}
                />
              </div>
              <ErrorMessage
                className="label-error"
                name="keywordsEN"
                component="div"
              />
            </FormLabel>
          </FormGroup>
        </div>
      </div>
      <FormGroup>
        <DocumentImporter
          onContentExtracted={handleContentExtracted}
          onMetadataExtracted={handleMetadataExtracted}
          language="FR"
        />
        <CustomSunEditor
          content={contentfr || values?.contentFR || ""}
          onChange={handleEditorChange}
          onPaste={handlePaste}
          onImageUpload={handlePhotoBlogChange}
        />
      </FormGroup>
      <br></br>

      <FaqSection
        values={values}
        setFieldValue={setFieldValue}
        errors={errors}
        touched={touched}
        language="FR"
        debounce={debounce}
      />
      <br></br>
      <div className="inline-group">
        <div>
          <FormGroup>
            <CustomTextInput
              label={t("createArticle:metaTitle")}
              name="metaTitleFR"
              value={metatitlefr || values.metaTitleFR}
              onChange={(e) => {
                const metaTitleFR = e.target.value;
                setFieldValue("metaTitleFR", metaTitleFR);
                setMetatitlefr(metaTitleFR);
              }}
              showLength={true}
              maxLength={65}
            />
          </FormGroup>
        </div>
        <div>
          <FormGroup>
            <CustomTextInput
              label={t("createArticle:url")}
              name="urlFR"
              value={values.urlFR}
              onChange={(e) => {
                setFieldValue("urlFR", e.target.value);
              }}

            />
          </FormGroup>
        </div>
      </div>
      <div className="inline-group">
        <div>
          <FormGroup>
            <CustomTextInput
              label={t("createArticle:metaDescription")}
              name="metaDescriptionFR"
              value={metaDescriptionfr || values.metaDescriptionFR}
              onChange={(e) => {
                const metaDescriptionFR = e.target.value;
                setFieldValue("metaDescriptionFR", metaDescriptionFR);
                setMetaDescriptionfr(metaDescriptionFR);
              }}
              showLength={true}
              maxLength={160}
              multiline={true}
              rows={2}

            />
          </FormGroup>
        </div>
      </div>
      <div className="inline-group">
        <div>
          <FormGroup>
            <FormLabel className="label-form">
              {t("createArticle:featuredImage")}

              <div className="upload-container">
                <label htmlFor={`image-upload-fr`} className="file-labels">
                  <input
                    type="file"
                    id={`image-upload-fr`}
                    name="imageEN"
                    accept=".png, .jpg, .jpeg, .webp"
                    ref={imageInputRef}
                    onChange={(e) => {
                      setFieldValue("imageFR", e.target.files[0]);
                      handlePhotoChange();
                    }}
                    className={
                      "file-input" +
                      (errors.imageFR && touched.imageFR ? " is-invalid" : "")
                    }
                  />
                  <div className="upload-area">
                    <div>
                      <div
                        className="icon-pic"
                        style={{
                          backgroundImage: `url("${
                            selectedImage
                              ? URL.createObjectURL(selectedImage)
                              : values.imageFR
                              ? `${process.env.REACT_APP_API_URL}${API_URLS.files}/${values.imageFR}`
                              : upload.src
                          }")`,
                          backgroundSize: "cover",
                          backgroundRepeat: "no-repeat",
                          backgroundPosition: "center",
                          //backgroundColor: "#40bd3921",
                        }}
                      ></div>
                    </div>
                    <div>
                      <p className="upload-text">
                        {t("createArticle:addFeatImg")}
                      </p>
                      <p className="upload-description">
                        {t("createArticle:clickBox")}
                      </p>
                    </div>
                  </div>
                  <ErrorMessage
                    name="imageFR"
                    component="div"
                    className="invalid-feedback error"
                  />
                </label>
              </div>
            </FormLabel>
          </FormGroup>
        </div>
      </div>

      <div className="inline-group">
        <div>
          <FormGroup>
            <CustomTextInput
              label={t("createArticle:alt")}
              name="altFR"
              value={values.altFR}
              onChange={(e) => {
                setFieldValue("altFR", e.target.value);
              }}

            />
          </FormGroup>
        </div>
        <div>
          <FormGroup>
            <CustomSelect
              label={t("createArticle:visibility")}
              name="visibilityFR"
              value={values.visibilityFR}
              onChange={(e) => setFieldValue("visibilityFR", e.target.value)}
              options={Visibility}
            />

          </FormGroup>
        </div>
        <div className="inline-group">
                  <div>
          <FormGroup>
            <FormLabel className="label-form">
              {t("createArticle:keyword")}
              <div id="tags">
                <ReactTags
                  tags={tags}
                  className={
                    "input-pentabell" +
                    (errors.keywordsFR && touched.keywordsFR
                      ? " is-invalid"
                      : "") +
                    (tags.length === 0 ? " no-tags" : "")
                  }
                  delimiters={delimiters}
                  handleDelete={(i) => {
                    const updatedTags = tags.filter(
                      (tag, index) => index !== i
                    );
                    setTags(updatedTags);
                    setFieldValue(
                      "keywordsFR",
                      updatedTags.map((tag) => tag.text)
                    );
                  }}
                  handleAddition={(tag) => {
                    setTags([...tags, tag]);
                    setFieldValue(
                      "keywordsFR",
                      [...tags, tag].map((item) => item.text)
                    );
                  }}
                  inputFieldPosition="bottom"
                  autocomplete
                  allowDragDrop={false}
                />
              </div>
              <ErrorMessage
                className="label-error"
                name="keywordsFR"
                component="div"
              />
            </FormLabel>
          </FormGroup>
        </div>
      </div></div>
      <label className="label-form">
        <Field
          type="checkbox"
          name="publishNow"
          checked={publishNow}
          onChange={(e) => {
            setPublishNow(e.target.checked);
            if (e.target.checked) {
              setFieldValue("publishDateFR", new Date().toISOString());
            }
          }}
        />
        {t("createArticle:publishNow")}
      </label>

      {!publishNow && (
        <div>
          <FormGroup>
            <FormLabel className="label-form">
              {t("createArticle:publishDate")}
              <LocalizationProvider dateAdapter={AdapterDayjs}>
                <DemoContainer components={["DatePicker"]}>
                  <DatePicker
                    variant="standard"
                    className="input-date"
                    format="DD/MM/YYYY"
                    value={dayjs(values.publishDateEN)}
                    onChange={(date) => {
                      setFieldValue(
                        "publishDateEN",
                        dayjs(date).format("YYYY-MM-DD")
                      );
                    }}
                  />{" "}
                  <ErrorMessage
                    className="label-error"
                    name="publishDateEN"
                    component="div"
                  />
                </DemoContainer>
              </LocalizationProvider>
            </FormLabel>
          </FormGroup>
        </div>
      )}

      <Field
        type="hidden"
        name="publishDateFR"
        value={
          publishNow ? new Date().toISOString() : publishDate.toISOString()
        }
      />
    </>
  );
}

export default AddArticleFR;