"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const slide_service_1 = __importDefault(require("./slide.service"));
const authentication_middleware_1 = __importDefault(require("@/middlewares/authentication.middleware"));
const authorization_middleware_1 = require("@/middlewares/authorization.middleware");
const constants_1 = require("@/utils/helpers/constants");
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const validateApiKey_middleware_1 = __importDefault(require("@/middlewares/validateApiKey.middleware"));
const messages_1 = require("@/utils/helpers/messages");
class SlideController {
    constructor() {
        this.path = '/sliders';
        this.slideService = new slide_service_1.default();
        this.router = (0, express_1.Router)();
        this.getSliders = async (req, res, next) => {
            try {
                const queries = req.query;
                const language = req.query.language || constants_1.Language.ENGLISH;
                const sliders = await this.slideService.getSliders(queries, language);
                res.send(sliders);
            }
            catch (error) {
                next(error);
            }
        };
        this.archivedallslider = async (request, response, next) => {
            try {
                const sliderId = request.params.id;
                const isArchived = request.body.isArchived;
                const archivedSlider = await this.slideService.archiveddisarchivedSlider(sliderId, isArchived);
                if (!archivedSlider) {
                    throw new http_exception_1.default(404, `No slider found with ID ${sliderId}.`);
                }
                response.status(204).send();
            }
            catch (error) {
                next(error);
            }
        };
        this.deleteslider = async (request, response, next) => {
            try {
                const { sliderId } = request.params;
                const slider = await this.slideService.deleteSlide(sliderId);
                if (!slider) {
                    throw new http_exception_1.default(404, messages_1.MESSAGES.SLIDER.NOT_FOUND);
                }
                response.status(204).send();
            }
            catch (error) {
                next(error);
            }
        };
        this.togglearchivedslide = async (req, res, next) => {
            try {
                const { language, slideId } = req.params;
                const { archive } = req.body;
                if (!language || !slideId) {
                    res.status(400).json({ message: messages_1.MESSAGES.SLIDER.MISSING_LANGUAGE_OR_ID });
                    return;
                }
                if (archive === undefined) {
                    res.status(400).json({ message: 'Missing archive flag (true or false) in the request body.' });
                    return;
                }
                const updatedslide = await this.slideService.toglleArchivedslide(language, slideId, archive);
                if (!updatedslide) {
                    res.status(404).json({ message: messages_1.MESSAGES.SLIDER.MISSING_LANGUAGE_OR_ID });
                    return;
                }
                res.status(200).json({
                    updatedslide,
                });
            }
            catch (error) {
                next(error);
                res.status(500).json({ message: messages_1.MESSAGES.GENERAL.SERVER_ERROR });
            }
        };
        this.createSlide = async (request, response, next) => {
            try {
                const slideData = request.body;
                const newSlide = await this.slideService.addSlide(slideData);
                response.status(201).send({ slide: newSlide });
            }
            catch (error) {
                next(error);
            }
        };
        this.updatesliderorder = async (req, res, next) => {
            try {
                const slidersOrder = req.body.slidersOrder;
                if (!Array.isArray(slidersOrder) || slidersOrder.length === 0) {
                    return res.status(400).json({
                        success: false,
                        message: 'Invalid sliders order data provided.',
                    });
                }
                const result = await this.slideService.updateSlidersOrder(slidersOrder);
                res.status(200).json(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.getSlideByIdandlangage = async (req, res, next) => {
            try {
                const { slideId, language } = req.params;
                const slide = await this.slideService.getSlideBylanguageandId(language, slideId);
                if (!slide) {
                    if (language === 'fr') {
                        res.status(204).send();
                        return;
                    }
                    else {
                        res.status(404).json({ message: 'slide not found' });
                        return;
                    }
                }
                res.status(200).json(slide);
            }
            catch (error) {
                next(error);
            }
        };
        this.get = async (request, response, next) => {
            try {
                response.send(await this.slideService.get(request.params.sliderId));
            }
            catch (error) {
                next(error);
            }
        };
        this.archiverSlide = async (req, res, next) => {
            try {
                const { language, slideId } = req.params;
                const { isArchived } = req.body;
                if (!language || !slideId) {
                    throw new http_exception_1.default(400, 'Missing language or slideId parameter.');
                }
                const deletedSlidse = await this.slideService.archiverversionslide(language, slideId, isArchived);
                if (!deletedSlidse) {
                    throw new http_exception_1.default(404, `No slide or version found for language ${language} with slideId ${slideId}.`);
                }
                res.send({
                    message: `Slide version ${isArchived ? 'archived' : 'unarchived'} successfully.`,
                    deletedSlidse,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.updateSlideVersion = async (req, res, next) => {
            try {
                const { slideId, language } = req.params;
                const versionData = req.body;
                if (!slideId || !language) {
                    res.status(400).json({ message: messages_1.MESSAGES.SLIDER.MISSING_LANGUAGE_OR_ID });
                }
                const languageEnum = language;
                const updatedSlide = await this.slideService.updateSlide(slideId, languageEnum, versionData);
                res.send(updatedSlide);
            }
            catch (error) {
                next(error);
            }
        };
        this.initialiseRoutes();
    }
    initialiseRoutes() {
        this.router.get(`${this.path}`, this.getSliders);
        this.router.post(`${this.path}`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), this.createSlide);
        this.router.post(`${this.path}/:language/:slideId`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), this.updateSlideVersion);
        this.router.put(`${this.path}/:language/:slideId/desarchiver`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), this.togglearchivedslide);
        this.router.put(`${this.path}/archive/:id`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), this.archivedallslider);
        this.router.put(`${this.path}/order/`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), this.updatesliderorder);
        this.router.get(`${this.path}/:sliderId`, validateApiKey_middleware_1.default, this.get);
        this.router.delete(`${this.path}/:sliderId`, 
        /*  isAuthenticated,
         hasRoles([Role.ADMIN, Role.EDITEUR]), */
        this.deleteslider);
        this.router.put(`${this.path}/:language/:slideId`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), this.archiverSlide);
        this.router.get(`${this.path}/:language/:slideId`, validateApiKey_middleware_1.default, this.getSlideByIdandlangage);
    }
}
exports.default = SlideController;
//# sourceMappingURL=slide.controller.js.map