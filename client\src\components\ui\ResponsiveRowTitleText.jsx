import { memo } from "react";
import { Container } from "@mui/material";

const ResponsiveRowTitleText = memo(function ResponsiveRowTitleText({
  title,
  paragraph,
  paragraph2,
  bold,
  textGrey,
  word,
  link,
  word2,
  link2,
}) {
  const subHeadingClass = `sub-heading ${textGrey ? "text-grey" : ""}`;
  const boldClass = bold ? "bold" : "";

  const updateText = (paragraph, word, link) => {
    if (word && link) {
      const updatedLink = `<a href=${link}>${word}</a>`;
      return <span dangerouslySetInnerHTML={{ __html: paragraph.replace(word, updatedLink) }} />; ;
    }
    return paragraph;
  };

    const updateText2 = (paragraph, word2, link2) => {
    if (word2 && link2) {
      const updatedLink = `<a href=${link2}>${word2}</a>`;
      return <span dangerouslySetInnerHTML={{ __html: paragraph.replace(word2, updatedLink) }} />; ;
    }
    return paragraph;
  };

  return (
    <Container className="responsive-row-title-text custom-max-width">
      <div className="grid-item">
        <h2 className="section heading-h1">{title}</h2>
      </div>
      <div className="grid-item">
        <p
          className={`${subHeadingClass} ${boldClass}`}
          style={{ fontSize: "20px !important" }}
        >
          {updateText(paragraph, word, link)}
        </p>
        {paragraph2 && (
          <p
            className={subHeadingClass}
            style={{ fontSize: "20px !important" }}
          >
          {updateText2(paragraph2, word2, link2)}
          </p>
        )}
      </div>
    </Container>
  );
});

export default ResponsiveRowTitleText;
