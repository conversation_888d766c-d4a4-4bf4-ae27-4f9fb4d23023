"use client";
import { Container } from "@mui/material";
import { useTranslation } from "react-i18next";

import candidateJourneyImg1 from "@/assets/images/services/candJourneyImg1.png";
import candidateJourneyImg2 from "@/assets/images/services/candJourneyImg2.png";
import candidateJourneyImg3 from "@/assets/images/services/candJourneyImg3.png";
import candidateJourneyImg4 from "@/assets/images/services/candJourneyImg4.png";

function CandidateJourney() {
  const { t } = useTranslation();
  return (
    <div id="candidate-journey-section" className="light-bg">
      <Container className="custom-max-width">
        <h2 className="heading-h1 text-blue text-center">
          {t("joinUs:candidateJourney:title")}
        </h2>
        <div className="support-reasons">
          <div className="support-reason">
            <div className="img">
              <img
                src={candidateJourneyImg1.src}
                alt="Candidate Journey 1"
                loading="lazy"
              />
            </div>
            <p className="support-reason-title">
              {t("joinUs:candidateJourney:item1:title")}
            </p>
            <p className="support-reason-item">
              {t("joinUs:candidateJourney:item1:description")}
            </p>
          </div>
          <div className="support-reason">
            <div className="img">
              <img
                width={303}
                height={103}
                src={candidateJourneyImg2.src}
                alt="Candidate Journey 2"
                loading="lazy"
              />
            </div>
            <p className="support-reason-title">
              {t("joinUs:candidateJourney:item2:title")}
            </p>
            <p className="support-reason-item">
              {t("joinUs:candidateJourney:item2:description")}
            </p>
          </div>
          <div className="support-reason">
            <div className="img">
              {" "}
              <img
                width={303}
                height={103}
                src={candidateJourneyImg3.src}
                alt="Candidate Journey 2"
                loading="lazy"
              />
            </div>

            <p className="support-reason-title">
              {t("joinUs:candidateJourney:item3:title")}
            </p>
            <p className="support-reason-item">
              {t("joinUs:candidateJourney:item2:description")}
            </p>
          </div>
          <div className="support-reason">
            <div className="img">
              <img
                width={303}
                height={103}
                src={candidateJourneyImg4.src}
                alt="Candidate Journey 3"
                loading="lazy"
              />
            </div>

            <p className="support-reason-title">
              {t("joinUs:candidateJourney:item4:title")}
            </p>
            <p className="support-reason-item">
              {t("joinUs:candidateJourney:item4:description")}
            </p>
          </div>
        </div>
      </Container>
    </div>
  );
}

export default CandidateJourney;
