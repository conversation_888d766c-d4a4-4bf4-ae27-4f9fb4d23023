import HrSolutionsSection from "@/components/ui/HrSolutionsSection";

function HrSolutionsInMiddleEast({ t }) {
    const sections = [];
    let i = 1;
    while (t(`middleeast:whyPentabell:s${i}:title`, { defaultValue: "" })) {
        sections.push({
            title: t(`middleeast:whyPentabell:s${i}:title`),
            description: t(`middleeast:whyPentabell:s${i}:description`)
        });
        i++;
    }

    return (
        <HrSolutionsSection
            title={t("middleeast:whyPentabell:title")}
            items={sections}
        />
    );
}

export default HrSolutionsInMiddleEast;