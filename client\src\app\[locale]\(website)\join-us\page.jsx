import GloablBenefits from "@/components/pages/sites/services/GloablBenefits";
import banner from "@/assets/images/website/banner/Pentabell-joinUs.webp";

import JoinUsBanner from "@/components/pages/sites/sections/JoinUsBanner";
import CandidateJourney from "@/components/pages/sites/services/CandidateJourney";
import MeetTheTeam from "@/components/pages/sites/services/MeetTheTeam";
import ConnectingTalentForm from "@/features/forms/components/ConnectingTalentForm";
import LastOpportunitiesInHouse from "@/features/opportunity/components/opportunityFrontOffice/LastOpportunitiesInHouse";
import OurCultureAndLocation from "@/components/pages/sites/services/OurCultureAndLocation";
import initTranslations from "@/app/i18n";
import { axiosGetJsonSSR } from "@/config/axios";

export async function generateMetadata({ params: { locale } }) {
  const canonicalUrl = `https://www.pentabell.com/${
    locale !== "en" ? `${locale}/` : ""
  }join-us/`;

  const languages = {
    fr: `https://www.pentabell.com/fr/join-us/`,
    en: `https://www.pentabell.com/join-us/`,
    "x-default": `https://www.pentabell.com/join-us/`,
  };
  const { t } = await initTranslations(locale, ["joinUs"]);
  try {
    const res = await axiosGetJsonSSR.get(
      `${process.env.NEXT_PUBLIC_BASE_API_URL_SSR}/seoTags/${locale}/join-us`
    );
    const seoTags = res?.data?.status === 200;
    if (seoTags) {
      return {
        title: res?.data?.data?.versions[0]?.metaTitle,
        description: res?.data?.data?.versions[0]?.metaDescription,
        robots: res?.data?.data?.robotMeta,
        alternates: {
          canonical: canonicalUrl,
          languages,
        },
      };
    }
  } catch (error) {
    console.error("Error fetching SEO tags:", error);
  }
  return {
    title: t("joinUs:metaTitle"),
    description: t("joinUs:metaDescription"),
    alternates: {
      canonical: canonicalUrl,
      languages,
    },
    robots: "follow, index, max-snippet:-1, max-image-preview:large",
  };
}
async function page({ params: { locale } }) {
  return (
    <div>
      <JoinUsBanner
        altImg={"Join Us, Find Careers and Jobs at Pentabell"}
        bannerImg={banner}
        height={"100vh"}
      />
      <LastOpportunitiesInHouse locale={locale} />
      <MeetTheTeam />
      <OurCultureAndLocation locale={locale} />
      <GloablBenefits />
      <CandidateJourney />
      <ConnectingTalentForm />
    </div>
  );
}

export default page;
