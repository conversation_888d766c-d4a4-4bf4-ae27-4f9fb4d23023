import SvgComprehensiveEmploymentIcon from "@/assets/images/services/icons/ComprehensiveEmploymentIcon.svg";
import SvgGlobalPayrollIcon from "@/assets/images/services/icons/GlobalPayrollIcon.svg";
import SvgtickIcon from "@/assets/images/services/icons/tickIcon.svg";
import SvgMultiCurrencyIcon from "@/assets/images/services/icons/MultiCurrencyIcon.svg";
import SvglegalIcon from "@/assets/images/services/icons/legalIcon.svg";
import SvgtaxManagementIcon from "@/assets/images/services/icons/taxManagementIcon.svg";
import ComplexityControlSection from "@/components/ui/ComplexityControlSection";
function ComplexityControlSectionQatar({ t }) {
  const items = [
    {
      icon: SvgComprehensiveEmploymentIcon,
      title: t("qatar:complexity:dataS1:title"),
      description: t("qatar:complexity:dataS1:description"),
    },
    {
      icon: SvgGlobalPayrollIcon,
      title: t("qatar:complexity:dataS2:title"),
      description: t("qatar:complexity:dataS2:description"),
    },
    {
      icon: SvgtickIcon,
      title: t("qatar:complexity:dataS3:title"),
      description: t("qatar:complexity:dataS3:description"),
    },
    {
      icon: SvgtaxManagementIcon,
      title: t("qatar:complexity:dataS4:title"),
      description: t("qatar:complexity:dataS4:description"),
    },
    {
      icon: SvglegalIcon,
      title: t("qatar:complexity:dataS5:title"),
      description: t("qatar:complexity:dataS5:description"),
    },
    {
      icon: SvgMultiCurrencyIcon,
      title: t("qatar:complexity:dataS6:title"),
      description: t("qatar:complexity:dataS6:description"),
    },
  ];

  return (
    <ComplexityControlSection
      title1={t("qatar:complexity:title1")}
      title2={t("qatar:complexity:title2")}
      description=""
      items={items}
    />
  );
}

export default ComplexityControlSectionQatar;
