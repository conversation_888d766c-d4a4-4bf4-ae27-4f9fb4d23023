"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const newsletterSchema = new mongoose_1.Schema({
    email: {
        type: String,
        unique: true,
        index: true,
    },
    isActive: {
        type: Boolean,
        default: true
    }
}, {
    timestamps: true,
});
exports.default = (0, mongoose_1.model)('Newsletter', newsletterSchema);
//# sourceMappingURL=newsletter.model.js.map