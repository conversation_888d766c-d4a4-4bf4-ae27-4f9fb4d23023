"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const fileSchema = new mongoose_1.Schema({
    resource: String,
    folder: String,
    ipSender: String,
    uuid: String,
    fileName: String,
    originalName: String,
    fileType: String,
    fileSize: String,
    checksum: String,
}, {
    timestamps: true,
});
exports.default = (0, mongoose_1.model)('Files', fileSchema);
//# sourceMappingURL=files.model.js.map