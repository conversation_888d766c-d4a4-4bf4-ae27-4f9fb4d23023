"use client";
import { useEffect, useState } from "react";
import {
  Checkbox,
  Container,
  FormControlLabel,
  FormGroup,
  FormLabel,
  TextField,
} from "@mui/material";
import { useTranslation } from "react-i18next";

import CustomButton from "@/components/ui/CustomButton";
import { Role } from "@/utils/constants";
import AlertMessage from "@/components/ui/AlertMessage";
import { useLogin } from "@/features/auth/hooks/login.hooks";
import ConnectWithSocialMedia from "@/features/auth/component/ConnectWithSocialMedia";
import { adminRoutes, baseUrlBackoffice } from "@/helpers/routesList";
import { authRoutes } from "@/helpers/routesList";

function ApplyLogin({ successMsg, errorMsg, opportunity, language }) {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [errMsg, setErrMsg] = useState("");
  const [registerSuccess, setRegisterSuccess] = useState(false);

  const { t, i18n } = useTranslation();

  useEffect(() => {
    if (successMsg) setRegisterSuccess(successMsg);
    if (errorMsg) setErrMsg(errorMsg);
  }, [errorMsg, successMsg]);

  const useLoginHook = useLogin(t, setErrMsg, setRegisterSuccess);

  const handleLogin = async (e) => {
    e.preventDefault();

    await useLoginHook.mutateAsync(
      { email, password },
      {
        onSuccess: (response) => {
          setEmail("");
          setPassword("");
          setErrMsg("");
          if (response?.user?.roles?.includes(Role.CANDIDATE)) {
            window.location.href =
              language === "en"
                ? `/apply/${opportunity?.versions[i18n.language]?.url}/`
                : `/${language}/apply/${
                    opportunity?.versions[i18n.language]?.url
                  }/`;
          } else if (
            response?.user?.roles?.includes(Role.EDITOR) ||
            response?.user?.roles?.includes(Role.ADMIN)
          ) {
            window.location.href = `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.blogs.route}/`;
          }
        },
      }
    );
  };

  return (
    <Container className="container">
      <div>
        <p className="heading-h2 text-yellow">{t("login:haveAnAccount")}</p>
        <p className="heading-h3 text-white">{t("login:connectWithUs")}</p>
      </div>
      <div>
        <form id="login-form" className="pentabell-form">
          <FormGroup className="form-group">
            <FormLabel className="label-pentabell light">
              {t("register:email")}
            </FormLabel>
            <TextField
              className="input-pentabell light"
              placeholder={t("register:email")}
              variant="standard"
              type="email"
              value={email}
              onChange={(e) => {
                setEmail(e.target.value);
                setErrMsg("");
                setRegisterSuccess(false);
              }}
            />
          </FormGroup>
          <FormGroup className="form-group">
            <FormLabel className="label-pentabell light">
              {t("register:password")}
            </FormLabel>
            <TextField
              className="input-pentabell light"
              placeholder={t("register:password")}
              variant="standard"
              type="password"
              value={password}
              onChange={(e) => {
                setPassword(e.target.value);
                setErrMsg("");
                setRegisterSuccess(false);
              }}
            />
          </FormGroup>
          <CustomButton
            text={t("login:forgotPassword")}
            className={"btn btn-reset-pwd"}
            link={`/${authRoutes.forgetPassword.route}`}
          />
          <FormControlLabel
            className="checkbox-pentabell light"
            control={<Checkbox name="keep-loggin" />}
            label={t("login:keepMeSignedIn")}
          />
          <AlertMessage errMsg={errMsg} success={registerSuccess} />
          <CustomButton
            text={"Login"}
            className={"btn btn-filled full-width btn-submit"}
            onClick={handleLogin}
          />
          <ConnectWithSocialMedia
            removeBottomSection
            t={t}
            redirection={
              language === "en"
                ? `/apply/${opportunity?.versions[i18n.language]?.url}`
                : `/${language}/apply/${
                    opportunity?.versions[i18n.language]?.url
                  }`
            }
          />
        </form>
      </div>
    </Container>
  );
}

export default ApplyLogin;
