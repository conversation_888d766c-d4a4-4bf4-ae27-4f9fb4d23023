"use client";
import { Container, Grid } from "@mui/material";
import FeaturedEventsCard from "./FeaturedEventsCard.jsx";
import frenchSaudi from "@/assets/images/events/frenchSaudi.png";
import pentabellFrance from "@/assets/images/events/pentabellFrance.png";
import SalonSME from "@/assets/images/events/SalonSME.png";
import cop from "@/assets/images/events/cop.png";
import leap from "@/assets/images/events/leap.png";
import useEmblaCarousel from "embla-carousel-react";
import { useCallback } from "react";
import { useTranslation } from "react-i18next";

export default function FeaturedEvents({ language }) {
  const { t } = useTranslation();
  const [emblaRef, emblaApi] = useEmblaCarousel({
    align: "start",
    loop: true,
    containScroll: "trim",
  });

  const scrollPrev = useCallback(
    () => emblaApi && emblaApi.scrollPrev(),
    [emblaApi]
  );
  const scrollNext = useCallback(
    () => emblaApi && emblaApi.scrollNext(),
    [emblaApi]
  );
  const eventData = [
    {
      title: t("event:events.event1.title"),
      eventDate: t("event:events.event1.eventDate"),
      postingDate: t("event:events.event1.postingDate"),
      exactPlace: t("event:events.event1.exactPlace"),
      link: "franco-saudi-decarbonization-days",
      type: "events",
      image: frenchSaudi,
    },
    {
      title: t("event:events.event11.title"),
      eventDate: t("event:events.event11.eventDate"),
      exactPlace: t("event:events.event11.exactPlace"),
      link: "leap-tech-conference-2025-riyadh",
      type: "events",
      image: leap,
    },
    {
      title: t("event:events.event2.title"),
      eventDate: t("event:events.event2.eventDate"),
      postingDate: t("event:events.event2.postingDate"),
      exactPlace: t("event:events.event2.exactPlace"),
      link:
        language === "en"
          ? `pentabell-salon-sme-and-european-microwave-week`
          : "pentabell-salon-sme-and-european-microwave-week",
      type: "blog",
      image: pentabellFrance,
    },
    {
      title: t("event:events.event3.title"),
      eventDate: t("event:events.event3.eventDate"),
      postingDate: t("event:events.event3.postingDate"),
      exactPlace: t("event:events.event3.exactPlace"),
      type: "blog",
      link:
        language === "en"
          ? `pentabell-salon-sme-and-european-microwave-week`
          : "pentabell-salon-sme-and-european-microwave-week",
      image: SalonSME,
    },
    {
      title: t("event:events.event4.title"),
      eventDate: t("event:events.event4.eventDate"),
      postingDate: t("event:events.event4.postingDate"),
      exactPlace: t("event:events.event4.exactPlace"),
      type: "blog",
      link:
        language === "en"
          ? `cop-28-uae-2023-time-for-decisions`
          : "cop-28-uae-2023-time-for-decisions",
      image: cop,
    },
  ];

  return (
    <div id="events-page">
      <div id="event-page">
        <div className="featured-event-wrapper">
          <Grid container columnSpacing={2} sx={{ alignItems: "baseline" }}>
            <Grid item sm={6} sx={{ paddingLeft: "30px !important" }}>
              <p className="heading-h2 text-white">
                {t("ksa:featuredEvents:subTitle")}{" "}
              </p>
              <p className="heading-h1 text-white">
                {t("ksa:featuredEvents:title")}
              </p>
            </Grid>
            <Grid item sm={6} sx={{ paddingLeft: "30px !important" }}>
              <p className="label-pentabell light">
                {t("ksa:featuredEvents:description")}
              </p>
            </Grid>
          </Grid>
          <div className="event-slider">
            <div className="carousel">
              <div className="viewport" ref={emblaRef}>
                <div className="container">
                  {eventData?.map((event, index) => (
                    <div className="slide" key={index}>
                      <FeaturedEventsCard
                        key={index}
                        eventData={event}
                        language={language}
                        isEvent={true}
                      />
                    </div>
                  ))}
                </div>
              </div>
            </div>{" "}
          </div>{" "}
        </div>
      </div>
    </div>
  );
}
