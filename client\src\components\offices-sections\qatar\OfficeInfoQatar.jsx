import SvgUsers from "@/assets/images/icons/yellow/Users.svg";
import Svglanguage from "@/assets/images/icons/yellow/language.svg";
import Svggdp from "@/assets/images/icons/yellow/gdp.svg";
import Svgcurrency from "@/assets/images/icons/yellow/currency.svg";
import SvgcapitalCity from "@/assets/images/icons/yellow/capitalCity.svg";
import Svggross from "@/assets/images/icons/yellow/gross.svg";
import OfficeInfo from "@/components/ui/OfficeInfo";
async function OfficeInfoQatar({ t }) {
  const data = [
    {
      icon: <SvgUsers />,
      titleKey: "qatar:officeInfoQatar:title1",
      descriptionKey: "qatar:officeInfoQatar:description1"
    },
    {
      icon: <Svglanguage />,
      titleKey: "qatar:officeInfoQatar:title2",
      descriptionKey: "qatar:officeInfoQatar:description2"
    },
    {
      icon: <Svggdp />,
      titleKey: "qatar:officeInfoQatar:title3",
      descriptionKey: "qatar:officeInfoQatar:description3"
    },
    {
      icon: <Svgcurrency />,
      titleKey: "qatar:officeInfoQatar:title4",
      descriptionKey: "qatar:officeInfoQatar:description4"
    },
    {
      icon: <SvgcapitalCity />,
      titleKey: "qatar:officeInfoQatar:title5",
      descriptionKey: "qatar:officeInfoQatar:description5"
    },
    {
      icon: <Svggross />,
      titleKey: "qatar:officeInfoQatar:title6",
      descriptionKey: "qatar:officeInfoQatar:description6"
    }
  ];

  return <OfficeInfo data={data} t={t} />;
}

export default OfficeInfoQatar;
