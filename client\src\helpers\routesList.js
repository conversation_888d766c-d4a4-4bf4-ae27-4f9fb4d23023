export const baseUrlBackoffice = {
  baseURL: {
    route: "backoffice",
    name: "Home",
    key: "baseUrlBackoffice",
  },
};
export const baseUrlFrontoffice = {
  baseURL: {
    route: "dashboard",
    name: "Home",
    key: "baseUrlBackoffice",
  },
};
export const websiteRoutesList = {
  home: {
    route: "",
    name: "Home",
    key: "homePage",
  },
  aboutUs: {
    route: "about-us",
    name: "aboutUs",
    key: "aboutUs",
    i18nName: "menu:aboutUs",
  },
  services: {
    route: "hr-services",
    name: "services",
    key: "services",
    i18nName: "menu:services",
  },
  resources: {
    route: "resources",
    name: "resources",
    key: "resources",
    i18nName: "Resources",
  },
  events: {
    route: "events",
    name: "events",
    key: "events",
    i18nName: "Events",
  },
  payrollServices: {
    route: "payroll-service",
    name: "payrollServices",
    key: "payrollServices",
    i18nName: "menu:payrollServices",
  },
  consultingServices: {
    route: "consulting-services",
    name: "consultingServices",
    key: "consultingServices",
    i18nName: "menu:consultingServices",
  },

  technicalAssistance: {
    route: "technical-assistance",
    name: "technicalAssistance",
    key: "technicalAssistance",
    i18nName: "menu:technicalAssistance",
  },
  aiSourcing: {
    route: "pentabell-ai-sourcing-coordinators",
    name: "aiSourcing",
    key: "aiSourcing",
    i18nName: "menu:aiSourcing",
  },
  directHiring: {
    route: "direct-hiring-solutions",
    name: "directHiring",
    key: "directHiring",
    i18nName: "menu:directHiring",
  },
  opportunities: {
    route: "opportunities",
    name: "Opportunities",
    key: "opportunities",
    i18nName: "menu:opportunities",
  },
  jobCategory: {
    route: "job-category",
    name: "jobCategory",
    key: "jobCategory",
    i18nName: "menu:jobCategory",
  },
  /*  oilGas: {
    route: "oil-and-gas",
    name: "oilGas",
    key: "oilGas",
    i18nName: "menu:oilGas",
  }, */
  transportation: {
    route: "transport",
    name: "transportation",
    key: "transportation",
    i18nName: "menu:transportation",
  },
  itTelecom: {
    route: "it-telecom",
    name: "itTelecom",
    key: "itTelecom",
    i18nName: "menu:itTelecom",
  },

  insuranceBanking: {
    route: "banking-insurance",
    name: "insuranceBanking",
    key: "insuranceBanking",
    i18nName: "menu:insuranceBanking",
  },

  energies: {
    route: "energies",
    name: "energies",
    key: "energies",
    i18nName: "menu:energies",
  },

  others: {
    route: "other",
    name: "others",
    key: "others",
    i18nName: "menu:others",
  },

  pharmaceutical: {
    route: "pharmaceutical",
    name: "pharmaceutical",
    key: "pharmaceutical",
    i18nName: "menu:pharma",
  },
  blog: {
    route: "blog",
    name: "Blog",
    key: "blog",
    i18nName: "menu:blog",
  },

  guide: {
    route: "guides",
    name: "guides",
    key: "guides",
    i18nName: "menu:guides",
  },

  // glossaries: {
  //   route: "glossaries",
  //   name: "glossaries",
  //   key: "glossaries",
  //   i18nName: "menu:glossaries",
  // },

  joinUs: {
    route: "join-us",
    name: "joinUs",
    key: "joinUs",
    i18nName: "menu:joinUs",
  },
  contact: {
    route: "contact",
    name: "contact",
    key: "contact",
    i18nName: "menu:contact",
  },

  category: {
    route: "category",
    name: "category",
    key: "category",
    i18nName: "menu:category",
  },

  apply: {
    route: "apply",
    name: "apply",
    key: "apply",
  },

  egyptePage: {
    route: "guide-to-hiring-employees-in-egypt",
    name: "egypte",
    key: "egyptePage",
  },
  libyaPage: {
    route: "guide-to-hiring-employees-in-libya",
    name: "libya",
    key: "libyaPage",
  },
  tunisiaPage: {
    route: "hiring-employees-tunisia-guide",
    name: "tunisia",
    key: "tunisiaPage",
  },
  ksaPage: {
    route: "international-hr-services-recruitment-agency-ksa",
    name: "ksa",
    key: "ksaPage",
  },
  qatarPage: {
    route: "international-hr-services-recruitment-agency-qatar",
    name: "qatar",
    key: "qatarPage",
  },
  iraqPage: {
    route: "international-hr-services-recruitment-agency-iraq",
    name: "iraq",
    key: "iraqPage",
  },
  africaPage: {
    route: "international-recruitment-staffing-company-in-africa",
    name: "africa",
    key: "africaPage",
  },
  europePage: {
    route: "international-recruitment-staffing-company-in-europe",
    name: "europe",
    key: "europePage",
  },
  middleEastPage: {
    route: "international-recruitment-staffing-company-in-middle-east",
    name: "middleEast",
    key: "middleEastPage",
  },
  francePage: {
    route: "recruitment-agency-france",
    name: "france",
    key: "francePage",
  },
  dubaiPage: {
    route: "recruitment-staffing-agency-dubai",
    name: "dubai",
    key: "dubaiPage",
  },
  algeriaPage: {
    route: "ultimate-guide-to-hiring-employees-in-algeria",
    name: "algeria",
    key: "algeriaPage",
  },
  moroccoPage: {
    route: "ultimate-guide-to-hiring-employees-in-morocco",
    name: "morocco",
    key: "moroccoPage",
  },
  privacyPolicy: {
    route: "privacy-policy",
    name: "privacyPolicy",
    key: "privacyPolicy",
  },
  termsAndConditions: {
    route: "terms-and-conditions",
    name: "termsAndConditions",
    key: "termsAndConditions",
  },
  document: {
    route: "document",
    name: "document",
    key: "document",
  },
  pfeBookLink: {
    route: "pfe-book-2024-2025",
    name: "pfeBookLink",
    key: "pfeBookLink",
  },
  jobLocation: {
    route: "job-location",
    name: "jobLocation",
    key: "jobLocation",
    i18nName: "menu:jobLocation",
  },
  corporatProfile: {
    route: "corporate-profile",
    name: "corporateProfile",
    key: "corporateProfile",
    i18nName: "menu:companyProfile",
  },
};

export const authRoutes = {
  login: {
    route: "login",
    name: "login",
    key: "login",
    i18nName: "menu:login",
  },
  register: {
    route: "register",
    name: "register",
    key: "register",
    i18nName: "menu:register",
  },

  forgetPassword: {
    route: "forgot-password",
    name: "forgetPassword",
    key: "forgetPassword",
  },
  logout: {
    route: "logout",
    name: "logout",
    key: "logout",
    i18nName: "sidebar:logout",
  },

  resetPassword: {
    route: "reset-password/:token",
    name: "Reset Password",
    key: "resetPassword",
  },

  resend: {
    route: "resend-activation",
    name: "Resend Activation",
    key: "resend",
  },

  activation: {
    route: "activation/:token",
    name: "Activation",
    key: "activation",
  },
};

export const commonRoutes = {
  settings: {
    route: "settings",
    name: "Settings",
    key: "settings",
    i18nName: "sidebar:settings",
  },
  myProfile: {
    route: "my-profile",
    name: "profile",
    key: "myProfile",
    i18nName: "menu:profile",
  },
  notifications: {
    route: "notifications",
    name: "notifications",
    key: "notifications",
    i18nName: "menu:notifications",
  },
};

export const candidateRoutes = {
  resumes: {
    route: "my-resumes",
    name: "my resumes",
    key: "Resumes",
    i18nName: "menu:myResumes",
  },
  myApplications: {
    route: "my-applications",
    name: "My Applications",
    key: "myApplications",
    i18nName: "menu:myApplications",
  },
  favoris: {
    route: "favoris",
    name: "Favoris",
    key: "favoris",
    i18nName: "menu:favoris",
  },
  home: {
    route: "home",
    name: "home",
    key: "home",
    i18nName: "menu:home",
  },

  ...Object.assign({}, commonRoutes),
};

export const editorRoutes = {
  home: {
    route: "home",
    name: "Home",
    key: "homePage",
  },
  blogs: {
    route: "blogs",
    name: "Blogs",
    key: "blogs",
    i18nName: "menu:blog",
  },
  sliders: {
    route: "sliders",
    name: "sliders",
    key: "sliders",
    i18nName: "menu:sliders",
  },
  glossaries: {
    route: "glossaries",
    name: "glossaries",
    key: "glossaries",
    i18nName: "menu:glossaries",
  },
  downloads: {
    route: "downloads",
    name: "downloads",
    key: "downloads",
    i18nName: "menu:downloads",
  },
  add: {
    route: "add",
    name: "create",
    key: "add",
  },
  edit: {
    route: "edit",
    name: "edit",
    key: "edit",
  },
  updateslider: {
    route: "updateslider",
    name: "updateslider",
    key: "updateslider",
  },
  comments: {
    route: "comments",
    name: "comments",
    key: "comments",
    i18nName: "menu:comments",
  },
  archived: {
    route: "archived",
    name: "archived",
    key: "archived",
  },

  candidate: {
    route: "candidate",
    name: "candidate",
    key: "candidate",
  },

  categories: {
    route: "categories",
    name: "categories",
    key: "categories",
  },

  detail: {
    route: "detail",
    name: "detail",
    key: "detail",
  },
  newsletters: {
    route: "newsletters",
    name: "newsletters",
    key: "newsletters",
  },
  opportunities: {
    route: "opportunities",
    name: "Opportunities",
    key: "opportunities",
    i18nName: "menu:opportunities",
  },
  categoriesguide: {
    route: "CategoriesGuide",
    name: "CategoriesGuide",
    key: "CategoriesGuide",
    i18nName: "guides:categoriesGuide",
  },
  opportunity: {
    route: "opportunity",
    name: "opportunity",
    key: "opportunity",
  },
  editSEOTags: {
    route: "edit-seo-tags",
    name: "edit SEO Tags",
    key: "editSEOTags",
    i18nName: "menu:editSEOTags",
  },
  contacts: {
    route: "contacts",
    name: "contacts",
    key: "contacts",
    i18nName: "menu:contact",
  },
  seoSettings: {
    route: "seo-settings",
    name: "seo-settings",
    key: "seo-settings",
    i18nName: "menu:seoSettings",
  },
  guides: {
    route: "guides",
    name: "guides",
    key: "guides",
  },
  events: {
    route: "events",
    name: "Events",
    key: "events",
    i18nName: "menu:events",
  },
  ...Object.assign({}, commonRoutes),
};

export const adminRoutes = {
  statistics: {
    route: "statistics",
    name: "statistics",
    key: "statistics",
    i18nName: "menu:statistics",
  },
  applications: {
    route: "applications",
    name: "applications",
    key: "applications",
    i18nName: "application:candidatures",
  },
  downloadReport: {
    route: "download-report",
    name: "downloadReport",
    key: "downloadReport",
    i18nName: "Downloads Report",
  },
  users: {
    route: "users",
    name: "users",
    key: "users",
    i18nName: "menu:users",
  },

  user: {
    route: "user",
    name: "user",
    key: "user",
  },

  // ...Object.assign({}, commonRoutes),
  ...Object.assign({}, editorRoutes),
};

export const editorPermissionsRoutes = [
  `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.myProfile.route}`,
  `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.home.route}`,
  `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.guides.route}`,
  `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.guides.route}/${editorRoutes.downloads.route}/:id`,
  `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.guides.route}/${editorRoutes.add.route}`,
  `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.blogs.route}`,
  `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.guides.route}/${editorRoutes.categories.route},${editorRoutes.add.route}`,
  `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.guides.route}/${editorRoutes.categories.route}`,
  `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.blogs.route}/${editorRoutes.add.route}`,
  `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.blogs.route}/${editorRoutes.archived.route}`,
  `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.blogs.route}/${editorRoutes.comments.route}/:id`,
  `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.blogs.route}/${editorRoutes.edit.route}/:id`,
  `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.sliders.route}`,
  `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.sliders.route}/${adminRoutes.updateslider.route}`,
  `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.comments.route}`,
  `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.opportunities.route}`,
  `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.opportunities.route}/${editorRoutes.add.route}`,
  `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.opportunities.route}/${editorRoutes.edit.route}/:id`,
  `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.opportunities.route}/${editorRoutes.editSEOTags.route}`,
  `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.categories.route}`,
  `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.categories.route}/${editorRoutes.add.route}`,
  `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.categories.route}/${editorRoutes.edit.route}/:id`,
  `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.notifications.route}`,
  `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.settings.route}`,
  `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.contacts.route}`,
  `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.contacts.route}/${editorRoutes.edit.route}/:id`,
  `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.newsletters.route}`,
  `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.seoSettings.route}`,
  `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.events.route}`,
  `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.glossaries.route}`,
  `/${authRoutes.logout.route}`,
];

export const adminPermissionsRoutes = [
  `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.applications.route}`,
  `/${baseUrlBackoffice.baseURL.route}/${editorRoutes.guides.route}/${editorRoutes.downloads.route}/:id`,
  `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.applications.route}/:id`,
  `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.downloadReport.route}`,
  `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.applications.route}/${adminRoutes.edit.route}/:id`,
  `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.applications.route}/${adminRoutes.detail.route}/:id`,
  `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.applications.route}/${adminRoutes.opportunity.route}/:id`,
  `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.guides.route}/${adminRoutes.downloads.route}/:id`,
  `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.users.route}`,
  `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.guides.route}/${adminRoutes.categories.route}`,
  `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.guides.route}`,
  `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.guides.route}/${adminRoutes.add.route}`,
  `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.guides.route}/${adminRoutes.categories.route},${adminRoutes.add.route}`,
  `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.users.route}/${adminRoutes.detail.route}/:id`,
  `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.users.route}/${adminRoutes.edit.route}/:id`,
  `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.users.route}/${adminRoutes.add.route}`,
  `/${baseUrlBackoffice.baseURL.route}/${adminRoutes.statistics.route}`,
  ...editorPermissionsRoutes,
];
export const candidatePermissionsRoutes = [
  `/${baseUrlFrontoffice.baseURL.route}/${candidateRoutes.favoris.route}`,
  `/${baseUrlFrontoffice.baseURL.route}/${candidateRoutes.home.route}`,
  `/${baseUrlFrontoffice.baseURL.route}/${candidateRoutes.myApplications.route}`,
  `/${baseUrlFrontoffice.baseURL.route}/${candidateRoutes.myProfile.route}`,
  `/${baseUrlFrontoffice.baseURL.route}/${candidateRoutes.resumes.route}`,
  `/${baseUrlFrontoffice.baseURL.route}/${candidateRoutes.notifications.route}`,
  `/${baseUrlFrontoffice.baseURL.route}/${candidateRoutes.settings.route}`,
  `/${authRoutes.logout.route}`,
];
