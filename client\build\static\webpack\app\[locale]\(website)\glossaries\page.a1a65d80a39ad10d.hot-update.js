"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/glossaries/page",{

/***/ "(app-pages-browser)/./src/components/sections/GlossaryBanner.jsx":
/*!****************************************************!*\
  !*** ./src/components/sections/GlossaryBanner.jsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Container_Input_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Container,Input,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Container_Input_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Container,Input,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Input/Input.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Container_Input_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Container,Input,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _assets_images_icons_BookIcon_svg__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/assets/images/icons/BookIcon.svg */ \"(app-pages-browser)/./src/assets/images/icons/BookIcon.svg\");\n/* harmony import */ var _assets_images_icons_searchIcon_svg__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/assets/images/icons/searchIcon.svg */ \"(app-pages-browser)/./src/assets/images/icons/searchIcon.svg\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction GlossaryBanner(param) {\n    let { bannerImg, height, altImg, letters = [], searchWord = \"\", hasContent = true } = param;\n    _s();\n    const { t } = useTranslation();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [keyword, setKeyword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(searchWord || searchParams?.get(\"word\") || \"\");\n    const handleSearchChange = (e)=>{\n        setKeyword(e.target.value);\n    };\n    const handleSearchClick = ()=>{\n        const params = new URLSearchParams();\n        if (keyword.trim()) {\n            params.set(\"word\", keyword.trim());\n        }\n        const queryString = params.toString();\n        router.push(`${pathname}${queryString ? `?${queryString}` : \"\"}`);\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\") {\n            handleSearchClick();\n        }\n    };\n    const handleClearSearch = ()=>{\n        setKeyword(\"\");\n        router.push(pathname);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"glossary-banner\",\n        className: \"center-banner\",\n        style: {\n            backgroundImage: `url(${bannerImg.src})`,\n            height: height\n        },\n        children: [\n            altImg && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                width: 0,\n                height: 0,\n                alt: altImg,\n                src: \"\",\n                style: {\n                    display: \"none\"\n                },\n                loading: \"lazy\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                lineNumber: 60,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_Input_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"top-section custom-max-width\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_BookIcon_svg__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"heading-h1 text-white page-title\",\n                        children: t(\"glossary:banner:title\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"glossary-search\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_Input_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"glossary-search-input\",\n                                type: \"text\",\n                                placeholder: \"What are you looking for ?\",\n                                onChange: handleSearchChange,\n                                onKeyDown: handleKeyDown,\n                                value: keyword\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_Input_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"btn-search\",\n                                onClick: handleSearchClick,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_searchIcon_svg__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this),\n                    hasContent && letters?.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"letters\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"letter selected\",\n                                children: \"#\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, this),\n                            letters.map((letter, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: `#${letter}`,\n                                    className: \"letter\",\n                                    children: letter\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 15\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, this),\n                    !hasContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"letters-empty\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                color: \"white\",\n                                opacity: 0.8,\n                                fontSize: \"14px\",\n                                marginTop: \"20px\"\n                            },\n                            children: searchWord ? `No results found for \"${searchWord}\"` : \"No glossary terms available\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                            lineNumber: 101,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n_s(GlossaryBanner, \"XcJDQxtYUQazr3yog0GQznqGJAQ=\", true, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = GlossaryBanner;\n/* harmony default export */ __webpack_exports__[\"default\"] = (GlossaryBanner);\nvar _c;\n$RefreshReg$(_c, \"GlossaryBanner\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/GlossaryBanner.jsx\n"));

/***/ })

});