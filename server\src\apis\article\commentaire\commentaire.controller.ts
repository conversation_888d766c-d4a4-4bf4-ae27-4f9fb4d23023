import { Router, Request, Response, NextFunction } from 'express';
import isAuthenticated from '@/middlewares/authentication.middleware';
import { hasRoles } from '@/middlewares/authorization.middleware';
import { Role } from '@/utils/helpers/constants';

import CommentService from './commentaire.service';
import { CommentQuery } from 'types/request/CommentQuery';
import { MESSAGES } from '@/utils/helpers/messages';
import validateMongoId from '@/middlewares/mongoId-validation.middleware';
import { checkCurrentUserForComment, checkCurrentUserForResponse } from '@/middlewares/validation.middleware';
import apiKeyMiddleware from '@/middlewares/validateApiKey.middleware';
import { invalidateCache, validateCache } from '@/middlewares/cache.middleware';
import { CommentModel } from './commentaire.model';
import validateQueries from '@/middlewares/queries-validation.middleware';
import { MessagePort } from 'node:worker_threads';

class CommentController {
    public readonly path = '/comments';
    private readonly commentService = new CommentService();
    public readonly router = Router();

    constructor() {
        this.initialiseRoutes();
    }

    private initialiseRoutes(): void {
        this.router.get(`${this.path}`, apiKeyMiddleware, isAuthenticated,
            hasRoles([Role.ADMIN, Role.EDITEUR]),
            validateQueries, this.getAllComments);

        this.router.get(`${this.path}/:articleId`, apiKeyMiddleware, validateCache, this.getAllCommentsByArticleId);
        this.router.get(`${this.path}/detail/:commentId`, this.getCommentById);
        this.router.get(`${this.path}/Dashboard/:articleId`, apiKeyMiddleware, validateCache, this.getAllCommentsByArticleIdDashboard);


        this.router.post(`${this.path}/:articleId`, checkCurrentUserForComment, invalidateCache, this.addCommentToArticle);

        this.router.post(`${this.path}/:commentId/response`, checkCurrentUserForResponse, invalidateCache, this.addResponseToComment);
        this.router.put(
            `${this.path}/approve/:commentId`,
            isAuthenticated,
            hasRoles([Role.ADMIN, Role.EDITEUR]),
            invalidateCache,
            this.approveOrDisapproveComment,
        );
        this.router.delete(`${this.path}/:id`, isAuthenticated, validateMongoId, invalidateCache, this.deleteComment);
    }

    private getAllComments = async (req: Request, res: Response, next: NextFunction) => {
        try {
            const queries: CommentQuery = <CommentQuery>req.query;
            const comments = await this.commentService.getAllComments(queries);
            res.send(comments);
        } catch (error) {
            next(error);
        }
    };

    private addCommentToArticle = async (req: any, res: Response, next: NextFunction) => {
        try {
            await this.commentService.addCommentToArticle(req.params.articleId, req.body, req.user);
            res.send({ message: MESSAGES.COMMENT.CREATED });
        } catch (error) {
            next(error);
        }
    };

    private approveOrDisapproveComment = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
        try {
            const commentId: string = req.params.commentId;
            const approved = req.body.approve;
            res.send({ message: await this.commentService.approveOrDisapproveComment(commentId, approved) });
        } catch (error) {
            next(error);
        }
    };

    public getCommentById = async (req: Request, res: Response, next: NextFunction) => {
        try {
            const { commentId } = req.params;
            const comment = await this.commentService.get(commentId);
            res.status(200).json(comment);
        } catch (error) {
            next(error);
        }
    };
    private assignBadges = async () => {
        const usersWithApprovedComments = await CommentModel.aggregate([
            { $match: { approved: true } },
            {
                $group: {
                    _id: { email: '$email', name: '$name' },
                    approvedCount: { $sum: 1 },
                },
            },
            { $match: { approvedCount: { $gte: 20 } } },
        ]);
        const badgeUsers = usersWithApprovedComments.map((user: any) => user._id.email);
        await CommentModel.updateMany({ email: { $in: badgeUsers } }, { $set: { badge: 'top commenter' } });
    };

    private getAllCommentsByArticleId = async (req: Request, res: Response, next: NextFunction) => {
        try {
            await this.assignBadges();
            const queries: CommentQuery = <CommentQuery>req.query;
            const { articleId } = req.params;
            const comments = await this.commentService.getAllCommentsByArticle(articleId, queries);
            res.send(comments);
        } catch (error) {
            next(error);
        }
    };
    private getAllCommentsByArticleIdDashboard = async (req: Request, res: Response, next: NextFunction) => {
        try {
            await this.assignBadges();
            const queries: CommentQuery = <CommentQuery>req.query;
            const { articleId } = req.params;
            const comments = await this.commentService.getAllCommentsByArticleDashboard(articleId, queries);
            res.send(comments);
        } catch (error) {
            next(error);
        }
    };

    private addResponseToComment = async (req: any, res: Response, next: NextFunction) => {
        try {
            const { commentId } = req.params;
            const responseData = req.body;
            await this.commentService.addResponseToCommentService(commentId, responseData, req.user);
            res.send({ message: MESSAGES.COMMENT.CREATED });
        } catch (error) {
            next(error);
        }
    };

    private deleteComment = async (request: any, response: Response, next: NextFunction) => {
        try {
            const id: string = request.params.id;
            const currentUser = request.user;
            await this.commentService.deleteComment(id, currentUser);
            response.send({
                message: MESSAGES.COMMENT.DELETED,
            });
        } catch (error) {
            next(error);
        }
    };
}
export default CommentController;
