import OfficeLocationMap from "@/components/ui/OfficeLocationMap";

function OfficeLocationMapDubai({ t }) {
  return (
    <OfficeLocationMap
      title={t("dubai:officeLocation:label")}
      subtitle={t("dubai:officeLocation:title")}
      address={t("dubai:officeLocation:address")}
      tel={t("dubai:officeLocation:tel1")}
      email={t("dubai:officeLocation:mail")}
      linkText={t("dubai:officeLocation:talk")}
      mapSrc="https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d115651.58473888831!2d55.13805!3d25.064192!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3e5f6da910664d5f%3A0x5aacb31b12e9c24e!2sInternational%20Recruitment%20%26%20Staffing%20Agency%20Dubai%20%7C%20Pentabell%20Dubai!5e0!3m2!1sfr!2sus!4v1728638020968!5m2!1sfr!2sus"

    />
  );
}
export default OfficeLocationMapDubai;
