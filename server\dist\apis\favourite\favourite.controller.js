"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const favourite_service_1 = __importDefault(require("./favourite.service"));
const authentication_middleware_1 = __importDefault(require("@/middlewares/authentication.middleware"));
const mongoId_validation_middleware_1 = __importDefault(require("@/middlewares/mongoId-validation.middleware"));
const authorization_middleware_1 = require("@/middlewares/authorization.middleware");
const constants_1 = require("@/utils/helpers/constants");
const messages_1 = require("@/utils/helpers/messages");
class FavouriteController {
    constructor() {
        this.path = '/favourite';
        this.router = (0, express_1.Router)();
        this.favouriteService = new favourite_service_1.default();
        this.getAllFavourites = async (request, response, next) => {
            try {
                const queries = request.query;
                const id = request.user._id;
                const result = await this.favouriteService.getAllFavourites(queries, id);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.isItemSaved = async (request, response, next) => {
            try {
                const { itemId } = request.params;
                const id = request.user._id;
                const result = await this.favouriteService.isItemSaved(itemId, id);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.removeFavourite = async (request, response, next) => {
            try {
                const favouriteId = request.params.id;
                const currentUserId = request.user._id;
                const type = request.body.type;
                await this.favouriteService.removeFavourite(currentUserId, favouriteId, type);
                response.send({
                    message: messages_1.MESSAGES.CANDIDATE_SHORTLIST.DELETED,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.addToFavourite = async (request, response, next) => {
            try {
                const favouriteId = request.params.id;
                const type = request.body.type;
                const currentUser = request.user;
                const result = await this.favouriteService.addToFavourite(favouriteId, type, currentUser);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.initialiseRoutes();
    }
    initialiseRoutes() {
        this.router.get(`${this.path}`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.CANDIDATE]), this.getAllFavourites);
        this.router.delete(`${this.path}/:id`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.CANDIDATE]), mongoId_validation_middleware_1.default, this.removeFavourite);
        this.router.put(`${this.path}/:id`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.CANDIDATE]), mongoId_validation_middleware_1.default, this.addToFavourite);
        this.router.get(`${this.path}/is-saved/:itemId`, authentication_middleware_1.default, this.isItemSaved);
    }
}
exports.default = FavouriteController;
//# sourceMappingURL=favourite.controller.js.map