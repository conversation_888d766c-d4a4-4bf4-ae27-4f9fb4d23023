"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const guide_category_model_1 = __importDefault(require("./guide.category.model"));
const guide_interface_1 = require("../guide.interface");
const mongoose_1 = __importDefault(require("mongoose"));
const guide_model_1 = __importDefault(require("../guide.model"));
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const messages_1 = require("@/utils/helpers/messages");
const mongoose_2 = require("mongoose");
const functions_1 = require("@/utils/helpers/functions");
class CategoryGuideService {
    async createCategory(categoryData) {
        if (!categoryData.categoryguide || !Array.isArray(categoryData.categoryguide)) {
            throw new http_exception_1.default(400, "MESSAGES.Category.Invalid_Data");
        }
        const duplicateCategory = await guide_category_model_1.default.findOne({
            $or: categoryData.categoryguide.map((version) => ({
                'categoryguide.name': version.name.trim(),
                'categoryguide.language': version.language,
            })),
        });
        if (duplicateCategory) {
            throw new http_exception_1.default(409, messages_1.MESSAGES.CATEGORY.ALREADY_EXIST);
        }
        const existingUrls = new Set();
        const existingCategories = await guide_category_model_1.default.find({}, 'categoryguide.url');
        existingCategories.forEach(existingCategory => {
            existingCategory.categoryguide.forEach(version => {
                existingUrls.add(version.url);
            });
        });
        const defaultLanguage = guide_interface_1.Language.ENGLISH;
        for (const version of categoryData.categoryguide) {
            if (!version.language) {
                version.language = defaultLanguage;
            }
            let url = version.url || version.name.toLowerCase().replace(/\s+/g, '-');
            let count = 0;
            while (existingUrls.has(url)) {
                count++;
                url = `${url}-${count}`;
            }
            existingUrls.add(url);
            version.url = url;
        }
        const newCategory = new guide_category_model_1.default({ ...categoryData, categoryguide: categoryData.categoryguide });
        const savedCategory = await newCategory.save();
        for (const version of savedCategory.categoryguide) {
            const newguides = version.guides || [];
            for (const guideId of newguides) {
                const guideVersion = await guide_model_1.default.findOne({ 'versionsguide._id': guideId });
                if (guideVersion) {
                    const targetCategoryVersionIndex = guideVersion.versionsguide.findIndex((ver) => ver._id.equals(guideId));
                    if (targetCategoryVersionIndex !== -1) {
                        const targetCategoryVersion = guideVersion.versionsguide[targetCategoryVersionIndex];
                        if (!targetCategoryVersion.category.includes(version._id)) {
                            targetCategoryVersion.category.push(version._id);
                            await guideVersion.save();
                        }
                    }
                }
            }
        }
        return savedCategory;
    }
    async getCategoriesByLanguage(language) {
        const categories = await guide_category_model_1.default.find({ 'categoryguide.language': language }).lean();
        const filteredCategories = categories.map(category => ({
            _id: category._id,
            categoryguide: category.categoryguide
                .filter(version => version.language === language)
                .map(version => ({
                name: version.name,
                id: version._id,
            })),
        }));
        return filteredCategories;
    }
    async getOppositeLanguageVersionsCategory(language, versionIds) {
        const targetLanguage = language === 'en' ? 'fr' : 'en';
        const categories = await guide_category_model_1.default.find({ 'categoryguide._id': { $in: versionIds } }).exec();
        if (!categories || categories.length === 0) {
            return [];
        }
        const filteredCategories = categories.map(category => {
            const targetVersion = category.categoryguide.find(version => version.language === targetLanguage);
            if (targetVersion) {
                return {
                    _id: targetVersion._id,
                    name: targetVersion.name,
                };
            }
            else {
                return {
                    _id: null,
                    name: 'N/A',
                };
            }
        });
        return filteredCategories;
    }
    async getAllCategories(queries) {
        const { paginated = 'true', pageNumber = 1, pageSize = 10, language, sortOrder = 'desc', name } = queries;
        const queryConditions = {};
        if (language) {
            queryConditions['categoryguide.language'] = language;
        }
        if (name) {
            queryConditions['categoryguide.name'] = new RegExp(name, 'i');
        }
        let categoriesQuery = guide_category_model_1.default.find(queryConditions);
        if (sortOrder) {
            categoriesQuery = categoriesQuery.sort({ 'categoryguide.createdAt': sortOrder === 'asc' ? 1 : -1 }).select('-articles');
        }
        if (paginated === 'true') {
            const skip = (pageNumber - 1) * pageSize;
            categoriesQuery = categoriesQuery.skip(skip).limit(pageSize);
        }
        const categories = await categoriesQuery.lean();
        const filteredCategories = categories.map(category => ({
            _id: category._id,
            categoryguide: language ? category.categoryguide.filter(version => version.language === language) : category.categoryguide,
        }));
        const totalCategories = await guide_category_model_1.default.countDocuments(queryConditions);
        const totalPages = Math.ceil(totalCategories / pageSize);
        return {
            pageNumber: Number(pageNumber),
            pageSize: Number(pageSize),
            totalPages,
            totalCategories,
            categoriesData: filteredCategories,
        };
    }
    async updateCategoryByIdAndLanguage(language, categoryId, updateData) {
        const categoryToUpdate = await guide_category_model_1.default.findById(categoryId);
        if (!categoryToUpdate)
            throw new http_exception_1.default(404, messages_1.MESSAGES.CATEGORY.NOT_FOUND);
        const existingUrls = new Set();
        const existingCategories = await guide_category_model_1.default.find({}, 'categoryguide.url');
        existingCategories.forEach(existingCategory => {
            if (existingCategory.categoryguide) {
                existingCategory.categoryguide.forEach((version) => {
                    existingUrls.add(version.url);
                });
            }
        });
        let updatedVersion = null;
        if (categoryToUpdate.categoryguide) {
            for (const version of categoryToUpdate.categoryguide) {
                if (version.language === language) {
                    const previousVersion = JSON.parse(JSON.stringify(version));
                    const titleChanged = updateData.name && updateData.name !== version.name;
                    if (titleChanged) {
                        const titleExists = await guide_category_model_1.default.exists({
                            'categoryguide.name': updateData.name.trim(),
                            'categoryguide.language': updateData.language,
                            _id: { $ne: categoryToUpdate._id },
                        });
                        if (titleExists)
                            throw new http_exception_1.default(409, messages_1.MESSAGES.CATEGORY.ALREADY_EXIST);
                    }
                    const urlChanged = updateData.url && updateData.url !== version.url;
                    if (urlChanged) {
                        const urlExists = await guide_category_model_1.default.exists({ 'categoryguide.url': updateData.url });
                        if (urlExists)
                            throw new http_exception_1.default(409, messages_1.MESSAGES.CATEGORY.URL_ALREADY_EXIST);
                        version.url = updateData.url || updateData.name.toLowerCase().replace(/\s+/g, '-');
                    }
                    Object.assign(version, updateData);
                    updatedVersion = version;
                    const defaultLanguage = guide_interface_1.Language.ENGLISH;
                    if (!version.language) {
                        version.language = defaultLanguage;
                    }
                    await categoryToUpdate.save();
                    if (updatedVersion.guides.length >= 0) {
                        const previousArticles = previousVersion.guides.map((id) => id.toString());
                        const newArticles = updateData.guides || [];
                        const removedArticles = previousArticles.filter((articleId) => !newArticles.includes(articleId));
                        const addedArticles = newArticles.filter((articleId) => !previousArticles.includes(articleId));
                        for (const articleId of addedArticles) {
                            const articleVersion = await guide_model_1.default.findOne({ 'versionsguide._id': articleId });
                            if (articleVersion) {
                                const targetCategoryVersionIndex = articleVersion.versionsguide.findIndex((ver) => ver._id.equals(articleId));
                                if (targetCategoryVersionIndex !== -1) {
                                    const targetCategoryVersion = articleVersion.versionsguide[targetCategoryVersionIndex];
                                    if (!targetCategoryVersion.category.includes(updatedVersion._id)) {
                                        targetCategoryVersion.category.push(updatedVersion._id);
                                        await articleVersion.save();
                                    }
                                }
                            }
                        }
                        for (const articleId of removedArticles) {
                            const articleVersion = await guide_model_1.default.findOne({ 'versionsguide._id': new mongoose_2.Types.ObjectId(articleId) });
                            if (articleVersion) {
                                const targetCategoryVersionIndex = articleVersion.versionsguide.findIndex((ver) => ver._id.equals(articleId));
                                if (targetCategoryVersionIndex !== -1) {
                                    const targetCategoryVersion = articleVersion.versionsguide[targetCategoryVersionIndex];
                                    if (updatedVersion && targetCategoryVersion.category.includes(updatedVersion._id)) {
                                        targetCategoryVersion.category = targetCategoryVersion.category.filter((catId) => !catId.equals(updatedVersion?._id));
                                        await articleVersion.save();
                                    }
                                }
                            }
                        }
                    }
                    return categoryToUpdate;
                }
            }
        }
        throw new http_exception_1.default(404, messages_1.MESSAGES.CATEGORY.MISSING_ID_OR_LANGUAGE);
    }
    async updatecategoryVersions(categoryId, language, versionData) {
        const existingCategory = await guide_category_model_1.default.findById(categoryId);
        if (!existingCategory)
            throw new http_exception_1.default(404, messages_1.MESSAGES.CATEGORY.NOT_FOUND);
        const existingUrls = new Set();
        const existingCategorys = await guide_category_model_1.default.find({ _id: { $ne: categoryId } }, 'categoryguide.url');
        existingCategorys.forEach(existingCategory => {
            if (existingCategory.categoryguide) {
                existingCategory.categoryguide.forEach((version) => {
                    existingUrls.add(version.url);
                });
            }
        });
        let versionFound = false;
        if (existingCategory.categoryguide) {
            for (const version of existingCategory.categoryguide) {
                if (version.language === language) {
                    versionFound = true;
                    break;
                }
            }
        }
        if (versionFound) {
            return await this.updateCategoryByIdAndLanguage(language, categoryId, versionData);
        }
        else {
            let url = versionData.url || versionData.name.toLowerCase().replace(/\s+/g, '-');
            let count = 0;
            if (existingUrls.has(url)) {
                count = 1;
                while (existingUrls.has(`${url}-${count}`)) {
                    count++;
                }
                url = `${url}-${count}`;
            }
            existingUrls.add(url);
            const defaultLanguage = guide_interface_1.Language.ENGLISH;
            if (!versionData.language) {
                versionData.language = defaultLanguage;
            }
            const newVersion = {
                language: versionData.language || language,
                name: versionData.name,
                url: url,
                description: versionData.description,
                articles: versionData.articles || [],
                metaTitle: versionData.metaTitle,
                metaDescription: versionData.metaDescription,
                image: versionData.image,
                createdAt: new Date(),
                updatedAt: new Date(),
                _id: new mongoose_1.default.Types.ObjectId(),
                canonical: versionData.canonical,
            };
            return await this.addVersionToCategory(categoryId, newVersion);
        }
    }
    async addVersionToCategory(categoryId, newVersion) {
        const existingCategory = await guide_category_model_1.default.findById(categoryId);
        if (!existingCategory)
            throw new http_exception_1.default(404, messages_1.MESSAGES.CATEGORY.NOT_FOUND);
        const existingUrls = new Set();
        const existingCategorys = await guide_category_model_1.default.find({}, 'categoryguide.url');
        existingCategorys.forEach(existingCategory => {
            if (existingCategory.categoryguide) {
                existingCategory.categoryguide.forEach((version) => {
                    existingUrls.add(version.url);
                });
            }
        });
        if (!newVersion.url) {
            newVersion.url = `${newVersion.name.toLowerCase().replace(/\s+/g, '-')}`;
        }
        let tempUrl = newVersion.url;
        let count = 1;
        while (existingUrls.has(tempUrl)) {
            tempUrl = `${newVersion.url}-${count}`;
            count++;
        }
        newVersion.url = tempUrl;
        if (!existingCategory.categoryguide) {
            existingCategory.categoryguide = [];
        }
        existingCategory.categoryguide.push(newVersion);
        const updatedCategory = await existingCategory.save();
        return updatedCategory;
    }
    async getCategory(id) {
        try {
            const category = await guide_category_model_1.default.findById(id).lean();
            if (!category)
                throw new http_exception_1.default(404, messages_1.MESSAGES.CATEGORY.NOT_FOUND);
            return category;
        }
        catch (error) {
            throw new http_exception_1.default(500, messages_1.MESSAGES.GENERAL.SERVER_ERROR);
        }
    }
    async getCategoryByUrl(language, url, currentUser) {
        const categories = await guide_category_model_1.default
            .aggregate([
            { $match: { 'categoryguide.language': language, 'categoryguide.url': url.toLocaleLowerCase() } },
            { $unwind: '$categoryguide' },
            { $match: { 'categoryguide.language': language, 'categoryguide.url': url.toLocaleLowerCase() } },
            { $unwind: { path: '$categoryguide.guides', preserveNullAndEmptyArrays: true } },
            {
                $lookup: {
                    from: 'guides',
                    let: { guideId: '$categoryguide.guides' },
                    pipeline: [
                        { $unwind: '$versionsguide' },
                        { $match: { $expr: { $eq: ['$versionsguide._id', '$$guideId'] } } },
                        {
                            $project: {
                                id: '$versionsguide._id',
                                title: '$versionsguide.title',
                                url: '$versionsguide.url',
                                description: '$versionsguide.description',
                            },
                        },
                    ],
                    as: 'guidesDetails',
                },
            },
            { $unwind: { path: '$guidesDetails', preserveNullAndEmptyArrays: true } },
            {
                $group: {
                    _id: {
                        id: '$_id',
                        idCategory: '$categoryguide._id',
                        language: '$categoryguide.language',
                        url: '$categoryguide.url',
                        name: '$categoryguide.name',
                        robotsMeta: '$robotsMeta',
                    },
                    guides: {
                        $addToSet: {
                            id: '$guidesDetails.id',
                            title: '$guidesDetails.title',
                            url: '$guidesDetails.url',
                            description: '$guidesDetails.description',
                        },
                    },
                },
            },
            {
                $project: {
                    _id: '$_id.id',
                    categoryguide: {
                        language: '$_id.language',
                        idCategory: '$_id.idCategory',
                        name: '$_id.name',
                        url: '$_id.url',
                        guides: {
                            $cond: {
                                if: { $eq: ['$guides', [null]] },
                                then: [],
                                else: '$guides',
                            },
                        },
                    },
                    robotsMeta: '$_id.robotsMeta',
                },
            },
        ])
            .exec();
        if (categories?.length === 0)
            throw new http_exception_1.default(404, messages_1.MESSAGES.CATEGORY.NOT_FOUND);
        const category = categories[0];
        return {
            _id: category._id,
            categoryguide: [category.categoryguide],
            robotsMeta: category.robotsMeta || '',
        };
    }
    async getSlugBySlug(language, url) {
        const category = await guide_category_model_1.default.findOne({ 'categoryguide.language': language, 'categoryguide.url': url }).exec();
        if (!category)
            throw new http_exception_1.default(404, 'Category not found');
        const targetLanguage = language === 'en' ? 'fr' : 'en';
        const selectedVersions = category.categoryguide.filter(version => version.language === targetLanguage);
        if (selectedVersions.length === 0)
            throw new http_exception_1.default(404, messages_1.MESSAGES.CATEGORY.NOT_FOUND);
        const selectedVersion = selectedVersions[0];
        return {
            slug: selectedVersion.url,
            name: selectedVersion.name
        };
    }
    async getGuidesByCategory(language, urlCategory, queries) {
        const { title, visibility = 'Public', publishDate, sortOrder = 'desc', pageNumber = 1, pageSize = 10, createdAt } = queries;
        const category = await guide_category_model_1.default
            .findOne({
            'categoryguide.language': language,
            'categoryguide.url': urlCategory,
        })
            .lean();
        if (!category)
            throw new http_exception_1.default(404, messages_1.MESSAGES.CATEGORY.NOT_FOUND);
        const categoryVersion = category.categoryguide.find(version => version.language === language && version.url === urlCategory);
        if (!categoryVersion)
            throw new http_exception_1.default(404, messages_1.MESSAGES.CATEGORY.NOT_FOUND);
        const queryConditions = {
            'versionsguide.category': categoryVersion._id,
            'versionsguide.visibility': visibility,
        };
        if (createdAt) {
            const date = new Date(createdAt);
            queryConditions['createdAt'] = {
                $gte: new Date(date.getFullYear(), date.getMonth(), date.getDate()),
                $lte: new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59, 999),
            };
        }
        if (title) {
            queryConditions['versionsguide.title'] = RegExp(`.*${title}.*`, 'i');
        }
        if (publishDate) {
            const date = new Date(publishDate);
            queryConditions['publishDate'] = {
                $gte: new Date(date.getFullYear(), date.getMonth(), date.getDate()),
                $lte: new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59, 999),
            };
        }
        const sortCriteria = {};
        if (sortOrder) {
            sortCriteria['createdAt'] = sortOrder === 'asc' ? 1 : -1;
        }
        const skip = (pageNumber - 1) * pageSize;
        const guideQuery = guide_model_1.default.find(queryConditions).sort(sortCriteria).skip(skip).limit(pageSize);
        const guides = await guideQuery.lean();
        const filtredGuides = await Promise.all(guides.map(async (guide) => {
            const versionsguide = guide.versionsguide.filter((version) => version.language === language && version.visibility === 'Public' && (!title || RegExp(`.*${version.title}.*`, 'i')));
            const category = await (0, functions_1.getCategoryGuideUrlByIdVersion)(versionsguide[0]?.category[0]);
            return {
                ...guide,
                versionsguide,
                category,
            };
        }));
        filtredGuides.filter((guide) => guide.versionsguide.length > 0);
        const totalGuides = await guide_model_1.default.countDocuments(queryConditions);
        const totalPages = Math.ceil(totalGuides / pageSize);
        return {
            pageNumber,
            pageSize,
            totalPages,
            totalGuides,
            guides: filtredGuides,
        };
    }
}
exports.default = CategoryGuideService;
//# sourceMappingURL=guide.category.service.js.map