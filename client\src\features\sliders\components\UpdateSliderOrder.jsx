import  { useState, useEffect } from "react";
import { axiosGetJsonSSR } from "../../../config/axios";
import { toast } from "react-toastify";
import CustomButton from "@/components/ui/CustomButton";
import { useTranslation } from "react-i18next";
import { DndContext } from "@dnd-kit/core";
import {
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { useUpdateOrderSlider } from "../hooks/sliders.hooks";

function UpdateSliderOrder() {
  const { t } = useTranslation();
  const [sliderList, setSliderList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [clickedIndex, setClickedIndex] = useState(null);
  const { mutate: updateOrderSlider } = useUpdateOrderSlider();
  const handleDragEnd = (event) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = sliderList.findIndex(
        (slider) => slider._id === active.id
      );
      const newIndex = sliderList.findIndex((slider) => slider._id === over.id);

      if (oldIndex !== -1 && newIndex !== -1) {
        const newSliderList = [...sliderList];
        const [movedItem] = newSliderList.splice(oldIndex, 1);
        newSliderList.splice(newIndex, 0, movedItem);
        setSliderList(newSliderList);
      }
    }
  };


  const SliderItem = ({ slider, index }) => {
    const { attributes, listeners, setNodeRef, isDragging } = useSortable({
      id: slider._id,
    });

    const handleClick = () => {
      setClickedIndex(index);
    };

    const draggingStyle = isDragging
      ? {
        backgroundColor: "#b3c2e0"
      }
      : {};

    return (
      <div
        ref={setNodeRef}
        {...listeners}
        {...attributes}
        className={`slider-item ${clickedIndex === index ? "clicked" : ""}`}
        onClick={handleClick}
        style={draggingStyle}
      >
        <p>{slider?.versionslide[0].title}</p>
        <span>Order: {slider.order}</span>
      </div>
    );
  };

  const handleUpdateOrder = () => {
    const slidersOrder = sliderList.map((slider, index) => ({
      id: slider._id,
      order: index + 1,
    }));

    updateOrderSlider(slidersOrder, {
      onSuccess: () => {
        toast.success("Order updated successfully!");
        refetchSliders();
      },
      onError: () => {
        toast.error("Error updating sliders order");
      },
    });
  };

  const refetchSliders = async () => {
    setLoading(true);
    try {
      const response = await axiosGetJsonSSR.get("/sliders");
      setSliderList(response.data.sliders || []);
    } catch (error) {
      setError("Failed to fetch sliders");
      toast.error("Error fetching sliders");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    refetchSliders();
  }, []);

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>{error}</div>;
  }

  return (
    <>
      <div className="display-inline">
        <p className="heading-h2 semi-bold">{t("Update Order")}</p>
      </div>
      <div id="container" className="recent-application-pentabell">
        <div className="main-content">
          <header className="header"></header>
          <div
            container
            direction="column"
            spacing={1}
            id="notifications"
            className="slider-list"
          >
            <DndContext onDragMove={handleDragEnd}>
              <SortableContext
                items={sliderList}
                strategy={verticalListSortingStrategy}
              >
                {sliderList.map((slider, index) => (
                  <SliderItem key={slider._id} slider={slider} index={index} />
                ))}
              </SortableContext>
            </DndContext>
          </div>
          <div className="button-group14">
            <CustomButton
              text={t("global:edit")}
              className={"btn btn-filled "}
              onClick={handleUpdateOrder}
            />
          </div>
        </div>
      </div>
    </>
  );
}

export default UpdateSliderOrder;
