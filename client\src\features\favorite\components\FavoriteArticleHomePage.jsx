import { useEffect, useState } from "react";
import { Grid, Typography } from "@mui/material";
import { axiosGetJsonSSR } from "../../../config/axios";
import useCurrentUser from "@/features/auth/hooks/currentUser.hooks";
import { useTranslation } from "react-i18next";
import SvgArrow from "@/assets/images/icons/arrow.svg";

import {
  baseUrlFrontoffice,
  candidateRoutes,
  websiteRoutesList,
} from "../../../helpers/routesList";
import SvgBookmark from "@/assets/images/icons/bookmark.svg";

import CustomButton from "../../../components/ui/CustomButton";
import DialogModal from "@/features/user/component/updateProfile/experience/DialogModal";
import { toast } from "react-toastify";

const FavoriteArticleHomePage = () => {
  const { t } = useTranslation();
  const { user } = useCurrentUser();

  const [favouritesArticle, setFavouritesArticle] = useState([]);
  const [pageSize, setPageSize] = useState(2);
  const [loading, setLoading] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [ArticleToDelete, setArticleToDelete] = useState(false);

  const fetchFavorites = async () => {
    setLoading(true);
    try {
      const response = await axiosGetJsonSSR.get("/favourite", {
        params: { pageSize, typeOfFavourite: "article" },
      });
      setFavouritesArticle(response.data.favourites);
    } catch (err) {
      if (process.env.NODE_ENV === "dev")
        console.error("Failed to load favorites", err);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    fetchFavorites();
  }, [pageSize, user]);

  const handeleDeleteconfirmation = (id) => {
    setArticleToDelete(id);
    setOpenDialog(true);
  };
  const handlecanceldelete = () => {
    setOpenDialog(false);
  };

  const deleteArticleFromShortlist = async (id) => {
    const typeOfFavourite = "article";

    if (!id) {
      console.error("Invalid opportunity ID");
      return;
    }
    try {
      await axiosGetJsonSSR.delete(`/favourite/${id}`, {
        data: { type: typeOfFavourite },
      });
      setFavouritesArticle((prevFavourites) =>
        prevFavourites.filter((article) => article._id !== id)
      );
    } catch (error) {
      if (error.response && error.response.status === 404) {
        console.error("Article already deleted or not found");
      } else {
        console.error("Error while deleting article from favourites", error);
      }
    }
  };
  const handleToggleArticle = async () => {
    try {
      await deleteArticleFromShortlist(ArticleToDelete);
      toast.success("Article deleted successfully");
      fetchFavorites();
    } catch (error) {
      console.error("Error during article deletion", error);
    } finally {
      setOpenDialog(false);
    }
  };
  return (
    <>
      <div className="favorite-opportunities">
        <div className="header">
          <p variant="h4" className="heading-h3 semi-bold">
            {t("HomeDashboard:savearticle")}
          </p>
          <CustomButton
            link={`/${baseUrlFrontoffice.baseURL.route}/${candidateRoutes.favoris.route}`}
            className={"btn btn-ghost"}
            text={t("HomeDashboard:All")}
            icon={<SvgArrow />}
          />
        </div>
        {loading ? (
          <Typography>Loading...</Typography>
        ) : favouritesArticle?.length > 0 ? (
          <Grid container spacing={2} className="opportunity-list">
            {favouritesArticle?.map((article) => (
              <Grid
                item
                key={article._id}
                container
                alignItems="center"
                className=" opportunity-item opportunity-item-homePage"
              >
                <div className="opportunity-center">
                  <CustomButton
                    text={article.versions[0].title}
                    className={"btn p-0 job-title article-title"}
                    link={`/${websiteRoutesList.blog.route}/${article.versions[0].url}`}
                  />
                </div>
                <div className="opportunity-right">
                  <CustomButton
                    icon={<SvgBookmark />}
                    onClick={() => handeleDeleteconfirmation(article._id)}
                    className={"btn btn-ghost bookmark"}
                  />
                  <CustomButton
                    className="btn btn-homepage btn-filled"
                    text={t("HomeDashboard:ReadMore")}
                    link={`/${websiteRoutesList.blog.route}/${article.versions[0].url}`}
                  />
                </div>
              </Grid>
            ))}
          </Grid>
        ) : (
          <div className=" opportunity-item empty-state">
            <p className="empty-title-1">
              Your list of favorite articles is empty.
            </p>
            <span className="empty-title-2">
              Explore our blog for insights and resources that can help you stay
              informed and inspired!
            </span>
            <div className="empty-button">
              <CustomButton
                className="btn btn-homepage btn-filled"
                text={t("Explore")}
                link={`/${websiteRoutesList.blog.route}`}
              />
            </div>
          </div>
        )}

        <DialogModal
          open={openDialog}
          message={t("messages:supprimerarticlefavoris")}
          onClose={handlecanceldelete}
          onConfirm={handleToggleArticle}
        />
      </div>
    </>
  );
};

export default FavoriteArticleHomePage;
