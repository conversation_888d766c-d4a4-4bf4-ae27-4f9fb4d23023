"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const constants_1 = require("@/utils/helpers/constants");
const eventVersionSchema = new mongoose_1.Schema({
    language: { type: String, enum: constants_1.Language },
    title: { type: String },
    name: { type: String },
    subTitle: { type: String },
    sector: { type: String },
    countryConcerned: { type: String },
    organiser: { type: String },
    keywords: { type: [String] },
    metaTitle: { type: String, default: '' },
    metaDescription: { type: String, default: '' },
    slug: { type: String },
    shareOnSocialMedia: { type: Boolean, default: false },
    content: { type: String },
    canonical: { type: String },
    visibility: {
        type: String,
        enum: constants_1.Visibility,
        default: constants_1.Visibility.Draft,
    },
    publishDate: { type: Date },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now },
    isArchived: { type: Boolean, default: false },
});
const eventSchema = new mongoose_1.Schema({
    versions: { type: [eventVersionSchema] },
    robotsMeta: {
        type: String,
        enum: constants_1.robotsMeta,
        default: constants_1.robotsMeta.index,
    },
    country: { type: String },
    createdBy: { type: mongoose_1.Types.ObjectId, ref: 'User' },
    alt: { type: String, default: '' },
    image: { type: String },
    mobileImage: { type: String },
    imageList: { type: String },
    eventDate: { type: String },
}, {
    timestamps: true,
    toJSON: {
        transform: function (doc, ret) {
            delete ret.__v;
        },
    },
});
eventSchema.index({
    'versions.title': 'text',
    url: 'text',
}, {
    weights: {
        'versions.title': 50,
        url: 30,
    },
    name: 'EventTextIndex',
});
exports.default = (0, mongoose_1.model)('Event', eventSchema);
//# sourceMappingURL=event.model.js.map