"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/layout",{

/***/ "(app-pages-browser)/./src/components/ui/DropdownMenu.jsx":
/*!********************************************!*\
  !*** ./src/components/ui/DropdownMenu.jsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Menu/Menu.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Fade/Fade.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _assets_images_icons_arrowDown_svg__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../assets/images/icons/arrowDown.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowDown.svg\");\n/* harmony import */ var _assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../assets/images/icons/arrowUp.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowUp.svg\");\n/* harmony import */ var _utils_functions__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/functions */ \"(app-pages-browser)/./src/utils/functions.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst DropdownMenu = (param)=>{\n    let { buttonLabel, buttonHref, menuItems, subMenu, locale, withFlag, selectedFlag } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const open = Boolean(anchorEl);\n    const isDetailBlogPath = ()=>pathname.includes(\"/blog/\") && !pathname.includes(\"/blog/category/\") && pathname !== \"/blog/\" && pathname !== \"/fr/blog/\";\n    const handleClick = (event)=>{\n        setAnchorEl(event.currentTarget);\n    };\n    const handleClose = ()=>{\n        setAnchorEl(null);\n    };\n    const handleClick2 = (event, item)=>{\n        event.stopPropagation();\n        if (item.onClick) {\n            item.onClick();\n        } else if (item.subItems == undefined) handleClose();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: subMenu ? `sub-dropdown-menu` : `dropdown-menu`,\n        onMouseEnter: (e)=>setAnchorEl(e.currentTarget),\n        onMouseLeave: handleClose,\n        style: {\n            display: \"inline-block\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: pathname.includes(buttonHref) && buttonHref !== \"\" ? \"navbar-link dropdown-toggle active\" : isDetailBlogPath() ? \"navbar-link dropdown-toggle whiteBg\" : \"navbar-link dropdown-toggle\",\n                id: \"fade-button\",\n                \"aria-controls\": open ? \"fade-menu\" : undefined,\n                \"aria-haspopup\": \"true\",\n                \"aria-expanded\": open ? \"true\" : undefined,\n                onClick: handleClick,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: pathname.includes(buttonHref) && buttonHref !== \"\" ? \"dropdown-toggle-link active\" : isDetailBlogPath() ? \"dropdown-toggle-link whiteBg\" : \"dropdown-toggle-link\",\n                        href: buttonLabel?.toLowerCase() === \"resources\" || buttonLabel?.toLowerCase() === \"ressources\" ? \"#\" : buttonHref ? (0,_utils_functions__WEBPACK_IMPORTED_MODULE_6__.generateLocalizedSlug)(locale, buttonHref).replace(\"/en/\", \"/\") : pathname.replace(\"/en/\", \"/\"),\n                        locale: locale === \"en\" ? \"en\" : \"fr\",\n                        onClick: (e)=>{\n                            if (buttonLabel?.toLowerCase() === \"resources\" || buttonLabel?.toLowerCase() === \"ressources\") {\n                                e.preventDefault();\n                                e.stopPropagation();\n                            }\n                        },\n                        sx: {\n                            ...buttonLabel?.toLowerCase() === \"resources\" || buttonLabel?.toLowerCase() === \"ressources\" ? {\n                                cursor: \"default\",\n                                textDecoration: \"none\",\n                                \"&:hover\": {\n                                    textDecoration: \"none\"\n                                }\n                            } : {}\n                        },\n                        children: withFlag ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            className: \"flag-lang\",\n                            src: selectedFlag.src,\n                            width: 26,\n                            height: 22,\n                            disabled: true,\n                            loading: \"lazy\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 107,\n                            columnNumber: 13\n                        }, undefined) : buttonLabel\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, undefined),\n                    open ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowDown_svg__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 119,\n                        columnNumber: 17\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 119,\n                        columnNumber: 36\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                id: \"fade-menu\",\n                MenuListProps: {\n                    \"aria-labelledby\": \"fade-button\"\n                },\n                anchorEl: anchorEl,\n                open: open,\n                onClose: handleClose,\n                TransitionComponent: _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                disableScrollLock: true,\n                anchorOrigin: {\n                    vertical: \"bottom\",\n                    horizontal: subMenu ? \"right\" : \"left\"\n                },\n                transformOrigin: {\n                    vertical: \"top\",\n                    horizontal: subMenu ? \"left\" : \"left\"\n                },\n                children: menuItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: item.subItems ? `sub-menu dropdown-item` : `dropdown-item`,\n                        onClick: (e)=>handleClick2(e, item),\n                        sx: {\n                            // Remove default hover background for items with submenus\n                            ...item.subItems && {\n                                \"&:hover\": {\n                                    backgroundColor: \"transparent !important\"\n                                },\n                                \"&.Mui-focusVisible\": {\n                                    backgroundColor: \"transparent !important\"\n                                },\n                                \"&.Mui-selected\": {\n                                    backgroundColor: \"transparent !important\"\n                                }\n                            }\n                        },\n                        children: item.subItems ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DropdownMenu, {\n                            buttonLabel: item?.i18nName ? t(item?.i18nName) : item.name,\n                            buttonHref: item.route,\n                            menuItems: item.subItems,\n                            subMenu: true,\n                            locale: locale\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 164,\n                            columnNumber: 15\n                        }, undefined) : item.route ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            href: (0,_utils_functions__WEBPACK_IMPORTED_MODULE_6__.generateLocalizedSlug)(locale, item.route),\n                            locale: locale === \"en\" ? \"en\" : \"fr\",\n                            className: pathname.includes(item.route) ? \"dropdown-item-link active\" : \"dropdown-item-link\",\n                            children: [\n                                item.icon ?? item.icon,\n                                \" \",\n                                item?.i18nName ? t(item?.i18nName) : item.name\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 172,\n                            columnNumber: 15\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"dropdown-item-link\",\n                            href: \"#\",\n                            children: withFlag ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                className: \"flag-lang\",\n                                src: item.flag.src,\n                                width: 26,\n                                height: 22,\n                                loading: \"lazy\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                                lineNumber: 187,\n                                columnNumber: 19\n                            }, undefined) : item?.i18nName ? t(item?.i18nName) : item.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 185,\n                            columnNumber: 15\n                        }, undefined)\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 142,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DropdownMenu, \"kmMXD6S/WNK2NQEz2iCMSS4bykM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        react_i18next__WEBPACK_IMPORTED_MODULE_3__.useTranslation\n    ];\n});\n_c = DropdownMenu;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DropdownMenu);\nvar _c;\n$RefreshReg$(_c, \"DropdownMenu\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL0Ryb3Bkb3duTWVudS5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQWlDO0FBQzRCO0FBQ2Y7QUFDQztBQUVvQjtBQUNKO0FBQ0w7QUFFMUQsTUFBTVUsZUFBZTtRQUFDLEVBQ3BCQyxXQUFXLEVBQ1hDLFVBQVUsRUFDVkMsU0FBUyxFQUNUQyxPQUFPLEVBQ1BDLE1BQU0sRUFDTkMsUUFBUSxFQUNSQyxZQUFZLEVBQ2I7O0lBQ0MsTUFBTUMsV0FBV2IsNERBQVdBO0lBQzVCLE1BQU0sRUFBRWMsQ0FBQyxFQUFFLEdBQUdiLDZEQUFjQTtJQUM1QixNQUFNLENBQUNjLFVBQVVDLFlBQVksR0FBR3JCLCtDQUFRQSxDQUFDO0lBRXpDLE1BQU1zQixPQUFPQyxRQUFRSDtJQUVyQixNQUFNSSxtQkFBbUIsSUFDdkJOLFNBQVNPLFFBQVEsQ0FBQyxhQUNsQixDQUFDUCxTQUFTTyxRQUFRLENBQUMsc0JBQ25CUCxhQUFhLFlBQ2JBLGFBQWE7SUFFZixNQUFNUSxjQUFjLENBQUNDO1FBQ25CTixZQUFZTSxNQUFNQyxhQUFhO0lBQ2pDO0lBRUEsTUFBTUMsY0FBYztRQUNsQlIsWUFBWTtJQUNkO0lBRUEsTUFBTVMsZUFBZSxDQUFDSCxPQUFPSTtRQUMzQkosTUFBTUssZUFBZTtRQUNyQixJQUFJRCxLQUFLRSxPQUFPLEVBQUU7WUFDaEJGLEtBQUtFLE9BQU87UUFDZCxPQUFPLElBQUlGLEtBQUtHLFFBQVEsSUFBSUMsV0FBV047SUFDekM7SUFFQSxxQkFDRSw4REFBQ087UUFDQ0MsV0FBV3ZCLFVBQVUsQ0FBQyxpQkFBaUIsQ0FBQyxHQUFHLENBQUMsYUFBYSxDQUFDO1FBQzFEd0IsY0FBYyxDQUFDQyxJQUFNbEIsWUFBWWtCLEVBQUVYLGFBQWE7UUFDaERZLGNBQWNYO1FBQ2RZLE9BQU87WUFBRUMsU0FBUztRQUFlOzswQkFFakMsOERBQUN6QyxxR0FBTUE7Z0JBQ0xvQyxXQUNFbkIsU0FBU08sUUFBUSxDQUFDYixlQUFlQSxlQUFlLEtBQzVDLHVDQUNBWSxxQkFDQSx3Q0FDQTtnQkFFTm1CLElBQUc7Z0JBQ0hDLGlCQUFldEIsT0FBTyxjQUFjYTtnQkFDcENVLGlCQUFjO2dCQUNkQyxpQkFBZXhCLE9BQU8sU0FBU2E7Z0JBQy9CRixTQUFTUDs7a0NBRVQsOERBQUN6QixxR0FBTUE7d0JBQ0xvQyxXQUNFbkIsU0FBU08sUUFBUSxDQUFDYixlQUFlQSxlQUFlLEtBQzVDLGdDQUNBWSxxQkFDQSxpQ0FDQTt3QkFFTnVCLE1BQ0VwQyxhQUFhcUMsa0JBQWtCLGVBQy9CckMsYUFBYXFDLGtCQUFrQixlQUMzQixNQUNBcEMsYUFDQUgsdUVBQXFCQSxDQUFDTSxRQUFRSCxZQUFZcUMsT0FBTyxDQUFDLFFBQVEsT0FDMUQvQixTQUFTK0IsT0FBTyxDQUFDLFFBQVE7d0JBRS9CbEMsUUFBUUEsV0FBVyxPQUFPLE9BQU87d0JBQ2pDa0IsU0FBUyxDQUFDTTs0QkFDUixJQUNFNUIsYUFBYXFDLGtCQUFrQixlQUMvQnJDLGFBQWFxQyxrQkFBa0IsY0FDL0I7Z0NBQ0FULEVBQUVXLGNBQWM7Z0NBQ2hCWCxFQUFFUCxlQUFlOzRCQUNuQjt3QkFDRjt3QkFDQW1CLElBQUk7NEJBQ0YsR0FBSXhDLGFBQWFxQyxrQkFBa0IsZUFDbkNyQyxhQUFhcUMsa0JBQWtCLGVBQzNCO2dDQUNFSSxRQUFRO2dDQUNSQyxnQkFBZ0I7Z0NBQ2hCLFdBQVc7b0NBQ1RBLGdCQUFnQjtnQ0FDbEI7NEJBQ0YsSUFDQSxDQUFDLENBQUM7d0JBQ1I7a0NBRUNyQyx5QkFDQyw4REFBQ3NDOzRCQUNDakIsV0FBVTs0QkFDVmtCLEtBQUt0QyxhQUFhc0MsR0FBRzs0QkFDckJDLE9BQU87NEJBQ1BDLFFBQVE7NEJBQ1JDLFFBQVE7NEJBQ1JDLFNBQVE7Ozs7O3dDQUdWaEQ7Ozs7OztvQkFHSFcscUJBQU8sOERBQUNmLDBFQUFZQTs7OztrREFBTSw4REFBQ0Msd0VBQVVBOzs7Ozs7Ozs7OzswQkFHeEMsOERBQUNOLHFHQUFJQTtnQkFDSHlDLElBQUc7Z0JBQ0hpQixlQUFlO29CQUNiLG1CQUFtQjtnQkFDckI7Z0JBQ0F4QyxVQUFVQTtnQkFDVkUsTUFBTUE7Z0JBQ051QyxTQUFTaEM7Z0JBQ1RpQyxxQkFBcUIxRCxxR0FBSUE7Z0JBQ3pCMkQsbUJBQW1CO2dCQUNuQkMsY0FBYztvQkFDWkMsVUFBVTtvQkFDVkMsWUFBWXBELFVBQVUsVUFBVTtnQkFDbEM7Z0JBQ0FxRCxpQkFBaUI7b0JBQ2ZGLFVBQVU7b0JBQ1ZDLFlBQVlwRCxVQUFVLFNBQVM7Z0JBQ2pDOzBCQUVDRCxVQUFVdUQsR0FBRyxDQUFDLENBQUNyQyxNQUFNc0Msc0JBQ3BCLDhEQUFDbEUsc0dBQVFBO3dCQUNQa0MsV0FDRU4sS0FBS0csUUFBUSxHQUFHLENBQUMsc0JBQXNCLENBQUMsR0FBRyxDQUFDLGFBQWEsQ0FBQzt3QkFHNURELFNBQVMsQ0FBQ00sSUFBTVQsYUFBYVMsR0FBR1I7d0JBQ2hDb0IsSUFBSTs0QkFDRiwwREFBMEQ7NEJBQzFELEdBQUlwQixLQUFLRyxRQUFRLElBQUk7Z0NBQ25CLFdBQVc7b0NBQ1RvQyxpQkFBaUI7Z0NBQ25CO2dDQUNBLHNCQUFzQjtvQ0FDcEJBLGlCQUFpQjtnQ0FDbkI7Z0NBQ0Esa0JBQWtCO29DQUNoQkEsaUJBQWlCO2dDQUNuQjs0QkFDRixDQUFDO3dCQUNIO2tDQUVDdkMsS0FBS0csUUFBUSxpQkFDWiw4REFBQ3hCOzRCQUNDQyxhQUFhb0IsTUFBTXdDLFdBQVdwRCxFQUFFWSxNQUFNd0MsWUFBWXhDLEtBQUt5QyxJQUFJOzRCQUMzRDVELFlBQVltQixLQUFLMEMsS0FBSzs0QkFDdEI1RCxXQUFXa0IsS0FBS0csUUFBUTs0QkFDeEJwQixTQUFTOzRCQUNUQyxRQUFRQTs7Ozs7d0NBRVJnQixLQUFLMEMsS0FBSyxpQkFDWiw4REFBQ3hFLHFHQUFNQTs0QkFDTDhDLE1BQU10Qyx1RUFBcUJBLENBQUNNLFFBQVFnQixLQUFLMEMsS0FBSzs0QkFDOUMxRCxRQUFRQSxXQUFXLE9BQU8sT0FBTzs0QkFDakNzQixXQUNFbkIsU0FBU08sUUFBUSxDQUFDTSxLQUFLMEMsS0FBSyxJQUN4Qiw4QkFDQTs7Z0NBR0wxQyxLQUFLMkMsSUFBSSxJQUFJM0MsS0FBSzJDLElBQUk7Z0NBQUU7Z0NBQ3hCM0MsTUFBTXdDLFdBQVdwRCxFQUFFWSxNQUFNd0MsWUFBWXhDLEtBQUt5QyxJQUFJOzs7Ozs7c0RBR2pELDhEQUFDdkUscUdBQU1BOzRCQUFDb0MsV0FBVTs0QkFBcUJVLE1BQUs7c0NBQ3pDL0IseUJBQ0MsOERBQUNzQztnQ0FDQ2pCLFdBQVU7Z0NBQ1ZrQixLQUFLeEIsS0FBSzRDLElBQUksQ0FBQ3BCLEdBQUc7Z0NBQ2xCQyxPQUFPO2dDQUNQQyxRQUFRO2dDQUNSRSxTQUFROzs7Ozs0Q0FFUjVCLE1BQU13QyxXQUNScEQsRUFBRVksTUFBTXdDLFlBRVJ4QyxLQUFLeUMsSUFBSTs7Ozs7O3VCQW5EVkg7Ozs7Ozs7Ozs7Ozs7Ozs7QUE0RGpCO0dBcE1NM0Q7O1FBU2FMLHdEQUFXQTtRQUNkQyx5REFBY0E7OztLQVZ4Qkk7QUFzTU4sK0RBQWVBLFlBQVlBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvdWkvRHJvcGRvd25NZW51LmpzeD84NGQxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCB7IEJ1dHRvbiwgTWVudSwgTWVudUl0ZW0sIEZhZGUgfSBmcm9tIFwiQG11aS9tYXRlcmlhbFwiO1xyXG5pbXBvcnQgeyB1c2VQYXRobmFtZSB9IGZyb20gXCJuZXh0L25hdmlnYXRpb25cIjtcclxuaW1wb3J0IHsgdXNlVHJhbnNsYXRpb24gfSBmcm9tIFwicmVhY3QtaTE4bmV4dFwiO1xyXG5cclxuaW1wb3J0IFN2Z0Fycm93RG93biBmcm9tIFwiLi4vLi4vYXNzZXRzL2ltYWdlcy9pY29ucy9hcnJvd0Rvd24uc3ZnXCI7XHJcbmltcG9ydCBTdmdBcnJvd1VwIGZyb20gXCIuLi8uLi9hc3NldHMvaW1hZ2VzL2ljb25zL2Fycm93VXAuc3ZnXCI7XHJcbmltcG9ydCB7IGdlbmVyYXRlTG9jYWxpemVkU2x1ZyB9IGZyb20gXCJAL3V0aWxzL2Z1bmN0aW9uc1wiO1xyXG5cclxuY29uc3QgRHJvcGRvd25NZW51ID0gKHtcclxuICBidXR0b25MYWJlbCxcclxuICBidXR0b25IcmVmLFxyXG4gIG1lbnVJdGVtcyxcclxuICBzdWJNZW51LFxyXG4gIGxvY2FsZSxcclxuICB3aXRoRmxhZyxcclxuICBzZWxlY3RlZEZsYWcsXHJcbn0pID0+IHtcclxuICBjb25zdCBwYXRobmFtZSA9IHVzZVBhdGhuYW1lKCk7XHJcbiAgY29uc3QgeyB0IH0gPSB1c2VUcmFuc2xhdGlvbigpO1xyXG4gIGNvbnN0IFthbmNob3JFbCwgc2V0QW5jaG9yRWxdID0gdXNlU3RhdGUobnVsbCk7XHJcblxyXG4gIGNvbnN0IG9wZW4gPSBCb29sZWFuKGFuY2hvckVsKTtcclxuXHJcbiAgY29uc3QgaXNEZXRhaWxCbG9nUGF0aCA9ICgpID0+XHJcbiAgICBwYXRobmFtZS5pbmNsdWRlcyhcIi9ibG9nL1wiKSAmJlxyXG4gICAgIXBhdGhuYW1lLmluY2x1ZGVzKFwiL2Jsb2cvY2F0ZWdvcnkvXCIpICYmXHJcbiAgICBwYXRobmFtZSAhPT0gXCIvYmxvZy9cIiAmJlxyXG4gICAgcGF0aG5hbWUgIT09IFwiL2ZyL2Jsb2cvXCI7XHJcblxyXG4gIGNvbnN0IGhhbmRsZUNsaWNrID0gKGV2ZW50KSA9PiB7XHJcbiAgICBzZXRBbmNob3JFbChldmVudC5jdXJyZW50VGFyZ2V0KTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVDbG9zZSA9ICgpID0+IHtcclxuICAgIHNldEFuY2hvckVsKG51bGwpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZUNsaWNrMiA9IChldmVudCwgaXRlbSkgPT4ge1xyXG4gICAgZXZlbnQuc3RvcFByb3BhZ2F0aW9uKCk7XHJcbiAgICBpZiAoaXRlbS5vbkNsaWNrKSB7XHJcbiAgICAgIGl0ZW0ub25DbGljaygpO1xyXG4gICAgfSBlbHNlIGlmIChpdGVtLnN1Ykl0ZW1zID09IHVuZGVmaW5lZCkgaGFuZGxlQ2xvc2UoKTtcclxuICB9O1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdlxyXG4gICAgICBjbGFzc05hbWU9e3N1Yk1lbnUgPyBgc3ViLWRyb3Bkb3duLW1lbnVgIDogYGRyb3Bkb3duLW1lbnVgfVxyXG4gICAgICBvbk1vdXNlRW50ZXI9eyhlKSA9PiBzZXRBbmNob3JFbChlLmN1cnJlbnRUYXJnZXQpfVxyXG4gICAgICBvbk1vdXNlTGVhdmU9e2hhbmRsZUNsb3NlfVxyXG4gICAgICBzdHlsZT17eyBkaXNwbGF5OiBcImlubGluZS1ibG9ja1wiIH19IC8vIFJlcXVpcmVkIHRvIGtlZXAgbGF5b3V0IGludGFjdFxyXG4gICAgPlxyXG4gICAgICA8QnV0dG9uXHJcbiAgICAgICAgY2xhc3NOYW1lPXtcclxuICAgICAgICAgIHBhdGhuYW1lLmluY2x1ZGVzKGJ1dHRvbkhyZWYpICYmIGJ1dHRvbkhyZWYgIT09IFwiXCJcclxuICAgICAgICAgICAgPyBcIm5hdmJhci1saW5rIGRyb3Bkb3duLXRvZ2dsZSBhY3RpdmVcIlxyXG4gICAgICAgICAgICA6IGlzRGV0YWlsQmxvZ1BhdGgoKVxyXG4gICAgICAgICAgICA/IFwibmF2YmFyLWxpbmsgZHJvcGRvd24tdG9nZ2xlIHdoaXRlQmdcIlxyXG4gICAgICAgICAgICA6IFwibmF2YmFyLWxpbmsgZHJvcGRvd24tdG9nZ2xlXCJcclxuICAgICAgICB9XHJcbiAgICAgICAgaWQ9XCJmYWRlLWJ1dHRvblwiXHJcbiAgICAgICAgYXJpYS1jb250cm9scz17b3BlbiA/IFwiZmFkZS1tZW51XCIgOiB1bmRlZmluZWR9XHJcbiAgICAgICAgYXJpYS1oYXNwb3B1cD1cInRydWVcIlxyXG4gICAgICAgIGFyaWEtZXhwYW5kZWQ9e29wZW4gPyBcInRydWVcIiA6IHVuZGVmaW5lZH1cclxuICAgICAgICBvbkNsaWNrPXtoYW5kbGVDbGlja31cclxuICAgICAgPlxyXG4gICAgICAgIDxCdXR0b25cclxuICAgICAgICAgIGNsYXNzTmFtZT17XHJcbiAgICAgICAgICAgIHBhdGhuYW1lLmluY2x1ZGVzKGJ1dHRvbkhyZWYpICYmIGJ1dHRvbkhyZWYgIT09IFwiXCJcclxuICAgICAgICAgICAgICA/IFwiZHJvcGRvd24tdG9nZ2xlLWxpbmsgYWN0aXZlXCJcclxuICAgICAgICAgICAgICA6IGlzRGV0YWlsQmxvZ1BhdGgoKVxyXG4gICAgICAgICAgICAgID8gXCJkcm9wZG93bi10b2dnbGUtbGluayB3aGl0ZUJnXCJcclxuICAgICAgICAgICAgICA6IFwiZHJvcGRvd24tdG9nZ2xlLWxpbmtcIlxyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgaHJlZj17XHJcbiAgICAgICAgICAgIGJ1dHRvbkxhYmVsPy50b0xvd2VyQ2FzZSgpID09PSBcInJlc291cmNlc1wiIHx8XHJcbiAgICAgICAgICAgIGJ1dHRvbkxhYmVsPy50b0xvd2VyQ2FzZSgpID09PSBcInJlc3NvdXJjZXNcIlxyXG4gICAgICAgICAgICAgID8gXCIjXCJcclxuICAgICAgICAgICAgICA6IGJ1dHRvbkhyZWZcclxuICAgICAgICAgICAgICA/IGdlbmVyYXRlTG9jYWxpemVkU2x1Zyhsb2NhbGUsIGJ1dHRvbkhyZWYpLnJlcGxhY2UoXCIvZW4vXCIsIFwiL1wiKVxyXG4gICAgICAgICAgICAgIDogcGF0aG5hbWUucmVwbGFjZShcIi9lbi9cIiwgXCIvXCIpXHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgICBsb2NhbGU9e2xvY2FsZSA9PT0gXCJlblwiID8gXCJlblwiIDogXCJmclwifVxyXG4gICAgICAgICAgb25DbGljaz17KGUpID0+IHtcclxuICAgICAgICAgICAgaWYgKFxyXG4gICAgICAgICAgICAgIGJ1dHRvbkxhYmVsPy50b0xvd2VyQ2FzZSgpID09PSBcInJlc291cmNlc1wiIHx8XHJcbiAgICAgICAgICAgICAgYnV0dG9uTGFiZWw/LnRvTG93ZXJDYXNlKCkgPT09IFwicmVzc291cmNlc1wiXHJcbiAgICAgICAgICAgICkge1xyXG4gICAgICAgICAgICAgIGUucHJldmVudERlZmF1bHQoKTtcclxuICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9fVxyXG4gICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgLi4uKGJ1dHRvbkxhYmVsPy50b0xvd2VyQ2FzZSgpID09PSBcInJlc291cmNlc1wiIHx8XHJcbiAgICAgICAgICAgIGJ1dHRvbkxhYmVsPy50b0xvd2VyQ2FzZSgpID09PSBcInJlc3NvdXJjZXNcIlxyXG4gICAgICAgICAgICAgID8ge1xyXG4gICAgICAgICAgICAgICAgICBjdXJzb3I6IFwiZGVmYXVsdFwiLFxyXG4gICAgICAgICAgICAgICAgICB0ZXh0RGVjb3JhdGlvbjogXCJub25lXCIsXHJcbiAgICAgICAgICAgICAgICAgIFwiJjpob3ZlclwiOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgdGV4dERlY29yYXRpb246IFwibm9uZVwiLFxyXG4gICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgIDoge30pLFxyXG4gICAgICAgICAgfX1cclxuICAgICAgICA+XHJcbiAgICAgICAgICB7d2l0aEZsYWcgPyAoXHJcbiAgICAgICAgICAgIDxpbWdcclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGFnLWxhbmdcIlxyXG4gICAgICAgICAgICAgIHNyYz17c2VsZWN0ZWRGbGFnLnNyY31cclxuICAgICAgICAgICAgICB3aWR0aD17MjZ9XHJcbiAgICAgICAgICAgICAgaGVpZ2h0PXsyMn1cclxuICAgICAgICAgICAgICBkaXNhYmxlZFxyXG4gICAgICAgICAgICAgIGxvYWRpbmc9XCJsYXp5XCJcclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgIGJ1dHRvbkxhYmVsXHJcbiAgICAgICAgICApfVxyXG4gICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgIHtvcGVuID8gPFN2Z0Fycm93RG93biAvPiA6IDxTdmdBcnJvd1VwIC8+fVxyXG4gICAgICA8L0J1dHRvbj5cclxuXHJcbiAgICAgIDxNZW51XHJcbiAgICAgICAgaWQ9XCJmYWRlLW1lbnVcIlxyXG4gICAgICAgIE1lbnVMaXN0UHJvcHM9e3tcclxuICAgICAgICAgIFwiYXJpYS1sYWJlbGxlZGJ5XCI6IFwiZmFkZS1idXR0b25cIixcclxuICAgICAgICB9fVxyXG4gICAgICAgIGFuY2hvckVsPXthbmNob3JFbH1cclxuICAgICAgICBvcGVuPXtvcGVufVxyXG4gICAgICAgIG9uQ2xvc2U9e2hhbmRsZUNsb3NlfVxyXG4gICAgICAgIFRyYW5zaXRpb25Db21wb25lbnQ9e0ZhZGV9XHJcbiAgICAgICAgZGlzYWJsZVNjcm9sbExvY2s9e3RydWV9XHJcbiAgICAgICAgYW5jaG9yT3JpZ2luPXt7XHJcbiAgICAgICAgICB2ZXJ0aWNhbDogXCJib3R0b21cIixcclxuICAgICAgICAgIGhvcml6b250YWw6IHN1Yk1lbnUgPyBcInJpZ2h0XCIgOiBcImxlZnRcIixcclxuICAgICAgICB9fVxyXG4gICAgICAgIHRyYW5zZm9ybU9yaWdpbj17e1xyXG4gICAgICAgICAgdmVydGljYWw6IFwidG9wXCIsXHJcbiAgICAgICAgICBob3Jpem9udGFsOiBzdWJNZW51ID8gXCJsZWZ0XCIgOiBcImxlZnRcIixcclxuICAgICAgICB9fVxyXG4gICAgICA+XHJcbiAgICAgICAge21lbnVJdGVtcy5tYXAoKGl0ZW0sIGluZGV4KSA9PiAoXHJcbiAgICAgICAgICA8TWVudUl0ZW1cclxuICAgICAgICAgICAgY2xhc3NOYW1lPXtcclxuICAgICAgICAgICAgICBpdGVtLnN1Ykl0ZW1zID8gYHN1Yi1tZW51IGRyb3Bkb3duLWl0ZW1gIDogYGRyb3Bkb3duLWl0ZW1gXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAga2V5PXtpbmRleH1cclxuICAgICAgICAgICAgb25DbGljaz17KGUpID0+IGhhbmRsZUNsaWNrMihlLCBpdGVtKX1cclxuICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAvLyBSZW1vdmUgZGVmYXVsdCBob3ZlciBiYWNrZ3JvdW5kIGZvciBpdGVtcyB3aXRoIHN1Ym1lbnVzXHJcbiAgICAgICAgICAgICAgLi4uKGl0ZW0uc3ViSXRlbXMgJiYge1xyXG4gICAgICAgICAgICAgICAgXCImOmhvdmVyXCI6IHtcclxuICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBcInRyYW5zcGFyZW50ICFpbXBvcnRhbnRcIixcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICBcIiYuTXVpLWZvY3VzVmlzaWJsZVwiOiB7XHJcbiAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogXCJ0cmFuc3BhcmVudCAhaW1wb3J0YW50XCIsXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgXCImLk11aS1zZWxlY3RlZFwiOiB7XHJcbiAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogXCJ0cmFuc3BhcmVudCAhaW1wb3J0YW50XCIsXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgIH0pLFxyXG4gICAgICAgICAgICB9fVxyXG4gICAgICAgICAgPlxyXG4gICAgICAgICAgICB7aXRlbS5zdWJJdGVtcyA/IChcclxuICAgICAgICAgICAgICA8RHJvcGRvd25NZW51XHJcbiAgICAgICAgICAgICAgICBidXR0b25MYWJlbD17aXRlbT8uaTE4bk5hbWUgPyB0KGl0ZW0/LmkxOG5OYW1lKSA6IGl0ZW0ubmFtZX1cclxuICAgICAgICAgICAgICAgIGJ1dHRvbkhyZWY9e2l0ZW0ucm91dGV9XHJcbiAgICAgICAgICAgICAgICBtZW51SXRlbXM9e2l0ZW0uc3ViSXRlbXN9XHJcbiAgICAgICAgICAgICAgICBzdWJNZW51PXt0cnVlfVxyXG4gICAgICAgICAgICAgICAgbG9jYWxlPXtsb2NhbGV9XHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgKSA6IGl0ZW0ucm91dGUgPyAoXHJcbiAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgaHJlZj17Z2VuZXJhdGVMb2NhbGl6ZWRTbHVnKGxvY2FsZSwgaXRlbS5yb3V0ZSl9XHJcbiAgICAgICAgICAgICAgICBsb2NhbGU9e2xvY2FsZSA9PT0gXCJlblwiID8gXCJlblwiIDogXCJmclwifVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtcclxuICAgICAgICAgICAgICAgICAgcGF0aG5hbWUuaW5jbHVkZXMoaXRlbS5yb3V0ZSlcclxuICAgICAgICAgICAgICAgICAgICA/IFwiZHJvcGRvd24taXRlbS1saW5rIGFjdGl2ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgOiBcImRyb3Bkb3duLWl0ZW0tbGlua1wiXHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAge2l0ZW0uaWNvbiA/PyBpdGVtLmljb259e1wiIFwifVxyXG4gICAgICAgICAgICAgICAge2l0ZW0/LmkxOG5OYW1lID8gdChpdGVtPy5pMThuTmFtZSkgOiBpdGVtLm5hbWV9XHJcbiAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgPEJ1dHRvbiBjbGFzc05hbWU9XCJkcm9wZG93bi1pdGVtLWxpbmtcIiBocmVmPVwiI1wiPlxyXG4gICAgICAgICAgICAgICAge3dpdGhGbGFnID8gKFxyXG4gICAgICAgICAgICAgICAgICA8aW1nXHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxhZy1sYW5nXCJcclxuICAgICAgICAgICAgICAgICAgICBzcmM9e2l0ZW0uZmxhZy5zcmN9XHJcbiAgICAgICAgICAgICAgICAgICAgd2lkdGg9ezI2fVxyXG4gICAgICAgICAgICAgICAgICAgIGhlaWdodD17MjJ9XHJcbiAgICAgICAgICAgICAgICAgICAgbG9hZGluZz1cImxhenlcIlxyXG4gICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgKSA6IGl0ZW0/LmkxOG5OYW1lID8gKFxyXG4gICAgICAgICAgICAgICAgICB0KGl0ZW0/LmkxOG5OYW1lKVxyXG4gICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgaXRlbS5uYW1lXHJcbiAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgICAgPC9NZW51SXRlbT5cclxuICAgICAgICApKX1cclxuICAgICAgPC9NZW51PlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IERyb3Bkb3duTWVudTtcclxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwiQnV0dG9uIiwiTWVudSIsIk1lbnVJdGVtIiwiRmFkZSIsInVzZVBhdGhuYW1lIiwidXNlVHJhbnNsYXRpb24iLCJTdmdBcnJvd0Rvd24iLCJTdmdBcnJvd1VwIiwiZ2VuZXJhdGVMb2NhbGl6ZWRTbHVnIiwiRHJvcGRvd25NZW51IiwiYnV0dG9uTGFiZWwiLCJidXR0b25IcmVmIiwibWVudUl0ZW1zIiwic3ViTWVudSIsImxvY2FsZSIsIndpdGhGbGFnIiwic2VsZWN0ZWRGbGFnIiwicGF0aG5hbWUiLCJ0IiwiYW5jaG9yRWwiLCJzZXRBbmNob3JFbCIsIm9wZW4iLCJCb29sZWFuIiwiaXNEZXRhaWxCbG9nUGF0aCIsImluY2x1ZGVzIiwiaGFuZGxlQ2xpY2siLCJldmVudCIsImN1cnJlbnRUYXJnZXQiLCJoYW5kbGVDbG9zZSIsImhhbmRsZUNsaWNrMiIsIml0ZW0iLCJzdG9wUHJvcGFnYXRpb24iLCJvbkNsaWNrIiwic3ViSXRlbXMiLCJ1bmRlZmluZWQiLCJkaXYiLCJjbGFzc05hbWUiLCJvbk1vdXNlRW50ZXIiLCJlIiwib25Nb3VzZUxlYXZlIiwic3R5bGUiLCJkaXNwbGF5IiwiaWQiLCJhcmlhLWNvbnRyb2xzIiwiYXJpYS1oYXNwb3B1cCIsImFyaWEtZXhwYW5kZWQiLCJocmVmIiwidG9Mb3dlckNhc2UiLCJyZXBsYWNlIiwicHJldmVudERlZmF1bHQiLCJzeCIsImN1cnNvciIsInRleHREZWNvcmF0aW9uIiwiaW1nIiwic3JjIiwid2lkdGgiLCJoZWlnaHQiLCJkaXNhYmxlZCIsImxvYWRpbmciLCJNZW51TGlzdFByb3BzIiwib25DbG9zZSIsIlRyYW5zaXRpb25Db21wb25lbnQiLCJkaXNhYmxlU2Nyb2xsTG9jayIsImFuY2hvck9yaWdpbiIsInZlcnRpY2FsIiwiaG9yaXpvbnRhbCIsInRyYW5zZm9ybU9yaWdpbiIsIm1hcCIsImluZGV4IiwiYmFja2dyb3VuZENvbG9yIiwiaTE4bk5hbWUiLCJuYW1lIiwicm91dGUiLCJpY29uIiwiZmxhZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/DropdownMenu.jsx\n"));

/***/ })

});