import { useState } from "react";
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Grid,
  Container,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import SvgexpandIcon from "@/assets/images/icons/arrowUp.svg";
import LeaveEntitlementsContent from "./LeaveEntitlementsContent";
import PayrollContent from "./PayrollContent";


const LaborLaws = ({ data }) => {
  const { t } = useTranslation();
  const [expanded, setExpanded] = useState(data.sections[0]?.id || false);
  const handleChange = (panel) => (_, isExpanded) => {
    setExpanded(isExpanded ? panel : false);
  };

  return (
    <div id="labor-tn-laws" className="custom-max-width">
      <Container  >
        <h2 className="heading-h1">{t(data.title)}</h2>

        <Grid container columnSpacing={3} rowSpacing={2} className="container">
          {data.sections.map((section) => {
            const { id, title, type, subsections, data: sectionData } = section;

            return (
              <Grid item xs={12} sm={12} key={id}>
                <Accordion
                  elevation={0}
                  expanded={expanded === id}
                  className="services-accordion"
                  disableGutters
                  onChange={handleChange(id)}
                >
                  <AccordionSummary
                    aria-controls={`panel-content-${id}`}
                    id={`panel-header-${id}`}
                    className="services-accordion-header"
                    expandIcon={<SvgexpandIcon />}
                  >
                    <h3 className="service-title">{t(title)}</h3>
                  </AccordionSummary>

                  <AccordionDetails>
                    {type === "leaveEntitlements" && (
                      <LeaveEntitlementsContent t={t} section={section} />
                    )}
                    {type === "payroll" && (
                      <PayrollContent t={t} data={section} />
                    )}


                    {!type && subsections?.map((subsec, index) => (
                      <div key={index}>
                        {subsec.title && (
                          <p className="service-sub-title">{t(subsec.title)}</p>
                        )}

                        {subsec.description && (
                          Array.isArray(subsec.description) ? (
                            subsec.description.map((desc, i) => (
                              <p className="service-description paragraph" key={i}>
                                {t(desc)}
                              </p>
                            ))
                          ) : (
                            <p className="service-description paragraph">
                              {t(subsec.description)}
                            </p>
                          )
                        )}

                        {subsec.list && (
                          <div className="item">
                            <ul className="service-description paragraph">
                              {subsec.list.map((item, i) => (
                                <li key={i}>
                                  {t(item.text)}
                                  {item.sublist && (
                                    <ul>
                                      {item.sublist.map((sub, j) => (
                                        <li key={j}>{t(sub)}</li>
                                      ))}
                                    </ul>
                                  )}
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    ))}
                  </AccordionDetails>
                </Accordion>
              </Grid>
            );
          })}
        </Grid>

      </Container>
    </div>
  );
};

export default LaborLaws;
