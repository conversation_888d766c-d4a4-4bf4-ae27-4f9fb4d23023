"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/glossaries/page",{

/***/ "(app-pages-browser)/./src/components/sections/GlossaryBanner.jsx":
/*!****************************************************!*\
  !*** ./src/components/sections/GlossaryBanner.jsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Container_Input_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Container,Input!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Container_Input_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Container,Input!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Input/Input.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Container_Input_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Container,Input!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _assets_images_icons_BookIcon_svg__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/assets/images/icons/BookIcon.svg */ \"(app-pages-browser)/./src/assets/images/icons/BookIcon.svg\");\n/* harmony import */ var _assets_images_icons_searchIcon_svg__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/assets/images/icons/searchIcon.svg */ \"(app-pages-browser)/./src/assets/images/icons/searchIcon.svg\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction GlossaryBanner(param) {\n    let { bannerImg, height, altImg, letters = [], searchWord = \"\", hasContent = true, t } = param;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [keyword, setKeyword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(searchWord || searchParams?.get(\"word\") || \"\");\n    const handleSearchChange = (e)=>{\n        setKeyword(e.target.value);\n    };\n    const handleSearchClick = ()=>{\n        const params = new URLSearchParams();\n        if (keyword.trim()) {\n            params.set(\"word\", keyword.trim());\n        }\n        const queryString = params.toString();\n        router.push(`${pathname}${queryString ? `?${queryString}` : \"\"}`);\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\") {\n            handleSearchClick();\n        }\n    };\n    const handleClearSearch = ()=>{\n        setKeyword(\"\");\n        router.push(pathname);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"glossary-banner\",\n        className: \"center-banner\",\n        style: {\n            backgroundImage: `url(${bannerImg.src})`,\n            height: height\n        },\n        children: [\n            altImg && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                width: 0,\n                height: 0,\n                alt: altImg,\n                src: \"\",\n                style: {\n                    display: \"none\"\n                },\n                loading: \"lazy\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_Input_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"top-section custom-max-width\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_BookIcon_svg__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"heading-h1 text-white page-title\",\n                        children: t(\"glossary:banner:title\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"glossary-search\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_Input_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"glossary-search-input\",\n                                type: \"text\",\n                                placeholder: \"What are you looking for ?\",\n                                onChange: handleSearchChange,\n                                onKeyDown: handleKeyDown,\n                                value: keyword\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_Input_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"btn-search\",\n                                onClick: handleSearchClick,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_searchIcon_svg__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this),\n                    hasContent && letters?.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"letters\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"letter selected\",\n                                children: \"#\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                                lineNumber: 89,\n                                columnNumber: 13\n                            }, this),\n                            letters.map((letter, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: `#${letter}`,\n                                    className: \"letter\",\n                                    children: letter\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 15\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, this),\n                    !hasContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"letters-empty\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                color: \"white\",\n                                opacity: 0.8,\n                                fontSize: \"14px\",\n                                marginTop: \"20px\"\n                            },\n                            children: searchWord ? `No results found for \"${searchWord}\"` : \"No glossary terms available\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                            lineNumber: 100,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                        lineNumber: 99,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\GlossaryBanner.jsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n_s(GlossaryBanner, \"5cK5uZLgiTLM+7PkB8Pjb+OyIXg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = GlossaryBanner;\n/* harmony default export */ __webpack_exports__[\"default\"] = (GlossaryBanner);\nvar _c;\n$RefreshReg$(_c, \"GlossaryBanner\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/GlossaryBanner.jsx\n"));

/***/ })

});