"use client";

import { useRouter } from "next/navigation";
import React from "react";
import { useTranslation } from "react-i18next";

const BackButton = () => {
  const router = useRouter();
  const { t } = useTranslation();

  return (
    <button
      onClick={() => router.back()}
      className="btn btn-ghost"
      style={{
        width: "fit-content",
        fontFamily: "Proxima-Nova-Semibold !important",
        fontSize: "20px",
      }}
    >
      {"← " + t("HomeDashboard:Back")}
    </button>
  );
};

export default BackButton;
