"use client";
import LaborLaws from "../LaborLaws";
import { createList } from '../../../utils/functions';
export const LABOR_KEYS = {
    workingHours: {
        title: "ksa:KSALabor:workingHours:title",
    },
    employmentContracts: {
        title: "ksa:KSALabor:employmentContracts:title",
    },

};
export const ksaLaborData = {
    title: "ksa:KSALabor:title",
    sections: [
        {
            id: 1,
            title: LABOR_KEYS.workingHours.title,
            subsections: [
                {
                    title: "ksa:KSALabor:workingHours:title1",
                    description: "ksa:KSALabor:workingHours:description1",
                },
                {
                    title: "ksa:KSALabor:workingHours:title2",
                    description: "ksa:KSALabor:workingHours:description2",
                },
            ],
        },
        {
            id: 2,
            title: LABOR_KEYS.employmentContracts.title,
            subsections: [
                {
                    title: "ksa:KSALabor:employmentContracts:title",
                },
                {
                    title: "ksa:KSALabor:employmentContracts:title1",
                    list: createList(["ksa:KSALabor:employmentContracts:data1", "ksa:KSALabor:employmentContracts:data2"]),
                },

            ],
        },
        {
            id: 3,
            title: "ksa:KSALabor:payroll:title",
            type: "payroll",
            titleKey: "ksa:KSALabor:payroll:title1",
            descriptionKey: "ksa:KSALabor:payroll:description",
            items: [
                {
                    titleKey: "ksa:KSALabor:payroll:fiscalYear:title",
                    dateKeys: ["ksa:KSALabor:payroll:fiscalYear:date1", "ksa:KSALabor:payroll:fiscalYear:date2"],
                    descriptionKey: "ksa:KSALabor:payroll:fiscalYear:description",
                },
                {
                    titleKey: "ksa:KSALabor:payroll:payrollCycle:title",
                    dateKeys: ["ksa:KSALabor:payroll:payrollCycle:date"],
                    descriptionKey: "ksa:KSALabor:payroll:payrollCycle:description",
                },
                {
                    titleKey: "ksa:KSALabor:payroll:minimumWage:title",
                    dateKeys: ["ksa:KSALabor:payroll:minimumWage:wage", "ksa:KSALabor:payroll:minimumWage:date"],
                    descriptionKey: "ksa:KSALabor:payroll:minimumWage:description",
                },
                {
                    titleKey: "ksa:KSALabor:payroll:payrollManagement:title",
                    dateKeys: ["ksa:KSALabor:payroll:payrollManagement:date1", "ksa:KSALabor:payroll:payrollManagement:date2"],
                    descriptionKey: "ksa:KSALabor:payroll:payrollManagement:description",
                },


            ],
        },
        {
            id: 4,
            title: "ksa:KSALabor:termination:title",
            subsections: [
                {
                    title: "ksa:KSALabor:termination:title1",
                    description: "ksa:KSALabor:termination:description1",
                },

                {
                    list: createList(["ksa:KSALabor:termination:data1", "ksa:KSALabor:termination:data2", "ksa:KSALabor:termination:data3", "ksa:KSALabor:termination:data4"]),
                },
                {
                    title: "ksa:KSALabor:termination:title2",
                    description: "ksa:KSALabor:termination:description2",

                },
                {
                    title: "ksa:KSALabor:termination:title3",
                    description: "ksa:KSALabor:termination:description3",
                },
                { description: "ksa:KSALabor:termination:description4", list: createList(["ksa:KSALabor:termination:data5", "ksa:KSALabor:termination:data6"]), },
                { description: "ksa:KSALabor:termination:description5", list: createList(["ksa:KSALabor:termination:data7", "ksa:KSALabor:termination:data8", "ksa:KSALabor:termination:data9"]) },
                { description: "ksa:KSALabor:termination:description6" }


            ],
        },
        {
            id: 5,
            title: "ksa:KSALabor:leaveEntitlements:title",
            type: "leaveEntitlements",
            data: {
                description: "ksa:KSALabor:leaveEntitlements:description",
                leaves: [
                    { date: "ksa:KSALabor:leaveEntitlements:leaves:dataS1:date", title: "ksa:KSALabor:leaveEntitlements:leaves:dataS1:title" },
                    { date: "ksa:KSALabor:leaveEntitlements:leaves:dataS2:date", title: "ksa:KSALabor:leaveEntitlements:leaves:dataS2:title" },
                    { date: "ksa:KSALabor:leaveEntitlements:leaves:dataS3:date", title: "ksa:KSALabor:leaveEntitlements:leaves:dataS3:title" },
                    { date: "ksa:KSALabor:leaveEntitlements:leaves:dataS4:date", title: "ksa:KSALabor:leaveEntitlements:leaves:dataS4:title" },
                ],
                maternityLeave: {
                    title: "ksa:KSALabor:leaveEntitlements:leaves:maternityLeave:title",
                    description: ["ksa:KSALabor:leaveEntitlements:leaves:maternityLeave:description1"],
                },
                sickLeave: {
                    title: "ksa:KSALabor:leaveEntitlements:leaves:sickLeave:title",
                    description: "ksa:KSALabor:leaveEntitlements:leaves:sickLeave:description",
                },
            },
        },
        {
            id: 6,
            title: "ksa:KSALabor:tax:title",
            subsections: [
                { description: "ksa:KSALabor:tax:description" },
                {
                    title: "ksa:KSALabor:tax:title1",

                },
                {
                    list: createList(["ksa:KSALabor:tax:data1", "ksa:KSALabor:tax:data2", "ksa:KSALabor:tax:data3", "ksa:KSALabor:tax:data4"]),
                },
                {
                    title: "ksa:KSALabor:tax:title2",
                    list: createList(["ksa:KSALabor:tax:dataS1", "ksa:KSALabor:tax:dataS2", "ksa:KSALabor:tax:dataS3"]),
                },
            ],
        },
        {
            id: 7,
            title: "ksa:KSALabor:visa:title",
            subsections: [
                { description: "ksa:KSALabor:visa:description1" },
                { description: "ksa:KSALabor:visa:description2" },
                { title: "ksa:KSALabor:visa:title1" },
                {
                    title: "ksa:KSALabor:visa:title2",
                },
                { title: "ksa:KSALabor:visa:title3" },
                {
                    list: createList(["ksa:KSALabor:visa:data1", "ksa:KSALabor:visa:data2", "ksa:KSALabor:visa:data3", "ksa:KSALabor:visa:data4", "ksa:KSALabor:visa:data5", "ksa:KSALabor:visa:data6", "ksa:KSALabor:visa:data7", "ksa:KSALabor:visa:data8", "ksa:KSALabor:visa:data9", "ksa:KSALabor:visa:data10"]),
                },

            ],
        },
    ],
};


export default function KSALaborData() {
    return <LaborLaws data={ksaLaborData} />;
}
