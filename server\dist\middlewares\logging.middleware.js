"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.requestLogger = void 0;
const Logger_1 = require("@/utils/logger/Logger");
const functions_1 = require("@/utils/helpers/functions");
const requestLogger = async (request, response, next) => {
    const logger = new Logger_1.Logger(`${process.env.NODE_ENV}-requests-${new Date().toISOString().substring(0, 10)}.log`);
    const requestData = {
        method: request.method,
        url: request.originalUrl,
        ip: request.ip,
        headers: request.headers,
        body: request.body,
        query: request.query,
        user: request.cookies.refreshToken,
    };
    let responseBody = '';
    const originalSend = response.send.bind(response);
    const user = await (0, functions_1.getConnectedUser)(requestData.user);
    response.send = ((body) => {
        responseBody = body;
        originalSend(body);
    });
    response.on('finish', () => {
        const responseData = {
            statusCode: response.statusCode,
            headers: response.getHeaders(),
            body: responseBody,
        };
        const logMessage = `[${user}] - [${requestData.ip}] - [${requestData.method}] - [${requestData.url}] - [${responseData.statusCode}]`;
        logger.log(logMessage);
    });
    return next();
};
exports.requestLogger = requestLogger;
//# sourceMappingURL=logging.middleware.js.map