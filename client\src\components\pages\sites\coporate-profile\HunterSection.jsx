"use client";
import { useRef, useState } from "react";
import { Container, Grid } from "@mui/material";
import hunterBG from "@/assets/images/website/coporate-profile/bg-video.png";
import playBtn from "@/assets/images/website/coporate-profile/play-btn.png";
import arrow from "@/assets/images/website/coporate-profile/arrow.png";
import hunter from "@/assets/images/website/coporate-profile/hunter.png";

function HunterSection() {
  const videoRef = useRef(null);
  const [isPlaying, setIsPlaying] = useState(false);

  const handlePlay = () => {
    const video = videoRef.current;
    if (video.paused) {
      video.play();
      setIsPlaying(true);
    } else {
      video.pause();
      setIsPlaying(false);
    }
  };
  return (
    <div id="hunter-section">
      <div
        style={{
          backgroundImage: `url(${hunterBG.src})`,
          backgroundPosition: "bottom",
          backgroundRepeat: "no-repeat",
          backgroundSize: "cover",
        }}
      >
        <Container className="custom-max-width">
          <h2 className="heading-h2 text-white bold text-center">
            Pentabell Proprietary Technology Solutions
          </h2>
          <Grid container columnSpacing={3} alignItems={"center"}>
            <Grid item xs={12} sm={5}>
              <h3>
                <span className="heading-h1 text-yellow mt-2 ">Hunter AI</span>

                <br />
                <span className="heading-h3 text-white text-p ">
                  AI-Powered Talent Recruitment Platform
                </span>
              </h3>

              <p className="paragraph text-white semi-bold ">
                Hunter AI is Pentabell's internal recruitment platform powered by
                AI. Designed to facilitate the hiring process with automation,
                intelligent candidate matching, and advanced analytics. Using a
                database of over 260,000+ CVs, it connects you with top-tier talent
                faster and more effectively.
              </p>
            </Grid>
            <Grid item xs={12} sm={7}>  <div className="video-container">
              <video
                ref={videoRef}
                width="100%"
                height="auto"
                controls={isPlaying}
                onClick={handlePlay}
              >
                <source src="/videos/hunter.mp4" type="video/mp4" />
                Your browser does not support the video tag.
              </video>
              {!isPlaying && (
                <div className="play-button" onClick={handlePlay}>
                  <img src={playBtn.src} width={70} height={70} />
                </div>
              )}{" "}
            </div>
            </Grid>
          </Grid>


        </Container>
      </div>

      <Container className="hunter-content custom-max-width">
        <Grid container>
          <Grid item xs={12} sm={4} md={4}>
            <div className="why-hunter left-side">
              <img src={arrow.src} width={73} height={40} />
              <p className="content paragraph text-white">
                <b>Smart Candidate Matching</b>
                <span>
                  {" "}
                  : AI-powered algorithms quickly identify key skills,
                  experiences, qualifications, location, education, and job
                  titles to recommend the best candidates.
                </span>
              </p>
            </div>
            <div className="why-hunter left-side">
              <img src={arrow.src} width={73} />
              <p className="content paragraph text-white">
                <b>AI-Powered Talent Matching</b>
                <span>
                  {" "}
                  : Hunter goes beyond keyword searches, using advanced AI to
                  assess resumes based on recruiter-defined criteria.
                </span>
              </p>
            </div>
            <div className="why-hunter left-side">
              <img src={arrow.src} width={73} />
              <p className="content paragraph text-white">
                <b>Automated Scoring</b>
                <span>
                  {" "}
                  : Profiles are ranked with a compatibility score, delivering
                  the top 20 candidates for the role.
                </span>
              </p>
            </div>
          </Grid>
          <Grid item xs={12} sm={3} md={4} className="center-section">
            <img src={hunter.src} className="relative-img" />
            <p className="white-label">Hiring</p>
            <p className="yellow-label">Hunter Platform</p>
            <p className="white-label">Smart Recommendations</p>
          </Grid>
          <Grid item xs={12} sm={4} md={4}>
            <div className="why-hunter right-side">
              <p className="content paragraph text-white">
                <b> Extensive CV Database</b>
                <span>
                  {" "}
                  : Access a constantly expanding International talent pool of
                  over 260,000+ candidates across various industries.
                </span>
              </p>

              <img src={arrow.src} width={73} height={40} />
            </div>
            <div className="why-hunter right-side">
              <p className="content paragraph text-white">
                <b>Unbiased Selection</b>
                <span>
                  {" "}
                  : Focuses solely on relevant data, eliminating biases to
                  ensure fair and accurate recommendations.
                </span>
              </p>
              <img src={arrow.src} width={73} />
            </div>
            <div className="why-hunter right-side">
              <p className="content paragraph text-white">
                <b>Job Description Generation</b>
                <span>
                  {" "}
                  : AI-powered tools generate comprehensive and accurate job
                  descriptions based on industry standards and recruiter input
                  (job title, industry, language & others).
                </span>
              </p>
              <img src={arrow.src} width={73} />
            </div>
          </Grid>
        </Grid>
      </Container>
    </div>
  );
}

export default HunterSection;
