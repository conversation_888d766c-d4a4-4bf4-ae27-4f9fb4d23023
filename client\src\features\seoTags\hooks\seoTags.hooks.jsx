import { useMutation, useQuery, useQueryClient } from "react-query";
import { createSeo, getSeo, getSeoById, getSeoBySlug, updateSeo } from "../services/seoTags.services";

export const useCreateSeo = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (body) => {
      return createSeo(body);
    },
    onError: (err) => {
      err.message = "";
    },
  });
};

export const useUpdateSeo = () => {
  return useMutation({
    mutationFn: (data, language, id) => {
      return updateSeo(data, language, id);
    },
    onError: (err) => {
      err.message = "";
    },
  });
};

export const useGetSeo = (body) => {
  return useQuery({
    queryKey: ["seo", body],
    queryFn: () => getSeo(body),
    keepPreviousData: true,
    staleTime: 1000 * 60 * 5,
    refetchOnWindowFocus: false,
  });
};

export const useGetSeoBySlug = (body) => {
  return useQuery(["seo", body], async () => {
    const data = await getSeoBySlug(body);
    return data;
  });
};

export const useGetSeoById = (id) => {
  return useQuery(["seo", id], async () => {
    const data = await getSeoById(id);
    return data;
  });
};
