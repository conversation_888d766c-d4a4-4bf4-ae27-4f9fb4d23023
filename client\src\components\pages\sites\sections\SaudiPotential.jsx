"use client";
import { Container, Grid } from "@mui/material";
import Image from "next/image";
import potential from "../../assets/images/Saudi/saudi-potential.png";
import CustomButton from "@/components/ui/CustomButton";
import { useTranslation } from "react-i18next";

function SaudiPotential({ locale }) {
  const { t } = useTranslation();
  return (
    <Container className="purpose-section custom-max-width" id="ksa">
      <div
        style={{
          backgroundImage: `url(${potential.src})`,
          height: "auto",
        }}
        id="potential-component"
      >
        <Grid className="container" container rowSpacing={2}>
          <p className="heading-h2 text-white text-center">
            Ready to <span className="text-yellow">unlock</span> Saudi Arabia’s
            potential ?
          </p>
          <CustomButton 
            text="Schedule a call"
            className={"btn btn-filled"}

          />
        </Grid>
      </div>
    </Container>
  );
}

export default SaudiPotential;
