import React, { useState, useEffect } from 'react';

import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  Tooltip,
} from 'recharts';
import {
  Box,
  FormControl,
  Select,
  MenuItem,
  InputLabel,
} from '@mui/material';
import { axiosGetJsonSSR } from '../../../config/axios';
import { useTheme, useMediaQuery } from "@mui/material";
import { useTranslation } from 'react-i18next';

const CandidateApplicationsChart = () => {
  const [data, setData] = useState([]);
  const [status, setStatus] = useState('all');
  const [timeFrame, setTimeFrame] = useState('lastMonth');
  const theme = useTheme();
  const { t } = useTranslation()

  const monthNames = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const isTablet = useMediaQuery(theme.breakpoints.between("sm", "md"));
  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await axiosGetJsonSSR.get('/opportunities/stats', {
          params: {
            status: status !== 'all' ? status : undefined,
            timeFrame,
            barChart: true,
          },
        });

        const formattedData = response.data.map((item) => ({
          date: `${monthNames[item.month - 1]} ${item.year}`,
          applications: item.applications,
        }));

        setData(formattedData);
      } catch (error) {
        console.error('Error fetching application stats:', error);
      }
    };

    fetchData();
  }, [status, timeFrame]);


  const getBarColor = (status) => {
    switch (status) {
      case 'Accepted':
        return '#009966';
      case 'Rejected':
        return '#cc3233';
      default:
        return '#234791';
    }
  };  

  return (
    <Box
      sx={{
        width: '100%',
        maxWidth: 450,
        padding: 1.8,
        borderRadius: 2,
      }}
    >

      <Box  sx={{
          display: 'flex',
          gap: 2,
          flexWrap: 'wrap',
        }}>
        <p className='title-Applications-stat'  >{t("HomeDashboard:Applications")}</p>
        <FormControl size="small" sx={{ minWidth: 40 }}>

          <InputLabel>Status</InputLabel>
          <Select
            value={status}
            onChange={(e) => setStatus(e.target.value)}
            label="Status"
          >
            <MenuItem value="all">Submitted</MenuItem>
            <MenuItem value="Accepted">Accepted</MenuItem>
            <MenuItem value="Rejected">Rejected</MenuItem>
          </Select>
        </FormControl>
        <FormControl size="small" sx={{ minWidth: 40 }}>
          <InputLabel>Time </InputLabel>
          <Select
            value={timeFrame}
            onChange={(e) => setTimeFrame(e.target.value)}
            label="Time Frame"
          >
            <MenuItem value="lastMonth">Last Month</MenuItem>
            <MenuItem value="last3Months">Last 3 Months</MenuItem>
            <MenuItem value="last6Months">Last 6 Months</MenuItem>
          </Select>
        </FormControl>
      </Box>
      <BarChart
        width={isMobile ? 200 : isTablet ? 150 : 450}
        height={200}
        data={data}
        barSize={13}
        margin={{ top: 40, bottom: 20 }}
      >
        <XAxis dataKey="date"

          tickLine={false}
          axisLine={false} />
        <YAxis
          tickCount={data?.sort((a, b) =>  b.applications - a.applications)[0]?.applications + 1 || 5}
          tickLine={false}
          axisLine={false} />
        <Tooltip />
        <Bar
          dataKey="applications"
          fill={getBarColor(status)}
          radius={[10, 10, 10, 10]}
        />
      </BarChart>
    </Box>
  );
};

export default CandidateApplicationsChart;
