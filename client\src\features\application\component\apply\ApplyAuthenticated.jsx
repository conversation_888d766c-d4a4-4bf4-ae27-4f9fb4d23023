"use client";
import {
  <PERSON><PERSON>,
  Checkbox,
  Container,
  FormControlLabel,
  Grid,
  Radio,
  RadioGroup,
  TextField,
} from "@mui/material";
import { useEffect, useRef, useState } from "react";
import { FormGroup, FormLabel } from "react-bootstrap";
import { useTranslation } from "react-i18next";
import { ErrorMessage, Form, Formik } from "formik";
import { v4 as uuidv4 } from "uuid";
import moment from "moment";

import AlertMessage from "@/components/ui/AlertMessage";
import CustomButton from "@/components/ui/CustomButton";
import SvgFileText from "@/assets/images/icons/FileText.svg";
import SvgMapPin from "@/assets/images/icons/MapPin.svg";
import SvgTime from "@/assets/images/icons/time.svg";
import SvgUploadIcon from "@/assets/images/icons/uploadIcon.svg";
import {
  useCreateApplication,
  useSaveFile,
} from "@/features/opportunity/hooks/opportunity.hooks";
import {
  useGetCandidateData,
  useUpdateProfessionalInfos,
} from "@/features/user/hooks/updateProfile.hooks";
import { uploadResumeAuthenticatedSchema } from "@/utils/validations";
import Loading from "@/components/loading/Loading";

function ApplyAuthenticated({ data, successMsg, errorMsg }) {
  const [errMsg, setErrMsg] = useState("");
  const [registerSuccess, setRegisterSuccess] = useState(false);

  const { t, i18n } = useTranslation();
  let uuidResume;
  let uuidResumeFileName;
  let formData = new FormData();

  const fileInputRef = useRef(null);
  const [newResumeFile, setNewResumeFile] = useState(null);
  const [selectedResume, setSelectedResume] = useState(null);

  const { data: candidateDate } = useGetCandidateData();
  const [resumes, setResumes] = useState(candidateDate?.cv);
  const [errResume, setErrResume] = useState(null);
  const [currentDate, setCurrentDate] = useState("");
  const [originalName, setOriginalName] = useState("");

  useEffect(() => {
    if (successMsg) setRegisterSuccess(successMsg);
    if (errorMsg) setErrMsg(errorMsg);
  }, [errorMsg, successMsg]);

  useEffect(() => {
    setResumes(candidateDate?.cv);
  }, [candidateDate?.cv]);

  const useSaveFileHook = useSaveFile();
  const useCreateApplicationHook = useCreateApplication();
  const useUpdateProfessionallInfosHook = useUpdateProfessionalInfos();

  const handleResumeChange = (e, setFieldValue) => {
    setRegisterSuccess(false);
    uuidResume = uuidv4().replace(/-/g, "");

    const selectedFile = e.target.files[0];
    if (selectedFile) {
      formData.append("file", selectedFile);
      setCurrentDate(new Date().toISOString());
      setOriginalName(selectedFile.name);
      const extension = selectedFile.name.split(".").pop();
      uuidResumeFileName = `${uuidResume}.${extension}`;
      const currentYear = new Date().getFullYear();

      useSaveFileHook.mutate(
        {
          resource: "candidates",
          folder: currentYear,
          filename: uuidResume,
          body: { formData, t },
          t: t,
        },
        {
          onSuccess: (data) => {
            if (data.message === "uuid exist") {
              setNewResumeFile(data.uuid);
              setFieldValue("resume", data.uuid);
            } else {
              setNewResumeFile(uuidResumeFileName);
              setFieldValue("resume", uuidResumeFileName);
            }
          },
          onError: (error) => {
            setErrResume(error.response.data.message);
          },
        }
      );
    }
  };

  const handleSubmit = async (values, { resetForm }) => {
    setErrMsg("");
    const chosenResume = selectedResume ? selectedResume : newResumeFile;
    await useCreateApplicationHook.mutate(
      {
        opportunityId: data._id,
        data: {
          note: values.message,
          resume: chosenResume,
        },
      },
      {
        onSuccess: (data) => {
          if (newResumeFile)
            useUpdateProfessionallInfosHook.mutate({
              cv: [
                {
                  fileName: newResumeFile,
                  originalName: originalName,
                  publishDate: currentDate,
                },
              ],
              t,
            });
          //setRegisterSuccess(data.message);
          setNewResumeFile(null);
          resetForm();
          setTimeout(() => {
            window.location.href = "/application-received";
          }, 1000);
        },

        onError: (error) => {
          if (error.response.status !== 500) {
            if (error.response.status === 409)
              if (
                error.response.message?.includes(
                  "Candidate is now assigned to this opening!"
                )
              ) {
                setRegisterSuccess("Application submitted successfully");
                setTimeout(() => {
                  window.location.href = "/application-received";
                }, 1000);
              } else {
                setErrMsg("You've already applied for this position.");
              }
            else setErrMsg(error.message);
          }
        },
      }
    );
  };

  const initialValues = {
    resume: "",
    message: "",
    acceptTerms: false,
  };

  return (
    <Container className="container-apply-authenticated">
      <div className="opportunity-info">
        <Grid container spacing={1}>
          <Grid item xs={12} sm={6}>
            <p className="apply-title text-white">
              {t("global:applyForThisJob")}
            </p>
          </Grid>
          <Grid item xs={12} sm={6}>
            <p className="title text-yellow">
              {data?.versions[i18n.language]?.title}
            </p>
            <p className="job-ref">Ref: {data?.reference}</p>
            <div className="flex children-componenent">
              <Grid container>
                {data?.contractType ? (
                  <Grid item xs={12} sm={6}>
                    <div className="job-info">
                      <SvgFileText />
                      <p>
                        Contract&nbsp;:&nbsp;
                        <span>{data?.contractType}</span>
                      </p>
                    </div>
                  </Grid>
                ) : null}
                <Grid item xs={12} sm={6}>
                  <div className="job-info">
                    <SvgMapPin />
                    <p>
                      Location&nbsp;:&nbsp;
                      <span>{data?.country}</span>
                    </p>
                  </div>
                </Grid>
                <Grid item xs={12} sm={12}>
                  <div className="job-info">
                    <SvgTime />
                    <p>
                      Expiration date&nbsp;:&nbsp;
                      <span>
                        {data?.dateOfExpiration
                          ? moment(data?.dateOfExpiration).format("DD/MM/YYYY")
                          : moment().add(3, "months").format("DD/MM/YYYY")}
                      </span>
                    </p>
                  </div>
                </Grid>
              </Grid>
            </div>
          </Grid>
        </Grid>
        {resumes?.length > 0 && (
          <div className="resume-title">
            <div>
              <p className="title">Resume</p>
              <p>
                Be sure to include an updated resume{" "}
                <span className="text-yellow">*</span>
              </p>
            </div>
            <div>
              <CustomButton
                text={"Clear selection"}
                className={"btn btn-filled"}
                onClick={() => setSelectedResume(null)}
              />
            </div>
          </div>
        )}
        {resumes?.map((resume, index) => (
          <FormGroup key={index} id="choose-resume">
            <div className="resume-info">
              <SvgUploadIcon />
              <p className="resume-name">
                {typeof resume === "string"
                  ? resume
                  : resume.originalName !== ""
                  ? resume.originalName
                  : resume.fileName}
              </p>
            </div>
            <RadioGroup
              row
              aria-labelledby="mission-radio-btn"
              name="row-radio-buttons-group"
            >
              <FormControlLabel
                value="false"
                className="label-pentabell light"
                control={
                  <Radio
                    checked={selectedResume === resume.fileName}
                    value={resume.fileName}
                    onChange={() => {
                      setSelectedResume(resume.fileName);
                      setErrResume(null);
                    }}
                  />
                }
              />
            </RadioGroup>
          </FormGroup>
        ))}
        <div>
          <Formik
            initialValues={initialValues}
            onSubmit={handleSubmit}
            validationSchema={() =>
              uploadResumeAuthenticatedSchema(t, selectedResume)
            }
          >
            {({ values, handleChange, errors, touched, setFieldValue }) => (
              <Form id="login-form" className="pentabell-form">
                <FormGroup className="form-group light">
                  <div
                    className="custom-file-upload"
                    onClick={() => {
                      document.getElementById("file-upload").click();
                    }}
                  >
                    <div>
                      {useSaveFileHook?.isLoading ? (
                        <Loading />
                      ) : (
                        <>
                          <SvgUploadIcon />
                          {newResumeFile ? (
                            <>
                              <FormLabel className="label-pentabell light">
                                {t("application:messagesuccessupload")}
                              </FormLabel>
                              <p className="sub-label">{originalName}</p>
                            </>
                          ) : (
                            <>
                              <FormLabel className="label-pentabell light">
                                Upload your CV
                              </FormLabel>
                              <p className="sub-label">
                                Only jpg, png, and pdf files. 500kb max file
                                size.
                              </p>
                            </>
                          )}
                          <CustomButton
                            text={"Choose a file"}
                            className={"btn btn-outlined white"}
                          />
                        </>
                      )}
                    </div>
                    <input
                      id="file-upload"
                      type="file"
                      name="resume"
                      accept="application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/pdf, application/msword"
                      ref={fileInputRef}
                      style={{ display: "none" }}
                      onChange={(e) => {
                        setErrResume(null);
                        handleResumeChange(e, setFieldValue);
                      }}
                    />
                  </div>
                </FormGroup>
                {errResume && (
                  <>
                    <Alert variant="filled" severity="error">
                      {errResume}
                    </Alert>
                    <br />
                  </>
                )}
                <ErrorMessage name="resume">
                  {(msg) => (
                    <Alert variant="filled" severity="error">
                      {msg}
                    </Alert>
                  )}
                </ErrorMessage>
                <FormGroup className="form-group">
                  <FormLabel className="label-pentabell light">
                    Message
                  </FormLabel>
                  <TextField
                    className="input-pentabell light"
                    placeholder="Message"
                    name="message"
                    variant="standard"
                    type="text"
                    value={values.message}
                    onChange={handleChange}
                  />
                </FormGroup>
                <ErrorMessage name="message">
                  {(msg) => (
                    <Alert variant="filled" severity="error">
                      {msg}
                    </Alert>
                  )}
                </ErrorMessage>
                <AlertMessage errMsg={errMsg} success={registerSuccess} />
                <Grid container spacing={2} className="form-section">
                  <Grid item lg={7} md={6} sm={12}>
                    <FormControlLabel
                      className="checkbox-pentabell light "
                      control={
                        <Checkbox
                          name="acceptTerms"
                          checked={values.acceptTerms}
                          onChange={handleChange}
                        />
                      }
                      label={t("register:message")}
                    />
                    <ErrorMessage name="acceptTerms">
                      {(msg) => (
                        <Alert variant="filled" severity="error">
                          {msg}
                        </Alert>
                      )}
                    </ErrorMessage>
                  </Grid>
                  <Grid item lg={5} md={6} sm={12} className="btn-continue">
                    <CustomButton
                      disabled={useSaveFileHook?.isLoading}
                      text={"Submit"}
                      className={"btn btn-filled btn-submit"}
                      type="submit"
                    />
                  </Grid>
                </Grid>
              </Form>
            )}
          </Formik>
        </div>
      </div>
    </Container>
  );
}

export default ApplyAuthenticated;
