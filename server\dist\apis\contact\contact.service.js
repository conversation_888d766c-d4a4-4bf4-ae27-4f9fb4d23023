"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const contact_model_1 = __importDefault(require("./contact.model"));
const services_1 = require("@/utils/services");
const files_service_1 = __importDefault(require("../storage/files.service"));
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const user_model_1 = __importDefault(require("../user/user.model"));
const fs_1 = __importDefault(require("fs"));
const axios_1 = __importDefault(require("axios"));
const form_data_1 = __importDefault(require("form-data"));
const messages_1 = require("@/utils/helpers/messages");
class ContactService {
    constructor() {
        this.Contact = contact_model_1.default;
        this.User = user_model_1.default;
        this.filesService = new files_service_1.default();
    }
    async fillContactFrom(formContent) {
        await this.Contact.create(formContent);
        const { to, type, countryName, ...context } = formContent;
        let subjectContent;
        switch (type) {
            case 'mainService':
                subjectContent = `New message - [Hr Services] Contact Form [${formContent.youAre}]`;
                break;
            case 'payrollService':
                subjectContent = `New message - [Payroll Services] Contact Form [${formContent.youAre}]`;
                break;
            case 'consultingService':
                subjectContent = 'New message - [Consulting Services] Contact';
                break;
            case 'technicalAssistanceService':
                subjectContent = 'New message - [Technical Assistance Services] Contact';
                break;
            case 'aiSourcingService':
                subjectContent = `New message - [Ai Sourcing Services] Contact Form [${formContent.youAre}]`;
                break;
            case 'directHiringService':
                subjectContent = `New message - [Direct Hiring Services] Contact Form [${formContent.youAre}]`;
                break;
            case 'joinUs':
                subjectContent = 'Unsolicited Application - Pentabell Careers';
                break;
            case 'countryContact':
                subjectContent = `New Message - Contact Form ${countryName} [${formContent.youAre}]`;
                break;
            case 'getInTouch':
                subjectContent = 'New Message - [Home Page] Contact Form';
                break;
            default:
                subjectContent = 'New Contact Request';
        }
        let filePath;
        let downloadUrl;
        if (formContent.resume && formContent.resume.length !== 0) {
            filePath = await this.filesService.findFile(formContent.resume);
            downloadUrl = `${process.env.APP_PORT_l}/api/v1/files/${formContent.resume}`;
            const firstAndLastName = `${formContent?.firstName}_${formContent?.lastName}`;
            formContent.resume = `CV_${formContent.fullName || firstAndLastName}`;
            (0, services_1.sendEmail)({
                to,
                subject: subjectContent,
                template: 'contactForm',
                context: { ...context, resume: downloadUrl },
                attachments: [
                    {
                        filename: `CV_${formContent.fullName || firstAndLastName}`,
                        path: filePath,
                        contentType: 'application/pdf',
                    },
                ],
            });
            const form = new form_data_1.default();
            form.append('file', fs_1.default.createReadStream(filePath));
            try {
                const response = await axios_1.default.post(`${process.env.HUNTER_BASE_URL}/candidats/import-bulk`, form, {
                    headers: {
                        ...form.getHeaders(),
                        Cookie: `accessToken=${process.env.HUNTER_ACCESS_TOKEN}; refreshToken=${process.env.HUNTER_REFRESH_TOKEN}`,
                    },
                });
                console.log(response.data);
            }
            catch (error) {
                console.error('Resume upload failed:', error.response?.data || error.message);
            }
        }
        else
            (0, services_1.sendEmail)({
                to,
                subject: subjectContent,
                template: 'contactForm',
                context,
            });
        return { message: messages_1.MESSAGES.CONTACT.SENT_SUCCESS };
    }
    async getByEmail(email) {
        const user = await this.User.findOne({ email: new RegExp(`^${email}$`, 'i') });
        if (!user)
            return null;
        return user;
    }
    async getAllContactList(queries) {
        const { email, paginated, type, createdAt, keyword, sortOrder } = queries;
        const pageNumber = Number(queries.pageNumber) || 1;
        const pageSize = Number(queries.pageSize) || 3;
        const queryConditions = {};
        if (email) {
            const searchRegex = new RegExp('.*' + email + '.*', 'i');
            queryConditions['$or'] = [
                { email: { $regex: searchRegex } },
                { fullName: { $regex: searchRegex } },
                { firstName: { $regex: searchRegex } },
                { lastName: { $regex: searchRegex } },
            ];
        }
        if (type) {
            queryConditions['type'] = { $regex: new RegExp('.*' + type + '.*', 'i') };
        }
        if (keyword) {
            queryConditions['message'] = { $regex: new RegExp('.*' + keyword + '.*', 'i') };
        }
        if (createdAt) {
            const date = new Date(createdAt);
            if (!isNaN(date.getTime())) {
                queryConditions['createdAt'] = {
                    $gte: new Date(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0),
                };
            }
        }
        const sortCriteria = {};
        if (sortOrder) {
            sortCriteria['createdAt'] = sortOrder === 'asc' ? 1 : -1;
        }
        const totalContacts = await this.Contact.countDocuments(queryConditions);
        const totalPages = Math.ceil(totalContacts / pageSize);
        let contacts;
        if (!paginated) {
            contacts = await this.Contact.find(queryConditions).sort(sortCriteria);
        }
        else {
            contacts = await this.Contact.find(queryConditions)
                .sort(sortCriteria)
                .skip((pageNumber - 1) * pageSize)
                .limit(pageSize);
        }
        const enrichedContacts = await Promise.all(contacts.map(async (contact) => {
            const user = await this.getByEmail(contact.email);
            return {
                ...contact.toObject(),
                userId: user ? user._id : null,
            };
        }));
        return {
            pageNumber,
            pageSize,
            totalPages,
            totalContacts,
            contacts: enrichedContacts,
        };
    }
    async get(id) {
        const candidate = await this.Contact.findById(id).lean();
        if (!candidate)
            throw new http_exception_1.default(404, messages_1.MESSAGES.CONTACT.NOT_FOUND);
        return candidate;
    }
    async getStats(queries) {
        const { from, to } = queries;
        const matchStage = {};
        if (from || to) {
            matchStage.createdAt = {};
            if (from)
                matchStage.createdAt.$gte = new Date(from);
            if (to)
                matchStage.createdAt.$lte = new Date(to).setHours(23, 59, 59, 999);
        }
        const stats = await this.Contact.aggregate([
            { $match: matchStage },
            {
                $group: {
                    _id: { youAre: '$youAre', type: '$type' },
                    count: { $sum: 1 },
                },
            },
            {
                $group: {
                    _id: '$_id.youAre',
                    types: {
                        $push: {
                            type: '$_id.type',
                            count: '$count',
                        },
                    },
                },
            },
            {
                $project: {
                    _id: 0,
                    youAre: '$_id',
                    types: 1,
                },
            },
            { $sort: { youAre: 1 } },
        ]);
        return stats;
    }
}
exports.default = ContactService;
//# sourceMappingURL=contact.service.js.map