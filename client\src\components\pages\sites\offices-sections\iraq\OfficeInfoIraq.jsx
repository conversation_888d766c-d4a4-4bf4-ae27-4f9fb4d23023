import SvgUsers from "@/assets/images/icons/yellow/Users.svg";
import Svglanguage from "@/assets/images/icons/yellow/language.svg";
import Svggdp from "@/assets/images/icons/yellow/gdp.svg";
import Svgcurrency from "@/assets/images/icons/yellow/currency.svg";
import SvgcapitalCity from "@/assets/images/icons/yellow/capitalCity.svg";
import Svggross from "@/assets/images/icons/yellow/gross.svg";
import OfficeInfo from "@/components/ui/OfficeInfo";
async function OfficeInfoIraq({ t }) {
  const data = [
    {
      icon: <SvgUsers />,
      titleKey: "iraq:officeInfoIraq:title1",
      descriptionKey: "iraq:officeInfoIraq:description1"
    },
    {
      icon: <Svglanguage />,
      titleKey: "iraq:officeInfoIraq:title2",
      descriptionKey: "iraq:officeInfoIraq:description2"
    },
    {
      icon: <Svggdp />,
      titleKey: "iraq:officeInfoIraq:title3",
      descriptionKey: "iraq:officeInfoIraq:description3"
    },
    {
      icon: <Svgcurrency />,
      titleKey: "iraq:officeInfoIraq:title4",
      descriptionKey: "iraq:officeInfoIraq:description4"
    },
    {
      icon: <SvgcapitalCity />,
      titleKey: "iraq:officeInfoIraq:title5",
      descriptionKey: "iraq:officeInfoIraq:description5"
    },
    {
      icon: <Svggross />,
      titleKey: "iraq:officeInfoIraq:title6",
      descriptionKey: "iraq:officeInfoIraq:description6"
    }
  ];

  return <OfficeInfo data={data} t={t} />;
}

export default OfficeInfoIraq;
