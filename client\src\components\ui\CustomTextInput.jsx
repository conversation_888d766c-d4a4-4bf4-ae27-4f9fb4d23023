import { FormLabel, TextField } from "@mui/material";
import { ErrorMessage } from "formik";

export default function CustomTextInput({
  label,
  name,
  value,
  onChange,
  error,
  disabled = false,
  multiline = false,
  rows = 1,
  showLength = false,
  maxLength,

}) {
  return (
    <div className="form-group">
      {label && (
        <FormLabel className="label-form">
          {label}{" "}
          {showLength && (
            <span className={value?.length > maxLength ? "text-danger" : ""}>
              ( {value?.length || 0} / {maxLength} )
            </span>
          )}
        </FormLabel>
      )}
      <TextField
        variant="standard"
        name={name}
        disabled={disabled}
        value={value}
        onChange={onChange}
        multiline={multiline}
        rows={rows}
        className={`input-pentabell ${error ? "is-invalid" : ""}`}
   
      />
      <ErrorMessage name={name} component="div" className="label-error" />
    </div>
  );
}
