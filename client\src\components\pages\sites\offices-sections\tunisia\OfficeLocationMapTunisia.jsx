import OfficeLocationMap from "@/components/ui/OfficeLocationMap";

function OfficeLocationMapTunisia({ t }) {
  return (
    <OfficeLocationMap
      title={t("Tunisia:officeLocation:label")}
      subtitle={t("Tunisia:officeLocation:title")}
      address={t("Tunisia:officeLocation:address")}
      tel={t("Tunisia:officeLocation:tel1")}
      email={t("Tunisia:officeLocation:mail")}
      linkText={t("Tunisia:officeLocation:talk")}
      mapSrc="https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d12772.982761240593!2d10.300971!3d36.8365915!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x12fd4ba9d86da971%3A0x7ef02d88ba84ab8b!2sPentabell%20Tunisie%20-%20Cabinet%20de%20Recrutement%2C%20Portage%20Salarial%20et%20Assistance%20Technique!5e0!3m2!1sen!2stn!4v1728382192149!5m2!1sen!2stn"

    />
  );
}
export default OfficeLocationMapTunisia;
