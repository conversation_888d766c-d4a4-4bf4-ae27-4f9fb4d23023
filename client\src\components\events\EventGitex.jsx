"use client";
import { Container, Grid } from "@mui/material";
import frenchSaudi from "../../assets/images/events/frenchSaudi.png";
import leap from "../../assets/images/events/leap.png";
import ProcessHtml from "./ProcessHtml";
import InfoSection from '@/components/ui/InfoSection';
import EventsCard from "./EventsCard";
import libya from "../../assets/images/events/libya.png";
import image1 from "@/assets/images/Gitexevent/1.png";
import image2 from "@/assets/images/Gitexevent/2.png";
import image3 from "@/assets/images/Gitexevent/3.png";
import image4 from "@/assets/images/Gitexevent/4.png";
import image5 from "@/assets/images/Gitexevent/5.png";
import image6 from "@/assets/images/Gitexevent/6.png";
import image7 from "@/assets/images/Gitexevent/7.png";
import image8 from "@/assets/images/Gitexevent/8.png";
import image9 from "@/assets/images/Gitexevent/9.png";
import image10 from "@/assets/images/Gitexevent/10.png";
import imagemobile1 from "@/assets/images/Gitexevent/mobile1Gitex.png";
import imagemobile2 from "@/assets/images/Gitexevent/mobile2Gitex.png";
import imagemobile3 from "@/assets/images/Gitexevent/mobile3Gitex.png";
import imagemobile4 from "@/assets/images/Gitexevent/mobile4Gitex.png";
import imagemobile5 from "@/assets/images/Gitexevent/mobile5Gitex.png";
import imagemobile6 from "@/assets/images/Gitexevent/mobile6Gitex.png";
import imagemobile7 from "@/assets/images/Gitexevent/mobile7Gitex.png";
import imagemobile8 from "@/assets/images/Gitexevent/mobile8Gitex.png";
import imagemobile9 from "@/assets/images/Gitexevent/mobile9Gitex.png";
import imagemobile10 from "@/assets/images/Gitexevent/mobile10Gitex.png";
import EmblaCarousel from "@/components/embla_slider/EmblaCarousel";
import { API_URLS } from "@/utils/urls";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { axiosGetJson } from "@/config/axios";

export default function EventGitex({ language, event }) {
  const { t } = useTranslation();
  const [events, setEvents] = useState();
  const fetchEvents = async () => {
    try {
      const response = await axiosGetJson.get(API_URLS.events, {
        params: {
          visibility: "Public",
          pageNumber: 1,
          pageSize: 4,
          language: language,
        },
      });
      let { Events } = response.data;

      const eventsFiltered = Events?.filter(
        (currentEvent) => currentEvent._id !== event?._id
      ).slice(0, 3);
      setEvents(eventsFiltered);
    } catch (error) {
      console.error("Failed to fetch events:", error);
    }
  };

  useEffect(() => {
    fetchEvents();
  }, [event, language]);

  const OPTIONS = {};
  const SLIDES = [
    image1,
    image2,
    image3,
    image4,
    image5,
    image6,
    image7,
    image8,
    image9,
    image10,

  ];
  const SlidesMobile = [
    imagemobile1,
    imagemobile2,
    imagemobile3,
    imagemobile4,
    imagemobile5,
    imagemobile6,
    imagemobile7,
    imagemobile8,
    imagemobile9,
    imagemobile10
  ]

  const decarbonizationSection = {
    sector: t("eventDetailsGitex:sectorValue"),
    country: t("eventDetailsGitex:locationValue"),
    countryConcerned: t("eventDetailsGitex:countryConcernedValue"),
    organiser: t("eventDetailsGitex:organiserValue"),
  };
  let infoSection;
  if (event) {
    infoSection = {
      sector: event?.versions[0]?.sector,
      country: t(`country:${event?.country?.replace(/\s+/g, "")}`),
      countryConcerned: event?.versions[0]?.countryConcerned,
      organiser: event?.versions[0]?.organiser,
    };
  }
  const eventData = [

    {
      title: t("event:events.eventLibya.title"),
      eventDate: t("event:events.eventLibya.eventDate"),
      postingDate: t("event:events.eventLibya.postingDate"),
      exactPlace: t("event:events.eventLibya.exactPlace"),
      link: "Libyan-French-economic-forum-2025",
      type: "events",
      image: libya,
    },
    {
      title: t("event:events.event11.title"),
      eventDate: t("event:events.event11.eventDate"),
      postingDate: t("event:events.event11.postingDate"),
      exactPlace: t("event:events.event11.exactPlace"),
      type: "events",
      link: `leap-tech-conference-2025-riyadh`,
      image: leap,
    },
    {
      title: t("event:events.event1.title"),
      eventDate: t("event:events.event1.eventDate"),
      postingDate: t("event:events.event1.postingDate"),
      exactPlace: t("event:events.event1.exactPlace"),
      link: "franco-saudi-decarbonization-days",
      type: "events",
      image: frenchSaudi,
    },


  ];
  return (
    <div id="event-page">
      {event ? (
        <div id="event-detail">
          <div className="custom-max-width">
            <InfoSection t={t} infos={infoSection} />{" "}
            <ProcessHtml htmlString={event?.versions[0]?.content} />
            <Grid
              className="more-events-section"
              container
              rowSpacing={0}
              columnSpacing={3}
            >
              {" "}
              {events?.length > 0 &&
                events?.map((event, index) => (
                  <EventsCard
                    key={index}
                    eventData={event}
                    language={language}
                    isEvent={true}
                  />
                ))}{" "}
            </Grid>
          </div>{" "}
        </div>
      ) : (
        <div id="event-detail">
          <Container className="custom-max-width">
            <InfoSection t={t} infos={decarbonizationSection} />
            <div className="details">
              <p className="heading-h1">
                {t("eventDetailsGitex:eventProgram:eventProgram")}
              </p>
              <p className="text">
                {t("eventDetailsGitex:eventProgram:data12")}
              </p>
              <p className="heading-h1">
                {t("eventDetailsGitex:aboutEvent:aboutEvent")}
              </p>
              <p className="text">
                {t("eventDetailsGitex:aboutEvent:description")}
              </p>
              <EmblaCarousel slides={SLIDES} slidesMobile={SlidesMobile} options={OPTIONS} />
              <p className="heading-h1">{t("eventDetailsGitex:moreEvents")}</p>
            </div>
            <Grid
              className="more-events-section"
              container
              rowSpacing={0}
              columnSpacing={3}
            >
              {eventData?.map((event, index) => (
                <EventsCard
                  key={index}
                  eventData={event}
                  language={language}
                  isEvent={true}
                />
              ))}
            </Grid>
          </Container>
        </div>
      )}
    </div>
  );
}
