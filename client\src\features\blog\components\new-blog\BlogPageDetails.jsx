"use client";
import { useEffect, useState } from "react";
import moment from "moment";
import { Container, Grid } from "@mui/material";
import { useTheme, useMediaQuery } from "@mui/material";
import { toast } from "react-toastify";
import "moment/locale/fr";
import { useTranslation } from "react-i18next";
import DialogModal from "@/features/user/component/updateProfile/experience/DialogModal";

import {
  useGetArticleByUrlANDlanguage,
  useGetComments,
} from "../../hooks/blog.hook";
import ArticleContent from "../ArticleContent";
import {
  capitalizeFirstLetter,
  highlightMatchingWords,
} from "@/utils/functions";
import SvgTime from "@/assets/images/icons/time.svg";
import CustomButton from "@/components/ui/CustomButton";
import CommentsListByBlog from "../CommentsListByBlog";
import CreateBlogComment from "../CreateBlogComment";
import SvgArrowRight from "@/assets/images/icons/arrowRight.svg";
import useCurrentUser from "@/features/auth/hooks/currentUser.hooks";
import { API_URLS } from "@/utils/urls";
import { axiosGetJson, axiosGetJsonSSR } from "@/config/axios";
import NewsletterSubscription from "@/components/pages/sites/sections/NewsletterSubscription";
import { useAddToFavourite } from "../../../opportunity/hooks/opportunity.hooks";
import { Role } from "@/utils/constants";
import SvgBookmark from "@/assets/images/icons/bookmark.svg";
import { splitLastWord } from "@/utils/functions";
import ContentTable from "./ContentTable";
import ShareOnSocialMedia from "./ShareOnSocialMedia";
import RelatedBlog from "./RelatedBlog";
import FaqAccordion from "./FaqAccordion";
import CTSBanner from "./CTSBanner";

function BlogPageDetails({ id, article, language, url }) {
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [jobsTodelete, setJobsTodelete] = useState(false);
  const { t } = useTranslation();
  const handeleDeleteconfirmation = () => {
    setJobsTodelete(id);
    setShowDeleteConfirmation(true);
  };
  const handlecanceldelete = () => {
    setShowDeleteConfirmation(false);
  };
  const deleteOpportunityFromShortlist = async (articleId) => {
    try {
      await axiosGetJson.delete(`/favourite/${articleId}`, {
        data: { type: "article" },
      });
      setIsSaved(false);
    } catch (error) {}
  };
  const handleToggleOpportunity = async () => {
    try {
      await deleteOpportunityFromShortlist(jobsTodelete);
    } catch (error) {}
    setShowDeleteConfirmation(false);
  };

  const theme = useTheme();
  const { user } = useCurrentUser();
  const { i18n } = useTranslation();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const useAddTofavouriteHook = useAddToFavourite();

  moment.locale(i18n.language || "en");

  const [headings, setHeadings] = useState([]);
  const [modifiedHtmlContent, setModifiedHtmlContent] = useState(
    article?.content
  );
  const [relatedArticles, setRelatedArticles] = useState([]);
  const [isSaved, setIsSaved] = useState(false);
  const getArticle = useGetArticleByUrlANDlanguage(
    { language, urlArticle: url },
    { enabled: !!url }
  );

  useEffect(() => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(article?.content, "text/html");
    const extractedHeadings = [];

    Array.from(doc.querySelectorAll("h2, h3")).forEach((heading) => {
      const id = heading.innerText
        .toLowerCase()
        .replace(/\s+/g, "-")
        .replace(/[^a-z0-9\-]/g, "");
      heading.id = id;
      extractedHeadings.push({
        tagName: heading.tagName.toLowerCase(),
        content: heading.innerText,
        id,
      });
    });

    setHeadings(extractedHeadings);
    setModifiedHtmlContent(doc.body.innerHTML);
  }, [article?.content]);

  useEffect(() => {
    const checkIfSaved = async () => {
      if (id) {
        try {
          const response = await axiosGetJson.get(`/favourite/is-saved/${id}`);

          setIsSaved(response.data);
        } catch (error) {
          if (process.env.NODE_ENV === "dev")
            console.error("Failed to check if article is saved:", error);
        }
      }
    };

    checkIfSaved();
  }, [id]);

  const articleId = getArticle?.data?._id;

  const handleSaveClick = () => {
    if (!user) {
      toast.warning("Login or create account to save article.");
    } else {
      useAddTofavouriteHook.mutate(
        {
          id: id,
          title: article?.title,
          typeOfFavourite: "article",
        },
        {
          onSuccess: () => {
            setIsSaved(true);
          },
        }
      );
    }
  };
  const {
    data: commentsData,
    isLoading: commentsLoading,
    refetch: commentsRefetch,
  } = useGetComments(
    {
      articleId,
      pageNumber: 1,
      pageSize: 6,
      paginated: true,
    },
    { enabled: !!articleId }
  );

  // const categorySlugs = article.categories.map((cat) => cat.url).join("/");
  const sahredUrl = `${process.env.NEXT_PUBLIC_FRONTEND_URL}/blog/${url}`;

  useEffect(() => {
    const fetchRelatedArticles = async () => {
      if (article?.categories?.length > 0) {
        const categorySlugs = article?.categories
          .map((cat) => cat.url)
          .join("/");

        if (!categorySlugs) return;

        try {
          const response = await axiosGetJsonSSR.get(
            `${API_URLS.articles}/${language}/blog/category/${categorySlugs}`,
            {
              // params: { visibility: "Public" }
            }
          );

          if (response.status === 200) {
            const data = await response.data;
            const fetchedArticles = data?.articles?.map((item) => ({
              id: item._id,
              title: item.versions[0]?.title || "Titre non disponible",
              picture: item.versions[0]?.image || "",
              description:
                item.versions[0]?.metaDescription ||
                "Description non disponible",
              slug: item.versions[0]?.url || "",
            }));

            setRelatedArticles(fetchedArticles);
          } else {
            if (process.env.NODE_ENV === "dev")
              console.error("Failed to fetch related articles");
          }
        } catch {
          if (process.env.NODE_ENV === "dev")
            console.error("Failed to fetch related articles");
        }
      }
    };

    fetchRelatedArticles();
  }, [article?.categories, language]);

  return (
    <>
      <div id="blog-page-details">
        <div id="blog-header">
          <Container className="custom-max-width">
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                {article?.categories?.length > 0 && (
                  <div className="categories-list">
                    {article.categories.map(
                      (category) =>
                        category?.url && (
                          <span className="category light" key={category.url}>
                            <a
                              href={`${
                                language === "en"
                                  ? `/blog/category/${category.url}`
                                  : `/${language}/blog/category/${category.url}`
                              }/`}
                            >
                              {category.name}
                            </a>
                          </span>
                        )
                    )}
                  </div>
                )}
                <h1 className="heading-h1">
                  {article?.highlights
                    ? article?.highlights.length > 0
                      ? highlightMatchingWords(
                          article?.title,
                          article?.highlights
                        )
                      : splitLastWord(article?.title)
                    : splitLastWord(article?.title)}
                </h1>
                <p className="sub-heading date ">
                  <SvgTime />{" "}
                  {article?.publishDate
                    ? capitalizeFirstLetter(
                        moment(article?.publishDate).format("LL")
                      )
                    : ""}
                  <SvgTime />{" "}
                  {article?.publishDate
                    ? capitalizeFirstLetter(
                        moment(article?.publishDate).format("LT")
                      )
                    : ""}
                </p>
                {(!user || user?.roles?.includes(Role.CANDIDATE)) && (
                  <CustomButton
                    leftIcon={true}
                    text={isSaved ? "Saved" : "Save"}
                    onClick={
                      isSaved ? handeleDeleteconfirmation : handleSaveClick
                    }
                    icon={
                      <SvgBookmark
                        className={`${isSaved ? "btn-filled-yellow" : ""}`}
                      />
                    }
                    className={`btn btn-ghost p-save ${
                      isSaved ? "btn-filled-yellow " : ""
                    }`}
                  />
                )}
              </Grid>

              <Grid item xs={12} sm={6}>
                <div className="blog-img">
                  <img
                    src={`${process.env.NEXT_PUBLIC_BASE_API_URL}/files/${article?.image}`}
                    width={700}
                    height={700}
                    alt={article?.alt}
                    loading="lazy"
                    // priority
                    // quality={90}
                    // layout="intrinsic"
                  />
                </div>
              </Grid>
            </Grid>
          </Container>
        </div>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "BreadcrumbList",
              itemListElement: [
                {
                  "@type": "ListItem",
                  position: 1,
                  item: {
                    "@id":
                      language === "en"
                        ? `https://www.pentabell.com/blog/`
                        : `https://www.pentabell.com/${language}/blog/`,
                    name: "Blog",
                  },
                },
                article?.categories[0].url && {
                  "@type": "ListItem",
                  position: 2,
                  item: {
                    "@id":
                      language === "en"
                        ? `https://www.pentabell.com/blog/category/${article?.categories[0].url}/`
                        : `https://www.pentabell.com/${language}/blog/category/${article?.categories[0].url}/`,
                    name: article?.categories[0].name,
                  },
                },
              ],
            }),
          }}
        />

        <Container className="custom-max-width">
          <div className="categories-path">
            <a
              locale={language === "en" ? "en" : "fr"}
              href={`${language === "en" ? `/blog` : `/${language}/blog`}/`}
              className="link"
            >
              Blog
            </a>

            {article?.categories?.length > 0 && (
              <>
                <SvgArrowRight />
                {article?.categories[0].url && (
                  <a
                    className="link"
                    href={`${
                      language === "en"
                        ? `/blog/category/${article?.categories[0].url}`
                        : `/${language}/blog/category/${article?.categories[0].url}`
                    }/`}
                  >
                    {article?.categories[0].name}
                  </a>
                )}
              </>
            )}
          </div>
          <Grid className="container" container columnSpacing={2}>
            <Grid item xs={12} sm={8}>
              {isMobile && headings?.length > 0 ? (
                <ContentTable headings={headings} />
              ) : null}
              <div className="blog-content">
                <ArticleContent htmlContent={modifiedHtmlContent} />
              </div>
              <CTSBanner ctsBanner={article?.ctsBanner} />
              {article?.faq?.length > 0 && (
                <FaqAccordion title={article.faqTitle} items={article.faq} />
              )}
            </Grid>
            <Grid item xs={12} sm={4} id="sticky-sidebar">
              {!isMobile ? (
                <>
                  {headings?.length > 0 ? (
                    <ContentTable headings={headings} />
                  ) : null}

                  <ShareOnSocialMedia sahredUrl={sahredUrl} />
                  <div className="section">
                    <NewsletterSubscription />
                  </div>
                </>
              ) : null}
            </Grid>
          </Grid>

          <Grid className="container" container columnSpacing={2}>
            <Grid item xs={12} sm={8}>
              {isMobile ? (
                <>
                  <ShareOnSocialMedia sahredUrl={sahredUrl} />
                  <div className="section">
                    <NewsletterSubscription />
                  </div>
                </>
              ) : null}
              <RelatedBlog
                relatedArticles={relatedArticles}
                language={language}
                articleId={articleId}
              />
              <CreateBlogComment
                refetch={commentsRefetch}
                articleId={articleId}
                user={user}
              />
              <CommentsListByBlog
                articleId={articleId}
                data={commentsData}
                isLoading={commentsLoading}
                refetch={commentsRefetch}
                user={user}
              />
            </Grid>
          </Grid>
        </Container>
      </div>

      <DialogModal
        open={showDeleteConfirmation}
        message={t("messages:supprimerarticlefavoris")}
        onClose={handlecanceldelete}
        onConfirm={handleToggleOpportunity}
      />
    </>
  );
}

export default BlogPageDetails;
