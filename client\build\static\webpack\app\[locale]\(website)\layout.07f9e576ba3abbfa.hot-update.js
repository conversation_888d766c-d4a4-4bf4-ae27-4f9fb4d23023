"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/layout",{

/***/ "(app-pages-browser)/./src/components/ui/DropdownMenu.jsx":
/*!********************************************!*\
  !*** ./src/components/ui/DropdownMenu.jsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Menu/Menu.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Fade/Fade.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Fade,Menu,MenuItem!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _assets_images_icons_arrowDown_svg__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../assets/images/icons/arrowDown.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowDown.svg\");\n/* harmony import */ var _assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../assets/images/icons/arrowUp.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowUp.svg\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _utils_functions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/functions */ \"(app-pages-browser)/./src/utils/functions.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst DropdownMenu = (param)=>{\n    let { buttonLabel, buttonHref, menuItems, subMenu, locale, withFlag, selectedFlag, forceOpen = false } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    const buttonRef = useRef(null);\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_6__.useTranslation)();\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hoveredSubMenu, setHoveredSubMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const open = Boolean(anchorEl) || forceOpen;\n    // Handle forceOpen for hover functionality\n    useEffect(()=>{\n        if (forceOpen && buttonRef.current && !anchorEl) {\n            setAnchorEl(buttonRef.current);\n        } else if (!forceOpen && anchorEl && subMenu) {\n            setAnchorEl(null);\n        }\n    }, [\n        forceOpen,\n        anchorEl,\n        subMenu\n    ]);\n    const isDetailBlogPath = ()=>pathname.includes(\"/blog/\") && !pathname.includes(\"/blog/category/\") && pathname !== \"/blog/\" && pathname !== \"/fr/blog/\";\n    const handleClick = (event)=>{\n        if (!forceOpen) {\n            setAnchorEl(event.currentTarget);\n        }\n    };\n    const handleClose = ()=>{\n        setAnchorEl(null);\n    };\n    const handleClick2 = (event, item)=>{\n        event.stopPropagation(); // Prevent event bubbling\n        if (item.onClick) {\n            item.onClick(); // Call the onClick handler if provided\n        } else {\n            if (item.subItems == undefined) handleClose();\n        }\n    };\n    const handleMouseEnter = (item, index)=>{\n        if (item.subItems) {\n            setHoveredSubMenu(index);\n        }\n    };\n    const handleMouseLeave = ()=>{\n        setHoveredSubMenu(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: subMenu ? `sub-dropdown-menu` : `dropdown-menu`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: pathname.includes(buttonHref) && buttonHref !== \"\" ? \"navbar-link dropdown-toggle active\" : isDetailBlogPath() ? \"navbar-link dropdown-toggle whiteBg\" : \"navbar-link dropdown-toggle\",\n                id: \"fade-button\",\n                \"aria-controls\": open ? \"fade-menu\" : undefined,\n                \"aria-haspopup\": \"true\",\n                \"aria-expanded\": open ? \"true\" : undefined,\n                onClick: handleClick,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: pathname.includes(buttonHref) && buttonHref !== \"\" ? \"dropdown-toggle-link active\" : isDetailBlogPath() ? \"dropdown-toggle-link whiteBg\" : \"dropdown-toggle-link\",\n                        href: // Disable href when buttonLabel is \"resources\"\n                        buttonLabel?.toLowerCase() === \"resources\" || buttonLabel?.toLowerCase() === \"ressources\" ? \"#\" : buttonHref ? (0,_utils_functions__WEBPACK_IMPORTED_MODULE_5__.generateLocalizedSlug)(locale, buttonHref).replace(\"/en/\", \"/\") : pathname.replace(\"/en/\", \"/\"),\n                        locale: locale === \"en\" ? \"en\" : \"fr\",\n                        onClick: (e)=>{\n                            // Prevent navigation when buttonLabel is \"resources\"\n                            if (buttonLabel?.toLowerCase() === \"resources\" || buttonLabel?.toLowerCase() === \"ressources\") {\n                                e.preventDefault();\n                                e.stopPropagation();\n                            }\n                        },\n                        sx: {\n                            // Ensure resources text has the same styling as other menu items\n                            ...buttonLabel?.toLowerCase() === \"resources\" || buttonLabel?.toLowerCase() === \"ressources\" ? {\n                                cursor: \"default\",\n                                textDecoration: \"none\",\n                                \"&:hover\": {\n                                    textDecoration: \"none\"\n                                }\n                            } : {}\n                        },\n                        children: withFlag ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            className: \"flag-lang\",\n                            src: selectedFlag.src,\n                            width: 26,\n                            height: 22,\n                            disabled: true,\n                            loading: \"lazy\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 130,\n                            columnNumber: 13\n                        }, undefined) : buttonLabel\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, undefined),\n                    open ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowDown_svg__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 142,\n                        columnNumber: 17\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowUp_svg__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 142,\n                        columnNumber: 36\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                id: \"fade-menu\",\n                MenuListProps: {\n                    \"aria-labelledby\": \"fade-button\"\n                },\n                anchorEl: anchorEl,\n                open: open,\n                onClose: handleClose,\n                TransitionComponent: _barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                disableScrollLock: true,\n                anchorOrigin: {\n                    vertical: \"bottom\",\n                    horizontal: subMenu ? \"right\" : \"left\"\n                },\n                children: menuItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: item.subItems ? `sub-menu dropdown-item` : `dropdown-item`,\n                        onClick: (e)=>handleClick2(e, item),\n                        onMouseEnter: ()=>handleMouseEnter(item, index),\n                        onMouseLeave: handleMouseLeave,\n                        children: item.subItems ? // If the item has a subMenu, render another DropdownMenu\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DropdownMenu, {\n                            buttonLabel: item?.i18nName ? t(item?.i18nName) : item.name,\n                            buttonHref: item.route,\n                            menuItems: item.subItems,\n                            subMenu: true,\n                            forceOpen: hoveredSubMenu === index\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 171,\n                            columnNumber: 15\n                        }, undefined) : item.route ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            href: (0,_utils_functions__WEBPACK_IMPORTED_MODULE_5__.generateLocalizedSlug)(locale, item.route),\n                            locale: locale === \"en\" ? \"en\" : \"fr\",\n                            className: pathname.includes(item.route) ? \"dropdown-item-link active\" : \"dropdown-item-link\",\n                            children: [\n                                item.icon ?? item.icon,\n                                \" \",\n                                item?.i18nName ? t(item?.i18nName) : item.name\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 180,\n                            columnNumber: 15\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Fade_Menu_MenuItem_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"dropdown-item-link\",\n                            href: \"#\",\n                            children: withFlag ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                className: \"flag-lang\",\n                                src: item.flag.src,\n                                width: 26,\n                                height: 22,\n                                loading: \"lazy\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                                lineNumber: 195,\n                                columnNumber: 19\n                            }, undefined) : item?.i18nName ? t(item?.i18nName) : item.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                            lineNumber: 193,\n                            columnNumber: 15\n                        }, undefined)\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\ui\\\\DropdownMenu.jsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DropdownMenu, \"lxglQQ+J5oKa9HopvhsHr1LgQUg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname,\n        react_i18next__WEBPACK_IMPORTED_MODULE_6__.useTranslation\n    ];\n});\n_c = DropdownMenu;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DropdownMenu);\nvar _c;\n$RefreshReg$(_c, \"DropdownMenu\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/DropdownMenu.jsx\n"));

/***/ })

});