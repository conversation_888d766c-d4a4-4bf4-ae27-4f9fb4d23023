"use client";
import LaborLaws from "../LaborLaws";
import { createList } from '@/utils/functions';
export const LABOR_KEYS = {
    workingHours: {
        title: "qatar:QatarLabor:workingHours:title",
    },
    employmentContracts: {
        title: "qatar:QatarLabor:employmentContracts:title",
    },

};
export const qatarLaborData = {
    title: "qatar:QatarLabor:title",
    sections: [
        {
            id: 1,
            title: LABOR_KEYS.workingHours.title,
            subsections: [
                {
                    title: "qatar:QatarLabor:workingHours:title1",
                    description: "qatar:QatarLabor:workingHours:description1",
                },
                {
                    title: "qatar:QatarLabor:workingHours:title2",
                    description: "qatar:QatarLabor:workingHours:description2",
                },
            ],
        },
        {
            id: 2,
            title: LABOR_KEYS.employmentContracts.title,
            subsections: [
                {
                    description: "qatar:QatarLabor:employmentContracts:description",
                },
                {
                    title: "qatar:QatarLabor:employmentContracts:title1",
                    list: createList(["qatar:QatarLabor:employmentContracts:data1", "qatar:QatarLabor:employmentContracts:data2", "qatar:QatarLabor:employmentContracts:data3"]),
                },

            ],
        },
        {
            id: 3,
            title: "qatar:QatarLabor:payroll:title",
            type: "payroll",
            titleKey: "qatar:QatarLabor:payroll:description",
            items: [
                {
                    titleKey: "qatar:QatarLabor:payroll:fiscalYear:title",
                    dateKeys: ["qatar:QatarLabor:payroll:fiscalYear:date1", "qatar:QatarLabor:payroll:fiscalYear:date2"],
                    descriptionKey: "qatar:QatarLabor:payroll:fiscalYear:description",
                },
                {
                    titleKey: "qatar:QatarLabor:payroll:payrollCycle:title",
                    dateKeys: ["qatar:QatarLabor:payroll:payrollCycle:date"],
                    descriptionKey: "qatar:QatarLabor:payroll:payrollCycle:description",
                },
                {
                    titleKey: "qatar:QatarLabor:payroll:minimumWage:title",
                    dateKeys: ["qatar:QatarLabor:payroll:minimumWage:wage", "qatar:QatarLabor:payroll:minimumWage:date"],
                    descriptionKey: "qatar:QatarLabor:payroll:minimumWage:description",
                },
                {
                    titleKey: "qatar:QatarLabor:payroll:payrollManagement:title",
                    dateKeys: ["qatar:QatarLabor:payroll:payrollManagement:date1", "qatar:QatarLabor:payroll:payrollManagement:date2"],
                    descriptionKey: "qatar:QatarLabor:payroll:payrollManagement:description",
                },


            ],
        },
        {
            id: 4,
            title: "qatar:QatarLabor:termination:title",
            subsections: [
                {

                    description: "qatar:QatarLabor:termination:description",
                },
                {

                    description: "qatar:QatarLabor:termination:description1",
                },

                {
                    list: createList(["qatar:QatarLabor:termination:data1", "qatar:QatarLabor:termination:data2", "qatar:QatarLabor:termination:data3", "qatar:QatarLabor:termination:data4", "qatar:QatarLabor:termination:data5"]),
                },
                {
                    title: "qatar:QatarLabor:termination:title2",
                    description: "qatar:QatarLabor:termination:description2",

                },
                {
                    title: "qatar:QatarLabor:termination:title3",
                    description: "qatar:QatarLabor:termination:description3",
                },




            ],
        },
        {
            id: 5,
            title: "qatar:QatarLabor:leaveEntitlements:title",
            type: "leaveEntitlements",
            data: {
                description: "qatar:QatarLabor:leaveEntitlements:description",
                leaves: [
                    { date: "qatar:QatarLabor:leaveEntitlements:leaves:dataS1:date", title: "qatar:QatarLabor:leaveEntitlements:leaves:dataS1:title" },
                    { date: "qatar:QatarLabor:leaveEntitlements:leaves:dataS2:date", title: "qatar:QatarLabor:leaveEntitlements:leaves:dataS2:title" },
                    { date: "qatar:QatarLabor:leaveEntitlements:leaves:dataS3:date", title: "qatar:QatarLabor:leaveEntitlements:leaves:dataS3:title" },
                    { date: "qatar:QatarLabor:leaveEntitlements:leaves:dataS4:date", title: "qatar:QatarLabor:leaveEntitlements:leaves:dataS4:title" },
                ],
                maternityLeave: {
                    title: "qatar:QatarLabor:leaveEntitlements:leaves:maternityLeave:title",
                    description: ["qatar:QatarLabor:leaveEntitlements:leaves:maternityLeave:description1"],
                },
                sickLeave: {
                    title: "qatar:QatarLabor:leaveEntitlements:leaves:sickLeave:title",
                    description: "qatar:QatarLabor:leaveEntitlements:leaves:sickLeave:description",
                },
            },
        },
        {
            id: 6,
            title: "qatar:QatarLabor:tax:title",
            subsections: [

                {
                    title: "qatar:QatarLabor:tax:title1",
                    description: "qatar:QatarLabor:tax:description1"
                },
                {
                    title: "qatar:QatarLabor:tax:title2",
                    description: "qatar:QatarLabor:tax:description2"
                },


            ],
        },
        {
            id: 7,
            title: "qatar:QatarLabor:visa:title",
            subsections: [
                { description: "qatar:QatarLabor:visa:description1" },
                {
                    title: "qatar:QatarLabor:visa:title1",
                },
                {
                    title: "qatar:QatarLabor:visa:title2",
                    description: "qatar:QatarLabor:visa:description2",
                },
                {
                    title: "qatar:QatarLabor:visa:title3",
                    description: "qatar:QatarLabor:visa:description3"
                },
                {
                    list: createList(["qatar:QatarLabor:visa:data1", "qatar:QatarLabor:visa:data2", "qatar:QatarLabor:visa:data3", "qatar:QatarLabor:visa:data4", "qatar:QatarLabor:visa:data5", "qatar:QatarLabor:visa:data6", "qatar:QatarLabor:visa:data7"]),
                },
                { description: "qatar:QatarLabor:visa:description4" },
            ],
        },
    ],
};


export default function QatarLaborData() {
    return <LaborLaws data={qatarLaborData} />;
}
