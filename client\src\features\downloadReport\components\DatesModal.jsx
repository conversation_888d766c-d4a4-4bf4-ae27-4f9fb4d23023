import React from "react";
import { Modal, Box, Typography, IconButton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";



const DatesModal = ({ open, onClose, dates }) => (
  <Modal open={open} onClose={onClose}>
    <Box className="date-modal">
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="h6">All Date</Typography>
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </Box>
      <Box>
        {dates.length === 0 ? (
          <Typography>Aucune date disponible.</Typography>
        ) : (
          dates.map((date, idx) => (
            <Typography key={idx} variant="body2" mb={1}>
              {date}
            </Typography>
          ))
        )}
      </Box>
    </Box>
  </Modal>
);

export default DatesModal;