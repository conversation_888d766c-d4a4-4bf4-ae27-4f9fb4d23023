import { Container } from "@mui/material";

function ComplexityControlSection({ title1, title2, items, description }) {
    return (
        <Container id="approach-payroll-section" className="custom-max-width">
            <p className="heading-h1 text-center">
                {title1} <br /> {title2}
            </p>

            <p className="sub-heading text-center">
                {description}
            </p>

            <div className="locations">
                {items.map(({ icon: Icon, title, description }, index) => (
                    <div className="location-item" key={index}>
                        <p className="label">
                            {" "}
                            {Icon && <span>
                                {" "}
                                <Icon /></span>} {" "}
                            {title}
                        </p>
                        <p className="value paragraph">{description}</p>
                    </div>
                ))}
            </div>
        </Container>
    );
}

export default ComplexityControlSection;
