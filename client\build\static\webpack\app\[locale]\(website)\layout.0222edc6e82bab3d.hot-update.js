"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/layout",{

/***/ "(app-pages-browser)/./src/components/layouts/Header.jsx":
/*!*******************************************!*\
  !*** ./src/components/layouts/Header.jsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Button_Container_Dialog_IconButton_Toolbar_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Button,Container,Dialog,IconButton,Toolbar,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Button_Container_Dialog_IconButton_Toolbar_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Button,Container,Dialog,IconButton,Toolbar,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Button_Container_Dialog_IconButton_Toolbar_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Button,Container,Dialog,IconButton,Toolbar,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/AppBar/AppBar.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Button_Container_Dialog_IconButton_Toolbar_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Button,Container,Dialog,IconButton,Toolbar,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Button_Container_Dialog_IconButton_Toolbar_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Button,Container,Dialog,IconButton,Toolbar,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Toolbar/Toolbar.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Button_Container_Dialog_IconButton_Toolbar_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Button,Container,Dialog,IconButton,Toolbar,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Button_Container_Dialog_IconButton_Toolbar_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Button,Container,Dialog,IconButton,Toolbar,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Button_Container_Dialog_IconButton_Toolbar_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Button,Container,Dialog,IconButton,Toolbar,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _ui_DropdownMenu__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../ui/DropdownMenu */ \"(app-pages-browser)/./src/components/ui/DropdownMenu.jsx\");\n/* harmony import */ var _MobileMenu__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./MobileMenu */ \"(app-pages-browser)/./src/components/layouts/MobileMenu.jsx\");\n/* harmony import */ var _assets_images_icons_openMenu_svg__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../assets/images/icons/openMenu.svg */ \"(app-pages-browser)/./src/assets/images/icons/openMenu.svg\");\n/* harmony import */ var _languageChanger__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../languageChanger */ \"(app-pages-browser)/./src/components/languageChanger.js\");\n/* harmony import */ var _TranslationProvider__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../TranslationProvider */ \"(app-pages-browser)/./src/components/TranslationProvider.js\");\n/* harmony import */ var _ui_MyAccountDropdown__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../ui/MyAccountDropdown */ \"(app-pages-browser)/./src/components/ui/MyAccountDropdown.jsx\");\n/* harmony import */ var _features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/features/auth/hooks/currentUser.hooks */ \"(app-pages-browser)/./src/features/auth/hooks/currentUser.hooks.js\");\n/* harmony import */ var _ui_NotificationComponent__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../ui/NotificationComponent */ \"(app-pages-browser)/./src/components/ui/NotificationComponent.jsx\");\n/* harmony import */ var _assets_images_charte_logo_picto_light_webp__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/assets/images/charte/logo-picto-light.webp */ \"(app-pages-browser)/./src/assets/images/charte/logo-picto-light.webp\");\n/* harmony import */ var _assets_images_charte_logo_picto_dark_webp__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/assets/images/charte/logo-picto-dark.webp */ \"(app-pages-browser)/./src/assets/images/charte/logo-picto-dark.webp\");\n/* harmony import */ var _assets_images_charte_txt_ar_webp__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/assets/images/charte/txt_ar.webp */ \"(app-pages-browser)/./src/assets/images/charte/txt_ar.webp\");\n/* harmony import */ var _assets_images_charte_txt_en_webp__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/assets/images/charte/txt_en.webp */ \"(app-pages-browser)/./src/assets/images/charte/txt_en.webp\");\n/* harmony import */ var _assets_images_charte_txt_zh_webp__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/assets/images/charte/txt_zh.webp */ \"(app-pages-browser)/./src/assets/images/charte/txt_zh.webp\");\n/* harmony import */ var _assets_images_charte_txt_ar_dark_webp__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/assets/images/charte/txt_ar_dark.webp */ \"(app-pages-browser)/./src/assets/images/charte/txt_ar_dark.webp\");\n/* harmony import */ var _assets_images_charte_txt_en_dark_webp__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/assets/images/charte/txt_en_dark.webp */ \"(app-pages-browser)/./src/assets/images/charte/txt_en_dark.webp\");\n/* harmony import */ var _assets_images_charte_txt_zh_dark_webp__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/assets/images/charte/txt_zh_dark.webp */ \"(app-pages-browser)/./src/assets/images/charte/txt_zh_dark.webp\");\n/* harmony import */ var _assets_images_charte_txt_hi_webp__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/assets/images/charte/txt_hi.webp */ \"(app-pages-browser)/./src/assets/images/charte/txt_hi.webp\");\n/* harmony import */ var _assets_images_charte_txt_hi_dark_webp__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/assets/images/charte/txt_hi_dark.webp */ \"(app-pages-browser)/./src/assets/images/charte/txt_hi_dark.webp\");\n/* harmony import */ var _utils_functions__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ../../utils/functions */ \"(app-pages-browser)/./src/utils/functions.js\");\n/* harmony import */ var _helpers_MenuList__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ../../helpers/MenuList */ \"(app-pages-browser)/./src/helpers/MenuList.js\");\n/* harmony import */ var _helpers_routesList__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ../../helpers/routesList */ \"(app-pages-browser)/./src/helpers/routesList.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Header(param) {\n    let { resources, locale, isMobileSSR } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { user } = (0,_features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(false);\n    const [dialogOpen, setDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // State management for hover\n    const [hoveredItem, setHoveredItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const hoverTimeoutRef = useRef(null);\n    // Hover handlers\n    const handleMouseEnter = (event, item, index)=>{\n        if (item.subItems) {\n            if (hoverTimeoutRef.current) {\n                clearTimeout(hoverTimeoutRef.current);\n            }\n            setHoveredItem(index);\n        }\n    };\n    const handleMouseLeave = ()=>{\n        if (hoverTimeoutRef.current) {\n            clearTimeout(hoverTimeoutRef.current);\n        }\n        hoverTimeoutRef.current = setTimeout(()=>{\n            setHoveredItem(null);\n        }, 150);\n    };\n    const isDetailBlogPath = ()=>pathname.includes(\"/blog/\") && !pathname.includes(\"/blog/category/\") && pathname !== \"/blog/\" && pathname !== \"/fr/blog/\";\n    const handleCloseMenu = ()=>{\n        setAnchorEl(null);\n    };\n    const handleCloseDialog = ()=>{\n        setDialogOpen(false);\n    };\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const [drawerOpen, setDrawerOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const theme = (0,_barrel_optimize_names_AppBar_Button_Container_Dialog_IconButton_Toolbar_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"])();\n    const isMobile = (0,_barrel_optimize_names_AppBar_Button_Container_Dialog_IconButton_Toolbar_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"])(theme.breakpoints.down(\"sm\"), {\n        noSsr: true\n    }) || isMobileSSR;\n    const isTablet = (0,_barrel_optimize_names_AppBar_Button_Container_Dialog_IconButton_Toolbar_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"])(theme.breakpoints.down(\"md\"), {\n        noSsr: true\n    });\n    const [scrollClass, setScrollClass] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleDrawerToggle = ()=>{\n        setDrawerOpen(!drawerOpen);\n    };\n    const handleScroll = ()=>{\n        const scrollTop = window.scrollY;\n        if (scrollTop > 50) {\n            setScrollClass(\"scroll\");\n        } else {\n            setScrollClass(\"\");\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        isMobile && handleCloseMenu();\n    }, [\n        isMobile\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        isTablet && handleCloseMenu();\n    }, [\n        isTablet\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>{\n            window.removeEventListener(\"scroll\", handleScroll);\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Button_Container_Dialog_IconButton_Toolbar_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n        id: \"pentabell-header\",\n        position: \"fixed\",\n        elevation: 0,\n        className: scrollClass,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Button_Container_Dialog_IconButton_Toolbar_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n            className: \"custom-max-width\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Button_Container_Dialog_IconButton_Toolbar_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                    disableGutters: true,\n                    sx: {\n                        display: \"flex\",\n                        justifyContent: \"space-between\"\n                    },\n                    id: \"custom-toolbar\",\n                    children: isMobile || isTablet ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Button_Container_Dialog_IconButton_Toolbar_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                className: \"icon-open-menu\",\n                                edge: \"start\",\n                                onClick: handleDrawerToggle,\n                                \"aria-label\": \"Open drawer\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_openMenu_svg__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\layouts\\\\Header.jsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\layouts\\\\Header.jsx\",\n                                lineNumber: 136,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: locale === \"en\" ? \"https://www.pentabell.com/\" : `https://www.pentabell.com/${locale}/`,\n                                className: \"pentabell-logo\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    src: _assets_images_charte_logo_picto_light_webp__WEBPACK_IMPORTED_MODULE_13__[\"default\"].src,\n                                    alt: \"Pentabell : International Staffing agency - Global Job Recruiting\",\n                                    width: 40,\n                                    height: 40\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\layouts\\\\Header.jsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\layouts\\\\Header.jsx\",\n                                lineNumber: 144,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: locale === \"en\" ? \"https://www.pentabell.com/\" : `https://www.pentabell.com/${locale}/`,\n                                className: \"pentabell-logo222\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"logo-container\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            src: isDetailBlogPath() ? _assets_images_charte_logo_picto_dark_webp__WEBPACK_IMPORTED_MODULE_14__[\"default\"].src : _assets_images_charte_logo_picto_light_webp__WEBPACK_IMPORTED_MODULE_13__[\"default\"].src,\n                                            alt: \"Pentabell : International Staffing agency - Global Job Recruiting\",\n                                            className: \"logo-picto\",\n                                            width: 500,\n                                            height: 500\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\layouts\\\\Header.jsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-container\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flip-box\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flip-box-inner\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flip-box-face front\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                alt: \"International Recruitment, Staffing, and Payroll Agency-Pentabell\",\n                                                                src: isDetailBlogPath() ? _assets_images_charte_txt_en_dark_webp__WEBPACK_IMPORTED_MODULE_19__[\"default\"].src : _assets_images_charte_txt_en_webp__WEBPACK_IMPORTED_MODULE_16__[\"default\"].src,\n                                                                width: 500,\n                                                                height: 500\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\layouts\\\\Header.jsx\",\n                                                                lineNumber: 186,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\layouts\\\\Header.jsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flip-box-face back\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                alt: \"International Recruitment, Staffing, and Payroll Agency-Pentabell\",\n                                                                src: isDetailBlogPath() ? _assets_images_charte_txt_ar_dark_webp__WEBPACK_IMPORTED_MODULE_18__[\"default\"].src : _assets_images_charte_txt_ar_webp__WEBPACK_IMPORTED_MODULE_15__[\"default\"].src,\n                                                                width: 500,\n                                                                height: 500\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\layouts\\\\Header.jsx\",\n                                                                lineNumber: 194,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\layouts\\\\Header.jsx\",\n                                                            lineNumber: 193,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flip-box-face top\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                alt: \"International Recruitment, Staffing, and Payroll Agency-Pentabell\",\n                                                                src: isDetailBlogPath() ? _assets_images_charte_txt_zh_dark_webp__WEBPACK_IMPORTED_MODULE_20__[\"default\"].src : _assets_images_charte_txt_zh_webp__WEBPACK_IMPORTED_MODULE_17__[\"default\"].src,\n                                                                width: 500,\n                                                                height: 500\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\layouts\\\\Header.jsx\",\n                                                                lineNumber: 202,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\layouts\\\\Header.jsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flip-box-face bottom\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                alt: \"International Recruitment, Staffing, and Payroll Agency-Pentabell\",\n                                                                src: isDetailBlogPath() ? _assets_images_charte_txt_hi_dark_webp__WEBPACK_IMPORTED_MODULE_22__[\"default\"].src : _assets_images_charte_txt_hi_webp__WEBPACK_IMPORTED_MODULE_21__[\"default\"].src,\n                                                                width: 500,\n                                                                height: 500\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\layouts\\\\Header.jsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\layouts\\\\Header.jsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\layouts\\\\Header.jsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\layouts\\\\Header.jsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\layouts\\\\Header.jsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\layouts\\\\Header.jsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\layouts\\\\Header.jsx\",\n                                lineNumber: 162,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"menu\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"menu\",\n                                        children: [\n                                            _helpers_MenuList__WEBPACK_IMPORTED_MODULE_24__.MenuList.website?.map((item, index)=>{\n                                                const localizedRoute = (0,_utils_functions__WEBPACK_IMPORTED_MODULE_23__.generateLocalizedSlug)(locale, item.route);\n                                                if (item.subItems && item.name === _helpers_routesList__WEBPACK_IMPORTED_MODULE_25__.websiteRoutesList.resources.name) {\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_DropdownMenu__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        i18nName: item.i18nName,\n                                                        locale: locale,\n                                                        buttonLabel: item.i18nName ? t(item.i18nName) : item.name,\n                                                        menuItems: item.subItems\n                                                    }, index, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\layouts\\\\Header.jsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 25\n                                                    }, this);\n                                                } else if (item.subItems) {\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_DropdownMenu__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        i18nName: item.i18nName,\n                                                        locale: locale,\n                                                        buttonLabel: item.i18nName ? t(item.i18nName) : item.name,\n                                                        buttonHref: item.route,\n                                                        menuItems: item.subItems\n                                                    }, index, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\layouts\\\\Header.jsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 25\n                                                    }, this);\n                                                } else {\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Button_Container_Dialog_IconButton_Toolbar_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                        className: `nav-link ${pathname.includes(localizedRoute) ? \"navbar-link active\" : isDetailBlogPath() ? \"navbar-link whiteBg\" : \"navbar-link\"}`,\n                                                        locale: locale === \"en\" ? \"en\" : \"fr\",\n                                                        href: localizedRoute,\n                                                        children: item.i18nName ? t(item.i18nName) : item.name\n                                                    }, item.key, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\layouts\\\\Header.jsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 25\n                                                    }, this);\n                                                }\n                                            }),\n                                            !user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Button_Container_Dialog_IconButton_Toolbar_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                className: `nav-link ${pathname.includes(_helpers_routesList__WEBPACK_IMPORTED_MODULE_25__.authRoutes.login.route) ? \"navbar-link active\" : isDetailBlogPath() ? \"navbar-link whiteBg\" : \"navbar-link\"}`,\n                                                locale: locale === \"en\" ? \"en\" : \"fr\",\n                                                href: (0,_utils_functions__WEBPACK_IMPORTED_MODULE_23__.generateLocalizedSlug)(locale, `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_25__.authRoutes.login.route}`),\n                                                children: _helpers_routesList__WEBPACK_IMPORTED_MODULE_25__.authRoutes.login.i18nName ? t(_helpers_routesList__WEBPACK_IMPORTED_MODULE_25__.authRoutes.login.i18nName) : _helpers_routesList__WEBPACK_IMPORTED_MODULE_25__.authRoutes.login.name\n                                            }, \"login\", false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\layouts\\\\Header.jsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TranslationProvider__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                namespaces: [\n                                                    \"global\"\n                                                ],\n                                                locale: locale,\n                                                resources: resources,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_languageChanger__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\layouts\\\\Header.jsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\layouts\\\\Header.jsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 19\n                                            }, this),\n                                            user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_NotificationComponent__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                pathname: pathname,\n                                                websiteHeaderIcon: true,\n                                                locale: locale,\n                                                isDetailBlogPath: isDetailBlogPath\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\layouts\\\\Header.jsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 21\n                                            }, this) : null\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\layouts\\\\Header.jsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 17\n                                    }, this),\n                                    user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_MyAccountDropdown__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        locale: locale\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\layouts\\\\Header.jsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 25\n                                    }, this) : null\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\layouts\\\\Header.jsx\",\n                                lineNumber: 222,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\layouts\\\\Header.jsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MobileMenu__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    locale: locale,\n                    drawerOpen: drawerOpen,\n                    handleDrawerToggle: handleDrawerToggle\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\layouts\\\\Header.jsx\",\n                    lineNumber: 319,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Button_Container_Dialog_IconButton_Toolbar_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                    open: dialogOpen,\n                    onClose: handleCloseDialog,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_NotificationComponent__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        onClose: handleCloseDialog\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\layouts\\\\Header.jsx\",\n                        lineNumber: 325,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\layouts\\\\Header.jsx\",\n                    lineNumber: 324,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\layouts\\\\Header.jsx\",\n            lineNumber: 128,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\layouts\\\\Header.jsx\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, this);\n}\n_s(Header, \"7oMzgPaAEtgjx30j1thSAqMv8PU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        _features_auth_hooks_currentUser_hooks__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        _barrel_optimize_names_AppBar_Button_Container_Dialog_IconButton_Toolbar_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n        _barrel_optimize_names_AppBar_Button_Container_Dialog_IconButton_Toolbar_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n        _barrel_optimize_names_AppBar_Button_Container_Dialog_IconButton_Toolbar_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"]\n    ];\n});\n_c = Header;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Header);\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2xheW91dHMvSGVhZGVyLmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVd1QjtBQUNxQjtBQUNHO0FBQ0Q7QUFDZjtBQUVlO0FBQ1I7QUFDMkI7QUFDaEI7QUFDUztBQUNGO0FBQ2E7QUFDTDtBQUNjO0FBQ0c7QUFDMUI7QUFDQTtBQUNBO0FBQ1M7QUFDQTtBQUNBO0FBQ1Q7QUFDUztBQUVGO0FBQ1o7QUFDdUI7QUFFekUsU0FBU21DLE9BQU8sS0FBa0M7UUFBbEMsRUFBRUMsU0FBUyxFQUFFQyxNQUFNLEVBQUVDLFdBQVcsRUFBRSxHQUFsQzs7SUFDZCxNQUFNQyxXQUFXNUIsNERBQVdBO0lBQzVCLE1BQU0sQ0FBQzZCLFVBQVVDLFlBQVksR0FBR2hDLCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU0sRUFBRWlDLElBQUksRUFBRSxHQUFHdkIsbUZBQWNBLENBQUM7SUFDaEMsTUFBTSxDQUFDd0IsWUFBWUMsY0FBYyxHQUFHbkMsK0NBQVFBLENBQUM7SUFFN0MsNkJBQTZCO0lBQzdCLE1BQU0sQ0FBQ29DLGFBQWFDLGVBQWUsR0FBR3JDLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU1zQyxrQkFBa0JDLE9BQU87SUFFL0IsaUJBQWlCO0lBQ2pCLE1BQU1DLG1CQUFtQixDQUFDQyxPQUFPQyxNQUFNQztRQUNyQyxJQUFJRCxLQUFLRSxRQUFRLEVBQUU7WUFDakIsSUFBSU4sZ0JBQWdCTyxPQUFPLEVBQUU7Z0JBQzNCQyxhQUFhUixnQkFBZ0JPLE9BQU87WUFDdEM7WUFDQVIsZUFBZU07UUFDakI7SUFDRjtJQUVBLE1BQU1JLG1CQUFtQjtRQUN2QixJQUFJVCxnQkFBZ0JPLE9BQU8sRUFBRTtZQUMzQkMsYUFBYVIsZ0JBQWdCTyxPQUFPO1FBQ3RDO1FBQ0FQLGdCQUFnQk8sT0FBTyxHQUFHRyxXQUFXO1lBQ25DWCxlQUFlO1FBQ2pCLEdBQUc7SUFDTDtJQUVBLE1BQU1ZLG1CQUFtQixJQUN2Qm5CLFNBQVNvQixRQUFRLENBQUMsYUFDbEIsQ0FBQ3BCLFNBQVNvQixRQUFRLENBQUMsc0JBQ25CcEIsYUFBYSxZQUNiQSxhQUFhO0lBRWYsTUFBTXFCLGtCQUFrQjtRQUN0Qm5CLFlBQVk7SUFDZDtJQUVBLE1BQU1vQixvQkFBb0I7UUFDeEJqQixjQUFjO0lBQ2hCO0lBQ0EsTUFBTSxFQUFFa0IsQ0FBQyxFQUFFLEdBQUdwRCw2REFBY0E7SUFDNUIsTUFBTSxDQUFDcUQsWUFBWUMsY0FBYyxHQUFHdkQsK0NBQVFBLENBQUM7SUFDN0MsTUFBTXdELFFBQVEzRCx5SkFBUUE7SUFFdEIsTUFBTTRELFdBQ0ozRCx5SkFBYUEsQ0FBQzBELE1BQU1FLFdBQVcsQ0FBQ0MsSUFBSSxDQUFDLE9BQU87UUFBRUMsT0FBTztJQUFLLE1BQU0vQjtJQUNsRSxNQUFNZ0MsV0FBVy9ELHlKQUFhQSxDQUFDMEQsTUFBTUUsV0FBVyxDQUFDQyxJQUFJLENBQUMsT0FBTztRQUFFQyxPQUFPO0lBQUs7SUFFM0UsTUFBTSxDQUFDRSxhQUFhQyxlQUFlLEdBQUcvRCwrQ0FBUUEsQ0FBQztJQUUvQyxNQUFNZ0UscUJBQXFCO1FBQ3pCVCxjQUFjLENBQUNEO0lBQ2pCO0lBRUEsTUFBTVcsZUFBZTtRQUNuQixNQUFNQyxZQUFZQyxPQUFPQyxPQUFPO1FBQ2hDLElBQUlGLFlBQVksSUFBSTtZQUNsQkgsZUFBZTtRQUNqQixPQUFPO1lBQ0xBLGVBQWU7UUFDakI7SUFDRjtJQUVBaEUsZ0RBQVNBLENBQUM7UUFDUjBELFlBQVlOO0lBQ2QsR0FBRztRQUFDTTtLQUFTO0lBRWIxRCxnREFBU0EsQ0FBQztRQUNSOEQsWUFBWVY7SUFDZCxHQUFHO1FBQUNVO0tBQVM7SUFFYjlELGdEQUFTQSxDQUFDO1FBQ1JvRSxPQUFPRSxnQkFBZ0IsQ0FBQyxVQUFVSjtRQUNsQyxPQUFPO1lBQ0xFLE9BQU9HLG1CQUFtQixDQUFDLFVBQVVMO1FBQ3ZDO0lBQ0YsR0FBRyxFQUFFO0lBRUwscUJBQ0UsOERBQUMxRSxxSkFBTUE7UUFDTGdGLElBQUc7UUFDSEMsVUFBUztRQUNUQyxXQUFXO1FBQ1hDLFdBQVdaO2tCQUVYLDRFQUFDbkUscUpBQVNBO1lBQUMrRSxXQUFVOzs4QkFDbkIsOERBQUNsRixxSkFBT0E7b0JBQ05tRixnQkFBZ0I7b0JBQ2hCQyxJQUFJO3dCQUFFQyxTQUFTO3dCQUFRQyxnQkFBZ0I7b0JBQWdCO29CQUN2RFAsSUFBRzs4QkFFRmQsWUFBWUkseUJBQ1g7OzBDQUNFLDhEQUFDcEUscUpBQVVBO2dDQUNUaUYsV0FBVTtnQ0FDVkssTUFBSztnQ0FDTEMsU0FBU2hCO2dDQUNUaUIsY0FBVzswQ0FFWCw0RUFBQzNFLHlFQUFXQTs7Ozs7Ozs7OzswQ0FFZCw4REFBQzRFO2dDQUNDQyxNQUNFdkQsV0FBVyxPQUNQLCtCQUNBLENBQUMsMEJBQTBCLEVBQUVBLE9BQU8sQ0FBQyxDQUFDO2dDQUU1QzhDLFdBQVU7MENBRVYsNEVBQUN2RSxrREFBS0E7b0NBQ0ppRixLQUFLeEUsb0ZBQWtCQSxDQUFDd0UsR0FBRztvQ0FDM0JDLEtBQUk7b0NBQ0pDLE9BQU87b0NBQ1BDLFFBQVE7Ozs7Ozs7Ozs7OztxREFLZDs7MENBQ0UsOERBQUNMO2dDQUNDQyxNQUNFdkQsV0FBVyxPQUNQLCtCQUNBLENBQUMsMEJBQTBCLEVBQUVBLE9BQU8sQ0FBQyxDQUFDO2dDQUU1QzhDLFdBQVU7MENBRVYsNEVBQUNjO29DQUFJZCxXQUFVOztzREFDYiw4REFBQ3ZFLGtEQUFLQTs0Q0FDSmlGLEtBQ0VuQyxxQkFDSXBDLG1GQUFzQkEsQ0FBQ3VFLEdBQUcsR0FDMUJ4RSxvRkFBa0JBLENBQUN3RSxHQUFHOzRDQUU1QkMsS0FBSTs0Q0FDSlgsV0FBVTs0Q0FDVlksT0FBTzs0Q0FDUEMsUUFBUTs7Ozs7O3NEQUVWLDhEQUFDQzs0Q0FBSWQsV0FBVTtzREFDYiw0RUFBQ2M7Z0RBQUlkLFdBQVU7MERBQ2IsNEVBQUNjO29EQUFJZCxXQUFVOztzRUFDYiw4REFBQ2M7NERBQUlkLFdBQVU7c0VBQ2IsNEVBQUN2RSxrREFBS0E7Z0VBQ0prRixLQUFJO2dFQUNKRCxLQUFLbkMscUJBQXFCL0IsK0VBQVNBLENBQUNrRSxHQUFHLEdBQUdyRSwwRUFBS0EsQ0FBQ3FFLEdBQUc7Z0VBQ25ERSxPQUFPO2dFQUNQQyxRQUFROzs7Ozs7Ozs7OztzRUFHWiw4REFBQ0M7NERBQUlkLFdBQVU7c0VBQ2IsNEVBQUN2RSxrREFBS0E7Z0VBQ0prRixLQUFJO2dFQUNKRCxLQUFLbkMscUJBQXFCaEMsK0VBQVNBLENBQUNtRSxHQUFHLEdBQUd0RSwwRUFBS0EsQ0FBQ3NFLEdBQUc7Z0VBQ25ERSxPQUFPO2dFQUNQQyxRQUFROzs7Ozs7Ozs7OztzRUFHWiw4REFBQ0M7NERBQUlkLFdBQVU7c0VBQ2IsNEVBQUN2RSxrREFBS0E7Z0VBQ0prRixLQUFJO2dFQUNKRCxLQUFLbkMscUJBQXFCOUIsK0VBQVNBLENBQUNpRSxHQUFHLEdBQUdwRSwwRUFBS0EsQ0FBQ29FLEdBQUc7Z0VBQ25ERSxPQUFPO2dFQUNQQyxRQUFROzs7Ozs7Ozs7OztzRUFHWiw4REFBQ0M7NERBQUlkLFdBQVU7c0VBQ2IsNEVBQUN2RSxrREFBS0E7Z0VBQ0prRixLQUFJO2dFQUNKRCxLQUFLbkMscUJBQXFCNUIsK0VBQVNBLENBQUMrRCxHQUFHLEdBQUdoRSwwRUFBS0EsQ0FBQ2dFLEdBQUc7Z0VBQ25ERSxPQUFPO2dFQUNQQyxRQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FRdEIsOERBQUNDO2dDQUFJZCxXQUFVOztrREFDYiw4REFBQ2M7d0NBQUlkLFdBQVU7OzRDQUNabkQsd0RBQVFBLENBQUNrRSxPQUFPLEVBQUVDLElBQUksQ0FBQ2hELE1BQU1DO2dEQUM1QixNQUFNZ0QsaUJBQWlCckUsd0VBQXFCQSxDQUMxQ00sUUFDQWMsS0FBS2tELEtBQUs7Z0RBRVosSUFDRWxELEtBQUtFLFFBQVEsSUFDYkYsS0FBS21ELElBQUksS0FBS3BFLG1FQUFpQkEsQ0FBQ0UsU0FBUyxDQUFDa0UsSUFBSSxFQUM5QztvREFDQSxxQkFDRSw4REFBQ3pGLHdEQUFZQTt3REFFWDBGLFVBQVVwRCxLQUFLb0QsUUFBUTt3REFDdkJsRSxRQUFRQTt3REFDUm1FLGFBQ0VyRCxLQUFLb0QsUUFBUSxHQUFHekMsRUFBRVgsS0FBS29ELFFBQVEsSUFBSXBELEtBQUttRCxJQUFJO3dEQUU5Q0csV0FBV3RELEtBQUtFLFFBQVE7dURBTm5CRDs7Ozs7Z0RBU1gsT0FBTyxJQUFJRCxLQUFLRSxRQUFRLEVBQUU7b0RBQ3hCLHFCQUNFLDhEQUFDeEMsd0RBQVlBO3dEQUVYMEYsVUFBVXBELEtBQUtvRCxRQUFRO3dEQUN2QmxFLFFBQVFBO3dEQUNSbUUsYUFDRXJELEtBQUtvRCxRQUFRLEdBQUd6QyxFQUFFWCxLQUFLb0QsUUFBUSxJQUFJcEQsS0FBS21ELElBQUk7d0RBRTlDSSxZQUFZdkQsS0FBS2tELEtBQUs7d0RBQ3RCSSxXQUFXdEQsS0FBS0UsUUFBUTt1REFQbkJEOzs7OztnREFVWCxPQUFPO29EQUNMLHFCQUNFLDhEQUFDakQscUpBQU1BO3dEQUVMZ0YsV0FBVyxDQUFDLFNBQVMsRUFDbkI1QyxTQUFTb0IsUUFBUSxDQUFDeUMsa0JBQ2QsdUJBQ0ExQyxxQkFDQSx3QkFDQSxjQUNMLENBQUM7d0RBQ0ZyQixRQUFRQSxXQUFXLE9BQU8sT0FBTzt3REFDakN1RCxNQUFNUTtrRUFFTGpELEtBQUtvRCxRQUFRLEdBQUd6QyxFQUFFWCxLQUFLb0QsUUFBUSxJQUFJcEQsS0FBS21ELElBQUk7dURBWHhDbkQsS0FBS3dELEdBQUc7Ozs7O2dEQWNuQjs0Q0FDRjs0Q0FFQyxDQUFDakUsc0JBQ0EsOERBQUN2QyxxSkFBTUE7Z0RBRUxnRixXQUFXLENBQUMsU0FBUyxFQUNuQjVDLFNBQVNvQixRQUFRLENBQUMxQiw0REFBVUEsQ0FBQzJFLEtBQUssQ0FBQ1AsS0FBSyxJQUNwQyx1QkFDQTNDLHFCQUNBLHdCQUNBLGNBQ0wsQ0FBQztnREFDRnJCLFFBQVFBLFdBQVcsT0FBTyxPQUFPO2dEQUNqQ3VELE1BQU03RCx3RUFBcUJBLENBQ3pCTSxRQUNBLENBQUMsQ0FBQyxFQUFFSiw0REFBVUEsQ0FBQzJFLEtBQUssQ0FBQ1AsS0FBSyxDQUFDLENBQUM7MERBRzdCcEUsNERBQVVBLENBQUMyRSxLQUFLLENBQUNMLFFBQVEsR0FDdEJ6QyxFQUFFN0IsNERBQVVBLENBQUMyRSxLQUFLLENBQUNMLFFBQVEsSUFDM0J0RSw0REFBVUEsQ0FBQzJFLEtBQUssQ0FBQ04sSUFBSTsrQ0FoQnBCOzs7OzswREFtQlQsOERBQUNyRiw0REFBb0JBO2dEQUNuQjRGLFlBQVk7b0RBQUM7aURBQVM7Z0RBQ3RCeEUsUUFBUUE7Z0RBQ1JELFdBQVdBOzBEQUVYLDRFQUFDcEIsd0RBQWVBOzs7Ozs7Ozs7OzRDQUVqQjBCLHFCQUNDLDhEQUFDdEIsa0VBQXFCQTtnREFDcEJtQixVQUFVQTtnREFDVnVFLG1CQUFtQjtnREFDbkJ6RSxRQUFRQTtnREFDUnFCLGtCQUFrQkE7Ozs7O3VEQUVsQjs7Ozs7OztvQ0FFTGhCLHFCQUFPLDhEQUFDeEIsOERBQWlCQTt3Q0FBQ21CLFFBQVFBOzs7OzsrQ0FBYTs7Ozs7Ozs7Ozs7Ozs7OEJBS3hELDhEQUFDdkIsbURBQVVBO29CQUNUdUIsUUFBUUE7b0JBQ1IwQixZQUFZQTtvQkFDWlUsb0JBQW9CQTs7Ozs7OzhCQUV0Qiw4REFBQ3BFLHFKQUFNQTtvQkFBQzBHLE1BQU1wRTtvQkFBWXFFLFNBQVNuRDs4QkFDakMsNEVBQUN6QyxrRUFBcUJBO3dCQUFDNEYsU0FBU25EOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSzFDO0dBalNTMUI7O1FBQ1V4Qix3REFBV0E7UUFFWFEsK0VBQWNBO1FBdUNqQlQseURBQWNBO1FBRWRKLHFKQUFRQTtRQUdwQkMscUpBQWFBO1FBQ0VBLHFKQUFhQTs7O0tBaER2QjRCO0FBbVNULCtEQUFlQSxNQUFNQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL2xheW91dHMvSGVhZGVyLmpzeD85YTk4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xyXG5cclxuaW1wb3J0IHtcclxuICBBcHBCYXIsXHJcbiAgVG9vbGJhcixcclxuICBJY29uQnV0dG9uLFxyXG4gIEJ1dHRvbixcclxuICBDb250YWluZXIsXHJcbiAgRGlhbG9nLFxyXG4gIHVzZVRoZW1lLFxyXG4gIHVzZU1lZGlhUXVlcnksXHJcbn0gZnJvbSBcIkBtdWkvbWF0ZXJpYWxcIjtcclxuaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gXCJyZWFjdFwiO1xyXG5pbXBvcnQgeyB1c2VUcmFuc2xhdGlvbiB9IGZyb20gXCJyZWFjdC1pMThuZXh0XCI7XHJcbmltcG9ydCB7IHVzZVBhdGhuYW1lIH0gZnJvbSBcIm5leHQvbmF2aWdhdGlvblwiO1xyXG5pbXBvcnQgSW1hZ2UgZnJvbSBcIm5leHQvaW1hZ2VcIjtcclxuXHJcbmltcG9ydCBEcm9wZG93bk1lbnUgZnJvbSBcIi4uL3VpL0Ryb3Bkb3duTWVudVwiO1xyXG5pbXBvcnQgTW9iaWxlTWVudSBmcm9tIFwiLi9Nb2JpbGVNZW51XCI7XHJcbmltcG9ydCBTdmdPcGVuTWVudSBmcm9tIFwiLi4vLi4vYXNzZXRzL2ltYWdlcy9pY29ucy9vcGVuTWVudS5zdmdcIjtcclxuaW1wb3J0IExhbmd1YWdlQ2hhbmdlciBmcm9tIFwiLi4vbGFuZ3VhZ2VDaGFuZ2VyXCI7XHJcbmltcG9ydCBUcmFuc2xhdGlvbnNQcm92aWRlciBmcm9tIFwiLi4vVHJhbnNsYXRpb25Qcm92aWRlclwiO1xyXG5pbXBvcnQgTXlBY2NvdW50RHJvcGRvd24gZnJvbSBcIi4uL3VpL015QWNjb3VudERyb3Bkb3duXCI7XHJcbmltcG9ydCB1c2VDdXJyZW50VXNlciBmcm9tIFwiQC9mZWF0dXJlcy9hdXRoL2hvb2tzL2N1cnJlbnRVc2VyLmhvb2tzXCI7XHJcbmltcG9ydCBOb3RpZmljYXRpb25Db21wb25lbnQgZnJvbSBcIi4uL3VpL05vdGlmaWNhdGlvbkNvbXBvbmVudFwiO1xyXG5pbXBvcnQgcGljdG9Mb2dvUGVudGFiZWxsIGZyb20gXCJAL2Fzc2V0cy9pbWFnZXMvY2hhcnRlL2xvZ28tcGljdG8tbGlnaHQud2VicFwiO1xyXG5pbXBvcnQgcGljdG9Mb2dvUGVudGFiZWxsRGFyayBmcm9tIFwiQC9hc3NldHMvaW1hZ2VzL2NoYXJ0ZS9sb2dvLXBpY3RvLWRhcmsud2VicFwiO1xyXG5pbXBvcnQgdHh0QXIgZnJvbSBcIkAvYXNzZXRzL2ltYWdlcy9jaGFydGUvdHh0X2FyLndlYnBcIjtcclxuaW1wb3J0IHR4dEVuIGZyb20gXCJAL2Fzc2V0cy9pbWFnZXMvY2hhcnRlL3R4dF9lbi53ZWJwXCI7XHJcbmltcG9ydCB0eHRaaCBmcm9tIFwiQC9hc3NldHMvaW1hZ2VzL2NoYXJ0ZS90eHRfemgud2VicFwiO1xyXG5pbXBvcnQgdHh0QXJEYXJrIGZyb20gXCJAL2Fzc2V0cy9pbWFnZXMvY2hhcnRlL3R4dF9hcl9kYXJrLndlYnBcIjtcclxuaW1wb3J0IHR4dEVuRGFyayBmcm9tIFwiQC9hc3NldHMvaW1hZ2VzL2NoYXJ0ZS90eHRfZW5fZGFyay53ZWJwXCI7XHJcbmltcG9ydCB0eHRaaERhcmsgZnJvbSBcIkAvYXNzZXRzL2ltYWdlcy9jaGFydGUvdHh0X3poX2Rhcmsud2VicFwiO1xyXG5pbXBvcnQgdHh0SGkgZnJvbSBcIkAvYXNzZXRzL2ltYWdlcy9jaGFydGUvdHh0X2hpLndlYnBcIjtcclxuaW1wb3J0IHR4dEhpRGFyayBmcm9tIFwiQC9hc3NldHMvaW1hZ2VzL2NoYXJ0ZS90eHRfaGlfZGFyay53ZWJwXCI7XHJcblxyXG5pbXBvcnQgeyBnZW5lcmF0ZUxvY2FsaXplZFNsdWcgfSBmcm9tIFwiLi4vLi4vdXRpbHMvZnVuY3Rpb25zXCI7XHJcbmltcG9ydCB7IE1lbnVMaXN0IH0gZnJvbSBcIi4uLy4uL2hlbHBlcnMvTWVudUxpc3RcIjtcclxuaW1wb3J0IHsgYXV0aFJvdXRlcywgd2Vic2l0ZVJvdXRlc0xpc3QgfSBmcm9tIFwiLi4vLi4vaGVscGVycy9yb3V0ZXNMaXN0XCI7XHJcblxyXG5mdW5jdGlvbiBIZWFkZXIoeyByZXNvdXJjZXMsIGxvY2FsZSwgaXNNb2JpbGVTU1IgfSkge1xyXG4gIGNvbnN0IHBhdGhuYW1lID0gdXNlUGF0aG5hbWUoKTtcclxuICBjb25zdCBbYW5jaG9yRWwsIHNldEFuY2hvckVsXSA9IHVzZVN0YXRlKG51bGwpO1xyXG4gIGNvbnN0IHsgdXNlciB9ID0gdXNlQ3VycmVudFVzZXIoZmFsc2UpO1xyXG4gIGNvbnN0IFtkaWFsb2dPcGVuLCBzZXREaWFsb2dPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuXHJcbiAgLy8gU3RhdGUgbWFuYWdlbWVudCBmb3IgaG92ZXJcclxuICBjb25zdCBbaG92ZXJlZEl0ZW0sIHNldEhvdmVyZWRJdGVtXSA9IHVzZVN0YXRlKG51bGwpO1xyXG4gIGNvbnN0IGhvdmVyVGltZW91dFJlZiA9IHVzZVJlZihudWxsKTtcclxuXHJcbiAgLy8gSG92ZXIgaGFuZGxlcnNcclxuICBjb25zdCBoYW5kbGVNb3VzZUVudGVyID0gKGV2ZW50LCBpdGVtLCBpbmRleCkgPT4ge1xyXG4gICAgaWYgKGl0ZW0uc3ViSXRlbXMpIHtcclxuICAgICAgaWYgKGhvdmVyVGltZW91dFJlZi5jdXJyZW50KSB7XHJcbiAgICAgICAgY2xlYXJUaW1lb3V0KGhvdmVyVGltZW91dFJlZi5jdXJyZW50KTtcclxuICAgICAgfVxyXG4gICAgICBzZXRIb3ZlcmVkSXRlbShpbmRleCk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlTW91c2VMZWF2ZSA9ICgpID0+IHtcclxuICAgIGlmIChob3ZlclRpbWVvdXRSZWYuY3VycmVudCkge1xyXG4gICAgICBjbGVhclRpbWVvdXQoaG92ZXJUaW1lb3V0UmVmLmN1cnJlbnQpO1xyXG4gICAgfVxyXG4gICAgaG92ZXJUaW1lb3V0UmVmLmN1cnJlbnQgPSBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgc2V0SG92ZXJlZEl0ZW0obnVsbCk7XHJcbiAgICB9LCAxNTApO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGlzRGV0YWlsQmxvZ1BhdGggPSAoKSA9PlxyXG4gICAgcGF0aG5hbWUuaW5jbHVkZXMoXCIvYmxvZy9cIikgJiZcclxuICAgICFwYXRobmFtZS5pbmNsdWRlcyhcIi9ibG9nL2NhdGVnb3J5L1wiKSAmJlxyXG4gICAgcGF0aG5hbWUgIT09IFwiL2Jsb2cvXCIgJiZcclxuICAgIHBhdGhuYW1lICE9PSBcIi9mci9ibG9nL1wiO1xyXG5cclxuICBjb25zdCBoYW5kbGVDbG9zZU1lbnUgPSAoKSA9PiB7XHJcbiAgICBzZXRBbmNob3JFbChudWxsKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVDbG9zZURpYWxvZyA9ICgpID0+IHtcclxuICAgIHNldERpYWxvZ09wZW4oZmFsc2UpO1xyXG4gIH07XHJcbiAgY29uc3QgeyB0IH0gPSB1c2VUcmFuc2xhdGlvbigpO1xyXG4gIGNvbnN0IFtkcmF3ZXJPcGVuLCBzZXREcmF3ZXJPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCB0aGVtZSA9IHVzZVRoZW1lKCk7XHJcblxyXG4gIGNvbnN0IGlzTW9iaWxlID1cclxuICAgIHVzZU1lZGlhUXVlcnkodGhlbWUuYnJlYWtwb2ludHMuZG93bihcInNtXCIpLCB7IG5vU3NyOiB0cnVlIH0pIHx8IGlzTW9iaWxlU1NSO1xyXG4gIGNvbnN0IGlzVGFibGV0ID0gdXNlTWVkaWFRdWVyeSh0aGVtZS5icmVha3BvaW50cy5kb3duKFwibWRcIiksIHsgbm9Tc3I6IHRydWUgfSk7XHJcblxyXG4gIGNvbnN0IFtzY3JvbGxDbGFzcywgc2V0U2Nyb2xsQ2xhc3NdID0gdXNlU3RhdGUoXCJcIik7XHJcblxyXG4gIGNvbnN0IGhhbmRsZURyYXdlclRvZ2dsZSA9ICgpID0+IHtcclxuICAgIHNldERyYXdlck9wZW4oIWRyYXdlck9wZW4pO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZVNjcm9sbCA9ICgpID0+IHtcclxuICAgIGNvbnN0IHNjcm9sbFRvcCA9IHdpbmRvdy5zY3JvbGxZO1xyXG4gICAgaWYgKHNjcm9sbFRvcCA+IDUwKSB7XHJcbiAgICAgIHNldFNjcm9sbENsYXNzKFwic2Nyb2xsXCIpO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgc2V0U2Nyb2xsQ2xhc3MoXCJcIik7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlzTW9iaWxlICYmIGhhbmRsZUNsb3NlTWVudSgpO1xyXG4gIH0sIFtpc01vYmlsZV0pO1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaXNUYWJsZXQgJiYgaGFuZGxlQ2xvc2VNZW51KCk7XHJcbiAgfSwgW2lzVGFibGV0XSk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcihcInNjcm9sbFwiLCBoYW5kbGVTY3JvbGwpO1xyXG4gICAgcmV0dXJuICgpID0+IHtcclxuICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJzY3JvbGxcIiwgaGFuZGxlU2Nyb2xsKTtcclxuICAgIH07XHJcbiAgfSwgW10pO1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPEFwcEJhclxyXG4gICAgICBpZD1cInBlbnRhYmVsbC1oZWFkZXJcIlxyXG4gICAgICBwb3NpdGlvbj1cImZpeGVkXCJcclxuICAgICAgZWxldmF0aW9uPXswfVxyXG4gICAgICBjbGFzc05hbWU9e3Njcm9sbENsYXNzfVxyXG4gICAgPlxyXG4gICAgICA8Q29udGFpbmVyIGNsYXNzTmFtZT1cImN1c3RvbS1tYXgtd2lkdGhcIj5cclxuICAgICAgICA8VG9vbGJhclxyXG4gICAgICAgICAgZGlzYWJsZUd1dHRlcnM9e3RydWV9XHJcbiAgICAgICAgICBzeD17eyBkaXNwbGF5OiBcImZsZXhcIiwganVzdGlmeUNvbnRlbnQ6IFwic3BhY2UtYmV0d2VlblwiIH19XHJcbiAgICAgICAgICBpZD1cImN1c3RvbS10b29sYmFyXCJcclxuICAgICAgICA+XHJcbiAgICAgICAgICB7aXNNb2JpbGUgfHwgaXNUYWJsZXQgPyAoXHJcbiAgICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgICAgPEljb25CdXR0b25cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImljb24tb3Blbi1tZW51XCJcclxuICAgICAgICAgICAgICAgIGVkZ2U9XCJzdGFydFwiXHJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVEcmF3ZXJUb2dnbGV9XHJcbiAgICAgICAgICAgICAgICBhcmlhLWxhYmVsPVwiT3BlbiBkcmF3ZXJcIlxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIDxTdmdPcGVuTWVudSAvPlxyXG4gICAgICAgICAgICAgIDwvSWNvbkJ1dHRvbj5cclxuICAgICAgICAgICAgICA8YVxyXG4gICAgICAgICAgICAgICAgaHJlZj17XHJcbiAgICAgICAgICAgICAgICAgIGxvY2FsZSA9PT0gXCJlblwiXHJcbiAgICAgICAgICAgICAgICAgICAgPyBcImh0dHBzOi8vd3d3LnBlbnRhYmVsbC5jb20vXCJcclxuICAgICAgICAgICAgICAgICAgICA6IGBodHRwczovL3d3dy5wZW50YWJlbGwuY29tLyR7bG9jYWxlfS9gXHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwZW50YWJlbGwtbG9nb1wiXHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPEltYWdlXHJcbiAgICAgICAgICAgICAgICAgIHNyYz17cGljdG9Mb2dvUGVudGFiZWxsLnNyY31cclxuICAgICAgICAgICAgICAgICAgYWx0PVwiUGVudGFiZWxsIDogSW50ZXJuYXRpb25hbCBTdGFmZmluZyBhZ2VuY3kgLSBHbG9iYWwgSm9iIFJlY3J1aXRpbmdcIlxyXG4gICAgICAgICAgICAgICAgICB3aWR0aD17NDB9XHJcbiAgICAgICAgICAgICAgICAgIGhlaWdodD17NDB9XHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIDwvYT5cclxuICAgICAgICAgICAgPC8+XHJcbiAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICA8PlxyXG4gICAgICAgICAgICAgIDxhXHJcbiAgICAgICAgICAgICAgICBocmVmPXtcclxuICAgICAgICAgICAgICAgICAgbG9jYWxlID09PSBcImVuXCJcclxuICAgICAgICAgICAgICAgICAgICA/IFwiaHR0cHM6Ly93d3cucGVudGFiZWxsLmNvbS9cIlxyXG4gICAgICAgICAgICAgICAgICAgIDogYGh0dHBzOi8vd3d3LnBlbnRhYmVsbC5jb20vJHtsb2NhbGV9L2BcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInBlbnRhYmVsbC1sb2dvMjIyXCJcclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxvZ28tY29udGFpbmVyXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxJbWFnZVxyXG4gICAgICAgICAgICAgICAgICAgIHNyYz17XHJcbiAgICAgICAgICAgICAgICAgICAgICBpc0RldGFpbEJsb2dQYXRoKClcclxuICAgICAgICAgICAgICAgICAgICAgICAgPyBwaWN0b0xvZ29QZW50YWJlbGxEYXJrLnNyY1xyXG4gICAgICAgICAgICAgICAgICAgICAgICA6IHBpY3RvTG9nb1BlbnRhYmVsbC5zcmNcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgYWx0PVwiUGVudGFiZWxsIDogSW50ZXJuYXRpb25hbCBTdGFmZmluZyBhZ2VuY3kgLSBHbG9iYWwgSm9iIFJlY3J1aXRpbmdcIlxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImxvZ28tcGljdG9cIlxyXG4gICAgICAgICAgICAgICAgICAgIHdpZHRoPXs1MDB9XHJcbiAgICAgICAgICAgICAgICAgICAgaGVpZ2h0PXs1MDB9XHJcbiAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jb250YWluZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsaXAtYm94XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsaXAtYm94LWlubmVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxpcC1ib3gtZmFjZSBmcm9udFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxJbWFnZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYWx0PVwiSW50ZXJuYXRpb25hbCBSZWNydWl0bWVudCwgU3RhZmZpbmcsIGFuZCBQYXlyb2xsIEFnZW5jeS1QZW50YWJlbGxcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3JjPXtpc0RldGFpbEJsb2dQYXRoKCkgPyB0eHRFbkRhcmsuc3JjIDogdHh0RW4uc3JjfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg9ezUwMH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhlaWdodD17NTAwfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsaXAtYm94LWZhY2UgYmFja1wiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxJbWFnZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYWx0PVwiSW50ZXJuYXRpb25hbCBSZWNydWl0bWVudCwgU3RhZmZpbmcsIGFuZCBQYXlyb2xsIEFnZW5jeS1QZW50YWJlbGxcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3JjPXtpc0RldGFpbEJsb2dQYXRoKCkgPyB0eHRBckRhcmsuc3JjIDogdHh0QXIuc3JjfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg9ezUwMH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhlaWdodD17NTAwfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsaXAtYm94LWZhY2UgdG9wXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEltYWdlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbHQ9XCJJbnRlcm5hdGlvbmFsIFJlY3J1aXRtZW50LCBTdGFmZmluZywgYW5kIFBheXJvbGwgQWdlbmN5LVBlbnRhYmVsbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzcmM9e2lzRGV0YWlsQmxvZ1BhdGgoKSA/IHR4dFpoRGFyay5zcmMgOiB0eHRaaC5zcmN9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aD17NTAwfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaGVpZ2h0PXs1MDB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxpcC1ib3gtZmFjZSBib3R0b21cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8SW1hZ2VcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFsdD1cIkludGVybmF0aW9uYWwgUmVjcnVpdG1lbnQsIFN0YWZmaW5nLCBhbmQgUGF5cm9sbCBBZ2VuY3ktUGVudGFiZWxsXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNyYz17aXNEZXRhaWxCbG9nUGF0aCgpID8gdHh0SGlEYXJrLnNyYyA6IHR4dEhpLnNyY31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoPXs1MDB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBoZWlnaHQ9ezUwMH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvYT5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1lbnVcIj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWVudVwiPlxyXG4gICAgICAgICAgICAgICAgICB7TWVudUxpc3Qud2Vic2l0ZT8ubWFwKChpdGVtLCBpbmRleCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGxvY2FsaXplZFJvdXRlID0gZ2VuZXJhdGVMb2NhbGl6ZWRTbHVnKFxyXG4gICAgICAgICAgICAgICAgICAgICAgbG9jYWxlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgaXRlbS5yb3V0ZVxyXG4gICAgICAgICAgICAgICAgICAgICk7XHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgaXRlbS5zdWJJdGVtcyAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgaXRlbS5uYW1lID09PSB3ZWJzaXRlUm91dGVzTGlzdC5yZXNvdXJjZXMubmFtZVxyXG4gICAgICAgICAgICAgICAgICAgICkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPERyb3Bkb3duTWVudVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17aW5kZXh9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaTE4bk5hbWU9e2l0ZW0uaTE4bk5hbWV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgbG9jYWxlPXtsb2NhbGV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYnV0dG9uTGFiZWw9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaXRlbS5pMThuTmFtZSA/IHQoaXRlbS5pMThuTmFtZSkgOiBpdGVtLm5hbWVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgbWVudUl0ZW1zPXtpdGVtLnN1Ykl0ZW1zfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKGl0ZW0uc3ViSXRlbXMpIHtcclxuICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxEcm9wZG93bk1lbnVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e2luZGV4fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGkxOG5OYW1lPXtpdGVtLmkxOG5OYW1lfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGxvY2FsZT17bG9jYWxlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGJ1dHRvbkxhYmVsPXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGl0ZW0uaTE4bk5hbWUgPyB0KGl0ZW0uaTE4bk5hbWUpIDogaXRlbS5uYW1lXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGJ1dHRvbkhyZWY9e2l0ZW0ucm91dGV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgbWVudUl0ZW1zPXtpdGVtLnN1Ykl0ZW1zfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17aXRlbS5rZXl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgbmF2LWxpbmsgJHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBhdGhuYW1lLmluY2x1ZGVzKGxvY2FsaXplZFJvdXRlKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwibmF2YmFyLWxpbmsgYWN0aXZlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBpc0RldGFpbEJsb2dQYXRoKClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcIm5hdmJhci1saW5rIHdoaXRlQmdcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwibmF2YmFyLWxpbmtcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGxvY2FsZT17bG9jYWxlID09PSBcImVuXCIgPyBcImVuXCIgOiBcImZyXCJ9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaHJlZj17bG9jYWxpemVkUm91dGV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICB7aXRlbS5pMThuTmFtZSA/IHQoaXRlbS5pMThuTmFtZSkgOiBpdGVtLm5hbWV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgIH0pfVxyXG5cclxuICAgICAgICAgICAgICAgICAgeyF1c2VyICYmIChcclxuICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICBrZXk9e1wibG9naW5cIn1cclxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YG5hdi1saW5rICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHBhdGhuYW1lLmluY2x1ZGVzKGF1dGhSb3V0ZXMubG9naW4ucm91dGUpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcIm5hdmJhci1saW5rIGFjdGl2ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgOiBpc0RldGFpbEJsb2dQYXRoKClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwibmF2YmFyLWxpbmsgd2hpdGVCZ1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcIm5hdmJhci1saW5rXCJcclxuICAgICAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgbG9jYWxlPXtsb2NhbGUgPT09IFwiZW5cIiA/IFwiZW5cIiA6IFwiZnJcIn1cclxuICAgICAgICAgICAgICAgICAgICAgIGhyZWY9e2dlbmVyYXRlTG9jYWxpemVkU2x1ZyhcclxuICAgICAgICAgICAgICAgICAgICAgICAgbG9jYWxlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBgLyR7YXV0aFJvdXRlcy5sb2dpbi5yb3V0ZX1gXHJcbiAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgIHthdXRoUm91dGVzLmxvZ2luLmkxOG5OYW1lXHJcbiAgICAgICAgICAgICAgICAgICAgICAgID8gdChhdXRoUm91dGVzLmxvZ2luLmkxOG5OYW1lKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA6IGF1dGhSb3V0ZXMubG9naW4ubmFtZX1cclxuICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgPFRyYW5zbGF0aW9uc1Byb3ZpZGVyXHJcbiAgICAgICAgICAgICAgICAgICAgbmFtZXNwYWNlcz17W1wiZ2xvYmFsXCJdfVxyXG4gICAgICAgICAgICAgICAgICAgIGxvY2FsZT17bG9jYWxlfVxyXG4gICAgICAgICAgICAgICAgICAgIHJlc291cmNlcz17cmVzb3VyY2VzfVxyXG4gICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgPExhbmd1YWdlQ2hhbmdlciAvPlxyXG4gICAgICAgICAgICAgICAgICA8L1RyYW5zbGF0aW9uc1Byb3ZpZGVyPlxyXG4gICAgICAgICAgICAgICAgICB7dXNlciA/IChcclxuICAgICAgICAgICAgICAgICAgICA8Tm90aWZpY2F0aW9uQ29tcG9uZW50XHJcbiAgICAgICAgICAgICAgICAgICAgICBwYXRobmFtZT17cGF0aG5hbWV9XHJcbiAgICAgICAgICAgICAgICAgICAgICB3ZWJzaXRlSGVhZGVySWNvbj17dHJ1ZX1cclxuICAgICAgICAgICAgICAgICAgICAgIGxvY2FsZT17bG9jYWxlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgaXNEZXRhaWxCbG9nUGF0aD17aXNEZXRhaWxCbG9nUGF0aH1cclxuICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICApIDogbnVsbH1cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAge3VzZXIgPyA8TXlBY2NvdW50RHJvcGRvd24gbG9jYWxlPXtsb2NhbGV9IC8+IDogbnVsbH1cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC8+XHJcbiAgICAgICAgICApfVxyXG4gICAgICAgIDwvVG9vbGJhcj5cclxuICAgICAgICA8TW9iaWxlTWVudVxyXG4gICAgICAgICAgbG9jYWxlPXtsb2NhbGV9XHJcbiAgICAgICAgICBkcmF3ZXJPcGVuPXtkcmF3ZXJPcGVufVxyXG4gICAgICAgICAgaGFuZGxlRHJhd2VyVG9nZ2xlPXtoYW5kbGVEcmF3ZXJUb2dnbGV9XHJcbiAgICAgICAgLz5cclxuICAgICAgICA8RGlhbG9nIG9wZW49e2RpYWxvZ09wZW59IG9uQ2xvc2U9e2hhbmRsZUNsb3NlRGlhbG9nfT5cclxuICAgICAgICAgIDxOb3RpZmljYXRpb25Db21wb25lbnQgb25DbG9zZT17aGFuZGxlQ2xvc2VEaWFsb2d9IC8+XHJcbiAgICAgICAgPC9EaWFsb2c+XHJcbiAgICAgIDwvQ29udGFpbmVyPlxyXG4gICAgPC9BcHBCYXI+XHJcbiAgKTtcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgSGVhZGVyO1xyXG4iXSwibmFtZXMiOlsiQXBwQmFyIiwiVG9vbGJhciIsIkljb25CdXR0b24iLCJCdXR0b24iLCJDb250YWluZXIiLCJEaWFsb2ciLCJ1c2VUaGVtZSIsInVzZU1lZGlhUXVlcnkiLCJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsInVzZVRyYW5zbGF0aW9uIiwidXNlUGF0aG5hbWUiLCJJbWFnZSIsIkRyb3Bkb3duTWVudSIsIk1vYmlsZU1lbnUiLCJTdmdPcGVuTWVudSIsIkxhbmd1YWdlQ2hhbmdlciIsIlRyYW5zbGF0aW9uc1Byb3ZpZGVyIiwiTXlBY2NvdW50RHJvcGRvd24iLCJ1c2VDdXJyZW50VXNlciIsIk5vdGlmaWNhdGlvbkNvbXBvbmVudCIsInBpY3RvTG9nb1BlbnRhYmVsbCIsInBpY3RvTG9nb1BlbnRhYmVsbERhcmsiLCJ0eHRBciIsInR4dEVuIiwidHh0WmgiLCJ0eHRBckRhcmsiLCJ0eHRFbkRhcmsiLCJ0eHRaaERhcmsiLCJ0eHRIaSIsInR4dEhpRGFyayIsImdlbmVyYXRlTG9jYWxpemVkU2x1ZyIsIk1lbnVMaXN0IiwiYXV0aFJvdXRlcyIsIndlYnNpdGVSb3V0ZXNMaXN0IiwiSGVhZGVyIiwicmVzb3VyY2VzIiwibG9jYWxlIiwiaXNNb2JpbGVTU1IiLCJwYXRobmFtZSIsImFuY2hvckVsIiwic2V0QW5jaG9yRWwiLCJ1c2VyIiwiZGlhbG9nT3BlbiIsInNldERpYWxvZ09wZW4iLCJob3ZlcmVkSXRlbSIsInNldEhvdmVyZWRJdGVtIiwiaG92ZXJUaW1lb3V0UmVmIiwidXNlUmVmIiwiaGFuZGxlTW91c2VFbnRlciIsImV2ZW50IiwiaXRlbSIsImluZGV4Iiwic3ViSXRlbXMiLCJjdXJyZW50IiwiY2xlYXJUaW1lb3V0IiwiaGFuZGxlTW91c2VMZWF2ZSIsInNldFRpbWVvdXQiLCJpc0RldGFpbEJsb2dQYXRoIiwiaW5jbHVkZXMiLCJoYW5kbGVDbG9zZU1lbnUiLCJoYW5kbGVDbG9zZURpYWxvZyIsInQiLCJkcmF3ZXJPcGVuIiwic2V0RHJhd2VyT3BlbiIsInRoZW1lIiwiaXNNb2JpbGUiLCJicmVha3BvaW50cyIsImRvd24iLCJub1NzciIsImlzVGFibGV0Iiwic2Nyb2xsQ2xhc3MiLCJzZXRTY3JvbGxDbGFzcyIsImhhbmRsZURyYXdlclRvZ2dsZSIsImhhbmRsZVNjcm9sbCIsInNjcm9sbFRvcCIsIndpbmRvdyIsInNjcm9sbFkiLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsImlkIiwicG9zaXRpb24iLCJlbGV2YXRpb24iLCJjbGFzc05hbWUiLCJkaXNhYmxlR3V0dGVycyIsInN4IiwiZGlzcGxheSIsImp1c3RpZnlDb250ZW50IiwiZWRnZSIsIm9uQ2xpY2siLCJhcmlhLWxhYmVsIiwiYSIsImhyZWYiLCJzcmMiLCJhbHQiLCJ3aWR0aCIsImhlaWdodCIsImRpdiIsIndlYnNpdGUiLCJtYXAiLCJsb2NhbGl6ZWRSb3V0ZSIsInJvdXRlIiwibmFtZSIsImkxOG5OYW1lIiwiYnV0dG9uTGFiZWwiLCJtZW51SXRlbXMiLCJidXR0b25IcmVmIiwia2V5IiwibG9naW4iLCJuYW1lc3BhY2VzIiwid2Vic2l0ZUhlYWRlckljb24iLCJvcGVuIiwib25DbG9zZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layouts/Header.jsx\n"));

/***/ })

});