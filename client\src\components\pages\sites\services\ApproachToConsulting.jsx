import { Container } from "@mui/material";

import SvgfinancialIcon from "@/assets/images/services/icons/financialIcon.svg";
import SvgoperationalIcon from "@/assets/images/services/icons/operationalIcon.svg";
import SvgmanagementIcon from "@/assets/images/services/icons/managementIcon.svg";
import SvgbusinessIcon from "@/assets/images/services/icons/businessIcon.svg";
import initTranslations from "@/app/i18n";

async function ApproachToConsulting({ locale }) {

  const { t, resources } = await initTranslations(locale, [
    "consultingServices",
    "global",
  ]);
  return (
    <Container id="approach-payroll-section" className="custom-max-width">
      <h2 className="heading-h1 text-center">
        {t("consultingServices:approachToConsulting:title")}
      </h2>
      <p className="sub-heading text-center">
        {t("consultingServices:approachToConsulting:description")}{" "}
      </p>
      <div className="locations">
        <div className="location-item  four-items">
          <h3 className="label">
            {" "}
            <span>
              {" "}
              <SvgbusinessIcon />{" "}
            </span>{" "}
            {t(
              "consultingServices:approachToConsulting:businessStrategy:title"
            )}
          </h3>
          <p className="value paragraph">
            {t(
              "consultingServices:approachToConsulting:businessStrategy:description"
            )}
          </p>
        </div>
        <div className="location-item  four-items">
          <h3 className="label">
            {" "}
            <span>
              {" "}
              <SvgmanagementIcon />{" "}
            </span>{" "}
            {t(
              "consultingServices:approachToConsulting:changeManagement:title"
            )}
          </h3>
          <p className="value paragraph">
            {t(
              "consultingServices:approachToConsulting:changeManagement:description"
            )}
          </p>
        </div>
        <div className="location-item  four-items">
          <h3 className="label">
            {" "}
            <span>
              <SvgoperationalIcon />{" "}
            </span>
            {t(
              "consultingServices:approachToConsulting:operationalExcellence:title"
            )}
          </h3>
          <p className="value paragraph">
            {t(
              "consultingServices:approachToConsulting:operationalExcellence:description"
            )}
          </p>
        </div>

        <div className="location-item  four-items">
          <h3 className="label">
            {" "}
            <span>
              <SvgfinancialIcon />
            </span>{" "}
            {t(
              "consultingServices:approachToConsulting:workersCompensation:title"
            )}
          </h3>
          <p className="value paragraph">
            {t(
              "consultingServices:approachToConsulting:workersCompensation:description"
            )}
          </p>
        </div>
      </div>
    </Container>
  );
}

export default ApproachToConsulting;
